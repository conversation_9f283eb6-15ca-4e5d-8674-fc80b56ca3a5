package DonatePanel.mediator
{
   import DonatePanel.view.DonateWin;
   import flash.events.Event;
   import game.data.PirateMediator;
   import game.events.BaseEvent;
   import org.puremvc.as3.interfaces.INotification;
   
   public class DonateWinMediator extends PirateMediator
   {
      public static const NAME:String = "DonateWindowMediator";
      
      private var arr:Array = [];
      
      public function DonateWinMediator(param1:Object = null)
      {
         super("DonateWindowMediator",param1);
         this.getView().showHander = this.show;
         this.getView().addEventListener("DonateItem",this.enterDonatePanel);
         this.getView().addEventListener("ItemDonate",this.onClickItem);
         this.getView().addEventListener("CONFIRMFUN",this.onCllear);
      }
      
      private function show(param1:Object) : void
      {
         this.arr = [];
         this.getView().x = 210;
         this.getView().y = 130;
         this.getView().donateNum.text = String(param1);
         this.getView().donateNum.textColor = 65280;
         this.getView().curDonateNum.text = "0";
         this.getView().freshDataItem(this.arr);
      }
      
      private function onCllear(param1:Event) : void
      {
      }
      
      private function onClickItem(param1:BaseEvent) : void
      {
         this.arr.splice(this.arr.indexOf(param1.data),1);
         this.getView().freshDataItem(this.arr);
         sendNotification("DONATERETURN",param1.data);
      }
      
      private function enterDonatePanel(param1:BaseEvent) : void
      {
         var _loc2_:Object = this.checkItem(param1.data);
         if(_loc2_)
         {
            this.arr.push(_loc2_);
         }
         else
         {
            this.arr.push(param1.data);
         }
         this.getView().freshDataItem(this.arr);
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["DONATEPANEL","DevilFruitTreeContributeByItem"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc3_:Object = null;
         var _loc2_:Object = null;
         switch(param1.getName())
         {
            case "DONATEPANEL":
               _loc3_ = param1.getBody();
               _loc2_ = this.checkItem(_loc3_);
               if(_loc2_)
               {
                  this.arr.push(_loc2_);
               }
               else
               {
                  this.arr.push(_loc3_);
               }
               this.getView().freshDataItem(this.arr);
               break;
            case "DevilFruitTreeContributeByItem":
               this.arr.splice(0,this.arr.length);
               this.getView().freshDataItem(this.arr);
               this.getView().updateNum();
         }
      }
      
      private function checkItem(param1:Object) : Object
      {
         var _loc3_:Boolean = false;
         var _loc2_:Object = null;
         for each(_loc2_ in this.arr)
         {
            if(param1.slotItem.item.item_id == _loc2_.slotItem.item.item_id)
            {
               this.arr.splice(this.arr.indexOf(_loc2_),1);
               _loc2_.num += param1.num;
               _loc3_ = true;
               break;
            }
         }
         if(_loc3_)
         {
            return _loc2_;
         }
         return null;
      }
      
      public function getView() : DonateWin
      {
         return this.viewComponent as DonateWin;
      }
   }
}

