package DonatePanel.view
{
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.items.framework.templates.Template_BaseItem;
   import game.items.framework.templates.Template_CardItem;
   import game.items.framework.templates.Template_Demon;
   import game.items.framework.templates.Template_DirectUse;
   import game.items.framework.templates.Template_Equipment;
   import game.items.framework.templates.Template_Fish;
   import game.items.framework.templates.Template_Gem;
   import game.items.framework.templates.Template_GoodWill;
   import game.items.framework.templates.Template_PetEgg;
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.input.TextInput;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.slot.IBaseSlotItem;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotItem;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class DonateAlert extends PopUpWindow
   {
      private var _slot:Slot;
      
      private var _itemNameTxt:Label;
      
      private var _moneyTxt:Label;
      
      private var _descTxt:Label;
      
      private var _staticBuyLabel:Label;
      
      private var _staticValueLabel:Label;
      
      private var _buyLabel:TextInput;
      
      private var _okBtn:Button;
      
      private var _cancelBtn:Button;
      
      private var _upBtn:Button;
      
      private var _downBtn:Button;
      
      private var _maxBtn:Button;
      
      private var _okFn:Function;
      
      private var _cancelFn:Function;
      
      private var canBuyMaxNum:int;
      
      private var value:int;
      
      private var _slotItem:IBaseSlotItem;
      
      private var _maxStack:int = -1;
      
      private var _valueTxt:Label;
      
      public function DonateAlert(param1:IBaseSlotItem, param2:String, param3:Boolean = true, param4:Function = null, param5:Function = null)
      {
         super(250,225,UIManager.getUISkin("win_bg"));
         setTitleImageData(UIManager.getUISkin("seltNunTitle").bitmapData);
         pane.x += 5;
         pane.y -= 10;
         this._okFn = param4;
         this._cancelFn = param5;
         this._slotItem = param1;
         var _loc6_:UISkin = UIManager.getUISkin("group_bg");
         _loc6_.setSize(234,174);
         _loc6_.y = 5;
         pane.addChild(_loc6_);
         _loc6_ = UIManager.getUISkin("black_bg3");
         _loc6_.setSize(225,70);
         _loc6_.x = 4;
         _loc6_.y = 8;
         pane.addChild(_loc6_);
         this._slot = new Slot();
         this._slot.setItem(param1,false,false,false);
         this._slot.isShowGlowEffect = false;
         this._slot.x = 18;
         this._slot.y = 20;
         pane.addChild(this._slot);
         this._itemNameTxt = new Label("",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727],false);
         this._itemNameTxt.x = 120;
         this._itemNameTxt.y = 20;
         pane.addChild(this._itemNameTxt);
         var _loc8_:UISkin = UIManager.getUISkin("line2");
         _loc8_.width = 144;
         _loc8_.x = 80;
         _loc8_.y = 43;
         pane.addChild(_loc8_);
         var _loc7_:Label = new Label(Globalization.getString("devilFruit.14"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc7_.x = 98;
         _loc7_.y = 48;
         pane.addChild(_loc7_);
         this._valueTxt = new Label("",TextFormatLib.format_0xfff5ce_12px,[FilterLib.glow_0x272727]);
         this._valueTxt.x = 145;
         this._valueTxt.y = 48;
         pane.addChild(this._valueTxt);
         this._descTxt = new Label(Globalization.getString("devilFruit.15"),TextFormatLib.format_0xFFB932_14px,[FilterLib.glow_0x272727]);
         this._descTxt.autoSize = "center";
         this._descTxt.x = 6;
         this._descTxt.y = 83;
         this._descTxt.width = 206;
         pane.addChild(this._descTxt);
         this._buyLabel = new TextInput("1",54,TextFormatLib.format_0xFFFFFF_12px);
         this._buyLabel.x = 20;
         this._buyLabel.y = 114;
         pane.addChild(this._buyLabel);
         this._buyLabel.enabled = param3;
         this._buyLabel.addEventListener("change",this.changeBuyLabel_Handler);
         this._upBtn = new Button("",null,23,UIManager.getMultiUISkin("plus"));
         this._upBtn.x = 112;
         this._upBtn.y = 114;
         pane.addChild(this._upBtn);
         this._upBtn.enabled = param3;
         this._upBtn.addEventListener("click",this.addNum_Handler);
         this._downBtn = new Button("",null,23,UIManager.getMultiUISkin("minus"));
         this._downBtn.x = 78;
         this._downBtn.y = 114;
         pane.addChild(this._downBtn);
         this._downBtn.enabled = param3;
         this._downBtn.addEventListener("click",this.reduceNum_Handler);
         this._maxBtn = new Button(Globalization.getString("devilFruit.16"),null,60,UIManager.getMultiUISkin("btn_topMenu"));
         this._maxBtn.setTextOffset(0,-2);
         this._maxBtn.x = 150;
         this._maxBtn.y = 114;
         pane.addChild(this._maxBtn);
         this._maxBtn.addEventListener("click",this.maxClick);
         this._okBtn = new Button(Globalization.queding,TextFormatLib.format_0xFFB932_12px,68,UIManager.getMultiUISkin("btn_normal"));
         this._okBtn.x = 34;
         this._okBtn.y = 140;
         pane.addChild(this._okBtn);
         this._cancelBtn = new Button(Globalization.quxiao,TextFormatLib.format_0xFFB932_12px,68,UIManager.getMultiUISkin("btn_normal"));
         this._cancelBtn.x = 124;
         this._cancelBtn.y = 140;
         pane.addChild(this._cancelBtn);
         if(SlotItem(param1).item.template is Template_Fish)
         {
            this.value = Template_Fish(SlotItem(param1).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1).item.template is Template_Equipment)
         {
            this.value = Template_Equipment(SlotItem(param1).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1).item.template is Template_BaseItem)
         {
            this.value = Template_BaseItem(SlotItem(param1).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1).item.template is Template_GoodWill)
         {
            this.value = Template_GoodWill(SlotItem(param1).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1).item.template is Template_DirectUse)
         {
            this.value = Template_DirectUse(SlotItem(param1).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1).item.template is Template_PetEgg)
         {
            this.value = Template_PetEgg(SlotItem(param1).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1).item.template is Template_CardItem)
         {
            this.value = Template_CardItem(SlotItem(param1).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1).item.template is Template_Demon)
         {
            this.value = Template_Demon(SlotItem(param1).item.template).daimonappleDonation;
         }
         else
         {
            this.value = Template_Gem(SlotItem(param1).item.template).daimonappleDonation;
         }
         this._valueTxt.text = this.value.toString();
         this._itemNameTxt.text = SlotItem(param1).item.name;
         this._okBtn.addEventListener("click",this.doOk);
         this._cancelBtn.addEventListener("click",this.doCancel);
         this.setMaxStack(-1,param1.num);
         this._buyLabel.text = param1.num.toString();
      }
      
      private function maxClick(param1:MouseEvent) : void
      {
         this._buyLabel.text = this.canBuyMaxNum.toString();
      }
      
      public function setMaxStack(param1:int, param2:int) : void
      {
         this.canBuyMaxNum = param2;
         this._maxStack = param1;
         this._upBtn.enabled = this.canBuyMaxNum != 1;
         this._downBtn.enabled = this.canBuyMaxNum != 1;
      }
      
      private function changeBuyLabel_Handler(param1:Event) : void
      {
         var _loc2_:int = int(this._buyLabel.text);
         if(_loc2_ <= 0)
         {
            _loc2_ = 1;
         }
         if(!this._maxStack && !this.canBuyMaxNum)
         {
            this._buyLabel.text = _loc2_.toString();
            return;
         }
         if(_loc2_ > this.canBuyMaxNum)
         {
            this._buyLabel.text = this.canBuyMaxNum.toString();
         }
         else
         {
            this._buyLabel.text = _loc2_.toString();
         }
      }
      
      private function addNum_Handler(param1:MouseEvent) : void
      {
         var _loc2_:int = int(this._buyLabel.text) + 1;
         if(!this._maxStack && !this.canBuyMaxNum)
         {
            this._buyLabel.text = _loc2_.toString();
            return;
         }
         if(_loc2_ > this.canBuyMaxNum)
         {
            this._buyLabel.text = this.canBuyMaxNum.toString();
         }
         else
         {
            this._buyLabel.text = _loc2_.toString();
         }
      }
      
      private function reduceNum_Handler(param1:MouseEvent) : void
      {
         var _loc2_:int = int(this._buyLabel.text) - 1;
         if(_loc2_ > 0)
         {
            this._buyLabel.text = _loc2_.toString();
         }
      }
      
      override protected function onExitBtnClick(param1:Event) : void
      {
         this.doCancel(null);
      }
      
      private function doCancel(param1:MouseEvent) : void
      {
         this._cancelFn && this._cancelFn();
         close();
      }
      
      private function doOk(param1:MouseEvent) : void
      {
         this._okFn && this._okFn(this._slotItem,!!this._buyLabel ? uint(this._buyLabel.text) : 1);
         close();
      }
   }
}

