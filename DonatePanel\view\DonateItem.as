package DonatePanel.view
{
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import game.items.framework.interfaces.IBasicInterface;
   import game.items.framework.items.Item;
   import game.items.framework.templates.Template_BaseItem;
   import game.items.framework.templates.Template_CardItem;
   import game.items.framework.templates.Template_Demon;
   import game.items.framework.templates.Template_DirectUse;
   import game.items.framework.templates.Template_Equipment;
   import game.items.framework.templates.Template_Fish;
   import game.items.framework.templates.Template_Gem;
   import game.items.framework.templates.Template_GoodWill;
   import game.items.framework.templates.Template_PetEgg;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.infoMC.IconInfoMC;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.slot.IBaseSlotItem;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotItem;
   import mmo.ui.control.slot.SlotTemplete;
   import util.Globalization;
   
   public class DonateItem extends UISprite
   {
      private var _slot:Icon;
      
      private var _itemNameTxt:Label;
      
      private var _itemNumTxt:Label;
      
      private var _valueTxt:Label;
      
      private var _container:UISprite;
      
      private var donateTxt:Label;
      
      private var glowFilter:UISkin;
      
      public var selectBj:UISkin;
      
      private var pos:UISprite;
      
      private var _slotItem:IBaseSlotItem;
      
      private var temp:IBasicInterface;
      
      public var _data:Object;
      
      private var value:int;
      
      private var _slot_front:UISkin;
      
      public function DonateItem()
      {
         super();
         var _loc1_:UISkin = UIManager.getUISkin("descBorder");
         _loc1_.setSize(152,62);
         addChild(_loc1_);
         this.glowFilter = UIManager.getUISkin("glow_blue");
         this.glowFilter.setSize(160,70);
         this.glowFilter.x = -4;
         this.glowFilter.y = -4;
         this.glowFilter.visible = false;
         addChild(this.glowFilter);
         this.selectBj = UIManager.getUISkin("select_mask_bluej");
         this.selectBj.setSize(152,62);
         this.selectBj.visible = false;
         addChild(this.selectBj);
         buttonMode = true;
         useHandCursor = true;
         doubleClickEnabled = false;
         this._container = new UISprite();
         addChild(this._container);
         this.createUI();
         addEventListener("rollOver",this.rollOverHandler);
         addEventListener("rollOut",this.rollOutHandler);
         this.doubleClickEnabled = true;
         addEventListener("doubleClick",this.clickHandler);
      }
      
      private function clickHandler(param1:MouseEvent) : void
      {
         dispatchEvent(new MouseEvent("DOUBLE_CLICK"));
      }
      
      private function rollOutHandler(param1:MouseEvent) : void
      {
         this.glowFilter.visible = false;
         IconInfoMC.hide();
      }
      
      private function rollOverHandler(param1:MouseEvent) : void
      {
         var _loc2_:Point = null;
         this.glowFilter.visible = true;
         if(this.temp != null)
         {
            if(this._slotItem is SlotTemplete)
            {
               IconInfoMC.setContent(this.temp,null);
            }
            else
            {
               IconInfoMC.setContent(this.temp,SlotItem(this._slotItem).item);
            }
            _loc2_ = parent.localToGlobal(new Point(this.x + this.width,this.y));
            IconInfoMC.show(_loc2_,this.width);
         }
      }
      
      public function setItems(param1:Object) : void
      {
         this.clearItems();
         this._data = param1;
         var _loc3_:Item = param1.slotItem.item;
         var _loc2_:SlotItem = new SlotItem();
         _loc2_.item = _loc3_;
         _loc2_.num = param1.num;
         _loc2_.gid = 0;
         this._slotItem = _loc2_;
         this._itemNameTxt.text = _loc3_.template.name;
         this._itemNumTxt.text = _loc2_.num.toString();
         this.temp = _loc3_.template as IBasicInterface;
         this._slot.setData(UrlManager.getItemImgUrl(_loc2_.item.itemSmall));
         this.setQualityBox(this.temp.quality);
         if(SlotItem(param1.slotItem).item.template is Template_Fish)
         {
            this.value = Template_Fish(SlotItem(param1.slotItem).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1.slotItem).item.template is Template_Equipment)
         {
            this.value = Template_Equipment(SlotItem(param1.slotItem).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1.slotItem).item.template is Template_BaseItem)
         {
            this.value = Template_BaseItem(SlotItem(param1.slotItem).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1.slotItem).item.template is Template_GoodWill)
         {
            this.value = Template_GoodWill(SlotItem(param1.slotItem).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1.slotItem).item.template is Template_DirectUse)
         {
            this.value = Template_DirectUse(SlotItem(param1.slotItem).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1.slotItem).item.template is Template_PetEgg)
         {
            this.value = Template_PetEgg(SlotItem(param1.slotItem).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1.slotItem).item.template is Template_CardItem)
         {
            this.value = Template_CardItem(SlotItem(param1.slotItem).item.template).daimonappleDonation;
         }
         else if(SlotItem(param1.slotItem).item.template is Template_Demon)
         {
            this.value = Template_Demon(SlotItem(param1.slotItem).item.template).daimonappleDonation;
         }
         else
         {
            this.value = Template_Gem(SlotItem(param1.slotItem).item.template).daimonappleDonation;
         }
         this._valueTxt.text = this.value.toString();
      }
      
      private function setQualityBox(param1:int) : void
      {
         this._slot_front = Slot.getQualityBorder(param1);
         this._slot_front && this.pos.addChild(this._slot_front);
      }
      
      public function clearItems() : void
      {
         var _loc1_:Point = null;
         if(stage)
         {
            _loc1_ = this._container.globalToLocal(new Point(stage.mouseX,stage.mouseY));
            if(this._slot && _loc1_.x <= this._slot.width && _loc1_.y <= this._slot.height)
            {
               IconInfoMC.hide();
            }
         }
         mouseChildren = false;
         buttonMode = false;
         if(this._slot_front)
         {
            this._slot_front.dispose();
            this._slot_front.parent && this._slot_front.parent.removeChild(this._slot_front);
         }
      }
      
      private function createUI() : void
      {
         var _loc3_:UISkin = UIManager.getUISkin("slot_bg");
         _loc3_.x = 7;
         _loc3_.y = 8;
         this._container.addChild(_loc3_);
         this.pos = new UISprite();
         this.pos.x = 7;
         this.pos.y = 8;
         this._container.addChild(this.pos);
         this._slot = new Icon();
         this._slot.x = 7;
         this._slot.y = 8;
         this._container.addChild(this._slot);
         var _loc2_:UISkin = UIManager.getUISkin("slot_mask");
         _loc2_.x = 7;
         _loc2_.y = 8;
         this._container.addChild(_loc2_);
         var _loc1_:UISkin = UIManager.getUISkin("split_line2");
         _loc1_.width = 95;
         _loc1_.x = 55;
         _loc1_.y = 27;
         this._container.addChild(_loc1_);
         this._itemNameTxt = new Label("",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727],false);
         this._itemNameTxt.autoSize = "center";
         this._itemNameTxt.x = 56;
         this._itemNameTxt.y = 6;
         this._itemNameTxt.width = 88;
         this._container.addChild(this._itemNameTxt);
         this._itemNumTxt = new Label("",TextFormatLib.format_0xFFFFFF_12px,[FilterLib.glow_0x272727],false);
         this._itemNumTxt.autoSize = "right";
         this._itemNumTxt.x = 7;
         this._itemNumTxt.y = 38;
         this._itemNumTxt.width = 48;
         this._container.addChild(this._itemNumTxt);
         this.donateTxt = new Label(Globalization.getString("devilFruit.13"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.donateTxt.x = 63;
         this.donateTxt.y = 36;
         this._container.addChild(this.donateTxt);
         this._valueTxt = new Label("",TextFormatLib.format_0xFFED89_12px,[FilterLib.glow_0x272727]);
         this._valueTxt.x = 106;
         this._valueTxt.y = 36;
         this._container.addChild(this._valueTxt);
      }
   }
}

