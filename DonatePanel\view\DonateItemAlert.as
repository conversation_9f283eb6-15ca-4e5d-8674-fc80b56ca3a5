package DonatePanel.view
{
   import flash.events.MouseEvent;
   import game.items.ItemQualityInfo;
   import game.items.framework.templates.Template_BaseItem;
   import game.items.framework.templates.Template_CardItem;
   import game.items.framework.templates.Template_Demon;
   import game.items.framework.templates.Template_DirectUse;
   import game.items.framework.templates.Template_Equipment;
   import game.items.framework.templates.Template_Fish;
   import game.items.framework.templates.Template_Gem;
   import game.items.framework.templates.Template_GoodWill;
   import game.items.framework.templates.Template_PetEgg;
   import game.manager.UIManager;
   import game.modules.chat.msgInfo.MessageReceive;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.slot.SlotItem;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class DonateItemAlert extends PopUpWindow
   {
      public static const NAME:String = "DonateItemAlert";
      
      private var _okBtn:Button;
      
      private var _cancelBtn:Button;
      
      private var _okFn:Function;
      
      private var _cancelFn:Function;
      
      private var donateText:Label;
      
      public var sp:UISprite;
      
      private var _data:Object;
      
      private var num:int;
      
      private var scro:ScrollPane;
      
      public function DonateItemAlert(param1:Function = null, param2:Function = null)
      {
         super(280,330,UIManager.getUISkin("win_guide"));
         this.closeBtnVisible = false;
         title = Globalization.getString("devilFruit.8");
         pane.x += 5;
         pane.y -= 10;
         var _loc4_:Label = new Label(Globalization.getString("devilFruit.9"),TextFormatLib.format_0xffed89_14px,[FilterLib.glow_0x272727]);
         _loc4_.x = 15;
         _loc4_.y = 37;
         addChild(_loc4_);
         this._okFn = param1;
         this._cancelFn = param2;
         var _loc5_:UISkin = UIManager.getUISkin("group_bg");
         _loc5_.setSize(263,151);
         _loc5_.y = 35;
         pane.addChild(_loc5_);
         this.scro = new ScrollPane(252,138);
         this.scro.x = 17;
         this.scro.y = 66;
         this.addChild(this.scro);
         var _loc3_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc3_.setSize(247,43);
         _loc3_.x = 10;
         _loc3_.y = 224;
         addChild(_loc3_);
         this.donateText = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.donateText.x = 15;
         this.donateText.y = 225;
         this.donateText.width = 240;
         this.donateText.wordWrap = true;
         this.donateText.multiline = true;
         addChild(this.donateText);
         this._okBtn = new Button(Globalization.queding,TextFormatLib.format_0xFFB932_12px,68,UIManager.getMultiUISkin("button_big"));
         this._okBtn.x = 34;
         this._okBtn.y = 255;
         pane.addChild(this._okBtn);
         this._okBtn.addEventListener("click",this.doOk);
         this._cancelBtn = new Button(Globalization.quxiao,TextFormatLib.format_0xFFB932_12px,68,UIManager.getMultiUISkin("button_big"));
         this._cancelBtn.x = 154;
         this._cancelBtn.y = 255;
         pane.addChild(this._cancelBtn);
         this._cancelBtn.addEventListener("click",this.doCancel);
      }
      
      override public function show(param1:Object) : void
      {
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc6_:Object = null;
         var _loc2_:String = null;
         var _loc3_:String = null;
         var _loc4_:DonateItemTxt = null;
         this._data = param1;
         this.num = 0;
         for each(_loc6_ in param1)
         {
            _loc2_ = MessageReceive.parseColor(ItemQualityInfo.getQualityColor(_loc6_.slotItem.item.quality));
            _loc3_ = StringUtil.substitute(Globalization.getString("devilFruit.10"),_loc2_,_loc6_.slotItem.item.name,String(_loc6_.num));
            if(SlotItem(_loc6_.slotItem).item.template is Template_Fish)
            {
               _loc7_ = int(Template_Fish(SlotItem(_loc6_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc6_.slotItem).item.template is Template_Equipment)
            {
               _loc7_ = int(Template_Equipment(SlotItem(_loc6_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc6_.slotItem).item.template is Template_BaseItem)
            {
               _loc7_ = int(Template_BaseItem(SlotItem(_loc6_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc6_.slotItem).item.template is Template_GoodWill)
            {
               _loc7_ = int(Template_GoodWill(SlotItem(_loc6_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc6_.slotItem).item.template is Template_DirectUse)
            {
               _loc7_ = int(Template_DirectUse(SlotItem(_loc6_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc6_.slotItem).item.template is Template_PetEgg)
            {
               _loc7_ = int(Template_PetEgg(SlotItem(_loc6_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc6_.slotItem).item.template is Template_CardItem)
            {
               _loc7_ = int(Template_CardItem(SlotItem(_loc6_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc6_.slotItem).item.template is Template_Demon)
            {
               _loc7_ = int(Template_Demon(SlotItem(_loc6_.slotItem).item.template).daimonappleDonation);
            }
            else
            {
               _loc7_ = int(Template_Gem(SlotItem(_loc6_.slotItem).item.template).daimonappleDonation);
            }
            _loc4_ = new DonateItemTxt();
            _loc4_.txtMsg.htmlText = _loc3_;
            _loc4_.y += _loc5_;
            _loc4_.donateNum.htmlText = StringUtil.substitute(Globalization.getString("devilFruit.11"),_loc7_ * _loc6_.num);
            this.scro.addToPane(_loc4_);
            this.num += _loc7_ * _loc6_.num;
            _loc5_ += _loc4_.txtMsg.textHeight;
         }
         this.donateText.htmlText = StringUtil.substitute(Globalization.getString("devilFruit.12"),this.num);
      }
      
      private function doCancel(param1:MouseEvent) : void
      {
         this._cancelFn && this._cancelFn();
         close();
      }
      
      private function doOk(param1:MouseEvent) : void
      {
         var _loc4_:Array = null;
         var _loc2_:Object = null;
         var _loc3_:Array = null;
         if(this._data)
         {
            _loc4_ = [];
            for each(_loc2_ in this._data)
            {
               _loc3_ = [_loc2_.slotItem.item.item_id,_loc2_.num];
               _loc4_.push(_loc3_);
            }
         }
         this._okFn && this._okFn(_loc4_,this.num);
         close();
      }
   }
}

import mmo.ext.filter.FilterLib;
import mmo.ext.font.TextFormatLib;
import mmo.ui.control.UISprite;
import mmo.ui.control.label.Label;

class DonateItemTxt extends UISprite
{
   public var txtMsg:Label;
   
   public var donateNum:Label;
   
   public function DonateItemTxt()
   {
      super();
      this.txtMsg = new Label("",TextFormatLib.format_0xFFB932_14px,[FilterLib.glow_0x272727]);
      this.txtMsg.x = 5;
      this.txtMsg.width = 120;
      addChild(this.txtMsg);
      this.donateNum = new Label("",TextFormatLib.format_0xFFB932_14px,[FilterLib.glow_0x272727]);
      this.donateNum.x = 135;
      this.donateNum.width = 100;
      addChild(this.donateNum);
   }
}
