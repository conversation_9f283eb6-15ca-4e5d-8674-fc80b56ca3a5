package DonatePanel.view
{
   import DonatePanel.mediator.DonateWinMediator;
   import flash.events.MouseEvent;
   import flash.utils.Dictionary;
   import game.drag.DragSprite;
   import game.drag.IDropable;
   import game.events.BaseEvent;
   import game.events.PageNavigatorEvent;
   import game.items.framework.templates.Template_BaseItem;
   import game.items.framework.templates.Template_CardItem;
   import game.items.framework.templates.Template_Demon;
   import game.items.framework.templates.Template_DirectUse;
   import game.items.framework.templates.Template_Equipment;
   import game.items.framework.templates.Template_Fish;
   import game.items.framework.templates.Template_Gem;
   import game.items.framework.templates.Template_GoodWill;
   import game.items.framework.templates.Template_PetEgg;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   import mmo.ui.control.slot.SlotItem;
   import mmo.ui.control.tab.TabPane;
   import mmo.ui.control.window.PopUpWindow;
   import mmo.utils.Guid;
   import util.Globalization;
   
   public class DonateWin extends PopUpWindow implements IDropable
   {
      private var items:Dictionary;
      
      private var _tab_bg:UISkin;
      
      public var container:UISprite;
      
      public var tabBar:TabPane;
      
      public var pageNavigator:PageNavigator;
      
      public var donateBtn:Button;
      
      private const W:uint = 3;
      
      private const H:uint = 3;
      
      private var _arr:Array;
      
      public var donateNum:Label;
      
      public var curDonateNum:Label;
      
      private var _num:int;
      
      public function DonateWin()
      {
         var _loc4_:uint = 0;
         var _loc3_:DonateItem = null;
         super(519,420);
         this.isLive = false;
         this.allowDrag = true;
         setTitleImageData(UIManager.getUISkin("donateTitle").bitmapData);
         this.items = new Dictionary();
         this._tab_bg = UIManager.getUISkin("text_bg_2");
         this._tab_bg.setSize(450,25);
         this._tab_bg.y = -5;
         pane.addChild(this._tab_bg);
         var _loc9_:Label = new Label(Globalization.getString("devilFruit.5"),TextFormatLib.format_0xffed89_14px,[FilterLib.glow_0x272727]);
         _loc9_.x = 130;
         _loc9_.y = 32;
         addChild(_loc9_);
         this.donateNum = new Label("",TextFormatLib.format_0xFFFFFF_18px_verdana,[FilterLib.glow_0x272727]);
         this.donateNum.x = _loc9_.x + 80;
         this.donateNum.y = _loc9_.y - 4;
         addChild(this.donateNum);
         var _loc8_:Label = new Label(Globalization.getString("devilFruit.6"),TextFormatLib.format_0x21aeff_12px,[FilterLib.glow_0x272727]);
         _loc8_.textColor = 65280;
         _loc8_.x = 330;
         _loc8_.y = 33;
         addChild(_loc8_);
         var _loc6_:UISkin = UIManager.getUISkin("group_bg");
         _loc6_.setSize(496,290);
         _loc6_.x = 6;
         _loc6_.y = 25;
         pane.addChild(_loc6_);
         this.container = new UISprite();
         this.container.y = 35;
         this.container.x = 15;
         pane.addChild(this.container);
         this.donateBtn = new Button("",null,75,UIManager.getMultiUISkin("donateBtn"));
         this.donateBtn.x = 190;
         this.donateBtn.y = 360;
         addChild(this.donateBtn);
         this.donateBtn.enabled = false;
         this.donateBtn.addEventListener("click",this.donateClick);
         var _loc7_:Label = new Label(Globalization.getString("devilFruit.17"),TextFormatLib.format_0x21aeff_12px,[FilterLib.glow_0x272727]);
         _loc7_.textColor = 65280;
         _loc7_.x = 330;
         _loc7_.y = 370;
         addChild(_loc7_);
         var _loc1_:Label = new Label(Globalization.getString("devilFruit.7"),TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         _loc1_.x = 160;
         _loc1_.y = 290;
         addChild(_loc1_);
         this.curDonateNum = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.curDonateNum.x = 315;
         this.curDonateNum.y = _loc1_.y;
         addChild(this.curDonateNum);
         var _loc2_:uint = 0;
         while(_loc2_ < 3)
         {
            _loc4_ = 0;
            while(_loc4_ < 3)
            {
               _loc3_ = new DonateItem();
               _loc3_.visible = false;
               this.container.addChild(_loc3_);
               _loc3_.x = _loc4_ * (_loc3_.width + 2);
               _loc3_.y = _loc2_ * _loc3_.height;
               this.items[_loc2_ * 3 + _loc4_] = _loc3_;
               _loc4_++;
            }
            _loc2_++;
         }
         var _loc5_:String = Guid.getUID();
         this.pageNavigator = new PageNavigator();
         pane.addChild(this.pageNavigator);
         this.pageNavigator.x = (this.width - this.pageNavigator.width) / 2 - 20;
         this.pageNavigator.y = 283;
         this.pageNavigator.addEventListener("pageChange",this.changePage_Handler);
         AppFacade.instance.registerMediator(new DonateWinMediator(this));
      }
      
      private function donateClick(param1:MouseEvent) : void
      {
         var _loc2_:DonateItemAlert = new DonateItemAlert(this.confirmFun);
         PopUpCenter.addPopUp("DonateItemAlert",_loc2_,true,true,this._arr);
         _loc2_.y = 195;
      }
      
      public function confirmFun(param1:Array, param2:int) : void
      {
         this._num = param2;
         if(param1)
         {
            AppFacade.instance.sendNotification("DONATEITEM",[param1,param2]);
         }
      }
      
      private function changePage_Handler(param1:PageNavigatorEvent) : void
      {
         var _loc2_:uint = param1.currentPage;
         this.freshDataItem(this._arr,_loc2_);
      }
      
      public function dragHandler(param1:String, param2:DragSprite, param3:IDropable) : void
      {
         var _loc5_:SlotItem = null;
         var _loc4_:DonateAlert = null;
         switch(param1)
         {
            case "dragOver":
            case "dragOut":
               break;
            case "dragDrop":
               _loc5_ = SlotItem(param2.data);
               if(_loc5_.num == 1)
               {
                  this.confirmSell(_loc5_,_loc5_.num);
               }
               else
               {
                  _loc4_ = new DonateAlert(_loc5_,null,true,this.confirmSell);
                  PopUpCenter.addPopUp(Guid.getUID(),_loc4_,true,true);
               }
               param2.sourceSp.dragOver();
               param2.dragOver();
         }
      }
      
      public function freshDataItem(param1:Array, param2:uint = 1) : void
      {
         var _loc6_:int = 0;
         var _loc4_:* = undefined;
         this._arr = param1;
         if(param1.length == 0 || param1 == null)
         {
            this.donateBtn.enabled = false;
         }
         this.pageNavigator.init(param2,Math.ceil(param1.length / 9));
         param2 = param2;
         var _loc5_:uint = uint((param2 - 1) * 3 * 3);
         while(_loc5_ < param2 * 3 * 3)
         {
            DonateItem(this.items[_loc5_ % (3 * 3)]).visible = false;
            if(param1[_loc5_])
            {
               DonateItem(this.items[_loc5_ % (3 * 3)]).visible = true;
               DonateItem(this.items[_loc5_ % (3 * 3)]).selectBj.visible = false;
               DonateItem(this.items[_loc5_ % (3 * 3)]).name = param1[_loc5_].slotItem.item.item_id.toString();
               DonateItem(this.items[_loc5_ % (3 * 3)]).setItems(param1[_loc5_]);
               DonateItem(this.items[_loc5_ % (3 * 3)]).addEventListener("DOUBLE_CLICK",this.onDouble);
               DonateItem(this.items[_loc5_ % (3 * 3)]).buttonMode = true;
               this.donateBtn.enabled = true;
            }
            _loc5_++;
         }
         var _loc3_:Number = 0;
         for each(_loc4_ in param1)
         {
            if(SlotItem(_loc4_.slotItem).item.template is Template_Fish)
            {
               _loc6_ = int(Template_Fish(SlotItem(_loc4_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc4_.slotItem).item.template is Template_Equipment)
            {
               _loc6_ = int(Template_Equipment(SlotItem(_loc4_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc4_.slotItem).item.template is Template_BaseItem)
            {
               _loc6_ = int(Template_BaseItem(SlotItem(_loc4_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc4_.slotItem).item.template is Template_GoodWill)
            {
               _loc6_ = int(Template_GoodWill(SlotItem(_loc4_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc4_.slotItem).item.template is Template_DirectUse)
            {
               _loc6_ = int(Template_DirectUse(SlotItem(_loc4_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc4_.slotItem).item.template is Template_PetEgg)
            {
               _loc6_ = int(Template_PetEgg(SlotItem(_loc4_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc4_.slotItem).item.template is Template_CardItem)
            {
               _loc6_ = int(Template_CardItem(SlotItem(_loc4_.slotItem).item.template).daimonappleDonation);
            }
            else if(SlotItem(_loc4_.slotItem).item.template is Template_Demon)
            {
               _loc6_ = int(Template_Demon(SlotItem(_loc4_.slotItem).item.template).daimonappleDonation);
            }
            else
            {
               _loc6_ = int(Template_Gem(SlotItem(_loc4_.slotItem).item.template).daimonappleDonation);
            }
            _loc3_ += _loc6_ * _loc4_.num;
         }
         this.curDonateNum.text = String(_loc3_);
      }
      
      private function onDouble(param1:MouseEvent) : void
      {
         dispatchEvent(new BaseEvent("ItemDonate",param1.currentTarget._data));
      }
      
      override public function close() : void
      {
         super.close();
         if(PopUpCenter.containsWin("DonateBag"))
         {
            AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("DonateBag",ModuleParams.act_Close,false,false));
         }
      }
      
      override public function dispose() : void
      {
         super.dispose();
         AppFacade.instance.removeMediator("DonateWindowMediator");
      }
      
      private function confirmSell(param1:SlotItem, param2:uint) : void
      {
         var _loc3_:Object = {
            "slotItem":param1,
            "num":param2
         };
         dispatchEvent(new BaseEvent("DonateItem",_loc3_));
         AppFacade.instance.sendNotification("DONATEDATA",_loc3_);
      }
      
      public function updateNum() : void
      {
         this.donateNum.text = String(Number(this.donateNum.text) + this._num);
      }
   }
}

