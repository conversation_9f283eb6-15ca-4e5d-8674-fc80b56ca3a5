package activity.command
{
   import activity.proxy.ActivityCommonProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class ActivityCommonCommand extends SimpleCommand
   {
      public function ActivityCommonCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc5_:Object = null;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         var _loc2_:Boolean = false;
         var _loc3_:int = 0;
         if(!facade.hasProxy("activity.proxy.ActivityCommonProxy"))
         {
            facade.registerProxy(new ActivityCommonProxy());
         }
         var _loc4_:ActivityCommonProxy = facade.retrieveProxy("activity.proxy.ActivityCommonProxy") as ActivityCommonProxy;
         _loc5_ = param1.getBody();
         switch(param1.getName())
         {
            case "CS_CANRECIEVE_SHIP":
               _loc4_.getPrizePerDayInfo(String(param1.getBody()));
               break;
            case "CS_RECIEVE_CARDSHIP":
               _loc4_.getPrizePerDay("cardshop");
               break;
            case "CS_OPEN_BLACK_SHOPVIP":
               _loc7_ = int(_loc5_.cost);
               _loc6_ = int(_loc5_.type);
               _loc4_.openBlackVip(_loc7_,_loc6_);
               break;
            case "CS_SET_BLACKSHOP_MC_SHOW":
               _loc2_ = Boolean(_loc5_.isOpen);
               if(_loc2_)
               {
                  _loc3_ = 1;
               }
               else
               {
                  _loc3_ = 0;
               }
               _loc4_.setBlackShopMC(_loc3_);
               break;
            case "CS_BLACKSHOP_ACITIVYT_OPEN":
               _loc4_.getBlackShopInfo();
         }
      }
   }
}

