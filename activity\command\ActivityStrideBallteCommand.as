package activity.command
{
   import activity.proxy.ActivityStrideBattleProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class ActivityStrideBallteCommand extends SimpleCommand
   {
      public function ActivityStrideBallteCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc3_:ActivityStrideBattleProxy = facade.retrieveProxy("activity.proxy.ActivityStrideBattleProxy") as ActivityStrideBattleProxy;
         var _loc2_:Object = param1.getBody();
         switch(param1.getName())
         {
            case "CS_SERVICECHALLENGE_APPLY":
               _loc3_.applyStride();
               break;
            case "CS_GET_SERVICECHALLENGE_PRIZE":
               _loc3_.getStridePrize(int(_loc2_));
               break;
            case "CS_GUILDCHALLENGE_APPLY":
               _loc3_.applyGuildStride();
               break;
            case "CS_GUILDCHALLENGE_GETPRIZE":
               _loc3_.getGuildBattlePrize(int(_loc2_));
               break;
            case "CS_TEAM_CHALLENGE_DISMISS":
               _loc3_.dismissTeam();
               break;
            case "CS_BOAT_CHALLENGE_ISAPPLY":
               _loc3_.getBoatApplyInfo();
               break;
            case "CS_BOAT_CHALLENGE_APPLY":
               _loc3_.boatChallengeApply();
               break;
            case "CS_PIRATE_ARENA_ISAPPLY":
               _loc3_.getPirateArenaApplyInfo();
               break;
            case "CS_PIRATE_ARENA_APPLY":
               _loc3_.pirateArenaApply();
               break;
            case "CS_PIRATE_ARENA_UPDATE_INFO":
               _loc3_.pirateArenaUpdateFightInfo();
               break;
            case "CS_TOGET_STRONGWORLD_FIGHT":
               _loc3_.getStrongWorld();
               break;
            case "CS_STRONG_SAVEFORMATION":
               _loc3_.saveStrongFormation();
               break;
            case "CS_TOGET_STRONG_WORLD_RANK":
               _loc3_.getStrongWorldRank();
               break;
            case "CS_STRONG_HAS_SAVED":
               _loc3_.hasSavedStrongWorld();
         }
      }
   }
}

