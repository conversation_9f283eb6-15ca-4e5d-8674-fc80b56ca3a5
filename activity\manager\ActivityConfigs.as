package activity.manager
{
   public class ActivityConfigs
   {
      public static const ACTIVITY_LIST_MAX:int = 10;
      
      public static const ACTIVITY_BG_HEIGHT:int = 430;
      
      public static const ENTER_PIRATE_BATTLE:String = "activity.manager.ENTER_PIRATE_BATTLE";
      
      public static const JOIN_PIRATE_ARENA:String = "activity.manager.ENTER_PIRATE_ARENA";
      
      public static const PIRATE_BATTLE_LEVEL_LIMIT:int = 40;
      
      public static const WORLD_PIRATE_BATTLE_LEVEL_LIMIT:int = 130;
      
      public static const HONOUR_SHOP:String = "honourShop";
      
      public static const WGW_HONOUR_SHOP:String = "wgwHonourShop";
      
      public static const CARD_SHOP:String = "cardshop";
      
      public static const WORLD_TREE_SHOP:String = "worldtree";
      
      public static const VIEW_PRIZE_STRIDE:String = "view_prize_stride";
      
      public static const VIEW_PRIZE_WORLD_TREE:String = "view_prize_world_tree";
      
      public function ActivityConfigs()
      {
         super();
      }
   }
}

