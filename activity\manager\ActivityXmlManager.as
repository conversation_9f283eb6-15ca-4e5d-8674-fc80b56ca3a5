package activity.manager
{
   import game.manager.XmlManager;
   import game.modules.activity.utils.ActivityUtils;
   
   public class ActivityXmlManager
   {
      public function ActivityXmlManager()
      {
         super();
      }
      
      public static function getActivityExchangeInfo(param1:String) : Array
      {
         var _loc5_:XML = null;
         var _loc4_:Object = null;
         var _loc3_:Array = [];
         var _loc2_:XML = XmlManager.honourShop;
         if(param1 == "honourShop")
         {
            _loc2_ = XmlManager.honourShop;
         }
         else if(param1 == "wgwHonourShop")
         {
            _loc2_ = XmlManager.wgwHonourShop;
         }
         else if(param1 == "cardshop")
         {
            _loc2_ = XmlManager.getXml("card_exchange");
         }
         else if(param1 == "worldtree")
         {
            _loc2_ = XmlManager.getXml("donation_exchange");
         }
         for each(_loc5_ in _loc2_.children())
         {
            _loc4_ = {};
            if(param1 == "honourShop")
            {
               _loc4_.integral = int(_loc5_.@consumeHonour);
            }
            else if(param1 == "cardshop")
            {
               _loc4_.integral = int(_loc5_.@consumeChip);
            }
            else if(param1 == "worldtree")
            {
               _loc4_.integral = int(_loc5_.@needDonation);
            }
            _loc4_.exchangeId = int(_loc5_.@id);
            _loc4_.tempId = int(_loc5_.@itemTempId);
            _loc4_.lv = int(_loc5_.@neeLevel);
            _loc4_.prestige = int(_loc5_.@needPrestige);
            _loc4_.getNum = int(_loc5_.@getNum);
            _loc4_.canBatch = int(_loc5_.@canBatch);
            _loc4_.limitTimes = int(_loc5_.@limitTime);
            _loc3_.push(_loc4_);
         }
         return _loc3_;
      }
      
      public static function getExchangeInfoByExchangeId(param1:int, param2:String) : Object
      {
         var _loc4_:* = null;
         if(param2 == "honourShop")
         {
            _loc4_ = XmlManager.honourShop.children().(@id == param1);
         }
         else if(param2 == "wgwHonourShop")
         {
            _loc4_ = XmlManager.wgwHonourShop.children().(@id == param1);
         }
         else if(param2 == "cardshop")
         {
            _loc4_ = XmlManager.getXml("card_exchange").children().(@id == param1);
         }
         else if(param2 == "worldtree")
         {
            _loc4_ = XmlManager.getXml("donation_exchange").children().(@id == param1);
         }
         var _loc3_:Object = {};
         _loc3_.num = int(_loc4_.@getNum);
         if(param2 == "honourShop")
         {
            _loc3_.integral = int(_loc4_.@consumeHonour);
         }
         else if(param2 == "wgwHonourShop")
         {
            _loc3_.integral = int(_loc4_.@consumeHonour);
         }
         else if(param2 == "cardshop")
         {
            _loc3_.integral = int(_loc4_.@consumeChip);
         }
         else if(param2 == "worldtree")
         {
            _loc3_.integral = int(_loc4_.@needDonation);
         }
         _loc3_.tempId = int(_loc4_.@itemTempId);
         _loc3_.lv = int(_loc4_.@neeLevel);
         _loc3_.prestige = int(_loc4_.@needPrestige);
         _loc3_.limitTimes = int(_loc4_.@limitTime);
         _loc3_.canBatch = int(_loc4_.@canBatch);
         return _loc3_;
      }
      
      public static function getPrizeTypeIds(param1:int, param2:int, param3:int) : Array
      {
         var _loc4_:XML = null;
         _loc4_ = XmlManager.getXml("conquest").children().(@id == 1)[0];
         if(!_loc4_)
         {
            return null;
         }
         if(param2 == 0)
         {
            if(param3 == 0)
            {
               return <EMAIL>(",");
            }
            if(param3 == 1)
            {
               return <EMAIL>(",");
            }
         }
         else if(param2 == 1)
         {
            if(param3 == 0)
            {
               return <EMAIL>(",");
            }
            if(param3 == 1)
            {
               return <EMAIL>(",");
            }
         }
         return <EMAIL>(",");
      }
      
      private static function setPrizeInfoByPrizeId(param1:Array, param2:int) : Object
      {
         var _loc3_:Object = {};
         param1.reverse();
         if(param1.indexOf(String(param2)) != -1)
         {
            _loc3_.isHasPrize = true;
            _loc3_.index = param1.indexOf(String(param2));
            _loc3_.desc = ActivityUtils.getPrizeDesc(param1.indexOf(String(param2)));
         }
         else
         {
            _loc3_.isHasPrize = false;
         }
         return _loc3_;
      }
      
      public static function getGroupPrizeInfoById(param1:int, param2:int) : Object
      {
         var _loc6_:XML = null;
         var _loc4_:Object = null;
         var _loc5_:Array = null;
         var _loc3_:Array = null;
         _loc6_ = XmlManager.getXml("conquest").children().(@id == 1)[0];
         if(_loc6_ == null)
         {
            return null;
         }
         _loc4_ = {};
         _loc5_ = <EMAIL>(",");
         _loc3_ = <EMAIL>(",");
         _loc4_.atServer = 0;
         if(_loc5_.indexOf(String(param2)) != -1)
         {
            _loc4_.isHasPrize = true;
            _loc4_.atGroup = 0;
            _loc4_.index = _loc5_.indexOf(String(param2));
            _loc4_.desc = ActivityUtils.getPrizeDesc(_loc5_.indexOf(String(param2)));
            return _loc4_;
         }
         if(_loc3_.indexOf(String(param2)) != -1)
         {
            _loc4_.isHasPrize = true;
            _loc4_.atGroup = 1;
            _loc4_.index = _loc3_.indexOf(String(param2));
            _loc4_.desc = ActivityUtils.getPrizeDesc(_loc3_.indexOf(String(param2)));
            return _loc4_;
         }
         _loc4_.isHasPrize = false;
         return _loc4_;
      }
      
      public static function getWorldPrizeInfoById(param1:int, param2:int) : Object
      {
         var _loc6_:XML = null;
         var _loc4_:Object = null;
         var _loc5_:Array = null;
         var _loc3_:Array = null;
         _loc6_ = XmlManager.getXml("conquest").children().(@id == 1)[0];
         if(_loc6_ == null)
         {
            return null;
         }
         _loc4_ = {};
         _loc5_ = <EMAIL>(",");
         _loc3_ = <EMAIL>(",");
         _loc4_.atServer = 1;
         if(_loc5_.indexOf(String(param2)) != -1)
         {
            _loc4_.isHasPrize = true;
            _loc4_.atGroup = 0;
            _loc4_.index = _loc5_.indexOf(String(param2));
            _loc4_.desc = ActivityUtils.getPrizeDesc(_loc5_.indexOf(String(param2)));
            return _loc4_;
         }
         if(_loc3_.indexOf(String(param2)) != -1)
         {
            _loc4_.isHasPrize = true;
            _loc4_.atGroup = 1;
            _loc4_.index = _loc3_.indexOf(String(param2));
            _loc4_.desc = ActivityUtils.getPrizeDesc(_loc3_.indexOf(String(param2)));
            return _loc4_;
         }
         _loc4_.isHasPrize = false;
         return _loc4_;
      }
      
      public static function getPrizeInfoByIndex(param1:int, param2:int, param3:int, param4:int) : Object
      {
         var _loc10_:XML = null;
         var _loc6_:Object = null;
         var _loc9_:Array = null;
         var _loc7_:Array = null;
         var _loc8_:Array = null;
         var _loc5_:Array = null;
         _loc10_ = XmlManager.getXml("conquest").children().(@id == 1)[0];
         if(!_loc10_)
         {
            return false;
         }
         if(param2 == 0)
         {
            _loc9_ = <EMAIL>(",");
            _loc7_ = <EMAIL>(",");
            if(param3 == 0)
            {
               _loc6_ = setPrizeInfoByPrizeId(_loc9_,param4);
            }
            else if(param3 == 1)
            {
               _loc6_ = setPrizeInfoByPrizeId(_loc7_,param4);
            }
         }
         else if(param2 == 1)
         {
            _loc8_ = <EMAIL>(",");
            _loc5_ = <EMAIL>(",");
            if(param3 == 0)
            {
               _loc6_ = setPrizeInfoByPrizeId(_loc8_,param4);
            }
            else if(param3 == 1)
            {
               _loc6_ = setPrizeInfoByPrizeId(_loc5_,param4);
            }
         }
         return _loc6_;
      }
      
      public static function getCardOpenLevel() : int
      {
         var _loc2_:XMLList = XmlManager.getXml("card_config").card_config;
         return int(_loc2_.@openlevel);
      }
      
      public static function getGuildBattlePrizeIds(param1:int, param2:int) : Array
      {
         var _loc3_:XML = null;
         _loc3_ = XmlManager.getXml("guildConquest").children().(@id == param1)[0];
         if(!_loc3_)
         {
            return null;
         }
         if(param2 == 0)
         {
            return <EMAIL>(",");
         }
         return <EMAIL>(",");
      }
   }
}

