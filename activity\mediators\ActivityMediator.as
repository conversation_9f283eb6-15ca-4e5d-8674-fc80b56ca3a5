package activity.mediators
{
   import activity.command.ActivityCommonCommand;
   import activity.command.ActivityStrideBallteCommand;
   import activity.manager.ActivityXmlManager;
   import activity.proxy.ActivityCommonProxy;
   import activity.proxy.ActivityStrideBattleProxy;
   import activity.view.ActivityWindow;
   import flash.geom.Point;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.items.ItemManager;
   import game.items.ItemQualityInfo;
   import game.items.framework.interfaces.IBasicInterface;
   import game.modules.activity.command.ActivityCommand;
   import game.modules.activity.command.ActivityWorldGroupWarCommand;
   import game.modules.activity.proxy.ActivityProxy;
   import game.modules.activity.proxy.ActivityWorldGroupWarProxy;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.mvc.AppFacade;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   
   public class ActivityMediator extends PirateMediator
   {
      public static const NAME:String = "activity.mediators.ActivityMediator";
      
      private var _activetyWindow:ActivityWindow;
      
      private var _showData:Object;
      
      private var _needDataArr:Array = [12,13];
      
      public function ActivityMediator(param1:ActivityWindow = null)
      {
         super("activity.mediators.ActivityMediator");
         this._activetyWindow = param1 as ActivityWindow;
         this._activetyWindow.showHander = this.showHandler;
      }
      
      private function showHandler(param1:Object) : void
      {
         this._showData = param1;
         MainData.getInstance().serviceChallengeData.resetAvailable();
         MainData.getInstance().guildChallengeData.resetAvailable();
         MainData.getInstance().teamChallengeData.resetAvailable();
         checkDataAvialable(this.onComplete);
      }
      
      private function onComplete() : void
      {
         sendNotification("CS_CANRECIEVE_SHIP","cardshop");
         if(MainData.getInstance().userData.guild_id != 0)
         {
            sendNotification("CS_GET_MYGUILD_INFO_ACTIVITY");
         }
         sendNotification("CS_BOAT_CHALLENGE_ISAPPLY");
         sendNotification("CS_PIRATE_ARENA_ISAPPLY");
         sendNotification("CS_STRONG_HAS_SAVED");
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().groupData,MainData.getInstance().serviceChallengeData,MainData.getInstance().guildChallengeData,MainData.getInstance().teamChallengeData,MainData.getInstance().guildData];
      }
      
      private function onHonourExchange(param1:int, param2:int, param3:Point) : void
      {
         var userBagGridNum:int;
         var temp:IBasicInterface;
         var itemName:String;
         var color:uint;
         var itemColor:String;
         var exchangeId:int = param1;
         var exchangeNum:int = param2;
         var itemPoint:Point = param3;
         var itemInfo:Object = null;
         var tempId:int = 0;
         var unitIntegral:int = 0;
         var popText:String = null;
         var proxy:ActivityProxy = facade.retrieveProxy("ActivityProxy") as ActivityProxy;
         itemInfo = ActivityXmlManager.getExchangeInfoByExchangeId(exchangeId,"honourShop");
         tempId = int(itemInfo.tempId);
         unitIntegral = int(itemInfo.integral);
         var lv:int = int(itemInfo.lv);
         var prestige:int = int(itemInfo.prestige);
         var userLv:int = MainData.getInstance().groupData.roleModle.level;
         var userPrestige:int = MainData.getInstance().userData.prestige_num;
         var exchangeTimes:int = proxy.getItemExchangeTimes(exchangeId);
         if(itemInfo.limitTimes > 0)
         {
            if(exchangeTimes >= itemInfo.limitTimes)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("activity.53"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
         }
         if(proxy.honourIntegral < unitIntegral * exchangeNum)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.32"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(userLv < lv)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.33"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(userPrestige < prestige)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.34"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         userBagGridNum = MainData.getInstance().bagData.userBag.getLastGridsNum();
         if(userBagGridNum == 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("equipment.43"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         temp = ItemManager.getInstance().getItemTemplate(tempId.toString());
         itemName = temp.name;
         color = ItemQualityInfo.getQualityColor(temp.quality);
         itemColor = MessageReceive.parseColor(color);
         if(itemInfo.limitTimes > 0)
         {
            popText = StringUtil.substitute(Globalization.getString("activity.54"),itemName,itemColor,itemInfo.limitTimes - exchangeTimes,itemInfo.num * exchangeNum);
         }
         else
         {
            popText = StringUtil.substitute(Globalization.getString("activity.55"),itemName,itemColor,itemInfo.num * exchangeNum);
         }
         PopUpCenter.confirmWin(popText,function():void
         {
            sendNotification("CS_HONOUR_SHOP_EXCHANGE",{
               "exchangeId":exchangeId,
               "tempId":tempId,
               "getNum":itemInfo.num * exchangeNum,
               "itemPoint":itemPoint,
               "integral":unitIntegral * exchangeNum,
               "exchangeType":"honourShop",
               "exchangeNum":exchangeNum
            });
         },null,0,true);
      }
      
      private function onWgwHonourExchange(param1:int, param2:int, param3:Point) : void
      {
         var userBagGridNum:int = 0;
         var temp:IBasicInterface = null;
         var itemName:String = null;
         var color:uint = 0;
         var itemColor:String = null;
         var itemInfo:Object = null;
         var tempId:int = 0;
         var unitIntegral:int = 0;
         var exchangeId:int = 0;
         var exchangeNum:int = 0;
         var itemPoint:Point = null;
         itemInfo = null;
         tempId = 0;
         unitIntegral = 0;
         exchangeId = param1;
         exchangeNum = param2;
         itemPoint = param3;
         itemInfo = null;
         tempId = 0;
         unitIntegral = 0;
         var popText:String = null;
         var proxy:ActivityProxy = facade.retrieveProxy("ActivityProxy") as ActivityProxy;
         itemInfo = ActivityXmlManager.getExchangeInfoByExchangeId(exchangeId,"wgwHonourShop");
         tempId = int(itemInfo.tempId);
         unitIntegral = int(itemInfo.integral);
         var lv:int = int(itemInfo.lv);
         var prestige:int = int(itemInfo.prestige);
         var userLv:int = MainData.getInstance().groupData.roleModle.level;
         var userPrestige:int = MainData.getInstance().userData.prestige_num;
         var exchangeTimes:int = proxy.getItemExchangeTimes(exchangeId);
         if(itemInfo.limitTimes > 0)
         {
            if(exchangeTimes >= itemInfo.limitTimes)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("activity.53"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
         }
         if(proxy.honourIntegral < unitIntegral * exchangeNum)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("worldGroupWar.13"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(userLv < lv)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.33"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(userPrestige < prestige)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.34"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         userBagGridNum = MainData.getInstance().bagData.userBag.getLastGridsNum();
         if(userBagGridNum == 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("equipment.43"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         temp = ItemManager.getInstance().getItemTemplate(tempId.toString());
         itemName = temp.name;
         color = ItemQualityInfo.getQualityColor(temp.quality);
         itemColor = MessageReceive.parseColor(color);
         if(itemInfo.limitTimes > 0)
         {
            popText = StringUtil.substitute(Globalization.getString("activity.54"),itemName,itemColor,itemInfo.limitTimes - exchangeTimes,itemInfo.num * exchangeNum);
         }
         else
         {
            popText = StringUtil.substitute(Globalization.getString("activity.55"),itemName,itemColor,itemInfo.num * exchangeNum);
         }
         PopUpCenter.confirmWin(popText,function():void
         {
            sendNotification("CS_HONOUR_SHOP_EXCHANGE_WGW",{
               "exchangeId":exchangeId,
               "tempId":tempId,
               "getNum":itemInfo.num * exchangeNum,
               "itemPoint":itemPoint,
               "integral":unitIntegral * exchangeNum,
               "exchangeType":"wgwHonourShop",
               "exchangeNum":exchangeNum
            });
         },null,0,true);
      }
      
      override public function onRegister() : void
      {
         if(!facade.hasProxy("ActivityProxy"))
         {
            facade.registerProxy(new ActivityProxy());
         }
         if(!facade.hasProxy("activity.proxy.ActivityStrideBattleProxy"))
         {
            facade.registerProxy(new ActivityStrideBattleProxy());
         }
         if(!facade.hasProxy("activity.proxy.ActivityWorldGroupWarProxy"))
         {
            facade.registerProxy(new ActivityWorldGroupWarProxy());
         }
         facade.registerCommand("GETBOSSBOT",ActivityCommand);
         facade.registerCommand("SETBOSSBOT",ActivityCommand);
         facade.registerCommand("UNSETBOSSBOT",ActivityCommand);
         facade.registerCommand("CS_TOGET_BATTLEFIGHT_INFO",ActivityCommand);
         facade.registerCommand("CS_SET_BATTLEFIGHT_AUTO",ActivityCommand);
         facade.registerCommand("CS_ENTER_HONOUR_SHOP",ActivityCommand);
         facade.registerCommand("CS_HONOUR_SHOP_EXCHANGE",ActivityCommand);
         facade.registerCommand("CS_SERVICECHALLENGE_APPLY",ActivityStrideBallteCommand);
         facade.registerCommand("CS_GET_SERVICECHALLENGE_PRIZE",ActivityStrideBallteCommand);
         facade.registerCommand("CS_ACTIVITY_CARD_LOGIN",ActivityCommand);
         facade.registerCommand("CS_CANRECIEVE_SHIP",ActivityCommonCommand);
         facade.registerCommand("CS_RECIEVE_CARDSHIP",ActivityCommonCommand);
         facade.registerCommand("CS_GET_MYGUILD_INFO_ACTIVITY",ActivityCommand);
         facade.registerCommand("CS_GUILDCHALLENGE_APPLY",ActivityStrideBallteCommand);
         facade.registerCommand("CS_GUILDCHALLENGE_GETPRIZE",ActivityStrideBallteCommand);
         facade.registerCommand("SAVE_BOSS_LINEUP",ActivityCommand);
         facade.registerCommand("CS_TEAM_CHALLENGE_DISMISS",ActivityStrideBallteCommand);
         facade.registerCommand("CS_BOAT_CHALLENGE_ISAPPLY",ActivityStrideBallteCommand);
         facade.registerCommand("CS_BOAT_CHALLENGE_APPLY",ActivityStrideBallteCommand);
         facade.registerCommand("CS_ACTIVITY_BOAT_LOGIN",ActivityCommand);
         facade.registerCommand("CS_PIRATE_ARENA_ISAPPLY",ActivityStrideBallteCommand);
         facade.registerCommand("CS_PIRATE_ARENA_APPLY",ActivityStrideBallteCommand);
         facade.registerCommand("CS_PIRATE_ARENA_UPDATE_INFO",ActivityStrideBallteCommand);
         facade.registerCommand("CS_TOGET_STRONGWORLD_FIGHT",ActivityStrideBallteCommand);
         facade.registerCommand("CS_STRONG_SAVEFORMATION",ActivityStrideBallteCommand);
         facade.registerCommand("CS_STRONG_HAS_SAVED",ActivityStrideBallteCommand);
         facade.registerCommand("CS_TOGET_STRONG_WORLD_RANK",ActivityStrideBallteCommand);
         facade.registerCommand("CS_OPEN_BLACK_SHOPVIP",ActivityCommonCommand);
         facade.registerCommand("CS_SET_BLACKSHOP_MC_SHOW",ActivityCommonCommand);
         facade.registerCommand("CS_BLACKSHOP_ACITIVYT_OPEN",ActivityCommonCommand);
         facade.registerCommand("CS_WORLD_GROUPWAR_SIGNUP",ActivityWorldGroupWarCommand);
         facade.registerCommand("CS_WORLD_GROUPWAR_CANSIGNUP",ActivityWorldGroupWarCommand);
         facade.registerCommand("CS_ENTER_HONOUR_SHOP_WGW",ActivityCommand);
         facade.registerCommand("CS_HONOUR_SHOP_EXCHANGE_WGW",ActivityCommand);
      }
      
      override public function onRemove() : void
      {
         facade.removeProxy("activity.proxy.ActivityStrideBattleProxy");
         facade.removeProxy("activity.proxy.ActivityCommonProxy");
         facade.removeCommand("GETBOSSBOT");
         facade.removeCommand("SETBOSSBOT");
         facade.removeCommand("UNSETBOSSBOT");
         facade.removeCommand("CS_ENTER_HONOUR_SHOP");
         facade.removeCommand("CS_HONOUR_SHOP_EXCHANGE");
         facade.removeCommand("CS_SERVICECHALLENGE_APPLY");
         facade.removeCommand("CS_GET_SERVICECHALLENGE_PRIZE");
         facade.removeCommand("CS_CANRECIEVE_SHIP");
         facade.removeCommand("CS_RECIEVE_CARDSHIP");
         facade.removeCommand("CS_GET_MYGUILD_INFO_ACTIVITY");
         facade.removeCommand("CS_GUILDCHALLENGE_APPLY");
         facade.removeCommand("CS_GUILDCHALLENGE_GETPRIZE");
         facade.removeCommand("SAVE_BOSS_LINEUP");
         facade.removeCommand("CS_TEAM_CHALLENGE_DISMISS");
         facade.removeCommand("CS_BOAT_CHALLENGE_ISAPPLY");
         facade.removeCommand("CS_BOAT_CHALLENGE_APPLY");
         facade.removeCommand("CS_ACTIVITY_BOAT_LOGIN");
         facade.removeCommand("CS_PIRATE_ARENA_ISAPPLY");
         facade.removeCommand("CS_PIRATE_ARENA_APPLY");
         facade.removeCommand("CS_TOGET_STRONGWORLD_FIGHT");
         facade.removeCommand("CS_STRONG_SAVEFORMATION");
         facade.removeCommand("CS_TOGET_STRONG_WORLD_RANK");
         facade.removeCommand("CS_OPEN_BLACK_SHOPVIP");
         facade.removeCommand("CS_SET_BLACKSHOP_MC_SHOW");
         facade.removeCommand("CS_BLACKSHOP_ACITIVYT_OPEN");
         facade.removeCommand("CS_TOGET_BATTLEFIGHT_INFO");
         facade.removeCommand("CS_SET_BATTLEFIGHT_AUTO");
         facade.removeCommand("CS_WORLD_GROUPWAR_SIGNUP");
         facade.removeCommand("CS_WORLD_GROUPWAR_CANSIGNUP");
         facade.removeCommand("CS_ENTER_HONOUR_SHOP_WGW");
         facade.removeCommand("CS_HONOUR_SHOP_EXCHANGE_WGW");
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["wgwHonourShop","SC_HONOUR_SHOP_EXCHANGE_WGW","SC_ENTER_HONOUR_SHOP_WGW","SC_WORLD_GROUPWAR_SIGNUPRESULT","SC_WORLD_GROUPWAR_CANSIGNUP","SC_FISHING_CHECKREWARD","SC_BOSS_CANTERCHECK","SC_SETBOSSBOT","activity.manager.ENTER_PIRATE_BATTLE","SC_ENTER_HONOUR_SHOP","SC_HONOUR_SHOP_EXCHANGE","SC_SERVICECHALLENGE_APPLY","SC_GET_SERVICECHALLENGE_PRIZE","SC_CANRECIEVE_SHIP","SAVE_BOSS_LINEUP_SUC","SC_GUILDCHALLENGE_APPLY","SC_GUILDCHALLENGE_GETPRIZE","SC_GUILDSERVICEWAR_GETGUILDWARINFO","SC_TEAM_CHALLENGE_DISMISS","honourShop","SC_BOAT_CHALLENGE_APPLY","SC_BOAT_CHALLENGE_ISAPPLY","SC_PIRATE_ARENA_ISAPPLY","SC_PIRATE_ARENA_APPLY","TO_ENTER_STRONGWORLD_FIGHT","SC_BLACKSHOP_ACITIVYT_OPEN","SC_TOGET_BATTLEFIGHT_INFO","SC_SET_BATTLEFIGHT_AUTO","SC_STRONG_HAS_SAVED"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc8_:Boolean = false;
         var _loc6_:ActivityProxy = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc9_:int = 0;
         var _loc2_:int = 0;
         var _loc7_:Object = param1.getBody();
         _loc6_ = facade.retrieveProxy("ActivityProxy") as ActivityProxy;
         var _loc5_:ActivityCommonProxy = facade.retrieveProxy("activity.proxy.ActivityCommonProxy") as ActivityCommonProxy;
         switch(param1.getName())
         {
            case "SC_ENTER_HONOUR_SHOP_WGW":
               this._activetyWindow.enterWgwHonourShop(_loc6_.honourIntegral);
               break;
            case "SC_HONOUR_SHOP_EXCHANGE_WGW":
               _loc3_ = int(param1.getBody());
               _loc4_ = _loc6_.getItemExchangeTimes(_loc3_);
               this._activetyWindow.exchangeWgwHonourShop(_loc6_.honourIntegral,_loc3_,_loc4_);
               break;
            case "wgwHonourShop":
               this.onWgwHonourExchange(_loc7_.exchangeId,_loc7_.exchangeNum,_loc7_.itemPoint);
               break;
            case "SC_WORLD_GROUPWAR_CANSIGNUP":
               this._activetyWindow.updateWorldGroupWarSignUp();
               break;
            case "SC_WORLD_GROUPWAR_SIGNUPRESULT":
               this._activetyWindow.updateWorldGroupWarSignUpResult();
               break;
            case "SC_BOSS_CANTERCHECK":
               this._activetyWindow.setBtnEnabled(_loc7_ as Array);
               break;
            case "SC_SETBOSSBOT":
               this._activetyWindow.setBossBot(_loc7_);
               break;
            case "activity.manager.ENTER_PIRATE_BATTLE":
               BabelTimeSocket.getInstance().sendMessage("groupwar.enter",new SocketCallback("re.groupwar.enter"));
               BabelTimeSocket.getInstance().regCallback("re.groupwar.enter",this.getBattleInfoBack);
               break;
            case "SC_ENTER_HONOUR_SHOP":
               this._activetyWindow.enterHonourShop(_loc6_.honourIntegral);
               break;
            case "SC_HONOUR_SHOP_EXCHANGE":
               _loc9_ = int(param1.getBody());
               _loc2_ = _loc6_.getItemExchangeTimes(_loc9_);
               this._activetyWindow.exchangeHonourShop(_loc6_.honourIntegral,_loc9_,_loc2_);
               break;
            case "SC_SERVICECHALLENGE_APPLY":
               this._activetyWindow.kingApplyUpdate();
               break;
            case "SC_GET_SERVICECHALLENGE_PRIZE":
               this._activetyWindow.kingGetPrizeUpdate();
               break;
            case "SC_FISHING_CHECKREWARD":
               _loc8_ = Boolean(param1.getBody());
               this._activetyWindow.updateFishingClaimReward(_loc8_ as Boolean);
               break;
            case "SC_CANRECIEVE_SHIP":
               this._activetyWindow.setCardPrizeBtn(_loc5_.isCanGetCardPrize);
               break;
            case "SC_GUILDCHALLENGE_APPLY":
               this._activetyWindow.guildApplyUpdate();
               break;
            case "SC_GUILDCHALLENGE_GETPRIZE":
               this._activetyWindow.guildGetPrizeUpdate();
               break;
            case "SC_GUILDSERVICEWAR_GETGUILDWARINFO":
               this._activetyWindow.guildMemberUpdate();
               break;
            case "SAVE_BOSS_LINEUP_SUC":
               this._activetyWindow.bossLineSaveSuccess();
               break;
            case "SC_TEAM_CHALLENGE_DISMISS":
               this._activetyWindow.updateTeamBattleBtn();
               break;
            case "honourShop":
               this.onHonourExchange(_loc7_.exchangeId,_loc7_.exchangeNum,_loc7_.itemPoint);
               break;
            case "SC_BOAT_CHALLENGE_ISAPPLY":
               this._activetyWindow.dataNum++;
               if(this._activetyWindow.dataNum >= this._needDataArr.length)
               {
                  this._activetyWindow.showWinHandler(this._showData);
               }
               this._activetyWindow.updateBoatBattleBtn();
               break;
            case "SC_BOAT_CHALLENGE_APPLY":
               this._activetyWindow.updateBoatBattleBtn();
               break;
            case "SC_PIRATE_ARENA_ISAPPLY":
               this._activetyWindow.dataNum++;
               if(this._activetyWindow.dataNum >= this._needDataArr.length)
               {
                  this._activetyWindow.showWinHandler(this._showData);
               }
               this._activetyWindow.updatePirateArenaBtn();
               break;
            case "SC_PIRATE_ARENA_APPLY":
               this._activetyWindow.updatePirateArenaBtn();
               break;
            case "TO_ENTER_STRONGWORLD_FIGHT":
               sendNotification("CS_TOGET_STRONGWORLD_FIGHT");
               break;
            case "SC_BLACKSHOP_ACITIVYT_OPEN":
               if(MainData.getInstance().userData.isCloseBlackShopMC != 0)
               {
                  if(MainData.getInstance().blackShopData.highExist == 1 || MainData.getInstance().blackShopData.lowExist == 1)
                  {
                     this._activetyWindow.showWinHandler("blackShop");
                     this._activetyWindow.blackShopBtnUpdata();
                  }
                  else
                  {
                     this._activetyWindow.showWinHandler(this._showData);
                  }
               }
               else
               {
                  this._activetyWindow.showWinHandler(this._showData);
                  sendNotification("BLACKSHOP_SHOW_EFFECT",false);
               }
               break;
            case "SC_TOGET_BATTLEFIGHT_INFO":
               if(_loc7_.boo)
               {
                  this._activetyWindow.openBattleFightSet();
               }
               else
               {
                  this._activetyWindow.setBattleFightObj(_loc7_.curdata);
               }
               break;
            case "SC_SET_BATTLEFIGHT_AUTO":
               break;
            case "SC_STRONG_HAS_SAVED":
               this._activetyWindow.isSaveFormationb = Boolean(_loc7_.have_upload_formation);
               this._activetyWindow.strongWorldSaveFormation(Boolean(_loc7_.have_upload_formation));
               break;
            default:
               throw new Error("don\'t found [" + param1.getName() + "] function");
         }
      }
      
      private function getBattleInfoBack(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.groupwar.enter",this.getBattleInfoBack);
         if(param1.data.ret == "ok")
         {
            GameScene.enterScene(16,param1.data.res);
         }
         else if(param1.data.ret == "full")
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.35"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
         else
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.36"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
   }
}

