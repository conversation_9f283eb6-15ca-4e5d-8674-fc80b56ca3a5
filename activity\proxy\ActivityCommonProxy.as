package activity.proxy
{
   import game.data.MainData;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import game.xmlParsers.user.VIPData;
   import game.xmlParsers.user.VIPManager;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   
   public class ActivityCommonProxy extends Proxy
   {
      public static const NAME:String = "activity.proxy.ActivityCommonProxy";
      
      public var isCanGetCardPrize:Boolean;
      
      public function ActivityCommonProxy(param1:Object = null)
      {
         super("activity.proxy.ActivityCommonProxy",param1);
      }
      
      public function getPrizePerDayInfo(param1:String) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.exchangeshop.getIsReward",this.getPrizePerDayInfoBack);
         BabelTimeSocket.getInstance().sendMessage("exchangeshop.getIsReward",new SocketCallback("re.exchangeshop.getIsReward",[param1]),param1);
      }
      
      private function getPrizePerDayInfoBack(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.exchangeshop.getIsReward",this.getPrizePerDayInfoBack);
         var _loc2_:String = param1.callbackParames[0];
         if(param1.data == "ok")
         {
            if(_loc2_ == "cardshop")
            {
               this.isCanGetCardPrize = true;
            }
         }
         else if(_loc2_ == "cardshop")
         {
            this.isCanGetCardPrize = false;
         }
         sendNotification("SC_CANRECIEVE_SHIP");
      }
      
      public function getPrizePerDay(param1:String) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.exchangeshop.addReward",this.getPrizePerDayBack);
         BabelTimeSocket.getInstance().sendMessage("exchangeshop.addReward",new SocketCallback("re.exchangeshop.addReward",[param1]),param1);
      }
      
      private function getPrizePerDayBack(param1:SocketDataEvent) : void
      {
         var _loc2_:int = 0;
         var _loc5_:VIPData = null;
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         var _loc4_:String = null;
         BabelTimeSocket.getInstance().removeCallback("re.exchangeshop.addReward",this.getPrizePerDayBack);
         var _loc7_:String = param1.callbackParames[0];
         if(param1.data == "ok")
         {
            if(_loc7_ == "cardshop")
            {
               this.isCanGetCardPrize = false;
               _loc2_ = MainData.getInstance().userData.vip;
               _loc5_ = VIPManager.instance.getVIPDataByVIP(_loc2_);
               _loc3_ = _loc5_.getChipNum();
               _loc6_ = _loc5_.getBindingChipNum();
               _loc4_ = Globalization.getString("recieveCardPrize.3") + "\n";
               _loc4_ = _loc4_ + (!!_loc3_ ? StringUtil.substitute(Globalization.getString("recieveCardPrize.1"),_loc3_) + "\n" : "");
               _loc4_ = _loc4_ + (!!_loc6_ ? StringUtil.substitute(Globalization.getString("recieveCardPrize.2"),_loc6_) : "");
               sendNotification("POP_TEXT_TIPS",{
                  "text":_loc4_,
                  "textFormat":TextFormatLib.format_0x00FF00_14px
               });
               sendNotification("SC_CANRECIEVE_SHIP");
            }
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("valueBook.4"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      public function getBlackShopInfo() : void
      {
      }
      
      private function reBlackShopInfo(param1:SocketDataEvent) : void
      {
      }
      
      public function openBlackVip(param1:int, param2:int) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.blackmarket.buyBlackMarket",this.reOpenBlackVip);
         BabelTimeSocket.getInstance().sendMessage("blackmarket.buyBlackMarket",new SocketCallback("re.blackmarket.buyBlackMarket",[param1,param2]),param2);
      }
      
      private function reOpenBlackVip(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.blackmarket.buyBlackMarket",this.reOpenBlackVip);
         var _loc3_:int = int(param1.callbackParames[0]);
         var _loc2_:int = int(param1.callbackParames[1]);
         if(param1.error == "ok")
         {
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - _loc3_;
            MainData.getInstance().userData.openBlackShopId = param1.data.BM_buy_type;
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("blackShop.21"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
         }
      }
      
      public function setBlackShopMC(param1:int) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.blackmarket.setting",this.reSetBlackShopMC);
         BabelTimeSocket.getInstance().sendMessage("blackmarket.setting",new SocketCallback("re.blackmarket.setting",[param1]),param1);
      }
      
      private function reSetBlackShopMC(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.blackmarket.setting",this.reSetBlackShopMC);
         var _loc2_:int = int(param1.callbackParames[0]);
         if(param1.error == "ok")
         {
            if(_loc2_ == 1)
            {
               MainData.getInstance().userData.isCloseBlackShopMC = 1;
               if(MainData.getInstance().userData.openBlackShopId != 0 || MainData.getInstance().blackShopData.highExist == 1 || MainData.getInstance().blackShopData.lowExist == 1)
               {
                  sendNotification("BLACKSHOP_SHOW_EFFECT",true);
               }
               else
               {
                  sendNotification("BLACKSHOP_SHOW_EFFECT",false);
               }
            }
            else
            {
               MainData.getInstance().userData.isCloseBlackShopMC = 0;
               sendNotification("BLACKSHOP_SHOW_EFFECT",false);
            }
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("Gl.87"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
         }
      }
   }
}

