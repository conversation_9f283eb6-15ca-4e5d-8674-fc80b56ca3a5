package activity.proxy
{
   import activity.manager.ActivityXmlManager;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.ModuleData;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   import game.manager.XmlManager;
   import game.modules.activity.proxy.ActivityGuildProxy;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   import util.openModule;
   import util.time.TimeManager;
   
   public class ActivityStrideBattleProxy extends Proxy
   {
      public static const NAME:String = "activity.proxy.ActivityStrideBattleProxy";
      
      private var _boatIsApply:Boolean;
      
      private var _boatServerId:int;
      
      private var _pirateArenaIsApply:Boolean;
      
      private var _pirateArenaServerId:int;
      
      public function ActivityStrideBattleProxy(param1:Object = null)
      {
         super("activity.proxy.ActivityStrideBattleProxy",param1);
      }
      
      private function addPrize(param1:int, param2:Object) : void
      {
         var _loc7_:XML = null;
         var _loc6_:int = 0;
         var _loc12_:String = null;
         var _loc8_:Array = null;
         var _loc11_:String = null;
         var _loc10_:* = undefined;
         var _loc9_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:String = null;
         var _loc5_:Item = null;
         _loc7_ = XmlManager.getXml("leitai_jiangli").children().(@ID == param1)[0];
         _loc6_ = MainData.getInstance().groupData.roleModle.level;
         _loc12_ = Globalization.getString("equipment.11");
         if(int(_loc7_.@reward_belly) != 0)
         {
            _loc12_ += StringUtil.substitute(Globalization.getString("sign.5"),_loc6_ * int(_loc7_.@reward_belly)) + "\n";
            MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num + _loc6_ * int(_loc7_.@reward_belly);
         }
         if(int(_loc7_.@reward_experience) != 0)
         {
            _loc12_ += StringUtil.substitute(Globalization.getString("sign.6"),_loc6_ * int(_loc7_.@reward_experience)) + "\n";
            MainData.getInstance().userData.experience_num = MainData.getInstance().userData.experience_num + _loc6_ * int(_loc7_.@reward_experience);
         }
         if(int(_loc7_.@reward_gold) != 0)
         {
            _loc12_ += StringUtil.substitute(Globalization.getString("sign.7"),int(_loc7_.@reward_gold)) + "\n";
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num + int(_loc7_.@reward_gold);
         }
         if(int(_loc7_.@reward_prestige) != 0)
         {
            _loc12_ += StringUtil.substitute(Globalization.getString("sign.11"),int(_loc7_.@reward_prestige)) + "\n";
            MainData.getInstance().userData.prestige_num = MainData.getInstance().userData.prestige_num + int(_loc7_.@reward_prestige);
         }
         if(int(_loc7_.@reward_execution) != 0)
         {
            _loc12_ += StringUtil.substitute(Globalization.getString("sign.8"),int(_loc7_.@reward_execution)) + "\n";
            MainData.getInstance().userData.cur_execution = MainData.getInstance().userData.cur_execution + int(_loc7_.@reward_execution);
         }
         if(int(_loc7_.@reward_soul) != 0)
         {
            _loc12_ += StringUtil.substitute(Globalization.getString("activity.64"),int(_loc7_.@reward_soul)) + "\n";
            MainData.getInstance().userData._blueSoul = MainData.getInstance().userData._blueSoul + int(_loc7_.@reward_soul);
         }
         if(int(_loc7_.@reward_honour) != 0)
         {
            _loc12_ += StringUtil.substitute(Globalization.getString("activity.65"),int(_loc7_.@reward_honour)) + "\n";
         }
         if(_loc7_.@reward_items != "")
         {
            _loc8_ = _loc7_.@reward_items.split(",");
            for each(_loc11_ in _loc8_)
            {
               _loc9_ = int(_loc11_.split("|")[0]);
               _loc3_ = int(_loc11_.split("|")[1]);
               _loc4_ = MessageReceive.getItemColorName(_loc9_);
               _loc12_ += StringUtil.substitute(Globalization.getString("ServiceChallenge.80"),_loc4_,_loc3_) + "\n";
            }
            param2 = param2;
            for(_loc10_ in param2)
            {
               _loc5_ = ItemFactory.creatItem(param2[_loc10_]);
               MainData.getInstance().bagData.getBagByGridIndex(_loc10_).addGrid(_loc10_,_loc5_);
            }
         }
         sendNotification("POP_TEXT_TIPS",{
            "text":_loc12_,
            "textFormat":TextFormatLib.format_0x00FF00_14px
         });
      }
      
      public function applyStride() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.worldwar.signUp",this.applyResult);
         BabelTimeSocket.getInstance().sendMessage("worldwar.signUp",new SocketCallback("re.worldwar.signUp"));
      }
      
      private function applyResult(param1:SocketDataEvent) : void
      {
         var _loc2_:Number = NaN;
         BabelTimeSocket.getInstance().removeCallback("re.worldwar.signUp",this.applyResult);
         if(param1.data == "ok")
         {
            _loc2_ = TimeManager.getInstance().getTime();
            MainData.getInstance().serviceChallengeData.sign_time = _loc2_;
            sendNotification("SC_SERVICECHALLENGE_APPLY");
            sendNotification("SHOW_ACTIVITY_EFFECT",false);
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("Globalization.188"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("Globalization.97"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      public function getStridePrize(param1:int) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.worldwar.getPrize",this.getStridePrizeResult);
         BabelTimeSocket.getInstance().sendMessage("worldwar.getPrize",new SocketCallback("re.worldwar.getPrize",[param1]),param1);
      }
      
      public function getGuildBattlePrize(param1:int) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.guildwar.getPrize",this.getGuildPrizeResult);
         BabelTimeSocket.getInstance().sendMessage("guildwar.getPrize",new SocketCallback("re.guildwar.getPrize",[param1]),param1);
      }
      
      private function getStridePrizeResult(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.worldwar.getPrize",this.getStridePrizeResult);
         var _loc4_:int = int(param1.callbackParames[0]);
         var _loc3_:int = MainData.getInstance().serviceChallengeData.id;
         var _loc2_:Object = ActivityXmlManager.getGroupPrizeInfoById(_loc3_,_loc4_);
         if(param1.data != "err")
         {
            if(_loc2_.isHasPrize)
            {
               MainData.getInstance().serviceChallengeData.is_get_group_prize = true;
            }
            else
            {
               MainData.getInstance().serviceChallengeData.is_get_world_prize = true;
            }
            this.addPrize(_loc4_,param1.data);
            sendNotification("SC_GET_SERVICECHALLENGE_PRIZE");
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("valueBook.4"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      private function getGuildPrizeResult(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.guildwar.getPrize",this.getGuildPrizeResult);
         var _loc2_:int = int(param1.callbackParames[0]);
         if(param1.data != "err")
         {
            MainData.getInstance().guildChallengeData.is_get_world_prize = true;
            this.addPrize(_loc2_,param1.data);
            sendNotification("SC_GUILDCHALLENGE_GETPRIZE");
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("valueBook.4"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      public function applyGuildStride() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.guildwar.signUp",this.guildApplyResult);
         BabelTimeSocket.getInstance().sendMessage("guildwar.signUp",new SocketCallback("re.guildwar.signUp"));
      }
      
      private function guildApplyResult(param1:SocketDataEvent) : void
      {
         var _loc3_:Number = NaN;
         var _loc2_:ActivityGuildProxy = null;
         BabelTimeSocket.getInstance().removeCallback("re.guildwar.signUp",this.guildApplyResult);
         if(param1.data == "ok")
         {
            _loc3_ = TimeManager.getInstance().getTime();
            MainData.getInstance().guildChallengeData.sign_time = _loc3_;
            _loc2_ = facade.retrieveProxy("game.modules.activity.proxy.ActivityGuildInfoProxy") as ActivityGuildProxy;
            if(_loc2_.isPresent)
            {
               MainData.getInstance().guildChallengeData.is_player = 1;
            }
            sendNotification("SC_GUILDCHALLENGE_APPLY");
            sendNotification("SHOW_ACTIVITY_EFFECT",false);
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("Globalization.188"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("Globalization.97"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      public function dismissTeam() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.worldteam.dismissTeam",this.dismissResult);
         BabelTimeSocket.getInstance().sendMessage("worldteam.dismissTeam",new SocketCallback("re.worldteam.dismissTeam"));
      }
      
      private function dismissResult(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.worldteam.dismissTeam",this.dismissResult);
         if(param1.data == "ok")
         {
            MainData.getInstance().teamChallengeData.isJoin = false;
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("ActiTeamChallenge.10"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            sendNotification("SC_TEAM_CHALLENGE_DISMISS");
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("ActiTeamChallenge.9"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      public function getBoatApplyInfo() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.worldboat.getUserActiveInfo",this.boatApplyInfoResult);
         BabelTimeSocket.getInstance().sendMessage("worldboat.getUserActiveInfo",new SocketCallback("re.worldboat.getUserActiveInfo"));
      }
      
      private function boatApplyInfoResult(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.worldboat.getUserActiveInfo",this.boatApplyInfoResult);
         if(param1.data != "err")
         {
            this._boatIsApply = Boolean(param1.data.is_sign);
            this._boatServerId = int(param1.data.teamid);
            ModuleData.crossServerShipFightGold = int(param1.data.gold);
            sendNotification("SC_BOAT_CHALLENGE_ISAPPLY");
         }
      }
      
      public function get boatIsApply() : Boolean
      {
         return this._boatIsApply;
      }
      
      public function get boatServerId() : int
      {
         return this._boatServerId;
      }
      
      public function boatChallengeApply() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.worldboat.sign",this.boatApplyResult);
         BabelTimeSocket.getInstance().sendMessage("worldboat.sign",new SocketCallback("re.worldboat.sign"));
      }
      
      private function boatApplyResult(param1:SocketDataEvent) : void
      {
         var _loc3_:XML = null;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         BabelTimeSocket.getInstance().removeCallback("re.worldboat.sign",this.boatApplyResult);
         if(param1.data.success)
         {
            this._boatIsApply = true;
            _loc3_ = XmlManager.getXml("worldBoatWar").children()[0];
            _loc2_ = int(String(_loc3_.@signReward).split("|")[0]);
            _loc4_ = int(String(_loc3_.@signReward).split("|")[1]);
            sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("boatChallenge.3"),_loc2_,_loc4_),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            sendNotification("SC_BOAT_CHALLENGE_APPLY");
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("Globalization.97"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      public function getPirateArenaApplyInfo() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.worldbullfightarena.getUserActiveInfo",this.pirateArenaApplyInfoResult);
         BabelTimeSocket.getInstance().sendMessage("worldbullfightarena.getUserActiveInfo",new SocketCallback("re.worldbullfightarena.getUserActiveInfo"));
      }
      
      private function pirateArenaApplyInfoResult(param1:SocketDataEvent) : void
      {
         var _loc2_:Date = null;
         BabelTimeSocket.getInstance().removeCallback("re.worldbullfightarena.getUserActiveInfo",this.pirateArenaApplyInfoResult);
         if(param1.data != "err")
         {
            this._pirateArenaIsApply = Boolean(param1.data.is_sign);
            this._pirateArenaServerId = int(param1.data.teamid);
            _loc2_ = TimeManager.setTimezoneOffset(param1.data.cdTime * 1000);
            sendNotification("SC_PIRATE_ARENA_ISAPPLY");
         }
      }
      
      public function pirateArenaApply() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.worldbullfightarena.sign",this.pirateArenaApplyResult);
         BabelTimeSocket.getInstance().sendMessage("worldbullfightarena.sign",new SocketCallback("re.worldbullfightarena.sign"));
      }
      
      private function pirateArenaApplyResult(param1:SocketDataEvent) : void
      {
         var _loc3_:XML = null;
         var _loc2_:int = 0;
         BabelTimeSocket.getInstance().removeCallback("re.worldbullfightarena.sign",this.pirateArenaApplyResult);
         if(param1.data.success)
         {
            this._pirateArenaIsApply = true;
            _loc3_ = XmlManager.getXml("arena").children()[0];
            _loc2_ = int(String(_loc3_.@signReward).split("|")[0]);
            sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("pirateArena.11"),_loc2_),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            sendNotification("SC_PIRATE_ARENA_APPLY");
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("Globalization.97"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      public function pirateArenaUpdateFightInfo() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.worldbullfightarena.updateFightInfo",this.pirateArenaUpdateResult);
         BabelTimeSocket.getInstance().sendMessage("worldbullfightarena.updateFightInfo",new SocketCallback("re.worldbullfightarena.updateFightInfo"));
      }
      
      private function pirateArenaUpdateResult(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.worldbullfightarena.updateFightInfo",this.pirateArenaUpdateResult);
         if(param1.data.success)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("pirateArena.14"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("pirateArena.13"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      public function get pirateArenaIsApply() : Boolean
      {
         return this._pirateArenaIsApply;
      }
      
      public function get pirateArenaServerId() : int
      {
         return this._pirateArenaServerId;
      }
      
      public function getStrongWorld() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.burningCrusade.getInfo",this.reStrongWorldHandler);
         BabelTimeSocket.getInstance().sendMessage("burningCrusade.getInfo",new SocketCallback("re.burningCrusade.getInfo"));
      }
      
      private function reStrongWorldHandler(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.burningCrusade.getInfo",this.reStrongWorldHandler);
         if(param1.data.error)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":param1.data.mess,
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
            return;
         }
         if(param1.data.success)
         {
            GameScene.enterScene(48,param1.data);
         }
         else
         {
            openModule("EXPEDITIONWORDWINDOW",true,param1.data);
         }
      }
      
      public function hasSavedStrongWorld() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.burningCrusade.getAutoFormationInfo",this.reHasSavedHandler);
         BabelTimeSocket.getInstance().sendMessage("burningCrusade.getAutoFormationInfo",new SocketCallback("re.burningCrusade.getAutoFormationInfo"));
      }
      
      private function reHasSavedHandler(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.burningCrusade.getAutoFormationInfo",this.reHasSavedHandler);
         if(param1.data)
         {
            sendNotification("SC_STRONG_HAS_SAVED",param1.data);
         }
      }
      
      public function saveStrongFormation() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.burningCrusade.uploadAutoFormation",this.reSaveStrongFormation);
         BabelTimeSocket.getInstance().sendMessage("burningCrusade.uploadAutoFormation",new SocketCallback("re.burningCrusade.uploadAutoFormation"));
      }
      
      private function reSaveStrongFormation(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.burningCrusade.uploadAutoFormation",this.reSaveStrongFormation);
         if(param1.data)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":"保存成功！",
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
            this.hasSavedStrongWorld();
         }
      }
      
      public function getStrongWorldRank() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.burningCrusade.getSelfRank",this.regetStrongWorldRankHandler);
         BabelTimeSocket.getInstance().sendMessage("burningCrusade.getSelfRank",new SocketCallback("re.burningCrusade.getSelfRank"));
      }
      
      private function regetStrongWorldRankHandler(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.burningCrusade.getSelfRank",this.regetStrongWorldRankHandler);
         if(param1.data)
         {
            MainData.getInstance().userData.strongWorldFightRank = int(param1.data.rank);
         }
      }
   }
}

