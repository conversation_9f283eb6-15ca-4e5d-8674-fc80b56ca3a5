package activity.view
{
   import activity.mediators.ActivityMediator;
   import activity.proxy.ActivityStrideBattleProxy;
   import activity.view.activityItem.BaseActicityItem;
   import activity.view.activityItem.BattleFieldActivityItem;
   import activity.view.activityItem.BlackShopActItem;
   import activity.view.activityItem.BoatActivityItem;
   import activity.view.activityItem.BossActivityItem;
   import activity.view.activityItem.CardActivityItem;
   import activity.view.activityItem.FishingActivityItem;
   import activity.view.activityItem.GuildActivityItem;
   import activity.view.activityItem.KingActivityItem;
   import activity.view.activityItem.PirateArenaItem;
   import activity.view.activityItem.StrongWorldFightActivityItem;
   import activity.view.activityItem.TeamActivityItem;
   import activity.view.activityItem.WorldGroupWarActivityItem;
   import activity.view.mc.ActivityIcon;
   import activity.view.mc.ActivityUI;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.data.worldgroupwar.WorldGroupWarData;
   import game.events.PageNavigatorEvent;
   import game.manager.UIManager;
   import game.modules.activity.proxy.ActivityGuildProxy;
   import game.modules.fishing.FishingManager;
   import game.mvc.AppFacade;
   import game.xmlParsers.activity.ActivityManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class ActivityWindow extends PopUpWindow
   {
      public static const NAME:String = "ActivityWindow";
      
      private var _dailyIcon:ActivityIcon;
      
      private var _selectIcon:ActivityIcon;
      
      private var _selectActivityItem:BaseActicityItem;
      
      private var _activityUI:ActivityUI;
      
      private var _dataNum:int = 0;
      
      public var isSaveFormationb:Boolean = true;
      
      private var _closeSelIcon:ActivityIcon;
      
      public function ActivityWindow()
      {
         super(680,480);
         this.isLive = true;
         this.setTitleImageData(UIManager.getUISkin("title_activity").bitmapData);
         this._activityUI = new ActivityUI();
         this._activityUI.x = 10;
         pane.addChild(this._activityUI);
         this._activityUI.clickIconHandler = this.onIconMouseClick;
         this._activityUI.pageBtn.addEventListener("pageChange",this.onPageChangeHandler);
         AppFacade.instance.registerMediator(new ActivityMediator(this));
         this.closeHander = this.closeWinHandler;
      }
      
      public function showWinHandler(param1:Object = null) : void
      {
         var _loc2_:int = 0;
         if(param1 == "liveness")
         {
            _loc2_ = 10;
         }
         else if(param1 == "guildwar")
         {
            _loc2_ = 12;
         }
         else if(param1 == "serverChallenge")
         {
            _loc2_ = 9;
         }
         else if(param1 == "worldTree")
         {
            _loc2_ = 11;
         }
         else if(param1 == "boat")
         {
            _loc2_ = 15;
         }
         else if(param1 == "pirateFight")
         {
            _loc2_ = 14;
         }
         else if(param1 == "pirateArena")
         {
            _loc2_ = 16;
         }
         else if(param1 == "blackShop")
         {
            _loc2_ = 20;
         }
         this._activityUI.initActicityList(_loc2_);
         this._activityUI.setActivityData();
         this.updateIconStatus(String(param1));
         if(this._closeSelIcon && this._closeSelIcon != this._selectIcon)
         {
            this._closeSelIcon.selected = false;
         }
      }
      
      private function showAssignActi(param1:int) : void
      {
         this._selectIcon = this._activityUI.getIconById(param1);
         if(this._selectIcon == null)
         {
            this._selectIcon = this._activityUI.getIconById(1);
            return;
         }
         var _loc2_:Boolean = this._activityUI.getPage(param1);
         if(!_loc2_)
         {
            this._activityUI.pageBtn.dispatchEvent(new PageNavigatorEvent("pageChange",this._activityUI.pageBtn.currentPage));
         }
      }
      
      private function updateIconStatus(param1:String = "") : void
      {
         var _loc11_:* = 0;
         var _loc8_:BaseActicityItem = null;
         var _loc15_:ActivityIcon = null;
         var _loc12_:Boolean = false;
         var _loc16_:ActivityStrideBattleProxy = null;
         var _loc7_:Vector.<BaseActicityItem> = this._activityUI.activityList;
         var _loc3_:int = int(_loc7_.length);
         var _loc9_:Array = this._activityUI.iconList;
         var _loc14_:int = int(this._activityUI.pageBtn.currentPage);
         var _loc10_:int = 10;
         var _loc4_:int = 40;
         var _loc2_:int = 0;
         var _loc5_:int = (_loc14_ - 1) * 10;
         var _loc6_:int = _loc14_ * 10;
         var _loc13_:Array = ActivityManager.NEED_EFFECT_ACTI;
         this._selectIcon = _loc9_[0];
         _loc11_ = _loc5_;
         while(_loc11_ < _loc6_)
         {
            if(_loc11_ < _loc3_)
            {
               _loc15_ = _loc9_[_loc11_ % 10];
               _loc8_ = _loc7_[_loc11_];
               if(_loc8_)
               {
                  _loc12_ = _loc8_.activityData.isActive();
                  if(_loc8_.activityData.id == 20 && MainData.getInstance().blackShopData.IsShopExist())
                  {
                     this._dailyIcon = _loc15_;
                     this._selectIcon = this._dailyIcon;
                     this.blackShopBtnUpdata();
                  }
                  if(_loc12_ && _loc8_.activityData.type == 0)
                  {
                     if(int(_loc8_.activityData.action) == 1)
                     {
                        this._dailyIcon = _loc15_;
                        this._selectIcon = this._dailyIcon;
                        AppFacade.instance.sendNotification("CS_BOSS_CANTERCHECK",_loc8_.activityData.actionID);
                     }
                  }
                  else if(_loc12_ && _loc13_.indexOf(_loc8_.activityData.type) != -1)
                  {
                     _loc16_ = AppFacade.instance.retrieveProxy("activity.proxy.ActivityStrideBattleProxy") as ActivityStrideBattleProxy;
                     if(_loc8_.activityData.type == 12)
                     {
                        this._selectIcon = _loc15_;
                        _loc15_.activeEffect = true;
                     }
                     else if(_loc8_.activityData.type == 13)
                     {
                        this._selectIcon = _loc15_;
                        _loc15_.activeEffect = true;
                     }
                     else
                     {
                        this._selectIcon = _loc15_;
                        _loc15_.activeEffect = true;
                     }
                  }
                  else if(_loc8_.activityData.type == 19)
                  {
                     if(FishingManager.instance.model.canClaimReward)
                     {
                        this._selectIcon = this._dailyIcon;
                        _loc15_.activeEffect = true;
                     }
                  }
                  else if(_loc8_.activityData.type == 20)
                  {
                     if(WorldGroupWarData.canSignUp)
                     {
                        this._selectIcon = this._dailyIcon;
                        _loc15_.activeEffect = true;
                     }
                  }
                  if(_loc15_.visible)
                  {
                     _loc15_.x = _loc10_ + _loc2_ % 2 * 60;
                     _loc15_.y = _loc4_ + int(_loc2_ / 2) * 60;
                     _loc2_++;
                  }
               }
            }
            _loc11_ += 1;
         }
         if(param1 == "liveness")
         {
            this.showAssignActi(10);
         }
         else if(param1 == "guildwar")
         {
            this.showAssignActi(12);
         }
         else if(param1 == "serverChallenge")
         {
            this.showAssignActi(9);
         }
         else if(param1 == "worldTree")
         {
            this.showAssignActi(11);
         }
         else if(param1 == "boat")
         {
            this.showAssignActi(15);
         }
         else if(param1 == "pirateFight")
         {
            this.showAssignActi(14);
         }
         else if(param1 == "pirateArena")
         {
            this.showAssignActi(16);
         }
         else if(param1 == "blackShop")
         {
            this.showAssignActi(20);
         }
         if(this._selectIcon != null)
         {
            this._selectIcon.dispatchEvent(new MouseEvent("click"));
         }
      }
      
      private function onIconMouseClick(param1:ActivityIcon) : void
      {
         if(this._selectIcon != null)
         {
            this._selectIcon.selected = false;
            this._selectIcon.activityItem.removeBtns();
         }
         this._selectIcon = param1;
         this._selectIcon.selected = true;
         this._selectActivityItem = this._activityUI.getActivityItemById(this._selectIcon.id);
         if(this._selectActivityItem)
         {
            this._activityUI.setActicityInfo(this._selectActivityItem);
            this._selectActivityItem.showBtns();
            this._activityUI.addBtns(this._selectActivityItem);
         }
         if(this._selectActivityItem is BattleFieldActivityItem)
         {
            AppFacade.instance.sendNotification("CS_TOGET_BATTLEFIGHT_INFO",{"openWin":false});
         }
         if(this._selectActivityItem is StrongWorldFightActivityItem)
         {
            this.strongWorldSaveFormation(this.isSaveFormationb);
         }
      }
      
      private function onPageChangeHandler(param1:PageNavigatorEvent) : void
      {
         if(this._selectIcon != null)
         {
            this._selectIcon.selected = false;
            this._selectIcon.activityItem.removeBtns();
         }
         this._activityUI.setActivityData();
         this.updateIconStatus();
      }
      
      public function setBtnEnabled(param1:Array) : void
      {
         if(this._selectIcon.type != 0)
         {
            return;
         }
         if(this._dailyIcon != null)
         {
            this._dailyIcon.activeEffect = param1[0];
         }
         if(this._selectIcon == this._dailyIcon)
         {
            this._selectIcon.activityItem.updateJoinBtnStatus(param1[0]);
         }
      }
      
      public function setBattleFightObj(param1:Object) : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         if(this._selectActivityItem is BattleFieldActivityItem)
         {
            (this._selectActivityItem as BattleFieldActivityItem).currentBossSetting = param1;
         }
      }
      
      public function openBattleFightSet() : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         if(this._selectActivityItem is BattleFieldActivityItem)
         {
            (this._selectActivityItem as BattleFieldActivityItem).openSetWin();
         }
      }
      
      public function setBossBot(param1:Object) : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         if(this._selectActivityItem is BossActivityItem)
         {
            (this._selectActivityItem as BossActivityItem).currentBossSetting = param1;
         }
      }
      
      public function bossLineSaveSuccess() : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         if(this._selectActivityItem is BossActivityItem)
         {
            (this._selectActivityItem as BossActivityItem).autoJoinWin.isCanClick = true;
         }
      }
      
      public function strongWorldSaveFormation(param1:Boolean) : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         if(this._selectActivityItem is StrongWorldFightActivityItem)
         {
            if(param1)
            {
               (this._selectActivityItem as StrongWorldFightActivityItem).saveFightBtn.text = Globalization.getString("peeknessFight.12");
               (this._selectActivityItem as StrongWorldFightActivityItem).saveFightBtn.enabled = false;
               if(this._selectActivityItem.activityData.isActive())
               {
                  (this._selectActivityItem as StrongWorldFightActivityItem).saveFightBtn.enabled = true;
                  (this._selectActivityItem as StrongWorldFightActivityItem).saveFightBtn.filters = [FilterLib.enbaleFilter];
                  (this._selectActivityItem as StrongWorldFightActivityItem).saveFightBtn.setToolTip(Globalization.getString("peeknessFight.24"));
                  (this._selectActivityItem as StrongWorldFightActivityItem).removeSaveHandler(true);
               }
            }
            else
            {
               (this._selectActivityItem as StrongWorldFightActivityItem).saveFightBtn.text = Globalization.getString("peeknessFight.10");
               (this._selectActivityItem as StrongWorldFightActivityItem).saveFightBtn.enabled = true;
            }
            if(!this._selectActivityItem.activityData.isActive())
            {
               (this._selectActivityItem as StrongWorldFightActivityItem).saveFightBtn.text = Globalization.getString("peeknessFight.10");
               (this._selectActivityItem as StrongWorldFightActivityItem).saveFightBtn.enabled = true;
            }
         }
      }
      
      public function enterHonourShop(param1:Number) : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc2_:BattleFieldActivityItem = this._selectActivityItem as BattleFieldActivityItem;
         if(!_loc2_)
         {
            return;
         }
         _loc2_.honourShopWin.updateIntegral(param1);
         _loc2_.honourShopWin.setExchangeItemData();
      }
      
      public function enterWgwHonourShop(param1:Number) : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc2_:WorldGroupWarActivityItem = this._selectActivityItem as WorldGroupWarActivityItem;
         if(!_loc2_)
         {
            return;
         }
         _loc2_.honourShopWin.updateIntegral(param1);
         _loc2_.honourShopWin.setExchangeItemData();
      }
      
      public function updateWorldGroupWarSignUp() : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc1_:WorldGroupWarActivityItem = this._selectActivityItem as WorldGroupWarActivityItem;
         if(!_loc1_)
         {
            return;
         }
         _loc1_.enableApplyBtn(WorldGroupWarData.canSignUp);
         _loc1_.markSignedUp(WorldGroupWarData.alreadySignedUp);
      }
      
      public function updateWorldGroupWarSignUpResult() : void
      {
         this.updateIconStatus();
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc1_:WorldGroupWarActivityItem = this._selectActivityItem as WorldGroupWarActivityItem;
         if(!_loc1_)
         {
            return;
         }
         _loc1_.enableApplyBtn(WorldGroupWarData.canSignUp);
         _loc1_.markSignedUp(WorldGroupWarData.alreadySignedUp);
      }
      
      public function exchangeWgwHonourShop(param1:Number, param2:int, param3:int) : void
      {
         var _loc4_:WorldGroupWarActivityItem = null;
         if(!this._selectActivityItem)
         {
            return;
         }
         _loc4_ = this._selectActivityItem as WorldGroupWarActivityItem;
         if(!_loc4_)
         {
            return;
         }
         _loc4_.honourShopWin.updateIntegral(param1);
         _loc4_.honourShopWin.updateExchangeTimes(param2,param3);
      }
      
      public function updateFishingClaimReward(param1:Boolean) : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc2_:FishingActivityItem = this._selectActivityItem as FishingActivityItem;
         if(!_loc2_)
         {
            return;
         }
         _loc2_.enableClaimReward(param1);
      }
      
      public function exchangeHonourShop(param1:Number, param2:int, param3:int) : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc4_:BattleFieldActivityItem = this._selectActivityItem as BattleFieldActivityItem;
         if(!_loc4_)
         {
            return;
         }
         _loc4_.honourShopWin.updateIntegral(param1);
         _loc4_.honourShopWin.updateExchangeTimes(param2,param3);
      }
      
      public function kingApplyUpdate() : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc1_:KingActivityItem = this._selectActivityItem as KingActivityItem;
         if(!_loc1_)
         {
            return;
         }
         _loc1_.showBtns();
      }
      
      public function kingGetPrizeUpdate() : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc1_:KingActivityItem = this._selectActivityItem as KingActivityItem;
         if(!_loc1_)
         {
            return;
         }
         _loc1_.stridePrizeWin.updateWin();
      }
      
      public function setCardPrizeBtn(param1:Boolean) : void
      {
         var _loc2_:CardActivityItem = this._activityUI.getActivityItemById(10) as CardActivityItem;
         if(!_loc2_)
         {
            return;
         }
         _loc2_.updateCardPrizeBtn(param1);
      }
      
      public function guildApplyUpdate() : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc1_:GuildActivityItem = this._selectActivityItem as GuildActivityItem;
         if(!_loc1_)
         {
            return;
         }
         _loc1_.showBtns();
         _loc1_.x = 490 - _loc1_.diffX;
      }
      
      public function guildGetPrizeUpdate() : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc1_:GuildActivityItem = this._selectActivityItem as GuildActivityItem;
         if(!_loc1_)
         {
            return;
         }
         _loc1_.guildPrizeWin.updatePrizeBtn();
      }
      
      public function guildMemberUpdate() : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc1_:GuildActivityItem = this._selectActivityItem as GuildActivityItem;
         if(!_loc1_)
         {
            return;
         }
         _loc1_.guildBtns.updateBattleMemberBtn();
      }
      
      public function updateTeamBattleBtn() : void
      {
         if(!this._selectActivityItem)
         {
            return;
         }
         var _loc1_:TeamActivityItem = this._selectActivityItem as TeamActivityItem;
         if(!_loc1_)
         {
            return;
         }
         _loc1_.showBtns();
      }
      
      public function updateBoatBattleBtn() : void
      {
         var _loc2_:BoatActivityItem = this._activityUI.getActivityItemById(15) as BoatActivityItem;
         if(!_loc2_)
         {
            return;
         }
         var _loc1_:ActivityStrideBattleProxy = AppFacade.instance.retrieveProxy("activity.proxy.ActivityStrideBattleProxy") as ActivityStrideBattleProxy;
         _loc2_.updateApplyBtnStatus(_loc1_.boatIsApply);
      }
      
      public function updatePirateArenaBtn() : void
      {
         var _loc2_:PirateArenaItem = this._activityUI.getActivityItemById(16) as PirateArenaItem;
         if(!_loc2_)
         {
            return;
         }
         var _loc1_:ActivityStrideBattleProxy = AppFacade.instance.retrieveProxy("activity.proxy.ActivityStrideBattleProxy") as ActivityStrideBattleProxy;
         _loc2_.updateApplyBtnStatus(_loc1_.pirateArenaIsApply);
      }
      
      public function blackShopBtnUpdata() : void
      {
         if(this._selectActivityItem is BossActivityItem)
         {
            this._selectActivityItem.removeBtns();
         }
         var _loc1_:BlackShopActItem = this._activityUI.getActivityItemById(20) as BlackShopActItem;
         if(!_loc1_)
         {
            return;
         }
         if(this._selectIcon.type == 17)
         {
            if(this._dailyIcon != null)
            {
               if(MainData.getInstance().blackShopData.IsShopExist())
               {
                  this._dailyIcon.activeEffect = true;
               }
            }
            if(this._selectIcon == this._dailyIcon)
            {
               _loc1_.showBtns();
            }
         }
      }
      
      private function closeWinHandler() : void
      {
         this._activityUI.activityList.length = 0;
         if(this._selectIcon)
         {
            this._closeSelIcon = this._selectIcon;
         }
         if(PopUpCenter.containsWin("HonourShopWin"))
         {
            PopUpCenter.removePopUp("HonourShopWin");
         }
         if(PopUpCenter.containsWin("game.modules.pointExchange.ShopExchangeWinName"))
         {
            PopUpCenter.removePopUp("game.modules.pointExchange.ShopExchangeWinName");
         }
         var _loc1_:ActivityGuildProxy = AppFacade.instance.retrieveProxy("game.modules.activity.proxy.ActivityGuildInfoProxy") as ActivityGuildProxy;
         if(_loc1_)
         {
            _loc1_.guildInfoIsBack = false;
         }
         this._dataNum = 0;
      }
      
      override public function get posHeight() : Number
      {
         return 470;
      }
      
      public function get dataNum() : int
      {
         return this._dataNum;
      }
      
      public function set dataNum(param1:int) : void
      {
         this._dataNum = param1;
      }
   }
}

