package activity.view.activityItem
{
   import activity.controll.IActivity;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import game.core.scene.GameScene;
   import game.modules.activity.view.DevilTreeHelpPanel;
   import game.modules.activity.view.win.help.BoatHelpWindow;
   import game.modules.activity.view.win.help.CardShopIntroWin;
   import game.modules.activity.view.win.help.FishingIntro;
   import game.modules.activity.view.win.help.GuildDefendHelp;
   import game.modules.activity.view.win.help.PirateArenaHelp;
   import game.modules.activity.view.win.help.PirateBattleIntro;
   import game.modules.activity.view.win.help.StrongWorldHelpWindow;
   import game.modules.activity.view.win.help.WorldGroupWarIntro;
   import game.modules.task.model.TeamTools;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleManager;
   import game.mvc.module.ModuleParams;
   import game.xmlParsers.activity.Activity;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.window.WindowModel;
   import util.Globalization;
   import util.openModule;
   
   public class BaseActicityItem extends UISprite implements IActivity
   {
      private var _activityData:Activity;
      
      protected var joinBtn:Button;
      
      public function BaseActicityItem(param1:Activity)
      {
         super();
         this._activityData = param1;
         this.joinBtn = new Button(Globalization.canyuhuodong,null,90);
         this.joinBtn.addEventListener("click",this.onJoinHandler);
      }
      
      protected function onJoinHandler(param1:MouseEvent) : void
      {
         var _loc3_:ModuleParams = null;
         var _loc2_:ModuleParams = null;
         if(TeamTools.isMopup())
         {
            return;
         }
         if(int(this._activityData.action) == 2)
         {
            _loc3_ = new ModuleParams("ActivityTeamChooserWindow");
            if(PopUpCenter.containsWin(_loc3_.moduleName))
            {
               stage.focus = null;
               _loc3_.action = ModuleParams.act_Close;
            }
            AppFacade.instance.sendNotification("HANDLE_MODULE",_loc3_);
         }
         else if(int(this._activityData.action) == 3)
         {
            if(PopUpCenter.containsWin("CopyHookOnAttack"))
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{"text":Globalization.fubenguajitishi});
               return;
            }
            _loc2_ = new ModuleParams("EliteChooseCopyWindow");
            AppFacade.instance.sendNotification("HANDLE_MODULE",_loc2_);
         }
         else if(this._activityData.type == 4)
         {
            GameScene.enterScene(18);
         }
         else if(this._activityData.type == 15)
         {
            if(ModuleManager.instance.chkModuleIsOpen("IntegralBackModule"))
            {
               return;
            }
            openModule("IntegralBackModule");
         }
         else if(this._activityData.type == 16)
         {
            AppFacade.instance.sendNotification("TO_ENTER_STRONGWORLD_FIGHT");
         }
      }
      
      protected function onClickIntroBtn(param1:MouseEvent) : void
      {
         var _loc3_:WindowModel = null;
         var _loc2_:String = null;
         switch(this.activityData.type - 5)
         {
            case 0:
               _loc3_ = new PirateBattleIntro();
               _loc2_ = "PirateBattleIntro";
               break;
            case 2:
               _loc3_ = new CardShopIntroWin();
               _loc2_ = "CardShopIntroWin";
               break;
            case 3:
               _loc3_ = new DevilTreeHelpPanel();
               _loc2_ = "DevilTreeHelpPanel";
               break;
            case 7:
               _loc3_ = new BoatHelpWindow();
               _loc2_ = "BoatHelpWindow";
               break;
            case 8:
               _loc3_ = new PirateArenaHelp();
               _loc2_ = "game.modules.activity.view.win.help.PirateArenaHelp";
               break;
            case 9:
               _loc3_ = new GuildDefendHelp();
               _loc2_ = "game.modules.activity.view.win.help.GuildDefendHelp";
               break;
            case 11:
               _loc3_ = new StrongWorldHelpWindow();
               _loc2_ = "StrongWorldHelpWindow";
               break;
            case 14:
               _loc3_ = new FishingIntro();
               _loc2_ = "game.modules.activity.view.win.help.FishingIntro";
               break;
            case 15:
               _loc3_ = new WorldGroupWarIntro();
               _loc2_ = "WorldGroupWarIntro";
         }
         if(!_loc3_)
         {
            return;
         }
         PopUpCenter.addPopUp(_loc2_,_loc3_,true,true);
      }
      
      public function get isShowBtn() : Boolean
      {
         if(this.activityData.type == 1 || this.activityData.type == 10)
         {
            return false;
         }
         return true;
      }
      
      public function showBtns() : void
      {
         if(!this.isShowBtn)
         {
            return;
         }
         this.updateJoinBtnStatus(true);
      }
      
      public function updateJoinBtnStatus(param1:Boolean) : void
      {
         param1 = true;
         this.addChild(this.joinBtn);
         if(param1)
         {
            this.joinBtn.text = Globalization.canyuhuodong;
            this.joinBtn.enabled = true;
         }
         else
         {
            this.joinBtn.text = Globalization.huodongweikaiqi;
            this.joinBtn.enabled = false;
         }
      }
      
      public function removeBtns() : void
      {
         var _loc1_:DisplayObject = null;
         this.joinBtn.unSetToolTip();
         this.parent && this.parent.removeChild(this);
         while(this.numChildren > 0)
         {
            _loc1_ = this.getChildAt(0);
            _loc1_.parent && _loc1_.parent.removeChild(_loc1_);
            if(_loc1_ is MovieClip)
            {
               MovieClip(_loc1_).stop();
            }
         }
      }
      
      public function get diffX() : int
      {
         if(!this.isShowBtn)
         {
            return 0;
         }
         return this.width;
      }
      
      public function get activityData() : Activity
      {
         return this._activityData;
      }
   }
}

