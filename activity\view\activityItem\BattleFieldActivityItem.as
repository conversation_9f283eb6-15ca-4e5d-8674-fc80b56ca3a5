package activity.view.activityItem
{
   import activity.view.mc.AutoBattleFightJoin;
   import activity.view.win.pirateBattle.HonourShopWin;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import game.Environment;
   import game.data.MainData;
   import game.data.group.HeroDetailData;
   import game.manager.AssetManager;
   import game.modules.activity.proxy.ActivityProxy;
   import game.modules.task.model.TeamTools;
   import game.mvc.AppFacade;
   import game.xmlParsers.activity.Activity;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.Button;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.StringToDate;
   import util.time.TimeManager;
   
   public class BattleFieldActivityItem extends BaseActicityItem
   {
      private var _introBtn:Button;
      
      private var _honourBtn:SimpleButton;
      
      private var _honourShopWin:HonourShopWin;
      
      private var _jionFightSetBtn:Button;
      
      private var _autoJoinBattleWin:AutoBattleFightJoin;
      
      private var isCanSetAutoFight:Boolean = false;
      
      public function BattleFieldActivityItem(param1:Activity)
      {
         super(param1);
         this._introBtn = new Button(Globalization.shuoming,null,90);
         this._introBtn.addEventListener("click",onClickIntroBtn);
         this._introBtn.x = -95;
         joinBtn.x = 10;
         this._honourBtn = AssetManager.getObject("HonourMedalBtn") as SimpleButton;
         this._honourBtn.x = 290;
         this._honourBtn.y = 10;
         this._honourBtn.addEventListener("click",this.onHonourBtnHandler);
         this._jionFightSetBtn = new Button(Globalization.getString("activity.11"),null,90);
         this._jionFightSetBtn.addEventListener("click",this.jionFightSetBtnHandler);
         this._jionFightSetBtn.x = 110;
         this._autoJoinBattleWin = new AutoBattleFightJoin();
      }
      
      private function onHonourBtnHandler(param1:MouseEvent) : void
      {
         this._honourShopWin = new HonourShopWin();
         PopUpCenter.addPopUp("HonourShopWin",this._honourShopWin,true,true);
      }
      
      private function jionFightSetBtnHandler(param1:MouseEvent) : void
      {
         if(MainData.getInstance().groupData.roleModle.level < 40)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.37"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            return;
         }
         if(MainData.getInstance().userData.vip < 6)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("battleFight.1"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(this.isCanSetAutoFight)
         {
            AppFacade.instance.sendNotification("CS_TOGET_BATTLEFIGHT_INFO",{"openWin":true});
            return;
         }
         AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
            "text":Globalization.getString("activity.12"),
            "textFormat":TextFormatLib.format_0xFF0000_14px
         });
      }
      
      override public function showBtns() : void
      {
         this.addChild(this._introBtn);
         this.addChild(joinBtn);
         this.addChild(this._jionFightSetBtn);
         if(MainData.getInstance().groupData.roleModle.level < 40)
         {
            joinBtn.setToolTip(Globalization.getString("activity.37"));
         }
         this.addChild(this._honourBtn);
         updateJoinBtnStatus(activityData.isActive());
      }
      
      override protected function onJoinHandler(param1:MouseEvent) : void
      {
         var _loc12_:Date = null;
         var _loc13_:Number = NaN;
         var _loc5_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         var _loc11_:Number = NaN;
         var _loc4_:int = 0;
         var _loc16_:int = 0;
         var _loc6_:Number = NaN;
         var _loc7_:int = 0;
         var _loc15_:int = 0;
         var _loc10_:String = null;
         if(TeamTools.isMopup())
         {
            return;
         }
         var _loc8_:HeroDetailData = MainData.getInstance().groupData.roleModle;
         var _loc9_:int = _loc8_.level;
         if(_loc9_ < 40)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.37"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(Environment.loadingParams.hasOwnProperty("openDateTime"))
         {
            _loc12_ = StringToDate.transferOpenTimeToDate();
            _loc13_ = TimeManager.getInstance().getTime();
            _loc5_ = 518400000;
            _loc2_ = _loc13_ - _loc12_.getTime();
            if(_loc2_ < _loc5_)
            {
               _loc3_ = _loc5_ - _loc2_;
               _loc11_ = _loc3_ / 1000;
               _loc4_ = _loc11_ / 86400;
               _loc16_ = (_loc11_ - _loc4_ * 24 * 3600) / 3600;
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("activity.38"),_loc4_,_loc16_),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
         }
         if(activityData.isInSecondBattle())
         {
            _loc6_ = activityData.secondStartTime();
            _loc7_ = _loc6_ / 60;
            _loc15_ = _loc6_ % 60;
            _loc10_ = StringUtil.substitute(Globalization.getString("activity.39"),_loc7_,_loc15_);
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":_loc10_,
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         var _loc14_:ActivityProxy = AppFacade.instance.retrieveProxy("ActivityProxy") as ActivityProxy;
         _loc14_.getBattleFightBot("activityLatest");
      }
      
      public function set currentBossSetting(param1:Object) : void
      {
         var _loc2_:int = int(param1.isAuto);
         this.isCanSetAutoFight = Boolean(param1.canSet);
         if(_loc2_ == 1)
         {
            this._jionFightSetBtn.text = Globalization.yishezhi;
         }
         else
         {
            this._jionFightSetBtn.text = Globalization.shezhizidong;
         }
         this.autoJoinBattleWin.setBossBot(param1.isAuto);
      }
      
      override public function get diffX() : int
      {
         return 340;
      }
      
      public function get honourShopWin() : HonourShopWin
      {
         return this._honourShopWin;
      }
      
      public function get autoJoinBattleWin() : AutoBattleFightJoin
      {
         return this._autoJoinBattleWin;
      }
      
      public function openSetWin() : void
      {
         PopUpCenter.addPopUp("AutoBattleFightJoin",this._autoJoinBattleWin,true,true);
      }
   }
}

