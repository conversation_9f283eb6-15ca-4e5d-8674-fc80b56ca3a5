package activity.view.activityItem
{
   import activity.view.mc.blackShop.BlackShopLookAward;
   import activity.view.mc.blackShop.BlackShopOpenVipWin;
   import activity.view.mc.blackShop.BlackShopSetWin;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.AssetManager;
   import game.xmlParsers.activity.Activity;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.Button;
   import util.Globalization;
   import util.openModule;
   
   public class BlackShopActItem extends BaseActicityItem
   {
      private var _exchangeBtn:MovieClip;
      
      private var setMCActBtn:Button;
      
      private var blackShopBtn:Button;
      
      private var openVipBtn:Button;
      
      public function BlackShopActItem(param1:Activity)
      {
         super(param1);
         this.blackShopBtn = new Button(Globalization.getString("blackShop.27"),null,90);
         this.blackShopBtn.addEventListener("click",this.openBlackShopHandler);
         this.openVipBtn = new Button(Globalization.getString("blackShop.28"),null,90);
         this.openVipBtn.addEventListener("click",this.openVipWin);
         this.openVipBtn.x = 113;
         this.setMCActBtn = new Button(Globalization.getString("blackShop.1"),null,90);
         this.setMCActBtn.x = -73;
         this.setMCActBtn.y = this.blackShopBtn.y;
         this.blackShopBtn.x = 19;
         this.setMCActBtn.addEventListener("click",this.setMCActHandler);
         this._exchangeBtn = AssetManager.getMc("BlackShopLookBtn");
         this._exchangeBtn.x = 300;
         this._exchangeBtn.y = 10;
         this._exchangeBtn.addEventListener("click",this.onRewardBtnHandler);
         this._exchangeBtn.buttonMode = true;
         this._exchangeBtn.gotoAndStop(1);
         this._exchangeBtn.mouseChildren = false;
      }
      
      private function openBlackShopHandler(param1:MouseEvent) : void
      {
         openModule("BlackMarketWin");
      }
      
      private function openVipWin(param1:MouseEvent) : void
      {
         var _loc2_:BlackShopOpenVipWin = new BlackShopOpenVipWin();
         PopUpCenter.addPopUp("BlackShopOpenVipWin",_loc2_,true,true);
      }
      
      private function setMCActHandler(param1:MouseEvent) : void
      {
         var _loc2_:BlackShopSetWin = new BlackShopSetWin();
         if(MainData.getInstance().userData.isCloseBlackShopMC == 0)
         {
            _loc2_.setCheckButton(false);
         }
         else
         {
            _loc2_.setCheckButton(true);
         }
         PopUpCenter.addPopUp("BlackShopSetWin",_loc2_,true,true);
      }
      
      private function onRewardBtnHandler(param1:MouseEvent) : void
      {
         var _loc2_:BlackShopLookAward = new BlackShopLookAward();
         PopUpCenter.addPopUp("BlackShopLookAward",_loc2_,true,true);
      }
      
      override public function showBtns() : void
      {
         this.addChild(this.blackShopBtn);
         this.addChild(this.setMCActBtn);
         this.addChild(this._exchangeBtn);
         this.addChild(this.openVipBtn);
         this._exchangeBtn.gotoAndPlay(1);
      }
      
      override public function get diffX() : int
      {
         return 340;
      }
   }
}

