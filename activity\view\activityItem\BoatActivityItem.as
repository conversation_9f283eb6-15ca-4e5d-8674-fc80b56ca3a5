package activity.view.activityItem
{
   import activity.proxy.ActivityStrideBattleProxy;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import game.manager.AssetManager;
   import game.modules.task.model.TeamTools;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import game.xmlParsers.activity.Activity;
   import game.xmlParsers.activity.categoryActivity.BoatChallengeActi;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.Button;
   import util.ButtonBehavior;
   import util.Globalization;
   import util.openModule;
   
   public class BoatActivityItem extends BaseActicityItem
   {
      private var _applyBtn:Button;
      
      private var _introBtn:Button;
      
      private var _strengthBtn:Button;
      
      private var _goldBtn:Button;
      
      private var _exchangeBtn:MovieClip;
      
      private var _halo:MovieClip;
      
      public function BoatActivityItem(param1:Activity)
      {
         super(param1);
         joinBtn.text = Globalization.getString("boatChallenge.2");
         joinBtn.x = 282;
         this._applyBtn = new Button(Globalization.getString("ServiceChallenge.41"),null,90);
         this._applyBtn.x = 282;
         this._applyBtn.addEventListener("click",this.onClickApplyBtn);
         this._strengthBtn = new Button(Globalization.getString("boatChallenge.1"),null,90);
         this._strengthBtn.x = 94;
         this._strengthBtn.addEventListener("click",this.onClickStrengthBtn);
         this._introBtn = new Button(Globalization.getString("Globalization.60"),null,90);
         this._introBtn.x = 188;
         this._introBtn.addEventListener("click",onClickIntroBtn);
         this._goldBtn = new Button(Globalization.getString("boatChallenge.4"),null,90);
         this._goldBtn.addEventListener("click",this.onClickGoldBtn);
         this._exchangeBtn = AssetManager.getMc("BoatChallengeShopBtn");
         this._exchangeBtn.x = 426;
         this._exchangeBtn.y = 8;
         this._exchangeBtn.addEventListener("click",this.onExchangeHandler);
         this._exchangeBtn.buttonMode = true;
         this._exchangeBtn.gotoAndStop(1);
         this._exchangeBtn.mouseChildren = false;
         this._halo = AssetManager.getMc("HaloTips");
         this._halo.gotoAndStop(1);
         this._halo.mouseChildren = false;
         this._halo.mouseEnabled = false;
         this._halo.x = 282;
      }
      
      private function onExchangeHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("ShopExchangeModule",ModuleParams.act_Open,"worldboatshop",true,true));
      }
      
      private function onClickStrengthBtn(param1:MouseEvent) : void
      {
         openModule("WorldBoatStrengthenModule",true,false,true,true);
      }
      
      private function onClickGoldBtn(param1:MouseEvent) : void
      {
         openModule("BankWindowModule");
      }
      
      private function onClickApplyBtn(param1:MouseEvent) : void
      {
         if((activityData as BoatChallengeActi).isApply())
         {
            AppFacade.instance.sendNotification("CS_BOAT_CHALLENGE_APPLY");
            return;
         }
         AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
            "text":Globalization.getString("challenge.1"),
            "textFormat":TextFormatLib.format_0xFF0000_12px
         });
      }
      
      override protected function onJoinHandler(param1:MouseEvent) : void
      {
         if(TeamTools.isMopup())
         {
            return;
         }
         if(PopUpCenter.containsWin("CopyHookOnAttack"))
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{"text":Globalization.fubenguajitishi});
            return;
         }
         if((activityData as BoatChallengeActi).isAssginRoom())
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.121"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         AppFacade.instance.sendNotification("CS_ACTIVITY_BOAT_LOGIN");
      }
      
      override public function showBtns() : void
      {
         var _loc1_:ActivityStrideBattleProxy = AppFacade.instance.retrieveProxy("activity.proxy.ActivityStrideBattleProxy") as ActivityStrideBattleProxy;
         this.addChild(this._goldBtn);
         this.addChild(this._strengthBtn);
         this.addChild(this._introBtn);
         if((activityData as BoatChallengeActi).isApply())
         {
            this.addChild(this._applyBtn);
            this.updateApplyBtnStatus(_loc1_.boatIsApply);
         }
         else
         {
            if(_loc1_.boatServerId <= 0)
            {
               ButtonBehavior.setBtnStatus(joinBtn,false);
               joinBtn.setToolTip(Globalization.getString("activity.113"));
            }
            else
            {
               ButtonBehavior.setBtnStatus(joinBtn,true);
               joinBtn.unSetToolTip();
               updateJoinBtnStatus((activityData as BoatChallengeActi).isAssginRoom() || activityData.isActive());
            }
            this.addChild(joinBtn);
         }
         this.addChild(this._exchangeBtn);
         this._exchangeBtn.gotoAndPlay(1);
      }
      
      override public function get diffX() : int
      {
         return 440;
      }
      
      public function updateApplyBtnStatus(param1:Boolean) : void
      {
         var _loc2_:ActivityStrideBattleProxy = AppFacade.instance.retrieveProxy("activity.proxy.ActivityStrideBattleProxy") as ActivityStrideBattleProxy;
         if(_loc2_.boatServerId <= 0)
         {
            ButtonBehavior.setBtnStatus(this._applyBtn,false);
            this._applyBtn.setToolTip(Globalization.getString("activity.113"));
            return;
         }
         ButtonBehavior.setBtnStatus(this._applyBtn,true);
         this._applyBtn.unSetToolTip();
         if((activityData as BoatChallengeActi).isApply())
         {
            if(param1)
            {
               this._applyBtn.enabled = false;
               this._applyBtn.text = Globalization.getString("ServiceChallenge.47");
               this._halo.parent && this._halo.parent.removeChild(this._halo);
               this._halo.gotoAndStop(1);
            }
            else
            {
               this._applyBtn.enabled = true;
               this._applyBtn.text = Globalization.getString("ServiceChallenge.41");
               this.addChild(this._halo);
               this._halo.gotoAndPlay(1);
            }
         }
         else
         {
            this._applyBtn.enabled = false;
            this._applyBtn.text = Globalization.getString("ServiceChallenge.41");
            this._halo.parent && this._halo.parent.removeChild(this._halo);
            this._halo.gotoAndStop(1);
         }
      }
   }
}

