package activity.view.activityItem
{
   import activity.view.mc.AutoJoin;
   import activity.view.mc.WorldBossInfo;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.modules.task.model.TeamTools;
   import game.mvc.AppFacade;
   import game.xmlParsers.activity.Activity;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.Button;
   import util.Globalization;
   
   public class BossActivityItem extends BaseActicityItem
   {
      private var _bossIntroBtn:Button;
      
      private var _setAutoBtn:Button;
      
      public var _bossSecLineUp:Button;
      
      private var _autoJoinWin:AutoJoin;
      
      private var _currentBossSetting:Object;
      
      public function BossActivityItem(param1:Activity)
      {
         super(param1);
         this._bossIntroBtn = new Button(Globalization.shuoming,null,90,UIManager.getMultiUISkin("button_red"));
         this._bossIntroBtn.addEventListener("click",this.onBossInfoHandler);
         this._setAutoBtn = new Button(Globalization.shezhizidongcanyu,null,90,UIManager.getMultiUISkin("button_red"));
         this._setAutoBtn.addEventListener("click",this.onSetBtnClickHandler);
         this._bossSecLineUp = new Button(Globalization.getString("saveBossLineUp.6"),null,120,UIManager.getMultiUISkin("button_red"));
         this._bossSecLineUp.addEventListener("click",this.onSaveBossLineUpHandler);
         this._autoJoinWin = new AutoJoin();
      }
      
      private function onBossInfoHandler(param1:MouseEvent) : void
      {
         var _loc2_:WorldBossInfo = new WorldBossInfo();
         PopUpCenter.addPopUp("WorldBossInfo",_loc2_,true,true);
      }
      
      private function onSetBtnClickHandler(param1:MouseEvent) : void
      {
         if(this._currentBossSetting["set_status"] == 1)
         {
            if(this._autoJoinWin.isCanClick)
            {
               this._currentBossSetting["savedFormation"] = true;
            }
            this._autoJoinWin.setBossBot(this._currentBossSetting);
            PopUpCenter.addPopUp("AutoJoin",this._autoJoinWin,true,true);
         }
         else
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.baomingError,
               "textFormat":TextFormatLib.format_0xFF0000_14px,
               "runTime":2
            });
         }
      }
      
      private function onSaveBossLineUpHandler(param1:MouseEvent) : void
      {
         if(int(activityData.action) == 1)
         {
            AppFacade.instance.sendNotification("SAVE_BOSS_LINEUP",int(activityData.actionID));
         }
      }
      
      override public function showBtns() : void
      {
         var _loc1_:int = 0;
         var _loc2_:XML = null;
         super.showBtns();
         _loc1_ = MainData.getInstance().userData.vip;
         _loc2_ = XmlManager.vipConfing.elements().(@level == _loc1_)[0];
         if(int(_loc2_.@autoJoinCost) > 0)
         {
            this.addChild(this._bossSecLineUp);
            this._bossIntroBtn.x = this._bossSecLineUp.width + 5;
            this.addChild(this._bossIntroBtn);
            this._setAutoBtn.x = this._bossIntroBtn.x + this._bossIntroBtn.width + 5;
            this.addChild(this._setAutoBtn);
            joinBtn.x = this._setAutoBtn.x + this._setAutoBtn.width + 5;
            this.addChild(joinBtn);
            this._autoJoinWin.setBossID(int(activityData.actionID));
            AppFacade.instance.sendNotification("GETBOSSBOT",int(activityData.actionID));
         }
         else
         {
            this.addChild(this._bossSecLineUp);
            joinBtn.x = this._bossSecLineUp.width + 5;
            this.addChild(joinBtn);
         }
         if(activityData.isActive())
         {
            AppFacade.instance.sendNotification("CS_BOSS_CANTERCHECK",activityData.actionID);
         }
         else
         {
            updateJoinBtnStatus(activityData.isActive());
         }
      }
      
      override protected function onJoinHandler(param1:MouseEvent) : void
      {
         if(TeamTools.isMopup())
         {
            return;
         }
         if(this._currentBossSetting != null && this._currentBossSetting["bot"])
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.jinruError,
               "textFormat":TextFormatLib.format_0xFF0000_14px,
               "runTime":2
            });
         }
         else
         {
            AppFacade.instance.sendNotification("ENTER_WORLDBOSS",activityData.actionID);
         }
      }
      
      public function set currentBossSetting(param1:Object) : void
      {
         this._currentBossSetting = param1;
         if(this._currentBossSetting["bot"])
         {
            this._setAutoBtn.text = Globalization.yishezhi;
         }
         else
         {
            this._setAutoBtn.text = Globalization.shezhizidong;
         }
         this._autoJoinWin.setBossBot(param1);
      }
      
      public function get autoJoinWin() : AutoJoin
      {
         return this._autoJoinWin;
      }
   }
}

