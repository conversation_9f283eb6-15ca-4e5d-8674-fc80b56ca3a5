package activity.view.activityItem
{
   import activity.manager.ActivityXmlManager;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.data.group.HeroDetailData;
   import game.manager.AssetManager;
   import game.manager.XmlManager;
   import game.modules.task.model.TeamTools;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import game.xmlParsers.activity.Activity;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.button.Button;
   import mx.utils.StringUtil;
   import util.ButtonBehavior;
   import util.Globalization;
   
   public class CardActivityItem extends BaseActicityItem
   {
      private var _introBtn:Button;
      
      private var _cardPrizeDay:Button;
      
      private var _cardShopBtn:SimpleButton;
      
      private var _lightEffect:MovieClip;
      
      public function CardActivityItem(param1:Activity)
      {
         super(param1);
         this._introBtn = new Button(Globalization.shuoming,null,90);
         this._introBtn.addEventListener("click",onClickIntroBtn);
         this._cardPrizeDay = new Button("",null,90);
         this._cardPrizeDay.addEventListener("click",this.onCardPrizeClick);
         this._cardShopBtn = AssetManager.getObject("CardPresentBtn") as SimpleButton;
         this._cardShopBtn.addEventListener("click",this.onCardShopBtnClick);
         this._cardShopBtn.y = 10;
         this._lightEffect = AssetManager.getMc("HaloTips");
         this._lightEffect.gotoAndStop(1);
         this._lightEffect.mouseChildren = false;
         this._lightEffect.mouseEnabled = false;
      }
      
      private function onCardPrizeClick(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("CS_RECIEVE_CARDSHIP","cardshop");
      }
      
      private function onCardShopBtnClick(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("ShopExchangeModule",ModuleParams.act_Open,"cardshop",true,true));
      }
      
      override protected function onJoinHandler(param1:MouseEvent) : void
      {
         if(TeamTools.isMopup())
         {
            return;
         }
         var _loc2_:int = int(XmlManager.getXml("card_config").card_config.@openlevel);
         var _loc3_:int = MainData.getInstance().groupData.roleModle.level;
         if(_loc3_ < _loc2_)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("activity.72"),_loc2_),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(!activityData.isActive())
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.71"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         AppFacade.instance.sendNotification("CS_ACTIVITY_CARD_LOGIN");
      }
      
      override public function showBtns() : void
      {
         super.showBtns();
         this.addChild(this._introBtn);
         this._cardPrizeDay.x = 94;
         this.addChild(this._cardPrizeDay);
         joinBtn.x = 188;
         updateJoinBtnStatus(activityData.isActive());
         this._cardShopBtn.x = 365;
         var _loc3_:int = ActivityXmlManager.getCardOpenLevel();
         var _loc1_:HeroDetailData = MainData.getInstance().groupData.roleModle;
         var _loc2_:int = _loc1_.level;
         if(_loc2_ >= _loc3_)
         {
            this.addChild(this._cardShopBtn);
         }
         if(activityData.checkCardIsCanMatch())
         {
            this._lightEffect.x = joinBtn.x;
            this._lightEffect.y = joinBtn.y;
            this._lightEffect.gotoAndPlay(1);
            this.addChild(this._lightEffect);
         }
      }
      
      override public function get diffX() : int
      {
         return 386;
      }
      
      public function updateCardPrizeBtn(param1:Boolean) : void
      {
         var _loc2_:HeroDetailData = MainData.getInstance().groupData.roleModle;
         var _loc4_:int = _loc2_.level;
         var _loc5_:XMLList = XmlManager.getXml("card_config").card_config;
         var _loc3_:int = int(_loc5_.@openlevel);
         this._cardPrizeDay.setToolTip(StringUtil.substitute(Globalization.getString("activity.78"),_loc3_));
         ButtonBehavior.setBtnStatus(this._cardPrizeDay,param1 && _loc4_ >= _loc3_);
         if(_loc4_ >= _loc3_)
         {
            this._cardPrizeDay.text = param1 ? Globalization.getString("recieveCardPrize.4") : Globalization.getString("openprize.9");
         }
         else
         {
            this._cardPrizeDay.text = Globalization.getString("recieveCardPrize.4");
         }
      }
   }
}

