package activity.view.activityItem
{
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.modules.fishing.FishingManager;
   import game.mvc.AppFacade;
   import game.xmlParsers.activity.Activity;
   import mmo.ext.filter.FilterLib;
   import mmo.ui.control.button.Button;
   import util.Globalization;
   
   public class FishingActivityItem extends BaseActicityItem
   {
      private var _introBtn:Button;
      
      private var _claimRewardBtn:Button;
      
      public function FishingActivityItem(param1:Activity)
      {
         super(param1);
         this._introBtn = new Button(Globalization.shuoming,null,90);
         this._introBtn.addEventListener("click",onClickIntroBtn);
         this._introBtn.x = -60;
         joinBtn.x = 45;
         this._claimRewardBtn = new Button(Globalization.getString("Gl.77"),null,90);
         this._claimRewardBtn.addEventListener("click",this.onClaimReward);
         this._claimRewardBtn.x = joinBtn.x + this._claimRewardBtn.width + 10;
      }
      
      private function onClaimReward(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("CS_FISHING_CLAIMREWARD");
      }
      
      override public function showBtns() : void
      {
         this.addChild(this._introBtn);
         this.addChild(joinBtn);
         this.addChild(this._claimRewardBtn);
         if(MainData.getInstance().groupData.roleModle.level < FishingManager.instance.levelLimit)
         {
            joinBtn.setToolTip(Globalization.getString("等级不足"));
         }
         updateJoinBtnStatus(activityData.isActive());
         this.enableClaimReward(false);
         AppFacade.instance.sendNotification("CS_FISHING_CHECKREWARD");
      }
      
      override protected function onJoinHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("INIT_FISHING_SCENE");
      }
      
      override public function get diffX() : int
      {
         return 340;
      }
      
      public function enableClaimReward(param1:Boolean) : void
      {
         if(param1)
         {
            this._claimRewardBtn.enabled = true;
            this._claimRewardBtn.filters = [];
         }
         else
         {
            this._claimRewardBtn.enabled = false;
            this._claimRewardBtn.filters = [FilterLib.enbaleFilter];
         }
      }
   }
}

