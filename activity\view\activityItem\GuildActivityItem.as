package activity.view.activityItem
{
   import activity.view.mc.guildBattle.GuildBattleBtns;
   import activity.view.win.guildBattle.GuildBattlePrizeWin;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.AssetManager;
   import game.xmlParsers.activity.Activity;
   import mmo.ui.control.PopUpCenter;
   
   public class GuildActivityItem extends BaseActicityItem
   {
      private var _guildBtns:GuildBattleBtns;
      
      private var _viewRewardBtn:SimpleButton;
      
      private var _guildPrizeWin:GuildBattlePrizeWin;
      
      public function GuildActivityItem(param1:Activity)
      {
         super(param1);
         this._guildBtns = new GuildBattleBtns();
         this._viewRewardBtn = AssetManager.getObject("ViewKingPrizeBtn") as SimpleButton;
         this._viewRewardBtn.y = 10;
         this._viewRewardBtn.addEventListener("click",this.viewPrizeHandler);
      }
      
      private function viewPrizeHandler(param1:MouseEvent) : void
      {
         this._guildPrizeWin = new GuildBattlePrizeWin();
         PopUpCenter.addPopUp("activity.view.win.guildBattle.GuildBattlePrizeWin",this._guildPrizeWin,true,true);
      }
      
      override public function showBtns() : void
      {
         this._guildBtns.updateBtns(MainData.getInstance().guildChallengeData.id);
         this.addChild(this._guildBtns);
         this._viewRewardBtn.x = this._guildBtns.width + 70;
         this.addChild(this._viewRewardBtn);
      }
      
      override public function get diffX() : int
      {
         return this._guildBtns.width + 110;
      }
      
      override public function removeBtns() : void
      {
         this._guildBtns.removeBtns();
         super.removeBtns();
      }
      
      public function get guildBtns() : GuildBattleBtns
      {
         return this._guildBtns;
      }
      
      public function get guildPrizeWin() : GuildBattlePrizeWin
      {
         return this._guildPrizeWin;
      }
   }
}

