package activity.view.activityItem
{
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.manager.AssetManager;
   import game.modules.activity.view.win.guildDefend.GuildDefendChallengeInfo;
   import game.modules.guildClub.FeastUtil;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import game.mvc.module.ModuleUtil;
   import game.xmlParsers.activity.Activity;
   import game.xmlParsers.activity.categoryActivity.GuildDefendActi;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.Button;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class GuildDefendItem extends BaseActicityItem
   {
      private var _viewTime:Button;
      
      private var _challengeInfo:Button;
      
      private var _introBtn:Button;
      
      private var _viewRewardBtn:SimpleButton;
      
      public function GuildDefendItem(param1:Activity)
      {
         super(param1);
         this._viewTime = new Button(Globalization.getString("activity.122"),null,90);
         this.addChild(this._viewTime);
         this._viewTime.addEventListener("click",this.viewTimeHandler);
         this._challengeInfo = new Button(Globalization.getString("activity.123"),null,90);
         this._challengeInfo.x = 94;
         this.addChild(this._challengeInfo);
         this._challengeInfo.addEventListener("click",this.challengeInfoHandler);
         this._introBtn = new Button(Globalization.shuoming,null,90);
         this._introBtn.x = 188;
         this.addChild(this._introBtn);
         this._introBtn.addEventListener("click",onClickIntroBtn);
         this._viewRewardBtn = AssetManager.getObject("ViewKingPrizeBtn") as SimpleButton;
         this._viewRewardBtn.x = 360;
         this._viewRewardBtn.y = 10;
         this.addChild(this._viewRewardBtn);
         this._viewRewardBtn.addEventListener("click",this.viewPrizeHandler);
      }
      
      private function viewTimeHandler(param1:MouseEvent) : void
      {
         var event:MouseEvent = param1;
         var tips:String = null;
         var dt:Date = null;
         var hour:String = null;
         var minutes:String = null;
         var timeStr:String = null;
         tips = "";
         var startTime:Number = MainData.getInstance().guildData.guildboss_starttime;
         var endTime:Number = startTime + GuildDefendActi.activityTime;
         if(FeastUtil.isSetWillStart(endTime / 1000))
         {
            dt = new Date(startTime);
            hour = dt.hours + "";
            minutes = dt.minutes + "";
            if(hour.length < 2)
            {
               hour = "0" + hour;
            }
            if(minutes.length < 2)
            {
               minutes = "0" + minutes;
            }
            timeStr = hour + ":" + minutes;
            tips = StringUtil.substitute(Globalization.getString("Globalization.411"),timeStr);
            PopUpCenter.confirmWin2(tips,null,function():void
            {
               AppFacade.instance.sendNotification("CHAT_SENDMSG",{
                  "msg":tips,
                  "channel":"channelGuild"
               });
            },Globalization.getString("guide.6"),Globalization.getString("Globalization.412"),true);
         }
         else if(FeastUtil.isAlreadyFeast(endTime / 1000))
         {
            tips = Globalization.getString("Globalization.423");
            PopUpCenter.alertWin(tips,null,null,true);
         }
         else
         {
            tips = Globalization.getString("Globalization.409");
            if(MainData.getInstance().guildData.role_type != 0)
            {
               PopUpCenter.confirmWin2(tips,null,function():void
               {
                  if(GameScene.getCurrentScene() != 8)
                  {
                     AppFacade.instance.sendNotification("ENTER_CLUB");
                  }
                  else
                  {
                     ModuleUtil.closeModules(["ActivityModule"]);
                  }
               },Globalization.getString("Gl.161"),Globalization.getString("Globalization.410"),true);
            }
            else
            {
               PopUpCenter.alertWin(tips,null,null,true);
            }
         }
      }
      
      private function challengeInfoHandler(param1:MouseEvent) : void
      {
         var _loc2_:GuildDefendChallengeInfo = new GuildDefendChallengeInfo();
         PopUpCenter.addPopUp("game.modules.activity.view.win.help.GuildDefendChallengeInfo",_loc2_,true,true);
      }
      
      private function viewPrizeHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("GuildDefendPrizeModule",ModuleParams.act_Open,0,true,true));
      }
      
      override public function showBtns() : void
      {
         this.addChild(this._viewTime);
         this.addChild(this._challengeInfo);
         this.addChild(this._introBtn);
         this.addChild(this._viewRewardBtn);
      }
      
      override public function get diffX() : int
      {
         return 410;
      }
   }
}

