package activity.view.activityItem
{
   import activity.view.mc.strideBattle.StrideBattleBtns;
   import activity.view.win.strideBattle.GetStridePrizeWin;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.AssetManager;
   import game.xmlParsers.activity.Activity;
   import mmo.ui.control.PopUpCenter;
   
   public class KingActivityItem extends BaseActicityItem
   {
      private var _strideBtns:StrideBattleBtns;
      
      private var _viewRewardBtn:SimpleButton;
      
      private var _stridePrizeWin:GetStridePrizeWin;
      
      public function KingActivityItem(param1:Activity)
      {
         super(param1);
         this._strideBtns = new StrideBattleBtns();
         this._viewRewardBtn = AssetManager.getObject("ViewKingPrizeBtn") as SimpleButton;
         this._viewRewardBtn.y = 10;
         this._viewRewardBtn.addEventListener("click",this.viewPrizeHandler);
      }
      
      private function viewPrizeHandler(param1:MouseEvent) : void
      {
         this._stridePrizeWin = new GetStridePrizeWin();
         PopUpCenter.addPopUp("GetStridePrizeWin",this._stridePrizeWin,true,true);
      }
      
      override public function showBtns() : void
      {
         this._strideBtns.updateBtns(MainData.getInstance().serviceChallengeData.id);
         this.addChild(this._strideBtns);
         this._viewRewardBtn.x = this._strideBtns.width + 70;
         this.addChild(this._viewRewardBtn);
      }
      
      override public function get diffX() : int
      {
         return this._strideBtns.width + 110;
      }
      
      override public function removeBtns() : void
      {
         this._strideBtns.removeBtns();
         super.removeBtns();
      }
      
      public function get stridePrizeWin() : GetStridePrizeWin
      {
         return this._stridePrizeWin;
      }
   }
}

