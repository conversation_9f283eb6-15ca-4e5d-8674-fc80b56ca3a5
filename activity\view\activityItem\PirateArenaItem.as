package activity.view.activityItem
{
   import activity.proxy.ActivityStrideBattleProxy;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import game.manager.AssetManager;
   import game.modules.task.model.TaskTools;
   import game.modules.task.model.TeamTools;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import game.xmlParsers.activity.Activity;
   import game.xmlParsers.activity.categoryActivity.PirateArenaActi;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.button.Button;
   import util.ButtonBehavior;
   import util.Globalization;
   
   public class PirateArenaItem extends BaseActicityItem
   {
      private var _introBtn:Button;
      
      private var _exchangeBtn:MovieClip;
      
      private var _applyBtn:Button;
      
      private var _halo:MovieClip;
      
      public function PirateArenaItem(param1:Activity)
      {
         super(param1);
         this._introBtn = new Button(Globalization.shuoming,null,90);
         this._introBtn.addEventListener("click",onClickIntroBtn);
         joinBtn.x = 94;
         this._applyBtn = new Button(Globalization.getString("ServiceChallenge.41"),null,90);
         this._applyBtn.x = 94;
         this._applyBtn.addEventListener("click",this.onClickApplyBtn);
         this._exchangeBtn = AssetManager.getMc("BoatChallengeShopBtn");
         this._exchangeBtn.x = 300;
         this._exchangeBtn.y = 10;
         this._exchangeBtn.addEventListener("click",this.onRewardBtnHandler);
         this._exchangeBtn.buttonMode = true;
         this._exchangeBtn.gotoAndStop(1);
         this._exchangeBtn.mouseChildren = false;
         this._halo = AssetManager.getMc("HaloTips");
         this._halo.gotoAndStop(1);
         this._halo.mouseChildren = false;
         this._halo.mouseEnabled = false;
         this._halo.x = 94;
      }
      
      private function onRewardBtnHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("ShopExchangeModule",ModuleParams.act_Open,"bullfightarenashop",true,true));
      }
      
      private function onClickApplyBtn(param1:MouseEvent) : void
      {
         if(activityData.isApplyStep())
         {
            AppFacade.instance.sendNotification("CS_PIRATE_ARENA_APPLY");
         }
         else
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("challenge.1"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
         }
      }
      
      override public function showBtns() : void
      {
         var _loc1_:ActivityStrideBattleProxy = AppFacade.instance.retrieveProxy("activity.proxy.ActivityStrideBattleProxy") as ActivityStrideBattleProxy;
         this.addChild(this._introBtn);
         if(activityData.isApplyStep())
         {
            this.addChild(this._applyBtn);
            this.updateApplyBtnStatus(_loc1_.pirateArenaIsApply);
         }
         else
         {
            if(_loc1_.pirateArenaServerId <= 0)
            {
               ButtonBehavior.setBtnStatus(joinBtn,false);
               joinBtn.setToolTip(Globalization.getString("activity.113"));
            }
            else if((activityData as PirateArenaActi).isAssginRoom())
            {
               ButtonBehavior.setBtnStatus(joinBtn,false);
               joinBtn.setToolTip(Globalization.getString("activity.140"));
            }
            else
            {
               ButtonBehavior.setBtnStatus(joinBtn,true);
               joinBtn.unSetToolTip();
               updateJoinBtnStatus(activityData.isActive());
            }
            this.addChild(joinBtn);
         }
         this.addChild(this._exchangeBtn);
         this._exchangeBtn.gotoAndPlay(1);
      }
      
      override protected function onJoinHandler(param1:MouseEvent) : void
      {
         if(TeamTools.isMopup())
         {
            return;
         }
         if(TaskTools.isHangUp())
         {
            return;
         }
         AppFacade.instance.sendNotification("CS_ACTIVITY_PIRATE_ARENA_LOGIN");
      }
      
      override public function get diffX() : int
      {
         return 340;
      }
      
      public function updateApplyBtnStatus(param1:Boolean) : void
      {
         var _loc2_:ActivityStrideBattleProxy = AppFacade.instance.retrieveProxy("activity.proxy.ActivityStrideBattleProxy") as ActivityStrideBattleProxy;
         if(_loc2_.pirateArenaServerId <= 0)
         {
            ButtonBehavior.setBtnStatus(this._applyBtn,false);
            this._applyBtn.setToolTip(Globalization.getString("activity.113"));
            return;
         }
         ButtonBehavior.setBtnStatus(this._applyBtn,true);
         this._applyBtn.unSetToolTip();
         if(activityData.isApplyStep())
         {
            if(param1)
            {
               this._applyBtn.enabled = false;
               this._applyBtn.text = Globalization.getString("ServiceChallenge.47");
               this._halo.parent && this._halo.parent.removeChild(this._halo);
               this._halo.gotoAndStop(1);
            }
            else
            {
               this._applyBtn.enabled = true;
               this._applyBtn.text = Globalization.getString("ServiceChallenge.41");
               this.addChild(this._halo);
               this._halo.gotoAndPlay(1);
            }
         }
         else
         {
            this._applyBtn.enabled = false;
            this._applyBtn.text = Globalization.getString("ServiceChallenge.41");
            this._halo.parent && this._halo.parent.removeChild(this._halo);
            this._halo.gotoAndStop(1);
         }
      }
   }
}

