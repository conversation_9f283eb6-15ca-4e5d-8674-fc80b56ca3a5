package activity.view.activityItem
{
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import game.Environment;
   import game.data.MainData;
   import game.data.group.HeroDetailData;
   import game.manager.AssetManager;
   import game.manager.XmlManager;
   import game.modules.task.model.TeamTools;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import game.xmlParsers.activity.Activity;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.button.Button;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.StringToDate;
   import util.time.TimeManager;
   
   public class StrongWorldFightActivityItem extends BaseActicityItem
   {
      private var _introBtn:Button;
      
      private var _exchangeBtn:MovieClip;
      
      private var lookAwardBtn:Button;
      
      public var saveFightBtn:Button;
      
      private var CDTime:int = 0;
      
      private var cTimer:Timer;
      
      public function StrongWorldFightActivityItem(param1:Activity)
      {
         super(param1);
         this._introBtn = new Button(Globalization.shuoming,null,90);
         this._introBtn.addEventListener("click",onClickIntroBtn);
         joinBtn.x = 63;
         this.lookAwardBtn = new Button(Globalization.getString("rankAttack.7"),null,90);
         this.lookAwardBtn.x = -123;
         this.lookAwardBtn.y = this._introBtn.y;
         this._introBtn.x = -31;
         this.lookAwardBtn.addEventListener("click",this.lookAwardHandler);
         this._exchangeBtn = AssetManager.getMc("BoatChallengeShopBtn");
         this._exchangeBtn.x = 300;
         this._exchangeBtn.y = 10;
         this._exchangeBtn.addEventListener("click",this.onRewardBtnHandler);
         this._exchangeBtn.buttonMode = true;
         this._exchangeBtn.gotoAndStop(1);
         this._exchangeBtn.mouseChildren = false;
         this.saveFightBtn = new Button(Globalization.getString("peeknessFight.10"),null,90);
         this.saveFightBtn.x = joinBtn.x + 92;
         this.saveFightBtn.y = this._introBtn.y;
         this.saveFightBtn.addEventListener("click",this.saveFormationHandler);
      }
      
      public function removeSaveHandler(param1:Boolean = false) : void
      {
         if(param1)
         {
            this.saveFightBtn.removeEventListener("click",this.saveFormationHandler);
         }
      }
      
      private function saveFormationHandler(param1:MouseEvent) : void
      {
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         if(this.CDTime > 0)
         {
            _loc5_ = Math.ceil(this.CDTime / 1);
            this.saveFightBtn.enabled = false;
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("peeknessFight.33"),_loc5_),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         var _loc4_:HeroDetailData = MainData.getInstance().groupData.roleModle;
         var _loc6_:int = _loc4_.level;
         var _loc8_:int = int(XmlManager.getXml("strongWorld").children()[0].attribute("levelLimit"));
         if(_loc6_ < _loc8_)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("manlyBack.3"),_loc8_),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         var _loc3_:Array = MainData.getInstance().ownFormationsData.getFormationDataByID(MainData.getInstance().userData.cur_formation).heroList;
         var _loc2_:int = 0;
         _loc7_ = 0;
         while(_loc7_ < _loc3_.length)
         {
            if(_loc3_[_loc7_] != 0)
            {
               _loc2_ += 1;
            }
            _loc7_++;
         }
         if(_loc2_ < 5)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("peeknessFight.11"),
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
            return;
         }
         AppFacade.instance.sendNotification("CS_STRONG_SAVEFORMATION");
         this.CDTime = 5;
         this.cTimer = new Timer(1000);
         this.cTimer.start();
         this.cTimer.addEventListener("timer",this.timerHandler);
         this.cTimer.addEventListener("timerComplete",this.removeTimerHandler);
      }
      
      private function removeTimerHandler(param1:TimerEvent) : void
      {
         if(this.cTimer)
         {
            this.cTimer.stop();
            this.cTimer.removeEventListener("timer",this.timerHandler);
            this.cTimer.removeEventListener("timerComplete",this.removeTimerHandler);
            this.cTimer = null;
         }
      }
      
      private function timerHandler(param1:TimerEvent) : void
      {
         this.CDTime -= 1;
         if(this.CDTime < 0)
         {
            this.saveFightBtn.enabled = true;
            if(this.cTimer)
            {
               this.cTimer.stop();
               this.removeTimerHandler(null);
            }
         }
      }
      
      private function lookAwardHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("StrongWorldPrizeWin",ModuleParams.act_Open,0,true,true));
      }
      
      private function onRewardBtnHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("StrongWorldShopWin",ModuleParams.act_Open,null,true,true));
      }
      
      override public function showBtns() : void
      {
         this.addChild(this._introBtn);
         this.addChild(joinBtn);
         this.addChild(this.lookAwardBtn);
         this.addChild(this._exchangeBtn);
         this.addChild(this.saveFightBtn);
         this._exchangeBtn.gotoAndPlay(1);
         updateJoinBtnStatus(activityData.isActive());
         this.rankRefreshInfo();
      }
      
      private function rankRefreshInfo() : void
      {
         var _loc1_:HeroDetailData = MainData.getInstance().groupData.roleModle;
         var _loc2_:int = _loc1_.level;
         var _loc3_:int = int(XmlManager.getXml("strongWorld").children()[0].attribute("levelLimit"));
         if(_loc2_ < _loc3_)
         {
            return;
         }
         if(!activityData.isActive())
         {
            AppFacade.instance.sendNotification("CS_TOGET_STRONG_WORLD_RANK");
         }
      }
      
      override protected function onJoinHandler(param1:MouseEvent) : void
      {
         var _loc7_:Date = null;
         var _loc6_:Number = NaN;
         var _loc8_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc5_:int = 0;
         var _loc11_:int = 0;
         if(TeamTools.isMopup())
         {
            return;
         }
         var _loc9_:HeroDetailData = MainData.getInstance().groupData.roleModle;
         var _loc10_:int = _loc9_.level;
         var _loc12_:int = int(XmlManager.getXml("strongWorld").children()[0].attribute("levelLimit"));
         if(_loc10_ < _loc12_)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("manlyBack.3"),_loc12_),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(Environment.loadingParams.hasOwnProperty("openDateTime"))
         {
            _loc7_ = StringToDate.transferOpenTimeToDate();
            _loc6_ = TimeManager.getInstance().getTime();
            _loc8_ = 518400000;
            _loc2_ = _loc6_ - _loc7_.getTime();
            if(_loc2_ < _loc8_)
            {
               _loc3_ = _loc8_ - _loc2_;
               _loc4_ = _loc3_ / 1000;
               _loc5_ = _loc4_ / 86400;
               _loc11_ = (_loc4_ - _loc5_ * 24 * 3600) / 3600;
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("activity.17"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
         }
         AppFacade.instance.sendNotification("TO_ENTER_STRONGWORLD_FIGHT");
      }
      
      override public function get diffX() : int
      {
         return 340;
      }
   }
}

