package activity.view.activityItem
{
   import activity.view.mc.teamBattle.TeamBattleBtns;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.AssetManager;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import game.xmlParsers.activity.Activity;
   
   public class TeamActivityItem extends BaseActicityItem
   {
      private var _teamBtns:TeamBattleBtns;
      
      private var _viewRewardBtn:SimpleButton;
      
      public function TeamActivityItem(param1:Activity)
      {
         super(param1);
         this._teamBtns = new TeamBattleBtns();
         this._viewRewardBtn = AssetManager.getObject("ViewKingPrizeBtn") as SimpleButton;
         this._viewRewardBtn.y = 10;
         this._viewRewardBtn.addEventListener("click",this.viewPrizeHandler);
      }
      
      private function viewPrizeHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("TeamChallengePrizeModule",ModuleParams.act_Open,0,true,true));
      }
      
      override public function showBtns() : void
      {
         this._teamBtns.updateBtns(MainData.getInstance().teamChallengeData.id);
         this.addChild(this._teamBtns);
         this._viewRewardBtn.x = this._teamBtns.width + 70;
         this.addChild(this._viewRewardBtn);
      }
      
      override public function get diffX() : int
      {
         return this._teamBtns.width + 110;
      }
      
      override public function removeBtns() : void
      {
         this._teamBtns.removeBtns();
         super.removeBtns();
      }
   }
}

