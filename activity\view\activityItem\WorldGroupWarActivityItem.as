package activity.view.activityItem
{
   import activity.view.mc.AutoBattleFightJoin;
   import activity.view.win.worldGroupWarBattle.HonourShopWin;
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import game.Environment;
   import game.data.MainData;
   import game.data.group.HeroDetailData;
   import game.data.groupwar.GroupWarData;
   import game.data.worldgroupwar.WorldGroupWarData;
   import game.manager.AssetManager;
   import game.modules.task.model.TeamTools;
   import game.mvc.AppFacade;
   import game.xmlParsers.activity.Activity;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.Button;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.StringToDate;
   import util.openModule;
   import util.time.TimeManager;
   
   public class WorldGroupWarActivityItem extends BaseActicityItem
   {
      private var _introBtn:Button;
      
      private var _honourBtn:SimpleButton;
      
      private var _honourShopWin:HonourShopWin;
      
      private var _applyBtn:Button;
      
      private var _autoJoinBattleWin:AutoBattleFightJoin;
      
      private var isCanSetAutoFight:Boolean = false;
      
      public function WorldGroupWarActivityItem(param1:Activity)
      {
         super(param1);
         this._introBtn = new Button(Globalization.shuoming,null,90);
         this._introBtn.addEventListener("click",onClickIntroBtn);
         this._introBtn.x = -95;
         joinBtn.x = 10;
         this._honourBtn = AssetManager.getObject("HonourMedalBtn") as SimpleButton;
         this._honourBtn.x = 290;
         this._honourBtn.y = 10;
         this._honourBtn.addEventListener("click",this.onHonourBtnHandler);
         this._applyBtn = new Button(Globalization.getString("ServiceChallenge.41"),null,90);
         this._applyBtn.addEventListener("click",this.onApplyHandler);
         this._applyBtn.x = 110;
         this._autoJoinBattleWin = new AutoBattleFightJoin();
      }
      
      private function onHonourBtnHandler(param1:MouseEvent) : void
      {
         this._honourShopWin = new HonourShopWin();
         PopUpCenter.addPopUp("worldGroupWarBattle.HonourShopWin",this._honourShopWin,true,true);
      }
      
      private function onApplyHandler(param1:MouseEvent) : void
      {
         if(MainData.getInstance().groupData.roleModle.level < 130)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("worldGroupWar.16"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            return;
         }
         openModule("WGWFormationWindow",true,{});
      }
      
      public function enableApplyBtn(param1:Boolean) : void
      {
         if(param1)
         {
            this._applyBtn.enabled = true;
            this._applyBtn.filters = [];
         }
         else
         {
            this._applyBtn.enabled = false;
            this._applyBtn.filters = [FilterLib.enbaleFilter];
         }
      }
      
      public function markSignedUp(param1:Boolean) : void
      {
         if(param1)
         {
            this._applyBtn.text = Globalization.getString("ServiceChallenge.47");
         }
         else
         {
            this._applyBtn.text = Globalization.getString("ServiceChallenge.41");
         }
      }
      
      override public function showBtns() : void
      {
         this.addChild(this._introBtn);
         this.addChild(joinBtn);
         this.addChild(this._applyBtn);
         if(MainData.getInstance().groupData.roleModle.level < 130)
         {
            joinBtn.setToolTip(Globalization.getString("worldGroupWar.16"));
         }
         this.addChild(this._honourBtn);
         updateJoinBtnStatus(activityData.isActive());
         AppFacade.instance.sendNotification("CS_WORLD_GROUPWAR_CANSIGNUP");
      }
      
      override protected function onJoinHandler(param1:MouseEvent) : void
      {
         var _loc10_:int = 0;
         var _loc2_:HeroDetailData = null;
         var _loc9_:Date = null;
         var _loc7_:Number = NaN;
         var _loc8_:Number = NaN;
         var _loc11_:Number = NaN;
         var _loc12_:Number = NaN;
         var _loc15_:Number = NaN;
         var _loc13_:int = 0;
         var _loc14_:int = 0;
         var _loc4_:Number = NaN;
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc6_:String = null;
         if(TeamTools.isMopup())
         {
            return;
         }
         if(!WorldGroupWarData.alreadySignedUp)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("worldGroupWar.10"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
         _loc2_ = MainData.getInstance().groupData.roleModle;
         _loc10_ = _loc2_.level;
         if(_loc10_ < 130)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("worldGroupWar.16"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(Environment.loadingParams.hasOwnProperty("openDateTime"))
         {
            _loc9_ = StringToDate.transferOpenTimeToDate();
            _loc7_ = TimeManager.getInstance().getTime();
            _loc8_ = 518400000;
            _loc11_ = _loc7_ - _loc9_.getTime();
            if(_loc11_ < _loc8_)
            {
               _loc12_ = _loc8_ - _loc11_;
               _loc15_ = _loc12_ / 1000;
               _loc13_ = _loc15_ / 86400;
               _loc14_ = (_loc15_ - _loc13_ * 24 * 3600) / 3600;
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("activity.38"),_loc13_,_loc14_),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
         }
         if(activityData.isInSecondBattle())
         {
            _loc4_ = activityData.secondStartTime();
            _loc5_ = _loc4_ / 60;
            _loc3_ = _loc4_ % 60;
            _loc6_ = StringUtil.substitute(Globalization.getString("activity.39"),_loc5_,_loc3_);
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":_loc6_,
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(WorldGroupWarData.canSignUp)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("worldGroupWar.1"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         GroupWarData.isWorld = true;
         AppFacade.instance.sendNotification("CS_WORLD_GROUPWAR_CHECKLOGIN");
      }
      
      public function set currentBossSetting(param1:Object) : void
      {
         var _loc2_:int = int(param1.isAuto);
         this.isCanSetAutoFight = Boolean(param1.canSet);
         if(_loc2_ == 1)
         {
            this._applyBtn.text = Globalization.yishezhi;
         }
         else
         {
            this._applyBtn.text = Globalization.shezhizidong;
         }
         this.autoJoinBattleWin.setBossBot(param1.isAuto);
      }
      
      override public function get diffX() : int
      {
         return 340;
      }
      
      public function get honourShopWin() : HonourShopWin
      {
         return this._honourShopWin;
      }
      
      public function get autoJoinBattleWin() : AutoBattleFightJoin
      {
         return this._autoJoinBattleWin;
      }
      
      public function openSetWin() : void
      {
         PopUpCenter.addPopUp("AutoBattleFightJoin",this._autoJoinBattleWin,true,true);
      }
   }
}

