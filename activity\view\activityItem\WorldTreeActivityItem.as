package activity.view.activityItem
{
   import flash.display.SimpleButton;
   import flash.events.MouseEvent;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.manager.AssetManager;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import game.xmlParsers.activity.Activity;
   import game.xmlParsers.activity.ActivityManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.button.Button;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class WorldTreeActivityItem extends BaseActicityItem
   {
      private var _introBtn:Button;
      
      private var _worldTreeExchangeBtn:SimpleButton;
      
      public function WorldTreeActivityItem(param1:Activity)
      {
         super(param1);
         this._introBtn = new Button(Globalization.shuoming,null,90);
         joinBtn.x = 94;
         this._introBtn.addEventListener("click",onClickIntroBtn);
         this._worldTreeExchangeBtn = AssetManager.getObject("WorldTreeShopBtn") as SimpleButton;
         this._worldTreeExchangeBtn.x = 300;
         this._worldTreeExchangeBtn.y = 8;
         this._worldTreeExchangeBtn.addEventListener("click",this.onWorldTreeExchangeHandler);
      }
      
      private function onWorldTreeExchangeHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("ShopExchangeModule",ModuleParams.act_Open,"worldtree",true,true));
      }
      
      override protected function onJoinHandler(param1:MouseEvent) : void
      {
         var _loc2_:int = int(ActivityManager.getCurWorldTreeXML().@needLevel);
         var _loc3_:int = MainData.getInstance().groupData.roleModle.level;
         if(_loc3_ < _loc2_)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("activity.86"),_loc2_),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(!activityData.isActive())
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("activity.71"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         GameScene.enterScene(31);
      }
      
      override public function showBtns() : void
      {
         this.addChild(this._introBtn);
         this.addChild(joinBtn);
         this.addChild(this._worldTreeExchangeBtn);
         updateJoinBtnStatus(activityData.isActive());
      }
      
      override public function get diffX() : int
      {
         return 340;
      }
   }
}

