package activity.view.mc
{
   import activity.view.activityItem.BaseActicityItem;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.MouseEvent;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import mmo.Core;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   
   public class ActivityIcon extends UISprite
   {
      private var _content:DisplayObject;
      
      private var _url:String;
      
      private var _mask:Bitmap;
      
      private var _w:int;
      
      private var _h:int;
      
      private var _bg:UISkin;
      
      private var _eff:MovieClip;
      
      private var _selectSK:UISkin;
      
      private var _overSK:UISkin;
      
      public var activityItem:BaseActicityItem;
      
      public var id:int;
      
      public var type:int;
      
      public function ActivityIcon()
      {
         var _loc1_:Bitmap = null;
         super();
         this._bg = UIManager.getUISkin("formation_bg");
         this._bg.width = 50;
         this._bg.height = 50;
         this.addChild(this._bg);
         if($hasDefaultSkin)
         {
            _loc1_ = new Bitmap(new BitmapData($defaultWidth,$defaultHeight));
            addChild(_loc1_);
            this.mask = _loc1_;
         }
         this._eff = AssetManager.getMc("RightLight50");
         this._eff.visible = false;
         this._eff.mouseEnabled = false;
         this._eff.mouseChildren = false;
         this._eff.x = 25;
         this._eff.y = 25;
         this.addChild(this._eff);
         this._eff.stop();
         this._overSK = UIManager.getUISkin("focus_general");
         this._overSK.width = 50;
         this._overSK.height = 50;
         this._overSK.visible = false;
         this.addChild(this._overSK);
         this._selectSK = UIManager.getUISkin("selected_general");
         this._selectSK.visible = false;
         this._selectSK.x = -4;
         this._selectSK.y = -4;
         this._selectSK.width = 58;
         this._selectSK.height = 58;
         this.addChild(this._selectSK);
      }
      
      private function onRollOverHandler(param1:MouseEvent) : void
      {
         this._overSK.visible = true;
         setChildIndex(this._overSK,this.numChildren - 1);
      }
      
      private function onRollOutHandler(param1:MouseEvent) : void
      {
         this._overSK.visible = false;
      }
      
      public function setData(param1:*) : void
      {
         var data:* = param1;
         this.clear();
         if(data is String)
         {
            this._url = data;
            if(Core.dataLib.chkData(data.toString()))
            {
               this.setData(Core.dataLib.getImg(data.toString()));
            }
            else
            {
               Core.dataLib.load([data.toString()],function():void
               {
                  setData(Core.dataLib.getImg(data.toString()));
               });
            }
         }
         else if(data is BitmapData)
         {
            data.width = this._w;
            data.height = this._h;
            this.setData(new Bitmap(data));
         }
         else if(data is DisplayObject)
         {
            data.width = this._w;
            data.height = this._h;
            this._content = addChild(data);
         }
      }
      
      override public function setSize(param1:int, param2:int) : void
      {
         this._w = param1;
         this._h = param2;
         this._content && (this._content.width = param1);
         this._content && (this._content.height = param2);
         this._bg.width = param1;
         this._bg.height = param2;
      }
      
      public function clear() : void
      {
         if(this._content is MovieClip)
         {
            MovieClip(this._content).stop();
         }
         this._content && this._content.parent && removeChild(this._content);
      }
      
      override public function dispose() : void
      {
         this.clear();
         super.dispose();
      }
      
      public function clone() : Bitmap
      {
         return new Bitmap((this._content as Bitmap).bitmapData.clone());
      }
      
      public function set activeEffect(param1:Boolean) : void
      {
         this._eff.visible = param1;
         if(param1)
         {
            this._eff.play();
         }
         else
         {
            this._eff.stop();
         }
      }
      
      public function get activeEffect() : Boolean
      {
         return this._eff.visible;
      }
      
      public function enableMouseOver(param1:Boolean) : void
      {
         if(param1)
         {
            this.addEventListener("rollOver",this.onRollOverHandler);
            this.addEventListener("rollOut",this.onRollOutHandler);
         }
      }
      
      public function set selected(param1:Boolean) : void
      {
         this._selectSK.visible = param1;
      }
      
      public function get selected() : Boolean
      {
         return this._selectSK.visible;
      }
   }
}

