package activity.view.mc
{
   import activity.view.activityItem.BaseActicityItem;
   import activity.view.activityItem.BattleFieldActivityItem;
   import activity.view.activityItem.BlackShopActItem;
   import activity.view.activityItem.BoatActivityItem;
   import activity.view.activityItem.BossActivityItem;
   import activity.view.activityItem.CardActivityItem;
   import activity.view.activityItem.FishingActivityItem;
   import activity.view.activityItem.GuildActivityItem;
   import activity.view.activityItem.GuildDefendItem;
   import activity.view.activityItem.KingActivityItem;
   import activity.view.activityItem.PirateArenaItem;
   import activity.view.activityItem.StrongWorldFightActivityItem;
   import activity.view.activityItem.TeamActivityItem;
   import activity.view.activityItem.WorldGroupWarActivityItem;
   import activity.view.activityItem.WorldTreeActivityItem;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.xmlParsers.activity.Activity;
   import game.xmlParsers.activity.ActivityManager;
   import game.xmlParsers.activity.categoryActivity.GuildChallengeActi;
   import game.xmlParsers.activity.categoryActivity.ServerChallengeActi;
   import game.xmlParsers.activity.categoryActivity.TeamChallengeActi;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   import mmo.ui.control.scrollPane.ScrollPane;
   import util.Globalization;
   
   public class ActivityUI extends Sprite
   {
      private var _leftListContainer:Sprite;
      
      public var pageBtn:PageNavigator;
      
      public var activityList:Vector.<BaseActicityItem>;
      
      private var _allactivityList:Vector.<BaseActicityItem>;
      
      public var iconList:Array = [];
      
      private var _rightInfoContainer:Sprite;
      
      private var _activityImg:Icon;
      
      private var _openTimeLabel:Label;
      
      private var _openTimeValue:Label;
      
      private var _descScrollPane:ScrollPane;
      
      private var _infoTF:Label;
      
      public var clickIconHandler:Function;
      
      public function ActivityUI()
      {
         super();
         this._allactivityList = new Vector.<BaseActicityItem>();
         this.activityList = new Vector.<BaseActicityItem>();
         this._leftListContainer = new Sprite();
         this.addChild(this._leftListContainer);
         var _loc7_:UISkin = UIManager.getUISkin("pane_bg");
         _loc7_.setSize(135,430);
         this._leftListContainer.addChild(_loc7_);
         var _loc2_:Label = new Label(Globalization.getString("activity.7"),TextFormatLib.format_0xebce82_12px);
         _loc2_.x = 40;
         _loc2_.y = 2;
         this._leftListContainer.addChild(_loc2_);
         this.pageBtn = new PageNavigator();
         this.pageBtn.x = 20;
         this.pageBtn.y = 365;
         this._leftListContainer.addChild(this.pageBtn);
         this._rightInfoContainer = new Sprite();
         this._rightInfoContainer.x = 140;
         this.addChild(this._rightInfoContainer);
         var _loc8_:UISkin = UIManager.getUISkin("group_bg");
         _loc8_.width = 512;
         _loc8_.height = 430;
         this._rightInfoContainer.addChild(_loc8_);
         var _loc5_:UISkin = UIManager.getUISkin("formation_bg");
         _loc5_.setSize(487,256);
         _loc5_.x = 10;
         _loc5_.y = 7;
         this._rightInfoContainer.addChild(_loc5_);
         this._activityImg = new Icon();
         this._activityImg.x = 11;
         this._activityImg.y = 8;
         this._rightInfoContainer.addChild(this._activityImg);
         this._openTimeLabel = new Label(Globalization.getString("activity.8"),TextFormatLib.format_0x00a8ff_12px);
         this._openTimeLabel.x = 18;
         this._openTimeLabel.y = 220;
         this._rightInfoContainer.addChild(this._openTimeLabel);
         var _loc4_:TextFormat = TextFormatLib.format_0xFFED89_12px;
         _loc4_.leading = 1.4;
         this._openTimeValue = new Label("",_loc4_);
         this._openTimeValue.x = 18;
         this._openTimeValue.y = 240;
         this._rightInfoContainer.addChild(this._openTimeValue);
         var _loc6_:UISkin = UIManager.getUISkin("text_bg");
         _loc6_.y = 267;
         _loc6_.setSize(500,125);
         this._rightInfoContainer.addChild(_loc6_);
         var _loc1_:Label = new Label(Globalization.getString("activity.9"),TextFormatLib.format_0xFFB932_12px);
         _loc1_.x = 10;
         _loc1_.y = 268;
         this._rightInfoContainer.addChild(_loc1_);
         this._descScrollPane = new ScrollPane(502,105);
         this._descScrollPane.y = 282;
         this._rightInfoContainer.addChild(this._descScrollPane);
         var _loc3_:Sprite = new Sprite();
         _loc3_.y = 4;
         this._descScrollPane.addToPane(_loc3_);
         this._infoTF = new Label(Globalization.huodongjieshao,TextFormatLib.format_0xFFED89_12px);
         this._infoTF.x = 10;
         this._infoTF.wordWrap = true;
         this._infoTF.width = 485;
         _loc3_.addChild(this._infoTF);
         this._infoTF.mouseWheelEnabled = false;
         this.initUI();
      }
      
      private function initUI() : void
      {
         var _loc7_:int = 0;
         var _loc2_:Activity = null;
         var _loc1_:ActivityIcon = null;
         var _loc6_:BaseActicityItem = null;
         var _loc4_:int = 10;
         var _loc3_:int = 40;
         _loc7_ = 0;
         while(_loc7_ < 10)
         {
            _loc1_ = new ActivityIcon();
            _loc1_.setSize(50,50);
            _loc1_.x = _loc4_ + _loc7_ % 2 * 60;
            _loc1_.y = _loc3_ + int(_loc7_ / 2) * 60;
            _loc1_.addEventListener("click",this.onIconMouseClick);
            _loc1_.buttonMode = true;
            _loc1_.visible = false;
            _loc1_.enableMouseOver(true);
            _loc1_.activeEffect = false;
            this._leftListContainer.addChild(_loc1_);
            this.iconList.push(_loc1_);
            _loc7_ += 1;
         }
         var _loc5_:Vector.<Activity> = ActivityManager.getAllActivities();
         for each(_loc2_ in _loc5_)
         {
            if(_loc2_.type == 0 && int(_loc2_.action) == 1)
            {
               _loc6_ = new BossActivityItem(_loc2_);
            }
            else if(_loc2_.type == 5)
            {
               _loc6_ = new BattleFieldActivityItem(_loc2_);
            }
            else if(_loc2_.type == 7)
            {
               _loc6_ = new CardActivityItem(_loc2_);
            }
            else if(_loc2_.type == 9)
            {
               _loc6_ = new GuildActivityItem(_loc2_);
            }
            else if(_loc2_.type == 6)
            {
               _loc6_ = new KingActivityItem(_loc2_);
            }
            else if(_loc2_.type == 11)
            {
               _loc6_ = new TeamActivityItem(_loc2_);
            }
            else if(_loc2_.type == 8)
            {
               _loc6_ = new WorldTreeActivityItem(_loc2_);
            }
            else if(_loc2_.type == 12)
            {
               _loc6_ = new BoatActivityItem(_loc2_);
            }
            else if(_loc2_.type == 13)
            {
               _loc6_ = new PirateArenaItem(_loc2_);
            }
            else if(_loc2_.type == 14)
            {
               _loc6_ = new GuildDefendItem(_loc2_);
            }
            else if(_loc2_.type == 16)
            {
               _loc6_ = new StrongWorldFightActivityItem(_loc2_);
            }
            else if(_loc2_.type == 17)
            {
               _loc6_ = new BlackShopActItem(_loc2_);
            }
            else if(_loc2_.type == 19)
            {
               _loc6_ = new FishingActivityItem(_loc2_);
            }
            else if(_loc2_.type == 20)
            {
               _loc6_ = new WorldGroupWarActivityItem(_loc2_);
            }
            else
            {
               _loc6_ = new BaseActicityItem(_loc2_);
            }
            this._allactivityList.push(_loc6_);
            trace("========================:" + this._allactivityList);
         }
      }
      
      private function onIconMouseClick(param1:MouseEvent) : void
      {
         this.clickIconHandler && this.clickIconHandler(param1.target as ActivityIcon);
      }
      
      private function updateTimeLablePos(param1:int) : void
      {
         var _loc7_:int = 0;
         var _loc3_:Array = null;
         var _loc5_:int = 0;
         var _loc2_:Array = null;
         var _loc4_:int = 0;
         var _loc6_:Array = null;
         this._infoTF.defaultTextFormat = TextFormatLib.format_0xFFED89_12px;
         if(param1 == 5)
         {
            this._openTimeValue.y = 260 - this._openTimeValue.height;
            this._openTimeLabel.y = this._openTimeValue.y - this._openTimeLabel.height;
         }
         else if(param1 == 6)
         {
            _loc7_ = MainData.getInstance().serviceChallengeData.id;
            _loc3_ = ServerChallengeActi.getCurBattleTimeInfo(_loc7_);
            this._openTimeLabel.text = _loc3_[0];
            this._openTimeValue.htmlText = _loc3_[1];
            this._infoTF.htmlText = decodeURIComponent(_loc3_[2].replace(/\\n/g,"\n"));
            this._descScrollPane.nowUpdateUI();
         }
         else if(param1 == 9)
         {
            _loc5_ = MainData.getInstance().guildChallengeData.id;
            _loc2_ = GuildChallengeActi.getCurGuildBattleTimeInfo(_loc5_);
            this._openTimeLabel.text = _loc2_[0];
            this._openTimeValue.htmlText = _loc2_[1];
            this._infoTF.htmlText = decodeURIComponent(_loc2_[2].replace(/\\n/g,"\n"));
            this._descScrollPane.nowUpdateUI();
         }
         else if(param1 == 11)
         {
            _loc4_ = MainData.getInstance().teamChallengeData.id;
            _loc6_ = TeamChallengeActi.getCurTeamBattleTimeInfo(_loc4_);
            this._openTimeLabel.text = _loc6_[0];
            this._openTimeValue.htmlText = _loc6_[1];
            this._infoTF.htmlText = decodeURIComponent(_loc6_[2].replace(/\\n/g,"\n"));
            this._descScrollPane.nowUpdateUI();
         }
      }
      
      public function initActicityList(param1:int) : void
      {
         var _loc4_:BaseActicityItem = null;
         var _loc5_:BaseActicityItem = null;
         for each(_loc4_ in this._allactivityList)
         {
            trace("活动信息：" + _loc4_.activityData.id + ":" + _loc4_.activityData.isShow());
            if(this.activityList.indexOf(_loc4_) == -1)
            {
               this.activityList.push(_loc4_);
               _loc4_.removeBtns();
            }
         }
         if(param1 > 0)
         {
            this.getPage(param1);
            this.pageBtn.totalPage = Math.ceil(this.activityList.length / 10);
            return;
         }
         var _loc3_:int = 1;
         var _loc2_:Array = ActivityManager.NEED_EFFECT_ACTI;
         for each(_loc5_ in this.activityList)
         {
            if(_loc5_.activityData.isActive() && _loc2_.indexOf(_loc5_.activityData.type) != -1)
            {
               _loc3_ = int(this.activityList.indexOf(_loc5_));
               _loc3_ += 1;
               break;
            }
         }
         this.pageBtn.currentPage = Math.ceil(_loc3_ / 10);
         this.pageBtn.totalPage = Math.ceil(this.activityList.length / 10);
      }
      
      public function setActicityInfo(param1:BaseActicityItem) : void
      {
         if(!param1)
         {
            return;
         }
         this._activityImg.setData(UrlManager.getActivityBGUrl(param1.activityData.bg));
         this._openTimeLabel.y = 220;
         this._openTimeLabel.text = param1.activityData.titleInfo;
         this._openTimeValue.y = 240;
         this._openTimeValue.htmlText = decodeURIComponent(param1.activityData.timeInfo.replace(/\\n/g,"\n"));
         this._infoTF.htmlText = decodeURIComponent(param1.activityData.description.replace(/\\n/g,"\n"));
         this._descScrollPane.nowUpdateUI();
         this.updateTimeLablePos(param1.activityData.type);
      }
      
      public function setActivityData() : void
      {
         var _loc6_:* = 0;
         var _loc1_:ActivityIcon = null;
         var _loc5_:BaseActicityItem = null;
         var _loc3_:int = int(this.pageBtn.currentPage);
         var _loc2_:int = (_loc3_ - 1) * 10;
         var _loc4_:int = _loc3_ * 10;
         _loc6_ = _loc2_;
         while(_loc6_ < _loc4_)
         {
            _loc1_ = this.iconList[_loc6_ % 10];
            _loc1_.visible = false;
            _loc1_.selected = false;
            _loc1_.activeEffect = false;
            if(_loc6_ < this.activityList.length)
            {
               trace("============:" + _loc6_ + "||" + this.activityList[_loc6_]);
               _loc5_ = this.activityList[_loc6_] as BaseActicityItem;
               _loc1_.activityItem = _loc5_;
               _loc1_.id = _loc5_.activityData.id;
               _loc1_.type = _loc5_.activityData.type;
               _loc1_.setToolTip(decodeURIComponent(_loc5_.activityData.name.replace(/\\n/g,"\n")));
               _loc1_.setData(UrlManager.getActivityIconUrl(_loc5_.activityData.icon));
               _loc1_.visible = true;
            }
            _loc6_++;
         }
      }
      
      public function getIconById(param1:int) : ActivityIcon
      {
         var _loc2_:ActivityIcon = null;
         for each(_loc2_ in this.iconList)
         {
            if(_loc2_.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function getPage(param1:int) : Boolean
      {
         var _loc4_:BaseActicityItem = null;
         var _loc3_:int = 0;
         var _loc2_:int = 1;
         for each(_loc4_ in this._allactivityList)
         {
            if(_loc4_.activityData.id == param1)
            {
               _loc2_ = int(this.activityList.indexOf(_loc4_));
               _loc2_++;
               break;
            }
         }
         _loc3_ = Math.ceil(_loc2_ / 10);
         _loc3_ = _loc3_ > 1 ? _loc3_ : 1;
         if(this.pageBtn.currentPage == _loc3_)
         {
            return true;
         }
         this.pageBtn.currentPage = _loc3_;
         return false;
      }
      
      public function getActivityItemById(param1:int) : BaseActicityItem
      {
         var _loc2_:BaseActicityItem = null;
         for each(_loc2_ in this._allactivityList)
         {
            if(_loc2_.activityData.id == param1)
            {
               return _loc2_;
            }
         }
         return null;
      }
      
      public function addBtns(param1:BaseActicityItem) : void
      {
         param1.x = 490 - param1.diffX;
         param1.y = 395;
         this._rightInfoContainer.addChild(param1);
      }
   }
}

