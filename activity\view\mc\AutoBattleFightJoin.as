package activity.view.mc
{
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.CheckBox;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.WindowModel;
   import mmo.ui.event.ButtonEvent;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class AutoBattleFightJoin extends WindowModel
   {
      public static const NAME:String = "AutoBattleFightJoin";
      
      private var check1:CheckBox;
      
      public var isCanClick:Boolean;
      
      private var lab4:Label;
      
      public var sp:UISprite;
      
      private var config:int;
      
      private var isSet:Boolean = false;
      
      public function AutoBattleFightJoin()
      {
         super();
         this.isLive = true;
         var _loc3_:UISkin = UIManager.getUISkin("pane_bg_black");
         _loc3_.width = 150;
         _loc3_.height = 225;
         this.addChild(_loc3_);
         this.check1 = new CheckBox("");
         this.check1.x = 25;
         this.check1.y = 15;
         this.check1.addEventListener(ButtonEvent.Button_Update,this.onMouseClickHandler);
         this.addChild(this.check1);
         var _loc6_:Label = new Label(Globalization.zidongcanyu,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc6_.x = 60;
         _loc6_.y = 15;
         this.addChild(_loc6_);
         var _loc2_:TextFormat = TextFormatLib.format_0xFFED89_12px;
         _loc2_.leading = 3.5;
         var _loc5_:Label = new Label("",_loc2_,[FilterLib.glow_0x272727],false);
         _loc2_.leading = 0;
         _loc5_.x = 10;
         _loc5_.y = 50;
         _loc5_.width = 135;
         _loc5_.height = 130;
         _loc5_.wordWrap = true;
         var _loc4_:UISkin = UIManager.getUISkin("split_line2");
         _loc4_.x = 8;
         _loc4_.y = 40;
         _loc4_.width = 130;
         this.addChild(_loc4_);
         _loc5_.htmlText = Globalization.getString("autoJionBattle.1");
         this.addChild(_loc5_);
         var _loc1_:Button = new Button(Globalization.queding,null,60);
         _loc1_.x = 10;
         _loc1_.y = 180;
         _loc1_.addEventListener("click",this.onOkHandler);
         this.addChild(_loc1_);
         var _loc7_:Button = new Button(Globalization.quxiao,null,60);
         _loc7_.x = 80;
         _loc7_.y = 180;
         this.addChild(_loc7_);
         _loc7_.addEventListener("click",this.onCloseHandler);
      }
      
      private function onMouseClickHandler(param1:Event) : void
      {
         if(param1.target == this.check1)
         {
            if(this.check1.isCheck)
            {
            }
         }
      }
      
      private function okFun() : void
      {
         AppFacade.instance.sendNotification("CS_SET_BATTLEFIGHT_AUTO",{"setAuto":this.check1.isCheck});
         this.close();
      }
      
      private function reSetokFun() : void
      {
         AppFacade.instance.sendNotification("CS_SET_BATTLEFIGHT_AUTO",{"setAuto":this.check1.isCheck});
         this.close();
      }
      
      private function onCloseHandler(param1:MouseEvent) : void
      {
         this.close();
      }
      
      private function onOkHandler(param1:MouseEvent) : void
      {
         if(!this.isSet && this.check1.isCheck)
         {
            PopUpCenter.confirmWin(StringUtil.substitute(Globalization.getString("activity.5")),this.okFun,null,0,true);
            return;
         }
         if(!this.check1.isCheck && !this.isSet)
         {
            this.close();
         }
         else if(this.check1.isCheck == this.isSet == true)
         {
            this.close();
         }
         else
         {
            PopUpCenter.confirmWin(Globalization.getString("activity.21"),this.reSetokFun,null,0,true);
         }
      }
      
      public function setBossBot(param1:int) : void
      {
         this.config = param1;
         if(this.config == 1)
         {
            this.isSet = true;
            this.check1.isCheck = true;
         }
         else
         {
            this.isSet = false;
            this.check1.isCheck = false;
         }
      }
   }
}

