package activity.view.mc
{
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.CheckBox;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.WindowModel;
   import mmo.ui.event.ButtonEvent;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class AutoJoin extends WindowModel
   {
      public static const NAME:String = "AutoJoin";
      
      private var check1:CheckBox;
      
      private var check2:CheckBox;
      
      private var check3:CheckBox;
      
      private var bossID:int;
      
      public var isCanClick:Boolean;
      
      private var lab4:Label;
      
      public var sp:UISprite;
      
      private var config:Object;
      
      private var isSet:Boolean = false;
      
      public function AutoJoin()
      {
         super();
         this.isLive = true;
         var _loc3_:UISkin = UIManager.getUISkin("pane_bg_black");
         _loc3_.width = 150;
         _loc3_.height = 310;
         this.addChild(_loc3_);
         this.check1 = new CheckBox("");
         this.check1.x = 25;
         this.check1.y = 15;
         this.check1.addEventListener(ButtonEvent.Button_Update,this.onMouseClickHandler);
         this.addChild(this.check1);
         var _loc6_:Label = new Label(Globalization.zidongcanyu,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc6_.x = 60;
         _loc6_.y = 15;
         this.addChild(_loc6_);
         this.check2 = new CheckBox("");
         this.check2.x = 25;
         this.check2.y = 40;
         this.check2.enabled = false;
         this.check2.addEventListener(ButtonEvent.Button_Update,this.onMouseClickHandler);
         this.addChild(this.check2);
         var _loc8_:Label = new Label(Globalization.miaofuhuoCD,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc8_.x = 60;
         _loc8_.y = 40;
         this.addChild(_loc8_);
         this.check3 = new CheckBox("");
         this.check3.enabled = false;
         this.check3.addEventListener(ButtonEvent.Button_Update,this.onMouseClickHandler);
         this.sp = new UISprite();
         this.sp.addChild(this.check3);
         this.sp.x = 25;
         this.sp.y = 65;
         this.sp.setToolTip(Globalization.getString("saveBossLineUp.4"));
         this.addChild(this.sp);
         this.lab4 = new Label(Globalization.getString("saveBossLineUp.3"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.lab4.x = 42;
         this.lab4.y = 65;
         this.addChild(this.lab4);
         var _loc2_:TextFormat = TextFormatLib.format_0xFFED89_12px;
         _loc2_.leading = 3.5;
         var _loc5_:Label = new Label("",_loc2_,[FilterLib.glow_0x272727],false);
         _loc2_.leading = 0;
         _loc5_.x = 10;
         _loc5_.y = 95;
         _loc5_.width = 135;
         _loc5_.height = 170;
         _loc5_.wordWrap = true;
         var _loc4_:UISkin = UIManager.getUISkin("split_line2");
         _loc4_.x = 8;
         _loc4_.y = 85;
         _loc4_.width = 130;
         this.addChild(_loc4_);
         _loc5_.htmlText = Globalization.zidongcanyutips;
         this.addChild(_loc5_);
         var _loc1_:Button = new Button(Globalization.queding,null,60);
         _loc1_.x = 10;
         _loc1_.y = 260;
         _loc1_.addEventListener("click",this.onOkHandler);
         this.addChild(_loc1_);
         var _loc7_:Button = new Button(Globalization.quxiao,null,60);
         _loc7_.x = 80;
         _loc7_.y = 260;
         this.addChild(_loc7_);
         _loc7_.addEventListener("click",this.onCloseHandler);
      }
      
      public function setBossID(param1:int) : void
      {
         this.bossID = param1;
      }
      
      private function onMouseClickHandler(param1:Event) : void
      {
         if(param1.target == this.check1)
         {
            if(this.check1.isCheck)
            {
               this.check2.enabled = true;
               if(this.isCanClick)
               {
                  this.check3.enabled = true;
                  this.check3.isCheck = true;
               }
            }
            if(!this.check1.isCheck)
            {
               this.check2.enabled = false;
               this.check2.isCheck = false;
               this.check3.enabled = false;
               this.check3.isCheck = false;
            }
         }
         else if(param1.target == this.check2)
         {
            if(this.check2.isCheck)
            {
               this.check1.isCheck = true;
            }
         }
         else if(param1.target == this.check3)
         {
            if(this.check3.isCheck)
            {
            }
         }
      }
      
      private function okFun() : void
      {
         AppFacade.instance.sendNotification("SETBOSSBOT",[this.bossID,this.check1.isCheck,this.check2.isCheck,this.check3.isCheck]);
         this.close();
      }
      
      private function reSetokFun() : void
      {
         AppFacade.instance.sendNotification("UNSETBOSSBOT",[this.bossID,this.check1.isCheck,this.check2.isCheck,this.check3.isCheck]);
         this.close();
      }
      
      private function onCloseHandler(param1:MouseEvent) : void
      {
         this.close();
      }
      
      private function onOkHandler(param1:MouseEvent) : void
      {
         var _loc2_:XML = null;
         if(!this.isSet && this.check1.isCheck)
         {
            PopUpCenter.confirmWin(StringUtil.substitute("1000"),this.okFun,null,0,true);
            return;
         }
         if(this.config["bot"] == this.check1.isCheck && this.config["bot_sub_cdtime"] == this.check2.isCheck && this.config["isUseFormation"] == this.check3.isCheck)
         {
            this.close();
         }
         else
         {
            PopUpCenter.confirmWin(Globalization.getString("activity.21"),this.reSetokFun,null,0,true);
         }
      }
      
      public function setBossBot(param1:Object) : void
      {
         this.config = param1;
         if(param1["bot"])
         {
            this.isSet = true;
            this.check1.isCheck = true;
         }
         else
         {
            this.isSet = false;
            this.check1.isCheck = false;
         }
         if(param1["bot_sub_cdtime"])
         {
            this.check2.isCheck = true;
         }
         else
         {
            this.check2.isCheck = false;
         }
         if(param1["isUseFormation"])
         {
            this.check3.isCheck = true;
         }
         else
         {
            this.check3.isCheck = false;
         }
         if(param1["savedFormation"])
         {
            this.sp.clearAllEvent();
            this.isCanClick = true;
         }
         else
         {
            this.isCanClick = false;
         }
      }
   }
}

