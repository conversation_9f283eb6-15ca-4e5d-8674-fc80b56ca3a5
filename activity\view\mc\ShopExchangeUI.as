package activity.view.mc
{
   import activity.manager.ActivityXmlManager;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import game.events.PageNavigatorEvent;
   import game.manager.UIManager;
   import game.modules.activity.proxy.ActivityProxy;
   import game.mvc.AppFacade;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.page.PageNavigator;
   import util.Globalization;
   
   public class ShopExchangeUI extends UISprite
   {
      public static const LIST_MAX_NUM:int = 9;
      
      private var _itemBox:UIBox;
      
      private var _pageBtn:PageNavigator;
      
      private var _itemListArr:Array = [];
      
      private var _itemDataArr:Array;
      
      private var _curPage:int = 1;
      
      public var exchangeFun:Function;
      
      private var _shopType:String;
      
      public function ShopExchangeUI(param1:String, param2:int = 580, param3:int = 30)
      {
         super();
         this._shopType = param1;
         var _loc5_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc5_.setSize(param2,param3);
         _loc5_.x = 612 - param2 >> 1;
         this.addChild(_loc5_);
         var _loc4_:UISkin = UIManager.getUISkin("honourListBg");
         _loc4_.setSize(612,404);
         _loc4_.y = param3 + 2;
         this.addChild(_loc4_);
         this._itemBox = new UIBox();
         this._itemBox.x = 12;
         this._itemBox.y = param3 + 10;
         this._itemBox.lineMaxChildrenNumber = 3;
         this._itemBox.rowMaxChildrenNumber = 3;
         this._itemBox.lineSpace = 1;
         this._itemBox.rowSpace = 4;
         this.addChild(this._itemBox);
         this._pageBtn = new PageNavigator();
         this._pageBtn.x = 254;
         this._pageBtn.y = _loc4_.y + _loc4_.height;
         this.addChild(this._pageBtn);
         this._pageBtn.addEventListener("pageChange",this.onPageChange);
      }
      
      private function onPageChange(param1:PageNavigatorEvent) : void
      {
         this.freshItemList(this._itemDataArr);
      }
      
      private function onExchangeHandler(param1:MouseEvent) : void
      {
         var _loc4_:ExchangeItem = param1.target.parent as ExchangeItem;
         var _loc2_:Point = _loc4_.itemSlot.localToGlobal(new Point(0,0));
         var _loc3_:Object = {
            "exchangeId":_loc4_.exchangeId,
            "point":_loc2_
         };
         this.exchangeFun && this.exchangeFun(_loc3_);
      }
      
      private function freshItemList(param1:Array) : void
      {
         var _loc6_:ActivityProxy = null;
         var _loc8_:Object = null;
         var _loc2_:int = 0;
         var _loc3_:ExchangeItem = null;
         var _loc5_:ExchangeItem = null;
         while(this._itemBox.numChildren)
         {
            _loc3_ = this._itemBox.removeChildAt(0) as ExchangeItem;
            _loc3_.exchangeBtn.removeEventListener("click",this.onExchangeHandler);
         }
         this._curPage = this._pageBtn.currentPage;
         var _loc9_:int = int(this._pageBtn.totalPage);
         var _loc4_:int = (this._curPage - 1) * 9;
         var _loc7_:int = this._curPage >= _loc9_ ? int(param1.length) : this._curPage * 9;
         _loc6_ = AppFacade.instance.retrieveProxy("ActivityProxy") as ActivityProxy;
         while(_loc4_ < _loc7_)
         {
            _loc8_ = param1[_loc4_];
            if(this._itemListArr[_loc4_ % 9])
            {
               _loc5_ = this._itemListArr[_loc4_ % 9];
            }
            else
            {
               _loc5_ = new ExchangeItem();
               _loc5_.exchangeBtn.enabled = false;
               this._itemListArr.push(_loc5_);
            }
            _loc5_.setExchangeData(_loc8_.exchangeId,_loc8_.tempId,_loc8_.getNum,_loc8_.integral,_loc8_.lv,_loc8_.prestige,this._shopType);
            if(_loc8_.limitTimes > 0)
            {
               _loc2_ = _loc6_.getItemExchangeTimes(_loc8_.exchangeId);
               if(_loc2_ >= _loc8_.limitTimes)
               {
                  _loc5_.exchangeBtn.enabled = false;
                  _loc5_.exchangeBtn.text = Globalization.getString("activity.105");
               }
               else
               {
                  _loc5_.exchangeBtn.enabled = true;
                  _loc5_.exchangeBtn.text = Globalization.getString("equipmentExchange.5");
               }
            }
            else
            {
               _loc5_.exchangeBtn.enabled = true;
               _loc5_.exchangeBtn.text = Globalization.getString("equipmentExchange.5");
            }
            this._itemBox.addChild(_loc5_);
            _loc5_.exchangeBtn.addEventListener("click",this.onExchangeHandler);
            _loc4_++;
         }
      }
      
      public function setHonourShopItem(param1:Array) : void
      {
         if(param1 != null && param1.length != 0)
         {
            this._itemDataArr = param1;
            this._pageBtn.init(this._curPage,Math.ceil(param1.length / 9));
            this.freshItemList(param1);
         }
      }
      
      public function updateExchangeBtnState(param1:int, param2:int) : void
      {
         var _loc3_:ExchangeItem = null;
         var _loc4_:Object = ActivityXmlManager.getExchangeInfoByExchangeId(param1,"honourShop");
         if(_loc4_.limitTimes <= 0)
         {
            return;
         }
         for each(_loc3_ in this._itemListArr)
         {
            if(_loc3_.exchangeId == param1)
            {
               if(param2 >= _loc4_.limitTimes)
               {
                  _loc3_.exchangeBtn.enabled = false;
                  _loc3_.exchangeBtn.text = Globalization.getString("activity.105");
               }
               break;
            }
         }
      }
   }
}

