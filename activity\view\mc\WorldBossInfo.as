package activity.view.mc
{
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class WorldBossInfo extends PopUpWindow
   {
      public static const NAME:String = "WorldBossInfo";
      
      public function WorldBossInfo()
      {
         super(330,305);
         this.setTitleImageData(UIManager.getUISkin("title_shuoming").bitmapData);
         var _loc2_:UISkin = UIManager.getUISkin("pane_bg_light");
         _loc2_.width = 310;
         _loc2_.height = 220;
         _loc2_.x = 5;
         pane.addChild(_loc2_);
         var _loc3_:TextFormat = TextFormatLib.format_0xFFED89_12px;
         _loc3_.leading = 3.5;
         var _loc4_:Label = new Label("",_loc3_,[FilterLib.glow_0x272727],false);
         _loc3_.leading = 0;
         _loc4_.width = 290;
         _loc4_.height = 210;
         _loc4_.wordWrap = true;
         _loc4_.x = 13;
         _loc4_.y = 10;
         _loc4_.htmlText = Globalization.huodonginfo;
         pane.addChild(_loc4_);
         var _loc1_:Button = new Button(Globalization.queding,null,75,UIManager.getMultiUISkin("button_big"));
         _loc1_.x = 125;
         _loc1_.y = 230;
         _loc1_.addEventListener("click",this.onOKBtnClickHandler);
         pane.addChild(_loc1_);
      }
      
      private function onOKBtnClickHandler(param1:MouseEvent) : void
      {
         this.close();
      }
   }
}

