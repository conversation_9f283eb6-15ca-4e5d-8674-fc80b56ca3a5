package activity.view.mc.blackShop
{
   import flash.display.Sprite;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   
   public class BlackShopAwardComp extends UISprite
   {
      private var comBg:UISkin;
      
      private var scrollPane:ScrollPane;
      
      private var viewBox:UIBox;
      
      private var MAX_NUM_SHOW:int = 4;
      
      private var itemArr:Array = [];
      
      private var gbg:UISkin;
      
      public function BlackShopAwardComp()
      {
         super();
         this.initComp();
      }
      
      private function initComp() : void
      {
         this.gbg = UIManager.getUISkin("group_bg");
         this.gbg.width = 270;
         this.gbg.height = 290;
         addChild(this.gbg);
         var _loc1_:UISkin = UIManager.getUISkin("text_bg_4");
         _loc1_.width = 230;
         _loc1_.height = 250;
         _loc1_.x = 10;
         _loc1_.y = 30;
         addChild(_loc1_);
         this.scrollPane = new ScrollPane(255,235);
         this.scrollPane.x = 5;
         this.scrollPane.y = 40;
         addChild(this.scrollPane);
         var _loc2_:Sprite = new Sprite();
         _loc2_.x = 10;
         _loc2_.y = 0;
         this.scrollPane.addToPane(_loc2_);
         this.viewBox = new UIBox();
         this.viewBox.x = 0;
         this.viewBox.y = 3;
         this.viewBox.lineMaxChildrenNumber = this.MAX_NUM_SHOW;
         this.viewBox.rowSpace = 7;
         this.viewBox.lineSpace = 12;
         _loc2_.addChild(this.viewBox);
      }
      
      public function initXml(param1:int) : void
      {
         var _loc3_:Array = null;
         var _loc4_:XML = null;
         var _loc5_:String = null;
         var _loc2_:int = 0;
         _loc3_ = [];
         _loc4_ = XmlManager.blackShopXml.children().(@id == param1)[0];
         _loc5_ = _loc4_.@index_id;
         _loc3_ = _loc5_.split("|");
         _loc2_ = 0;
         while(_loc2_ < _loc3_.length)
         {
            this.itemArr.push(int(XmlManager.blackShopGoodsXml.children().(@index_id == _loc3_[_loc2_]).attribute("item")));
            _loc2_++;
         }
         this.initItemIcon(this.itemArr);
      }
      
      private function initItemIcon(param1:Array) : void
      {
         var _loc2_:int = 0;
         var _loc3_:Slot = null;
         var _loc4_:int = 0;
         var _loc5_:SlotTemplete = null;
         _loc2_ = 0;
         while(_loc2_ < param1.length)
         {
            _loc3_ = new Slot();
            _loc4_ = int(param1[_loc2_]);
            _loc5_ = new SlotTemplete();
            _loc5_.tempID = _loc4_;
            _loc3_.setItem(_loc5_,false,false,false,false);
            this.viewBox.addChild(_loc3_);
            this.viewBox.refresh();
            _loc2_++;
         }
      }
   }
}

