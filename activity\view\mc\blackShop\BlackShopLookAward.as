package activity.view.mc.blackShop
{
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   
   public class BlackShopLookAward extends PopUpWindow
   {
      public static const NAME:String = "BlackShopLookAward";
      
      private var highShop:BlackShopAwardComp;
      
      private var hLbSkin:UISkin;
      
      private var lowShop:BlackShopAwardComp;
      
      private var lowLbSkin:UISkin;
      
      private var tipsLb:Label;
      
      public function BlackShopLookAward()
      {
         super(570,390);
         this.setTitleImageData(UIManager.getUISkin("blackShopLookTitle").bitmapData,-25);
         this.initComp();
      }
      
      private function initComp() : void
      {
         var _loc1_:String = null;
         var _loc2_:TextFormat = null;
         this.hLbSkin = UIManager.getUISkin("blackShopHighTitle");
         this.hLbSkin.y = 35;
         this.lowLbSkin = UIManager.getUISkin("blackShopLowTitle");
         this.lowLbSkin.y = this.hLbSkin.y;
         this.highShop = new BlackShopAwardComp();
         this.highShop.initXml(1);
         addChild(this.highShop);
         this.highShop.x = 11;
         this.highShop.y = 40;
         this.hLbSkin.x = this.highShop.x + 63;
         this.lowShop = new BlackShopAwardComp();
         this.lowShop.initXml(2);
         addChild(this.lowShop);
         this.lowShop.x = 290;
         this.lowShop.y = this.highShop.y;
         this.lowLbSkin.x = this.lowShop.x + 63;
         addChild(this.hLbSkin);
         addChild(this.lowLbSkin);
         _loc1_ = XmlManager.blackShopXml.children().(@id == 1).attribute("description");
         _loc2_ = new TextFormat("Verdana",12,16772489);
         _loc2_.leading = 5;
         this.tipsLb = new Label("",_loc2_);
         this.tipsLb.htmlText = decodeURIComponent(_loc1_.replace(/\\n/g,"\n"));
         this.tipsLb.x = 15;
         this.tipsLb.y = 340;
         this.tipsLb.width = 445;
         this.tipsLb.height = 50;
         this.tipsLb.multiline = true;
         this.tipsLb.wordWrap = true;
         addChild(this.tipsLb);
      }
      
      override public function get height() : Number
      {
         return 360;
      }
   }
}

