package activity.view.mc.blackShop
{
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class BlackShopOpenVipWin extends PopUpWindow
   {
      public static const NAME:String = "BlackShopOpenVipWin";
      
      private var curBg:UISkin;
      
      private var lowOpenVipBtn:Button;
      
      private var highOpenVipBtn:Button;
      
      private var threeLb:Label;
      
      private var sixLb:Label;
      
      private var highLv:int;
      
      private var highCost:int;
      
      private var lowLv:int;
      
      private var lowCost:int;
      
      public function BlackShopOpenVipWin()
      {
         super(505,160);
         this.setTitleImageData(UIManager.getUISkin("blackShopOpenVipTitle").bitmapData,-25);
         this.initComp();
      }
      
      private function initComp() : void
      {
         var _loc3_:String = null;
         var _loc2_:String = null;
         var _loc1_:int = 0;
         var _loc5_:UISkin = UIManager.getUISkin("black_bg3");
         _loc5_.x = 10;
         _loc5_.y = 40;
         _loc5_.width = 240;
         _loc5_.height = 75;
         addChild(_loc5_);
         var _loc4_:UISkin = UIManager.getUISkin("black_bg3");
         _loc4_.y = 40;
         _loc4_.width = 240;
         _loc4_.height = 75;
         _loc4_.x = 260;
         addChild(_loc4_);
         this.highLv = int(XmlManager.blackShopXml.children().(@id == 1).attribute("permanent_vip_need"));
         this.highCost = int(XmlManager.blackShopXml.children().(@id == 1).attribute("permanent_cost"));
         this.lowLv = int(XmlManager.blackShopXml.children().(@id == 2).attribute("permanent_vip_need"));
         this.lowCost = int(XmlManager.blackShopXml.children().(@id == 2).attribute("permanent_cost"));
         _loc3_ = StringUtil.substitute(Globalization.getString("blackShop.3"),this.highLv,this.highCost);
         this.threeLb = new Label("",TextFormatLib.format_0xffffff_14px);
         this.threeLb.htmlText = _loc3_;
         this.threeLb.width = 215;
         this.threeLb.wordWrap = true;
         this.threeLb.x = 25;
         this.threeLb.y = _loc5_.y + 20;
         addChild(this.threeLb);
         _loc2_ = StringUtil.substitute(Globalization.getString("blackShop.4"),this.lowLv,this.lowCost);
         this.sixLb = new Label("",TextFormatLib.format_0xffffff_14px);
         this.sixLb.htmlText = _loc2_;
         this.sixLb.width = 215;
         this.sixLb.wordWrap = true;
         this.sixLb.x = 275;
         this.sixLb.y = _loc4_.y + 20;
         addChild(this.sixLb);
         this.highOpenVipBtn = new Button(Globalization.getString("blackShop.28"),null,85);
         this.highOpenVipBtn.x = 75;
         this.highOpenVipBtn.y = _loc5_.y + 75;
         addChild(this.highOpenVipBtn);
         this.highOpenVipBtn.addEventListener("click",this.highHandler);
         this.lowOpenVipBtn = new Button(Globalization.getString("blackShop.28"),null,85);
         this.lowOpenVipBtn.x = 345;
         this.lowOpenVipBtn.y = this.highOpenVipBtn.y;
         addChild(this.lowOpenVipBtn);
         this.lowOpenVipBtn.addEventListener("click",this.lowHandler);
         _loc1_ = MainData.getInstance().userData.openBlackShopId;
         if(_loc1_ != 0)
         {
            if(_loc1_ == 1)
            {
               this.highOpenVipBtn.text = Globalization.getString("blackShop.35");
               this.highOpenVipBtn.enabled = false;
            }
            else if(_loc1_ == 2)
            {
               this.lowOpenVipBtn.text = Globalization.getString("blackShop.35");
               this.lowOpenVipBtn.enabled = false;
            }
            else
            {
               this.lowOpenVipBtn.text = Globalization.getString("blackShop.35");
               this.lowOpenVipBtn.enabled = false;
               this.highOpenVipBtn.text = Globalization.getString("blackShop.35");
               this.highOpenVipBtn.enabled = false;
            }
         }
      }
      
      private function lowHandler(param1:MouseEvent) : void
      {
         var str:String;
         var e:MouseEvent = param1;
         var curLvStr:String = null;
         if(MainData.getInstance().userData.gold_num < this.lowCost)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("Gl.25"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            return;
         }
         if(MainData.getInstance().userData.vip < this.lowLv)
         {
            curLvStr = StringUtil.substitute(Globalization.getString("groupwar.47"),this.lowLv);
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":curLvStr,
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         str = StringUtil.substitute(Globalization.getString("blackShop.20"),this.lowCost);
         PopUpCenter.confirmWin2(str,(function():*
         {
            var okFun:Function;
            return okFun = function():void
            {
               AppFacade.instance.sendNotification("CS_OPEN_BLACK_SHOPVIP",{
                  "cost":lowCost,
                  "type":2
               });
               PopUpCenter.removePopUp("BlackShopOpenVipWin");
            };
         })(),null,Globalization.getString("Gl.161"),Globalization.getString("Globalization.38"),true);
      }
      
      private function highHandler(param1:MouseEvent) : void
      {
         var str:String;
         var e:MouseEvent = param1;
         var curLvStr:String = null;
         if(MainData.getInstance().userData.gold_num < this.highCost)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("Gl.25"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            return;
         }
         if(MainData.getInstance().userData.vip < this.highLv)
         {
            curLvStr = StringUtil.substitute(Globalization.getString("groupwar.47"),this.highLv);
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":curLvStr,
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         str = StringUtil.substitute(Globalization.getString("blackShop.20"),this.highCost);
         PopUpCenter.confirmWin2(str,(function():*
         {
            var okFun:Function;
            return okFun = function():void
            {
               AppFacade.instance.sendNotification("CS_OPEN_BLACK_SHOPVIP",{
                  "cost":highCost,
                  "type":1
               });
               PopUpCenter.removePopUp("BlackShopOpenVipWin");
            };
         })(),null,Globalization.getString("Gl.161"),Globalization.getString("Globalization.38"),true);
      }
   }
}

