package activity.view.mc.blackShop
{
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.CheckBox;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.window.PopUpWindow;
   import mmo.ui.event.ButtonEvent;
   import util.Globalization;
   
   public class BlackShopSetWin extends PopUpWindow
   {
      public static const NAME:String = "BlackShopSetWin";
      
      private var curbg:UISkin;
      
      private var confirmBtn:Button;
      
      private var cancelBtn:Button;
      
      private var tipsLb:Label;
      
      private var scrollPane:ScrollPane;
      
      private var selectBtn:CheckBox;
      
      private var isSelectB:Boolean;
      
      private var warningLb:Label;
      
      public function BlackShopSetWin()
      {
         super(375,315);
         this.setTitleImageData(UIManager.getUISkin("blackShopSetTitle").bitmapData,-25);
         this.initComp();
      }
      
      private function initComp() : void
      {
         var _loc5_:Sprite = null;
         var _loc1_:String = null;
         var _loc2_:TextFormat = null;
         var _loc4_:String = null;
         var _loc3_:TextFormat = null;
         this.curbg = UIManager.getUISkin("group_bg");
         this.curbg.width = 350;
         this.curbg.height = 150;
         addChild(this.curbg);
         this.curbg.x = 10;
         this.curbg.y = 33;
         this.scrollPane = new ScrollPane(350,120);
         this.scrollPane.x = 5;
         this.scrollPane.y = 40;
         addChild(this.scrollPane);
         _loc5_ = new Sprite();
         _loc5_.x = 0;
         _loc5_.y = 0;
         this.scrollPane.addToPane(_loc5_);
         _loc1_ = XmlManager.worldBoatHelp.desc.attribute("str");
         _loc2_ = new TextFormat("Verdana",12,16772489);
         _loc2_.leading = 5;
         this.tipsLb = new Label("",_loc2_);
         this.tipsLb.htmlText = decodeURIComponent(_loc1_.replace(/\\n/g,"\n"));
         this.tipsLb.x = 20;
         this.tipsLb.y = 0;
         this.tipsLb.width = 315;
         this.tipsLb.multiline = true;
         this.tipsLb.wordWrap = true;
         _loc5_.addChild(this.tipsLb);
         this.selectBtn = new CheckBox(Globalization.getString("blackShop.29"),TextFormatLib.format_0xFF0000_12px);
         this.selectBtn.x = 105;
         this.selectBtn.y = 195;
         addChild(this.selectBtn);
         this.selectBtn.addEventListener(ButtonEvent.Button_Update,this.selectHandler);
         _loc4_ = XmlManager.blackShopXml.children().(@id == 1).attribute("description");
         _loc3_ = new TextFormat("Verdana",12,65280);
         this.warningLb = new Label("",_loc3_);
         this.warningLb.htmlText = decodeURIComponent(_loc4_.replace(/\\n/g,"\n"));
         this.warningLb.width = 300;
         this.warningLb.x = 45;
         this.warningLb.y = 215;
         addChild(this.warningLb);
         this.confirmBtn = new Button(Globalization.getString("Gl.161"),null,70);
         this.confirmBtn.x = 105;
         this.confirmBtn.y = 265;
         addChild(this.confirmBtn);
         this.confirmBtn.addEventListener("click",this.confirmHandler);
         this.cancelBtn = new Button(Globalization.getString("Globalization.38"),null,70);
         this.cancelBtn.x = this.confirmBtn.x + 95;
         this.cancelBtn.y = this.confirmBtn.y;
         addChild(this.cancelBtn);
         this.cancelBtn.addEventListener("click",this.cancelHandler);
      }
      
      public function setCheckButton(param1:Boolean) : void
      {
         if(param1)
         {
            this.selectBtn.isCheck = true;
         }
         else
         {
            this.selectBtn.isCheck = false;
         }
      }
      
      private function cancelHandler(param1:MouseEvent) : void
      {
         PopUpCenter.removePopUp("BlackShopSetWin");
      }
      
      private function confirmHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("CS_SET_BLACKSHOP_MC_SHOW",{"isOpen":this.selectBtn.isCheck});
         PopUpCenter.removePopUp("BlackShopSetWin");
      }
      
      private function selectHandler(param1:Event) : void
      {
         this.isSelectB = (param1.target as CheckBox).isCheck;
      }
      
      override public function get height() : Number
      {
         return 360;
      }
   }
}

