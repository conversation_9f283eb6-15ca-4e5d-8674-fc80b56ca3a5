package activity.view.mc.guildBattle
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.serviceChallenge.GuildChallengeState;
   import game.data.serviceChallenge.ServiceChallengeState;
   import game.manager.AssetManager;
   import game.manager.XmlManager;
   import game.modules.CommonProxy;
   import game.modules.activity.proxy.ActivityGuildProxy;
   import game.mvc.AppFacade;
   import mmo.Config;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.button.Button;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.openModule;
   import util.time.TimeManager;
   
   public class GuildBattleBtns extends Sprite
   {
      private var _ruleBtn:Button;
      
      private var _worshipBtn:Button;
      
      private var _viewGainBtn:Button;
      
      private var _applyBtn:Button;
      
      private var _joinActivityBtn:Button;
      
      private var _battleMemberBtn:Button;
      
      private var _btnsArr:Array = [];
      
      private var _halo:MovieClip;
      
      public function GuildBattleBtns()
      {
         super();
         this._ruleBtn = new Button(Globalization.getString("ServiceChallenge.36"),null,90);
         this.addChild(this._ruleBtn);
         this._ruleBtn.setToolTip(Globalization.getString("ServiceChallenge.74"));
         this._worshipBtn = new Button(Globalization.getString("ServiceChallenge.39"),null,90);
         this._btnsArr.push(this._worshipBtn);
         this._worshipBtn.setToolTip(Globalization.getString("ServiceChallenge.76"));
         this._applyBtn = new Button(Globalization.getString("ServiceChallenge.41"),null,90);
         this._btnsArr.push(this._applyBtn);
         this._viewGainBtn = new Button(Globalization.getString("ServiceChallenge.42"),null,90);
         this._btnsArr.push(this._viewGainBtn);
         this._viewGainBtn.setToolTip(Globalization.getString("ServiceChallenge.78"));
         this._joinActivityBtn = new Button(Globalization.getString("activity.10"),null,90);
         this._btnsArr.push(this._joinActivityBtn);
         this._joinActivityBtn.setToolTip(Globalization.getString("ServiceChallenge.77"));
         this._battleMemberBtn = new Button(Globalization.getString("guildChallenge.2"),null,90);
         this._btnsArr.push(this._battleMemberBtn);
         this._battleMemberBtn.setToolTip(Globalization.getString("guildChallenge.3"));
         this._halo = AssetManager.getMc("HaloTips");
         this._halo.gotoAndStop(1);
         this._halo.mouseChildren = false;
         this._halo.mouseEnabled = false;
         this._btnsArr.push(this._halo);
         this.addEventListener("click",this.clickHandler);
      }
      
      private function clickHandler(param1:MouseEvent) : void
      {
         var _loc2_:int = 0;
         var _loc9_:XML = null;
         var _loc10_:String = null;
         var _loc6_:ActivityGuildProxy = null;
         var _loc8_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         var _loc3_:Number = NaN;
         var _loc7_:Number = NaN;
         _loc2_ = MainData.getInstance().guildChallengeData.id;
         _loc9_ = XmlManager.getXml("guildConquest").children().(@id == _loc2_)[0];
         _loc10_ = GuildChallengeState.getCurWholeProgressState(_loc2_);
         switch(param1.target)
         {
            case this._ruleBtn:
               openModule("ChallageMatchPanel",true,null,true,true);
               break;
            case this._worshipBtn:
               GameScene.enterScene(34);
               break;
            case this._applyBtn:
               if(MainData.getInstance().userData.guild_id == 0)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("guildChallenge.8"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               _loc6_ = AppFacade.instance.retrieveProxy("game.modules.activity.proxy.ActivityGuildInfoProxy") as ActivityGuildProxy;
               if(!_loc6_.guildInfoIsBack)
               {
                  return;
               }
               _loc8_ = int(_loc9_.@needLv);
               _loc5_ = int(_loc9_.@needGuildMembers);
               _loc4_ = int(_loc9_.@needGroupRank);
               if(!_loc6_.isPresent && !_loc6_.isDeputyPresent)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("guildChallenge.7"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               if(_loc6_.myGuildLevel < _loc8_)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":StringUtil.substitute(Globalization.getString("guildChallenge.4"),_loc8_),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               if(_loc6_.myGuildMembers < _loc5_)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":StringUtil.substitute(Globalization.getString("guildChallenge.5"),_loc5_),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               if(_loc6_.myGuildGroupRank > _loc4_)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":StringUtil.substitute(Globalization.getString("guildChallenge.6"),_loc4_),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               AppFacade.instance.sendNotification("CS_GUILDCHALLENGE_APPLY");
               break;
            case this._viewGainBtn:
               if(!MainData.getInstance().guildChallengeData.isSigned)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("ServiceChallenge.45"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               openModule("ChallengeGuildGainsWindow",true,null,true,true);
               break;
            case this._joinActivityBtn:
               if(_loc10_ == "guildChallenge_select")
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("guildChallenge.32"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
               }
               else
               {
                  GameScene.enterScene(33,_loc2_);
               }
               break;
            case this._battleMemberBtn:
               _loc3_ = MainData.getInstance().guildChallengeData.sign_time;
               _loc7_ = TimeManager.getInstance().getTime();
               if(_loc7_ - _loc3_ < 300000)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("guildChallenge.45"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               if(_loc10_ == "guildChallenge_select")
               {
                  AppFacade.instance.sendNotification("CS_GUILDSERVICEWAR_GETGUILDWARINFO",MainData.getInstance().userData.guild_id);
               }
               else
               {
                  openModule("ModuleGuildBattleMember",true,true,true,true);
               }
               break;
         }
      }
      
      private function setBtnStatus(param1:Boolean, param2:Button, param3:String, param4:Number, param5:Boolean) : void
      {
         if(param1)
         {
            param2.enabled = true;
            param2.x = param4;
            this.addChild(param2);
         }
         else
         {
            param2.enabled = false;
            if(param5)
            {
               param2.x = param4;
               this.addChild(param2);
            }
            else
            {
               param2.parent && param2.parent.removeChild(param2);
            }
         }
         param2.text = param3;
         if(param2 == this._battleMemberBtn)
         {
            this.addChild(this._halo);
            this._halo.gotoAndPlay(1);
            this._halo.x = this._battleMemberBtn.x;
         }
      }
      
      private function showBtns(param1:int) : void
      {
         var _loc3_:XML = null;
         var _loc5_:String = null;
         var _loc2_:CommonProxy = null;
         var _loc4_:int = 0;
         _loc3_ = XmlManager.getXml("guildConquest").children().(@id == param1)[0];
         if(!_loc3_)
         {
            return;
         }
         _loc5_ = GuildChallengeState.getCurWholeProgressState(param1);
         if(_loc5_ == "guildChallenge_unstart")
         {
            this.setBtnStatus(false,this._applyBtn,Globalization.getString("ServiceChallenge.41"),this._ruleBtn.x + this._ruleBtn.width,true);
            return;
         }
         if(_loc5_ == "guildChallenge_sign" || _loc5_ == "guildChallenge_between_signAndSelect")
         {
            if(MainData.getInstance().guildChallengeData.isSigned)
            {
               this.setBtnStatus(false,this._applyBtn,Globalization.getString("ServiceChallenge.47"),this._ruleBtn.x + this._ruleBtn.width,true);
               this.setBtnStatus(true,this._battleMemberBtn,this._battleMemberBtn.text,this._applyBtn.x + this._applyBtn.width,true);
            }
            else if(_loc5_ == "guildChallenge_sign")
            {
               this.setBtnStatus(true,this._applyBtn,Globalization.getString("ServiceChallenge.41"),this._ruleBtn.x + this._ruleBtn.width,true);
            }
            else
            {
               this.setBtnStatus(false,this._applyBtn,Globalization.getString("ServiceChallenge.48"),this._ruleBtn.x + this._ruleBtn.width,true);
            }
            return;
         }
         if(_loc5_ == "guildChallenge_select")
         {
            if(MainData.getInstance().guildChallengeData.isSigned)
            {
               this.setBtnStatus(true,this._viewGainBtn,this._viewGainBtn.text,this._ruleBtn.x + this._ruleBtn.width,true);
               _loc2_ = AppFacade.instance.retrieveProxy("game.modules.CommonProxy") as CommonProxy;
               _loc4_ = int(_loc3_.@last_loseNum);
               if(_loc2_.guildWarData.lose_times < _loc4_)
               {
                  this.setBtnStatus(true,this._battleMemberBtn,this._battleMemberBtn.text,this._viewGainBtn.x + this._viewGainBtn.width,true);
               }
            }
            else
            {
               this.setBtnStatus(true,this._joinActivityBtn,this._joinActivityBtn.text,this._ruleBtn.x + this._ruleBtn.width,true);
            }
            return;
         }
         if(_loc5_ == "guildChallenge_promotion")
         {
            this.setBtnStatus(true,this._joinActivityBtn,this._joinActivityBtn.text,this._ruleBtn.x + this._ruleBtn.width,true);
            return;
         }
      }
      
      private function showChampionBtn(param1:int) : void
      {
         var _loc6_:Boolean = false;
         var _loc5_:XML = null;
         var _loc7_:Array = null;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc5_ = XmlManager.getXml("guildConquest").children().(@id == param1)[0];
         if(_loc5_ == null)
         {
            _loc6_ = false;
         }
         else
         {
            _loc7_ = _loc5_.@serverId == "" ? [] : <EMAIL>(",");
            _loc4_ = int(_loc7_.length);
            _loc3_ = 0;
            while(_loc3_ < _loc4_)
            {
               _loc7_[_loc3_] = int(_loc7_[_loc3_]);
               _loc3_++;
            }
            _loc2_ = !!Config.serverID ? int(Config.serverID.replace("game","")) : 0;
            if(_loc4_ == 0 || _loc7_.indexOf(_loc2_) != -1)
            {
               _loc6_ = true;
            }
            else
            {
               _loc6_ = false;
            }
         }
         if(_loc6_)
         {
            this.addChild(this._worshipBtn);
            this._ruleBtn.x = this._worshipBtn.x + this._worshipBtn.width;
         }
         else
         {
            this._ruleBtn.x = 0;
         }
      }
      
      public function updateBtns(param1:int) : void
      {
         var _loc5_:XML = null;
         var _loc2_:String = null;
         var _loc3_:String = null;
         var _loc6_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc7_:int = 0;
         this.removeBtns();
         _loc5_ = XmlManager.getXml("guildConquest").children().(@id == param1)[0];
         if(_loc5_ == null)
         {
            return;
         }
         _loc2_ = _loc5_.@promoted_time.split(",")[4];
         _loc3_ = _loc2_.split("|")[1];
         _loc6_ = ServiceChallengeState.parseStringToTime(_loc3_).time;
         _loc4_ = TimeManager.getInstance().getTime();
         _loc7_ = int(_loc5_.@viewId);
         if(_loc4_ > _loc6_)
         {
            this.showChampionBtn(param1);
         }
         this.showBtns(param1);
      }
      
      public function updateApplyBtnState() : void
      {
         this.setBtnStatus(false,this._applyBtn,Globalization.getString("ServiceChallenge.47"),this._ruleBtn.x + this._ruleBtn.width,true);
         this.setBtnStatus(true,this._battleMemberBtn,this._battleMemberBtn.text,this._applyBtn.x + this._applyBtn.width,true);
      }
      
      public function updateBattleMemberBtn() : void
      {
         var _loc1_:int = 0;
         var _loc3_:XML = null;
         var _loc2_:CommonProxy = null;
         var _loc4_:int = 0;
         _loc1_ = MainData.getInstance().guildChallengeData.id;
         _loc3_ = XmlManager.getXml("guildConquest").children().(@id == _loc1_)[0];
         _loc2_ = AppFacade.instance.retrieveProxy("game.modules.CommonProxy") as CommonProxy;
         _loc4_ = int(_loc3_.@last_loseNum);
         if(_loc2_.guildWarData.lose_times < _loc4_)
         {
            openModule("ModuleGuildBattleMember",true,true,true,true);
         }
         else
         {
            this._halo.stop();
            this._halo.parent && this._halo.parent.removeChild(this._halo);
            this._battleMemberBtn.parent && this._battleMemberBtn.parent.removeChild(this._battleMemberBtn);
         }
      }
      
      public function removeBtns() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(this._btnsArr.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            this._btnsArr[_loc2_].parent && this._btnsArr[_loc2_].parent.removeChild(this._btnsArr[_loc2_]);
            if(this._btnsArr[_loc2_] == this._halo)
            {
               this._btnsArr[_loc2_].gotoAndStop(1);
            }
            _loc2_++;
         }
      }
   }
}

