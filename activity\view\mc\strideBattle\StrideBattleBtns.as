package activity.view.mc.strideBattle
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.serviceChallenge.ServiceChallengeState;
   import game.manager.AssetManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import mmo.Config;
   import mmo.Core;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.Button;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.time.TimeManager;
   
   public class StrideBattleBtns extends Sprite
   {
      private var _ruleBtn:Button;
      
      private var _updateFightInfoBtn:Button;
      
      private var _worshipBtn:Button;
      
      private var _viewMyGainBtn:Button;
      
      private var _viewGainBtn:Button;
      
      private var _applyBtn:Button;
      
      private var _joinActivityBtn:Button;
      
      private var _btnsArr:Array = [];
      
      private var _applyLv:int = 0;
      
      private var _halo:MovieClip;
      
      public var getPrize:Function;
      
      public function StrideBattleBtns()
      {
         super();
         this._ruleBtn = new Button(Globalization.getString("ServiceChallenge.36"),null,90);
         this.addChild(this._ruleBtn);
         this._ruleBtn.setToolTip(Globalization.getString("ServiceChallenge.74"));
         this._updateFightInfoBtn = new Button(Globalization.getString("ServiceChallenge.37"),null,90);
         this._btnsArr.push(this._updateFightInfoBtn);
         this._updateFightInfoBtn.setToolTip(Globalization.getString("ServiceChallenge.38"));
         this._worshipBtn = new Button(Globalization.getString("ServiceChallenge.39"),null,90);
         this._btnsArr.push(this._worshipBtn);
         this._worshipBtn.setToolTip(Globalization.getString("ServiceChallenge.76"));
         this._applyBtn = new Button(Globalization.getString("ServiceChallenge.41"),null,90);
         this._btnsArr.push(this._applyBtn);
         this._viewMyGainBtn = new Button(Globalization.getString("ServiceChallenge.86"),null,90);
         this._btnsArr.push(this._viewMyGainBtn);
         this._viewMyGainBtn.setToolTip(Globalization.getString("ServiceChallenge.85"));
         this._viewGainBtn = new Button(Globalization.getString("ServiceChallenge.42"),null,90);
         this._btnsArr.push(this._viewGainBtn);
         this._viewGainBtn.setToolTip(Globalization.getString("ServiceChallenge.78"));
         this._joinActivityBtn = new Button(Globalization.getString("activity.10"),null,90);
         this._btnsArr.push(this._joinActivityBtn);
         this._joinActivityBtn.setToolTip(Globalization.getString("ServiceChallenge.77"));
         this._halo = AssetManager.getMc("HaloTips");
         this._halo.gotoAndStop(1);
         this._halo.mouseChildren = false;
         this._halo.mouseEnabled = false;
         this._btnsArr.push(this._halo);
         this.addEventListener("click",this.clickHandler);
      }
      
      private function clickHandler(param1:MouseEvent) : void
      {
         var event:MouseEvent = param1;
         var param:ModuleParams = null;
         var periodId:int = 0;
         var xml:XML = null;
         var curState:String = null;
         var userLv:int = 0;
         var openModule:Function = function():void
         {
            if(param == null)
            {
               return;
            }
            param.isCenter = true;
            param.isModel = true;
            if(param)
            {
               if(PopUpCenter.containsWin(param.moduleName))
               {
                  param.action = ModuleParams.act_Close;
               }
               AppFacade.instance.sendNotification("HANDLE_MODULE",param);
            }
         };
         periodId = MainData.getInstance().serviceChallengeData.id;
         xml = XmlManager.getXml("conquest").children().(@id == 1)[0];
         curState = ServiceChallengeState.getCurWholeProgressState(1);
         switch(event.target)
         {
            case this._ruleBtn:
               param = new ModuleParams("MatchRule",ModuleParams.act_Open);
               openModule();
               break;
            case this._worshipBtn:
               GameScene.enterScene(26);
               break;
            case this._applyBtn:
               userLv = MainData.getInstance().groupData.roleModle.level;
               if(userLv < this._applyLv)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":StringUtil.substitute(Globalization.getString("ServiceChallenge.44"),this._applyLv),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               AppFacade.instance.sendNotification("CS_SERVICECHALLENGE_APPLY");
               break;
            case this._viewMyGainBtn:
               if(!MainData.getInstance().serviceChallengeData.isSigned)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("ServiceChallenge.45"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               if(curState == "serviceChallenge_select_inner")
               {
                  param = new ModuleParams("MatchMass",ModuleParams.act_Open);
               }
               else if(curState == "serviceChallenge_stride_select")
               {
                  param = new ModuleParams("MatchMyGain",ModuleParams.act_Open);
               }
               openModule();
               break;
            case this._joinActivityBtn:
            case this._viewGainBtn:
               GameScene.enterScene(25,periodId);
               break;
            case this._updateFightInfoBtn:
               if(!MainData.getInstance().serviceChallengeData.isSigned)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("ServiceChallenge.46"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
               }
               AppFacade.instance.sendNotification("CS_SERVICECHALLENGE_UPDATEUSERFIGHTINFO");
         }
      }
      
      private function setBtnStatus(param1:Boolean, param2:Button, param3:String, param4:Number, param5:Boolean) : void
      {
         if(param1)
         {
            param2.enabled = true;
            param2.x = param4;
            this.addChild(param2);
         }
         else
         {
            param2.enabled = false;
            if(param5)
            {
               param2.x = param4;
               this.addChild(param2);
            }
            else
            {
               param2.parent && param2.parent.removeChild(param2);
            }
         }
         param2.text = param3;
         if(param2 == this._updateFightInfoBtn)
         {
            this.addChild(this._halo);
            this._halo.gotoAndPlay(1);
            this._halo.x = this._updateFightInfoBtn.x;
         }
      }
      
      private function showActivityBtn(param1:int) : void
      {
         var _loc2_:XML = null;
         var _loc3_:String = null;
         _loc2_ = XmlManager.getXml("conquest").children().(@id == 1)[0];
         if(!_loc2_)
         {
            return;
         }
         this._applyLv = int(_loc2_.@needLv);
         _loc3_ = ServiceChallengeState.getCurWholeProgressState(1);
         if(_loc3_ == "serviceChallenge_unstart")
         {
            this.setBtnStatus(false,this._applyBtn,Globalization.getString("ServiceChallenge.41"),this._ruleBtn.x + this._ruleBtn.width,true);
            return;
         }
         if(_loc3_ == "serviceChallenge_sign")
         {
            if(MainData.getInstance().serviceChallengeData.isSigned)
            {
               this.setBtnStatus(false,this._applyBtn,Globalization.getString("ServiceChallenge.47"),this._ruleBtn.x + this._ruleBtn.width,true);
               this.setBtnStatus(true,this._updateFightInfoBtn,this._updateFightInfoBtn.text,this._applyBtn.x + this._applyBtn.width,true);
               return;
            }
            this.setBtnStatus(true,this._applyBtn,Globalization.getString("ServiceChallenge.41"),this._ruleBtn.x + this._ruleBtn.width,true);
            this._applyBtn.setToolTip(StringUtil.substitute(Globalization.getString("ServiceChallenge.73"),this._applyLv));
            return;
         }
         if(_loc3_ == "serviceChallenge_between_signAndSelect")
         {
            if(MainData.getInstance().serviceChallengeData.isSigned)
            {
               this.setBtnStatus(false,this._applyBtn,Globalization.getString("ServiceChallenge.47"),this._ruleBtn.x + this._ruleBtn.width,true);
               this.setBtnStatus(true,this._updateFightInfoBtn,this._updateFightInfoBtn.text,this._applyBtn.x + this._applyBtn.width,true);
            }
            else
            {
               this.setBtnStatus(false,this._applyBtn,Globalization.getString("ServiceChallenge.48"),this._ruleBtn.x + this._ruleBtn.width,true);
            }
            return;
         }
         if(_loc3_ == "serviceChallenge_select_inner")
         {
            this.setBtnStatus(true,this._viewMyGainBtn,this._viewMyGainBtn.text,this._ruleBtn.x + this._ruleBtn.width,true);
            if(MainData.getInstance().serviceChallengeData.isSigned)
            {
               this.setBtnStatus(true,this._updateFightInfoBtn,this._updateFightInfoBtn.text,this._viewMyGainBtn.x + this._viewMyGainBtn.width,true);
            }
            return;
         }
         if(_loc3_ == "serviceChallenge_promotion_inner")
         {
            this.setBtnStatus(true,this._joinActivityBtn,this._joinActivityBtn.text,this._ruleBtn.x + this._ruleBtn.width,true);
            return;
         }
         if(_loc3_ == "serviceChallenge_innerover_between_stride")
         {
            this.setBtnStatus(true,this._viewGainBtn,this._viewGainBtn.text,this._ruleBtn.x + this._ruleBtn.width,true);
            if(MainData.getInstance().serviceChallengeData.isSigned)
            {
               this.setBtnStatus(true,this._updateFightInfoBtn,this._updateFightInfoBtn.text,this._viewGainBtn.x + this._viewGainBtn.width,true);
            }
            return;
         }
         if(_loc3_ == "serviceChallenge_stride_select")
         {
            this.setBtnStatus(true,this._viewMyGainBtn,this._viewMyGainBtn.text,this._ruleBtn.x + this._ruleBtn.width,true);
            if(MainData.getInstance().serviceChallengeData.isSigned)
            {
               this.setBtnStatus(true,this._updateFightInfoBtn,this._updateFightInfoBtn.text,this._viewMyGainBtn.x + this._viewMyGainBtn.width,true);
            }
            return;
         }
         if(_loc3_ == "serviceChallenge_promotion_stride")
         {
            this.setBtnStatus(true,this._joinActivityBtn,this._joinActivityBtn.text,this._ruleBtn.x + this._ruleBtn.width,true);
            return;
         }
      }
      
      private function showChampionBtn(param1:int) : void
      {
         var _loc6_:Boolean = false;
         var _loc5_:XML = null;
         var _loc7_:Array = null;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc5_ = XmlManager.getXml("conquest").children().(@id == 1)[0];
         if(_loc5_ == null)
         {
            _loc6_ = false;
         }
         else
         {
            if(param1 == 0)
            {
               _loc6_ = false;
            }
            else
            {
               _loc6_ = true;
            }
            _loc7_ = _loc5_.@serverId == "" ? [] : <EMAIL>(",");
            _loc4_ = int(_loc7_.length);
            _loc3_ = 0;
            while(_loc3_ < _loc4_)
            {
               _loc7_[_loc3_] = int(_loc7_[_loc3_]);
               _loc3_++;
            }
            _loc2_ = !!Config.serverID ? int(Config.serverID.replace("game","")) : 0;
         }
         if(_loc6_)
         {
            this.addChild(this._worshipBtn);
            this._ruleBtn.x = this._worshipBtn.x + this._worshipBtn.width;
         }
         else
         {
            this._ruleBtn.x = 0;
         }
      }
      
      public function updateApplyBtnState() : void
      {
         this.setBtnStatus(false,this._applyBtn,Globalization.getString("ServiceChallenge.47"),this._ruleBtn.x + this._ruleBtn.width,true);
         this.setBtnStatus(true,this._updateFightInfoBtn,this._updateFightInfoBtn.text,this._applyBtn.x + this._applyBtn.width,true);
      }
      
      public function updateBtns(param1:int) : void
      {
         var _loc11_:int = 0;
         var _loc4_:Array = Core.autoactive.data;
         var _loc3_:String = "0";
         var _loc5_:String = "0";
         var _loc12_:String = "";
         _loc11_ = 0;
         while(_loc11_ < _loc4_.length)
         {
            if(_loc4_[_loc11_].hasOwnProperty("a_name") && _loc4_[_loc11_].a_name == "king_war")
            {
               _loc3_ = _loc4_[_loc11_].stime;
               _loc5_ = _loc4_[_loc11_].etime;
               _loc12_ = _loc4_[_loc11_].note;
               param1 = int(_loc12_.split("|")[1]);
               MainData.getInstance().serviceChallengeData.id = param1;
               break;
            }
            _loc11_++;
         }
         var _loc8_:XML = null;
         var _loc2_:String = null;
         var _loc6_:String = null;
         var _loc9_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc10_:int = 0;
         this.removeBtns();
         _loc8_ = XmlManager.getXml("conquest").children().(@id == 1)[0];
         if(_loc8_ == null)
         {
            return;
         }
         _loc2_ = _loc8_.@last_time.split(",")[5];
         _loc6_ = _loc2_.split("|")[1];
         _loc9_ = ServiceChallengeState.parseStringToTime(_loc6_).time;
         _loc7_ = TimeManager.getInstance().getTime();
         _loc10_ = int(_loc8_.@viewId);
         if(_loc7_ > _loc9_)
         {
            this.showChampionBtn(1);
         }
         else
         {
            this.showChampionBtn(0);
         }
         this.showActivityBtn(param1);
      }
      
      public function removeBtns() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(this._btnsArr.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            this._btnsArr[_loc2_].parent && this._btnsArr[_loc2_].parent.removeChild(this._btnsArr[_loc2_]);
            if(this._btnsArr[_loc2_] == this._halo)
            {
               this._btnsArr[_loc2_].gotoAndStop(1);
            }
            _loc2_++;
         }
      }
   }
}

