package activity.view.mc.teamBattle
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.serviceChallenge.ServiceChallengeState;
   import game.data.serviceChallenge.TeamChallengeState;
   import game.manager.AssetManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import mmo.Config;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.button.Button;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.openModule;
   import util.time.TimeManager;
   
   public class TeamBattleBtns extends Sprite
   {
      private var _ruleBtn:Button;
      
      private var _updateFightInfoBtn:Button;
      
      private var _worshipBtn:Button;
      
      private var _createBtn:Button;
      
      private var _dissolveBtn:Button;
      
      private var _joinActivityBtn:Button;
      
      private var _btnsArr:Array = [];
      
      private var _createLv:int = 0;
      
      private var _halo:MovieClip;
      
      public var getPrize:Function;
      
      public function TeamBattleBtns()
      {
         super();
         this._ruleBtn = new Button(Globalization.getString("ServiceChallenge.36"),null,90);
         this.addChild(this._ruleBtn);
         this._ruleBtn.setToolTip(Globalization.getString("ServiceChallenge.74"));
         this._updateFightInfoBtn = new Button(Globalization.getString("ServiceChallenge.37"),null,90);
         this._btnsArr.push(this._updateFightInfoBtn);
         this._updateFightInfoBtn.setToolTip(Globalization.getString("ServiceChallenge.38"));
         this._worshipBtn = new Button(Globalization.getString("ServiceChallenge.39"),null,90);
         this._btnsArr.push(this._worshipBtn);
         this._worshipBtn.setToolTip(Globalization.getString("ServiceChallenge.76"));
         this._createBtn = new Button(Globalization.getString("ActiTeamChallenge.1"),null,90);
         this._btnsArr.push(this._createBtn);
         this._dissolveBtn = new Button(Globalization.getString("ActiTeamChallenge.2"),null,90);
         this._btnsArr.push(this._dissolveBtn);
         this._joinActivityBtn = new Button(Globalization.getString("activity.10"),null,90);
         this._btnsArr.push(this._joinActivityBtn);
         this._joinActivityBtn.setToolTip(Globalization.getString("ServiceChallenge.77"));
         this._halo = AssetManager.getMc("HaloTips");
         this._halo.gotoAndStop(1);
         this._halo.mouseChildren = false;
         this._halo.mouseEnabled = false;
         this._btnsArr.push(this._halo);
         this.addEventListener("click",this.clickHandler);
      }
      
      private function clickHandler(param1:MouseEvent) : void
      {
         var _loc3_:ModuleParams = null;
         var _loc4_:int = 0;
         var _loc2_:int = MainData.getInstance().teamChallengeData.id;
         var _loc5_:String = TeamChallengeState.getCurWholeProgressState(_loc2_);
         switch(param1.target)
         {
            case this._ruleBtn:
               openModule("TeamChallengeRulePanel",true,null,false,true);
               break;
            case this._worshipBtn:
               GameScene.enterScene(42);
               break;
            case this._createBtn:
               _loc4_ = MainData.getInstance().groupData.roleModle.level;
               if(_loc4_ < this._createLv)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":StringUtil.substitute(Globalization.getString("ActiTeamChallenge.4"),this._createLv),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               if(_loc5_ != "teamChallenge_sign")
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("ActiTeamChallenge.14"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  this.showActivityBtn(_loc2_);
                  return;
               }
               openModule("CrossServerTeamCompModule");
               break;
            case this._dissolveBtn:
               if(!MainData.getInstance().teamChallengeData.isJoin)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("ActiTeamChallenge.9"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               if(_loc5_ != "teamChallenge_sign")
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("ActiTeamChallenge.15"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  this.showActivityBtn(_loc2_);
                  return;
               }
               AppFacade.instance.sendNotification("CS_TEAM_CHALLENGE_DISMISS");
               break;
            case this._joinActivityBtn:
               if(_loc5_ == "teamChallenge_promotion")
               {
                  GameScene.enterScene(41);
                  return;
               }
               GameScene.enterScene(40);
               break;
            case this._updateFightInfoBtn:
               if(!MainData.getInstance().teamChallengeData.isJoin)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("ActiTeamChallenge.3"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               openModule("CrossServerDeployCompModule");
               break;
         }
      }
      
      private function setBtnStatus(param1:Boolean, param2:Button, param3:Number, param4:Boolean) : void
      {
         if(param1)
         {
            param2.enabled = true;
            param2.x = param3;
            this.addChild(param2);
         }
         else
         {
            param2.enabled = false;
            if(param4)
            {
               param2.x = param3;
               this.addChild(param2);
            }
            else
            {
               param2.parent && param2.parent.removeChild(param2);
            }
         }
         if(param2 == this._updateFightInfoBtn)
         {
            this.addChild(this._halo);
            this._halo.gotoAndPlay(1);
            this._halo.x = this._updateFightInfoBtn.x;
         }
      }
      
      private function showActivityBtn(param1:int) : void
      {
         var _loc2_:XML = null;
         var _loc3_:String = null;
         _loc2_ = XmlManager.getXml("teamConquest").children().(@id == param1)[0];
         if(!_loc2_)
         {
            return;
         }
         this._createLv = int(_loc2_.@level);
         _loc3_ = TeamChallengeState.getCurWholeProgressState(param1);
         if(_loc3_ == "teamChallenge_unstart")
         {
            this.setBtnStatus(false,this._createBtn,this._ruleBtn.x + this._ruleBtn.width,true);
            return;
         }
         if(_loc3_ == "teamChallenge_sign")
         {
            if(MainData.getInstance().teamChallengeData.isJoin)
            {
               this.setBtnStatus(true,this._dissolveBtn,this._ruleBtn.x + this._ruleBtn.width,true);
               this.setBtnStatus(true,this._updateFightInfoBtn,this._dissolveBtn.x + this._dissolveBtn.width,true);
            }
            else
            {
               this.setBtnStatus(true,this._createBtn,this._ruleBtn.x + this._ruleBtn.width,true);
               this._createBtn.setToolTip(StringUtil.substitute(Globalization.getString("ActiTeamChallenge.5"),this._createLv));
            }
            return;
         }
         if(_loc3_ == "teamChallenge_between_signAndSelect" || _loc3_ == "teamChallenge_select")
         {
            this.setBtnStatus(true,this._joinActivityBtn,this._ruleBtn.x + this._ruleBtn.width,true);
            this._joinActivityBtn.setToolTip(Globalization.getString("ActiTeamChallenge.8"));
            return;
         }
         if(_loc3_ == "teamChallenge_promotion")
         {
            this.setBtnStatus(true,this._joinActivityBtn,this._ruleBtn.x + this._ruleBtn.width,true);
            this._joinActivityBtn.setToolTip(Globalization.getString("ServiceChallenge.77"));
            return;
         }
      }
      
      private function showChampionBtn(param1:int) : void
      {
         var _loc6_:Boolean = false;
         var _loc5_:XML = null;
         var _loc7_:Array = null;
         var _loc4_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         _loc5_ = XmlManager.getXml("teamConquest").children().(@id == param1)[0];
         if(_loc5_ == null)
         {
            _loc6_ = false;
         }
         else
         {
            _loc7_ = _loc5_.@serverId == "" ? [] : <EMAIL>(",");
            _loc4_ = int(_loc7_.length);
            _loc3_ = 0;
            while(_loc3_ < _loc4_)
            {
               _loc7_[_loc3_] = int(_loc7_[_loc3_]);
               _loc3_++;
            }
            _loc2_ = !!Config.serverID ? int(Config.serverID.replace("game","")) : 0;
            if(_loc4_ == 0 || _loc7_.indexOf(_loc2_) != -1)
            {
               _loc6_ = true;
            }
            else
            {
               _loc6_ = false;
            }
         }
         if(_loc6_)
         {
            this.addChild(this._worshipBtn);
            this._ruleBtn.x = this._worshipBtn.x + this._worshipBtn.width;
         }
         else
         {
            this._ruleBtn.x = 0;
         }
      }
      
      public function updateBtns(param1:int) : void
      {
         var _loc5_:XML = null;
         var _loc2_:String = null;
         var _loc3_:String = null;
         var _loc6_:Number = NaN;
         var _loc4_:Number = NaN;
         this.removeBtns();
         _loc5_ = XmlManager.getXml("teamConquest").children().(@id == param1)[0];
         if(_loc5_ == null)
         {
            return;
         }
         _loc2_ = _loc5_.@promoted_time.split(",")[3];
         _loc3_ = _loc2_.split("|")[1];
         _loc6_ = ServiceChallengeState.parseStringToTime(_loc3_).time;
         _loc4_ = TimeManager.getInstance().getTime();
         if(_loc4_ > _loc6_)
         {
            this.showChampionBtn(param1);
         }
         this.showActivityBtn(param1);
      }
      
      public function removeBtns() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(this._btnsArr.length);
         _loc2_ = 0;
         while(_loc2_ < _loc1_)
         {
            this._btnsArr[_loc2_].parent && this._btnsArr[_loc2_].parent.removeChild(this._btnsArr[_loc2_]);
            if(this._btnsArr[_loc2_] == this._halo)
            {
               this._btnsArr[_loc2_].gotoAndStop(1);
            }
            _loc2_++;
         }
      }
   }
}

