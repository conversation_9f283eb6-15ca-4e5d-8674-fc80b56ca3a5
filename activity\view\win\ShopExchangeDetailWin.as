package activity.view.win
{
   import activity.manager.ActivityXmlManager;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import game.manager.UIManager;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ButtonModel;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.input.TextInput;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class ShopExchangeDetailWin extends PopUpWindow
   {
      private var _itemSlot:Slot;
      
      private var _slotTemp:SlotTemplete;
      
      private var _itemName:Label;
      
      private var _point:Label;
      
      private var _numInput:TextInput;
      
      private var _reduceBtn:ImgButton;
      
      private var _addBtn:ImgButton;
      
      private var _costLable:Label;
      
      private var _costLableValue:Label;
      
      private var _okBtn:Button;
      
      private var _cancelBtn:Button;
      
      private var _myPoint:int;
      
      private var _unitPoint:int;
      
      private var _exchangeId:int;
      
      public var confirmExchange:Function;
      
      public function ShopExchangeDetailWin()
      {
         super(280,265);
         this.isLive = true;
         this.title = Globalization.getString("activity.98");
         var _loc4_:UISkin = UIManager.getUISkin("group_bg");
         _loc4_.x = 6;
         _loc4_.setSize(258,184);
         pane.addChild(_loc4_);
         this._itemSlot = new Slot();
         this._itemSlot.x = 30;
         this._itemSlot.y = 12;
         pane.addChild(this._itemSlot);
         this._slotTemp = new SlotTemplete();
         this._itemName = new Label("",TextFormatLib.format_verdana_0xffed89_12px);
         this._itemName.x = 90;
         this._itemName.y = 10;
         pane.addChild(this._itemName);
         this._point = new Label("",TextFormatLib.format_verdana_0xffed89_12px);
         this._point.x = 90;
         this._point.y = 30;
         pane.addChild(this._point);
         var _loc3_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc3_.x = 11;
         _loc3_.y = 65;
         _loc3_.setSize(245,108);
         pane.addChild(_loc3_);
         var _loc2_:Label = new Label(Globalization.getString("activity.99"));
         _loc2_.x = 30;
         _loc2_.y = 75;
         pane.addChild(_loc2_);
         this._numInput = new TextInput("",100);
         this._numInput.x = 40;
         this._numInput.y = 100;
         this._numInput.restrict = "0-9";
         pane.addChild(this._numInput);
         this._numInput.addEventListener("change",this.onChangeText);
         this._reduceBtn = new ImgButton(UIManager.getMultiUISkin("minus"));
         this._reduceBtn.x = 150;
         this._reduceBtn.y = 100;
         pane.addChild(this._reduceBtn);
         this._addBtn = new ImgButton(UIManager.getMultiUISkin("plus"));
         this._addBtn.x = 180;
         this._addBtn.y = 100;
         pane.addChild(this._addBtn);
         var _loc1_:UISkin = UIManager.getUISkin("key_value_bg");
         _loc1_.width = 165;
         _loc1_.x = 40;
         _loc1_.y = 136;
         pane.addChild(_loc1_);
         this._costLable = new Label("",TextFormatLib.format_0xffb932_12px);
         this._costLable.x = 40;
         this._costLable.y = 136;
         this._costLable.width = 66;
         this._costLable.autoSize = "center";
         pane.addChild(this._costLable);
         this._costLableValue = new Label("",TextFormatLib.format_verdana_0xffed89_12px);
         this._costLableValue.x = 110;
         this._costLableValue.y = 136;
         this._costLableValue.width = 100;
         this._costLableValue.autoSize = "center";
         pane.addChild(this._costLableValue);
         this._okBtn = new Button(Globalization.queding,null,78);
         this._okBtn.x = 48;
         this._okBtn.y = 190;
         pane.addChild(this._okBtn);
         this._cancelBtn = new Button(Globalization.quxiao,null,78);
         this._cancelBtn.x = 156;
         this._cancelBtn.y = 190;
         pane.addChild(this._cancelBtn);
         pane.addEventListener("click",this.onClickPanelHandler);
      }
      
      private function onChangeText(param1:Event) : void
      {
         var _loc2_:int = Math.floor(this._myPoint / this._unitPoint);
         if(_loc2_ == 0)
         {
            this._numInput.text = "1";
            return;
         }
         if(int(this._numInput.text) >= _loc2_)
         {
            this._numInput.text = String(_loc2_);
         }
         else
         {
            this._numInput.text = String(int(this._numInput.text));
         }
         this.updateCost(int(this._numInput.text));
      }
      
      private function onClickPanelHandler(param1:MouseEvent) : void
      {
         var _loc2_:ButtonModel = param1.target as ButtonModel;
         if(_loc2_ == null)
         {
            return;
         }
         var _loc4_:int = Math.floor(this._myPoint / this._unitPoint);
         var _loc3_:Point = this._itemSlot.localToGlobal(new Point(0,0));
         switch(_loc2_)
         {
            case this._addBtn:
               if(int(this._numInput.text) >= _loc4_)
               {
                  return;
               }
               this._numInput.text = String(int(this._numInput.text) + 1);
               this.updateCost(int(this._numInput.text));
               break;
            case this._reduceBtn:
               if(int(this._numInput.text) <= 1)
               {
                  return;
               }
               this._numInput.text = String(int(this._numInput.text) - 1);
               this.updateCost(int(this._numInput.text));
               break;
            case this._okBtn:
               if(int(this._numInput.text) <= 0)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("activity.90"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               this.confirmExchange && this.confirmExchange(this._exchangeId,_loc3_,int(this._numInput.text));
               break;
            case this._cancelBtn:
               this.close();
         }
      }
      
      private function updateCost(param1:int) : void
      {
         var _loc2_:int = param1 * this._unitPoint;
         this._costLableValue.text = String(_loc2_);
      }
      
      public function setExchangeData(param1:int, param2:int, param3:String) : void
      {
         this._myPoint = param2;
         this._exchangeId = param1;
         var _loc4_:Object = ActivityXmlManager.getExchangeInfoByExchangeId(param1,param3);
         this._unitPoint = _loc4_.integral;
         this._slotTemp.tempID = _loc4_.tempId;
         this._itemSlot.setItem(this._slotTemp,false,false,false,false);
         this._itemName.htmlText = MessageReceive.getItemColorName(_loc4_.tempId);
         this._numInput.text = "1";
         if(param3 == "honourShop")
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("activity.40"),param2);
            this._costLable.text = Globalization.getString("activity.91");
         }
         else if(param3 == "wgwHonourShop")
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("worldGroupWar.4"),param2);
            this._costLable.text = Globalization.getString("worldGroupWar.5");
         }
         else if(param3 == "cardshop")
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("activity.70"),param2);
            this._costLable.text = Globalization.getString("activity.92");
         }
         else if(param3 == "worldtree")
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("activity.89"),param2);
            this._costLable.text = Globalization.getString("activity.93");
         }
         else
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("activity.40"),param2);
            this._costLable.text = Globalization.getString("activity.91");
         }
         this._costLableValue.text = String(_loc4_.integral);
      }
      
      public function updatePoint(param1:String, param2:int) : void
      {
         this._numInput.text = "1";
         if(param1 == "honourShop")
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("activity.40"),param2);
         }
         else if(param1 == "wgwHonourShop")
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("worldGroupWar.4"),param2);
         }
         else if(param1 == "cardshop")
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("activity.70"),param2);
         }
         else if(param1 == "worldtree")
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("activity.89"),param2);
         }
         else
         {
            this._point.htmlText = StringUtil.substitute(Globalization.getString("activity.40"),param2);
         }
         this.updateCost(int(this._numInput.text));
      }
   }
}

