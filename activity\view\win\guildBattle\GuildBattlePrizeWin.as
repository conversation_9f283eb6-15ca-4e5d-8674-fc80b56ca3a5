package activity.view.win.guildBattle
{
   import activity.manager.ActivityXmlManager;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.modules.activity.utils.ActivityUtils;
   import game.modules.activity.view.mc.prize.PromotePrizeItem;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.tab.TabPane;
   import mmo.ui.control.window.PopUpWindow;
   import mmo.ui.event.TabEvent;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class GuildBattlePrizeWin extends PopUpWindow
   {
      public static const WIDTH:int = 646;
      
      public static const HEIGHT:int = 554;
      
      public static const NAME:String = "activity.view.win.guildBattle.GuildBattlePrizeWin";
      
      public static const PRIZE_MAX_NUM:int = 5;
      
      private var _prizeTab:TabPane;
      
      private var _prizeItemPane:ScrollPane;
      
      private var _prizeItemBox:UISprite;
      
      private var _prizeRankLabel:Label;
      
      private var _selectBg:UISkin;
      
      private var _prizeItemArr:Array;
      
      private var _getPrizeBtn:Button;
      
      public function GuildBattlePrizeWin()
      {
         var _loc7_:int = 0;
         var _loc2_:PromotePrizeItem = null;
         this._prizeItemArr = [];
         super(646,554);
         this.isLive = false;
         this.setTitleImageData(UIManager.getUISkin("stridePrizeTitle").bitmapData,-30);
         var _loc1_:UISkin = UIManager.getUISkin("tab_bg");
         _loc1_.width = 320;
         _loc1_.y = 6;
         pane.addChild(_loc1_);
         this._prizeTab = new TabPane([Globalization.getString("guildChallenge.27"),Globalization.getString("guildChallenge.28")],0,100);
         this._prizeTab.x = 3;
         this._prizeTab.y = 23;
         pane.addChild(this._prizeTab);
         this._prizeTab.addEventListener(TabEvent.Tab_IndexChange,this.onPrizeTabChange);
         var _loc9_:Label = new Label("",TextFormatLib.format_0xffb932_12px_verdana);
         _loc9_.htmlText = Globalization.getString("guildChallenge.29");
         _loc9_.x = 646 - _loc9_.width >> 1;
         _loc9_.y = 32;
         pane.addChild(_loc9_);
         var _loc5_:UISkin = UIManager.getUISkin("intro_small_bg");
         _loc5_.setSize(630,380);
         _loc5_.y = 64;
         pane.addChild(_loc5_);
         var _loc6_:UISkin = UIManager.getUISkin("split_vertical_short");
         _loc6_.x = 120;
         _loc6_.y = 68;
         pane.addChild(_loc6_);
         var _loc8_:Label = new Label(Globalization.getString("ServiceChallenge.51"),TextFormatLib.format_0xc9a359_12px,null,true);
         _loc8_.y = 69;
         _loc8_.width = 120;
         _loc8_.autoSize = "center";
         pane.addChild(_loc8_);
         var _loc4_:Label = new Label(Globalization.getString("ServiceChallenge.52"),TextFormatLib.format_0xc9a359_12px,null,true);
         _loc4_.x = 120;
         _loc4_.y = 69;
         _loc4_.width = 480;
         _loc4_.autoSize = "center";
         pane.addChild(_loc4_);
         this._selectBg = UIManager.getUISkin("select_mask");
         this._selectBg.setSize(570,86);
         this._prizeItemPane = new ScrollPane(612,336);
         this._prizeItemPane.x = 10;
         this._prizeItemPane.y = 98;
         pane.addChild(this._prizeItemPane);
         this._prizeItemBox = new UISprite();
         this._prizeItemPane.addToPane(this._prizeItemBox);
         var _loc3_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc3_.x = 112;
         _loc3_.y = 448;
         _loc3_.setSize(395,28);
         pane.addChild(_loc3_);
         this._prizeRankLabel = new Label("",TextFormatLib.format_verdana_0xffed89_12px,null,true);
         this._prizeRankLabel.x = 112;
         this._prizeRankLabel.y = 452;
         this._prizeRankLabel.width = 395;
         this._prizeRankLabel.autoSize = "center";
         pane.addChild(this._prizeRankLabel);
         _loc7_ = 0;
         while(_loc7_ < 5)
         {
            _loc2_ = new PromotePrizeItem(_loc7_ + 1);
            _loc2_.y = _loc7_ * 87;
            this._prizeItemBox.addChild(_loc2_);
            this._prizeItemArr.push(_loc2_);
            _loc7_++;
         }
         this._getPrizeBtn = new Button(Globalization.getString("Gl.77"),null,80,UIManager.getMultiUISkin("button_big"));
         this._getPrizeBtn.x = 646 - this._getPrizeBtn.width >> 1;
         this._getPrizeBtn.y = 482;
         pane.addChild(this._getPrizeBtn);
         this._getPrizeBtn.enabled = false;
         this._getPrizeBtn.addEventListener("click",this.onGetPrizeHandler);
         this.showHander = this.showHandler;
      }
      
      private function showHandler(param1:Object) : void
      {
         var _loc3_:int = 0;
         var _loc2_:int = MainData.getInstance().guildChallengeData.id;
         var _loc6_:Array = ActivityXmlManager.getGuildBattlePrizeIds(_loc2_,0);
         _loc6_.reverse();
         var _loc5_:Array = ActivityXmlManager.getGuildBattlePrizeIds(_loc2_,1);
         _loc5_.reverse();
         var _loc4_:int = MainData.getInstance().guildChallengeData.world_prize_id;
         var _loc7_:Boolean = MainData.getInstance().guildChallengeData.is_cur_send_prize_time;
         if(_loc6_.indexOf(String(_loc4_)) != -1 && _loc7_)
         {
            this._prizeTab.selectedIndex = 0;
            _loc3_ = int(_loc6_.indexOf(String(_loc4_)));
            this._prizeRankLabel.text = StringUtil.substitute(Globalization.getString("guildChallenge.31"),ActivityUtils.getPrizeDesc(_loc3_));
            return;
         }
         if(_loc5_.indexOf(String(_loc4_)) != -1 && _loc7_)
         {
            this._prizeTab.selectedIndex = 1;
            _loc3_ = int(_loc5_.indexOf(String(_loc4_)));
            this._prizeRankLabel.text = StringUtil.substitute(Globalization.getString("guildChallenge.31"),ActivityUtils.getPrizeDesc(_loc3_));
            return;
         }
         this._prizeTab.selectedIndex = 0;
         this._prizeRankLabel.text = Globalization.getString("guildChallenge.30");
      }
      
      private function onPrizeTabChange(param1:Event) : void
      {
         var _loc9_:int = 0;
         var _loc5_:PromotePrizeItem = null;
         var _loc4_:int = 0;
         var _loc3_:int = MainData.getInstance().guildChallengeData.id;
         var _loc2_:Array = ActivityXmlManager.getGuildBattlePrizeIds(_loc3_,this._prizeTab.selectedIndex);
         if(_loc2_ == null || _loc2_.length == 0)
         {
            return;
         }
         _loc2_.reverse();
         _loc9_ = 0;
         while(_loc9_ < 5)
         {
            _loc5_ = this._prizeItemArr[_loc9_];
            _loc5_.setPrize(_loc2_[_loc9_],5 - _loc9_ + 1);
            _loc9_++;
         }
         var _loc6_:int = MainData.getInstance().guildChallengeData.world_prize_id;
         var _loc7_:Boolean = MainData.getInstance().guildChallengeData.is_get_world_prize;
         var _loc8_:Boolean = MainData.getInstance().guildChallengeData.is_cur_send_prize_time;
         if(_loc2_.indexOf(String(_loc6_)) != -1 && _loc8_)
         {
            _loc4_ = int(_loc2_.indexOf(String(_loc6_)));
            this._prizeItemArr[_loc4_].addChildAt(this._selectBg,1);
            this._prizeItemPane.nowUpdateUI();
            this._prizeItemPane.scrollTo(_loc4_ / (this._prizeItemArr.length - 1));
            this._getPrizeBtn.enabled = !_loc7_;
            this._getPrizeBtn.text = _loc7_ ? Globalization.getString("openprize.9") : Globalization.getString("Gl.77");
         }
         else
         {
            this._selectBg.parent && this._selectBg.parent.removeChild(this._selectBg);
            this._prizeItemPane.scrollTo(0);
            this._getPrizeBtn.enabled = false;
            this._getPrizeBtn.text = Globalization.getString("Gl.77");
         }
      }
      
      private function onGetPrizeHandler(param1:MouseEvent) : void
      {
         var _loc5_:int = 0;
         var _loc3_:XML = null;
         var _loc2_:int = 0;
         var _loc4_:Array = null;
         _loc5_ = MainData.getInstance().guildChallengeData.world_prize_id;
         _loc3_ = XmlManager.getXml("leitai_jiangli").children().(@ID == _loc5_)[0];
         _loc2_ = MainData.getInstance().bagData.userBag.getLastGridsNum();
         if(_loc3_.@reward_items != "")
         {
            _loc4_ = _loc3_.@reward_items.split(",");
            if(_loc2_ < _loc4_.length)
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("valueBook.2"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
            AppFacade.instance.sendNotification("CS_GUILDCHALLENGE_GETPRIZE",_loc5_);
         }
         else
         {
            AppFacade.instance.sendNotification("CS_GUILDCHALLENGE_GETPRIZE",_loc5_);
         }
      }
      
      public function updatePrizeBtn() : void
      {
         this._getPrizeBtn.text = Globalization.getString("openprize.9");
         this._getPrizeBtn.enabled = false;
      }
      
      override public function get posHeight() : Number
      {
         return 554;
      }
   }
}

