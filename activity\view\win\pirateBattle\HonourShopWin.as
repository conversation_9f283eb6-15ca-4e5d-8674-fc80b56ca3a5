package activity.view.win.pirateBattle
{
   import activity.manager.ActivityXmlManager;
   import activity.view.mc.ShopExchangeUI;
   import activity.view.win.ShopExchangeDetailWin;
   import flash.geom.Point;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class HonourShopWin extends PopUpWindow
   {
      public static const WIDTH:int = 797;
      
      public static const NAME:String = "HonourShopWin";
      
      public static const EXCHANGE_WIN_NAME:String = "ShopExchangeWinName";
      
      private var _honourShopUI:ShopExchangeUI;
      
      private var _exchangeWin:ShopExchangeDetailWin;
      
      private var _curIntegral:Label;
      
      private var _myHonour:int;
      
      public function HonourShopWin()
      {
         super(797,533,UIManager.getUISkin("honourWinBg"));
         this.isLive = false;
         this.setTitleImageData(UIManager.getUISkin("honourTitle").bitmapData);
         this.updatePos();
         this._honourShopUI = new ShopExchangeUI("honourShop");
         pane.addChild(this._honourShopUI);
         this._honourShopUI.exchangeFun = this.exchangeHandler;
         this._exchangeWin = new ShopExchangeDetailWin();
         this._exchangeWin.confirmExchange = this.confirmExchangeHandler;
         this._curIntegral = new Label("",TextFormatLib.format_verdana_0xffed89_14px);
         this._curIntegral.y = 4;
         this._curIntegral.width = 605;
         this._curIntegral.autoSize = "center";
         pane.addChild(this._curIntegral);
         this.showHander = this.showHandler;
      }
      
      override protected function updatePos() : void
      {
         this.pane.y = 61;
         this.pane.x = 180;
         this.closeBtn.x = this.bg.width - closeBtn.width - 3;
         this.closeBtn.y = 28;
         this.titleBmp.x = 420;
         this.titleBmp.y = 10;
      }
      
      private function showHandler(param1:Object) : void
      {
         AppFacade.instance.sendNotification("CS_ENTER_HONOUR_SHOP");
      }
      
      private function exchangeHandler(param1:Object) : void
      {
         var _loc2_:Object = ActivityXmlManager.getExchangeInfoByExchangeId(param1.exchangeId,"honourShop");
         if(_loc2_.canBatch == 0)
         {
            AppFacade.instance.sendNotification("honourShop",{
               "exchangeId":param1.exchangeId,
               "itemPoint":param1.point,
               "exchangeNum":1
            });
            return;
         }
         if(PopUpCenter.containsWin("ShopExchangeWinName"))
         {
            PopUpCenter.removePopUp("ShopExchangeWinName");
         }
         else
         {
            PopUpCenter.addPopUp("ShopExchangeWinName",this._exchangeWin,true,true);
            this._exchangeWin.setExchangeData(param1.exchangeId,this._myHonour,"honourShop");
         }
      }
      
      private function confirmExchangeHandler(param1:int, param2:Point, param3:int) : void
      {
         AppFacade.instance.sendNotification("honourShop",{
            "exchangeId":param1,
            "itemPoint":param2,
            "exchangeNum":param3
         });
      }
      
      public function updateIntegral(param1:int) : void
      {
         this._myHonour = param1;
         this._curIntegral.htmlText = StringUtil.substitute(Globalization.getString("activity.40"),param1);
         this._exchangeWin.close();
      }
      
      public function setExchangeItemData() : void
      {
         this._honourShopUI.setHonourShopItem(ActivityXmlManager.getActivityExchangeInfo("honourShop"));
      }
      
      public function updateExchangeTimes(param1:int, param2:int) : void
      {
         this._honourShopUI.updateExchangeBtnState(param1,param2);
      }
      
      override public function close() : void
      {
         this._exchangeWin.close();
         super.close();
      }
      
      override public function dispose() : void
      {
         this._exchangeWin.dispose();
         super.dispose();
      }
   }
}

