package activity.view.win.strideBattle
{
   import activity.manager.ActivityXmlManager;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.data.serviceChallenge.ServiceChallengeState;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.modules.activity.view.mc.prize.PromotePrizeItem;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.tab.TabPane;
   import mmo.ui.control.window.PopUpWindow;
   import mmo.ui.event.TabEvent;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class GetStridePrizeWin extends PopUpWindow
   {
      public static const WIDTH:int = 646;
      
      public static const HEIGHT:int = 554;
      
      public static const NAME:String = "GetStridePrizeWin";
      
      public static const PRIZE_MAX_NUM:int = 6;
      
      private var _prizeTab:TabPane;
      
      private var _prizeTypeTab:TabPane;
      
      private var _prizeItemPane:ScrollPane;
      
      private var _prizeItemBox:UISprite;
      
      private var _prizeRankLabel:Label;
      
      private var _getPrizeBtn:Button;
      
      private var _selectBg:UISkin;
      
      private var _prizeItemArr:Array;
      
      public function GetStridePrizeWin()
      {
         var _loc7_:int = 0;
         var _loc2_:PromotePrizeItem = null;
         this._prizeItemArr = [];
         super(646,554);
         this.isLive = false;
         this.setTitleImageData(UIManager.getUISkin("stridePrizeTitle").bitmapData,-30);
         var _loc1_:UISkin = UIManager.getUISkin("tab_bg");
         _loc1_.width = 320;
         _loc1_.y = 6;
         pane.addChild(_loc1_);
         this._prizeTab = new TabPane([Globalization.getString("ServiceChallenge.49"),Globalization.getString("ServiceChallenge.50")],0,100);
         this._prizeTab.x = 3;
         this._prizeTab.y = 23;
         pane.addChild(this._prizeTab);
         this._prizeTab.addEventListener(TabEvent.Tab_IndexChange,this.onPrizeTabChange);
         this._prizeTypeTab = new TabPane([Globalization.getString("ServiceChallenge.19"),Globalization.getString("ServiceChallenge.20")],0,80,100,100,UIManager.getMultiUISkin("button2"),0);
         this._prizeTypeTab.x = 205;
         this._prizeTypeTab.y = 60;
         this._prizeTypeTab.Gap = 40;
         pane.addChild(this._prizeTypeTab);
         this._prizeTypeTab.addEventListener(TabEvent.Tab_IndexChange,this.onPrizeTabChange);
         var _loc5_:UISkin = UIManager.getUISkin("intro_small_bg");
         _loc5_.setSize(630,380);
         _loc5_.y = 64;
         pane.addChild(_loc5_);
         var _loc6_:UISkin = UIManager.getUISkin("split_vertical_short");
         _loc6_.x = 120;
         _loc6_.y = 68;
         pane.addChild(_loc6_);
         var _loc8_:Label = new Label(Globalization.getString("ServiceChallenge.51"),TextFormatLib.format_0xc9a359_12px,null,true);
         _loc8_.y = 69;
         _loc8_.width = 120;
         _loc8_.autoSize = "center";
         pane.addChild(_loc8_);
         var _loc4_:Label = new Label(Globalization.getString("ServiceChallenge.52"),TextFormatLib.format_0xc9a359_12px,null,true);
         _loc4_.x = 120;
         _loc4_.y = 69;
         _loc4_.width = 480;
         _loc4_.autoSize = "center";
         pane.addChild(_loc4_);
         this._selectBg = UIManager.getUISkin("select_mask");
         this._selectBg.setSize(592,86);
         this._prizeItemPane = new ScrollPane(612,336);
         this._prizeItemPane.x = 10;
         this._prizeItemPane.y = 98;
         pane.addChild(this._prizeItemPane);
         this._prizeItemBox = new UISprite();
         this._prizeItemPane.addToPane(this._prizeItemBox);
         var _loc3_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc3_.x = 112;
         _loc3_.y = 448;
         _loc3_.setSize(395,28);
         pane.addChild(_loc3_);
         this._prizeRankLabel = new Label("",TextFormatLib.format_verdana_0xffed89_12px,null,true);
         this._prizeRankLabel.x = 112;
         this._prizeRankLabel.y = 452;
         this._prizeRankLabel.width = 395;
         this._prizeRankLabel.autoSize = "center";
         pane.addChild(this._prizeRankLabel);
         this._getPrizeBtn = new Button(Globalization.getString("Gl.77"),null,80,UIManager.getMultiUISkin("button_big"));
         this._getPrizeBtn.x = 646 - this._getPrizeBtn.width >> 1;
         this._getPrizeBtn.y = 482;
         pane.addChild(this._getPrizeBtn);
         this._getPrizeBtn.mouseEnabled = false;
         this._getPrizeBtn.filters = [FilterLib.enbaleFilter];
         this._getPrizeBtn.addEventListener("click",this.onGetPrizeHandler);
         _loc7_ = 0;
         while(_loc7_ < 6)
         {
            _loc2_ = new PromotePrizeItem(_loc7_ + 1);
            _loc2_.y = _loc7_ * 87;
            this._prizeItemBox.addChild(_loc2_);
            this._prizeItemArr.push(_loc2_);
            _loc7_++;
         }
         this.showHander = this.showHandler;
      }
      
      private function showHandler(param1:Object) : void
      {
         var _loc4_:int = MainData.getInstance().serviceChallengeData.id;
         var _loc9_:String = ServiceChallengeState.getCurWholeProgressState(1);
         var _loc6_:int = MainData.getInstance().serviceChallengeData.group_prize_id;
         var _loc3_:Object = ActivityXmlManager.getGroupPrizeInfoById(_loc4_,_loc6_);
         var _loc7_:int = MainData.getInstance().serviceChallengeData.world_prize_id;
         var _loc8_:Object = ActivityXmlManager.getWorldPrizeInfoById(_loc4_,_loc7_);
         var _loc2_:Boolean = MainData.getInstance().serviceChallengeData.is_get_group_prize;
         var _loc5_:Boolean = MainData.getInstance().serviceChallengeData.is_get_world_prize;
         if(_loc8_ == null)
         {
            PopUpCenter.alertWin(Globalization.getString("ServiceChallenge.82"),null,null,true);
            return;
         }
         if(_loc8_.isHasPrize && !_loc5_)
         {
            this._prizeTab.selectedIndex = _loc8_.atServer;
            this._prizeTypeTab.selectedIndex = _loc8_.atGroup;
         }
         else if(_loc3_.isHasPrize && !_loc2_)
         {
            this._prizeTab.selectedIndex = _loc3_.atServer;
            this._prizeTypeTab.selectedIndex = _loc3_.atGroup;
         }
         else
         {
            if(_loc9_ == "serviceChallenge_promotion_inner")
            {
               this._prizeTab.selectedIndex = 0;
            }
            else
            {
               this._prizeTab.selectedIndex = 1;
            }
            this._prizeTypeTab.selectedIndex = 0;
         }
      }
      
      private function onGetPrizeHandler(param1:MouseEvent) : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc2_:Array = null;
         if(this._prizeTab.selectedIndex == 0)
         {
            _loc5_ = MainData.getInstance().serviceChallengeData.group_prize_id;
         }
         else if(this._prizeTab.selectedIndex == 1)
         {
            _loc5_ = MainData.getInstance().serviceChallengeData.world_prize_id;
         }
         var _loc4_:XML = XmlManager.getXml("leitai_jiangli").children().(@ID == _loc5_)[0];
         _loc3_ = MainData.getInstance().bagData.userBag.getLastGridsNum();
         if(_loc4_.@reward_items != "")
         {
            _loc2_ = String(_loc4_.@reward_items).split(",");
            if(_loc3_ == 0)
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("bag.14"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
            if(_loc3_ < _loc2_.length)
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("valueBook.2"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
            AppFacade.instance.sendNotification("CS_GET_SERVICECHALLENGE_PRIZE",_loc5_);
         }
         else
         {
            AppFacade.instance.sendNotification("CS_GET_SERVICECHALLENGE_PRIZE",_loc5_);
         }
      }
      
      private function onPrizeTabChange(param1:Event) : void
      {
         var _loc10_:int = 0;
         var _loc5_:PromotePrizeItem = null;
         var _loc4_:int = MainData.getInstance().serviceChallengeData.id;
         var _loc3_:Array = ActivityXmlManager.getPrizeTypeIds(_loc4_,this._prizeTab.selectedIndex,this._prizeTypeTab.selectedIndex);
         if(_loc3_ == null || _loc3_.length == 0)
         {
            return;
         }
         _loc3_.reverse();
         _loc10_ = 0;
         while(_loc10_ < 6)
         {
            _loc5_ = this._prizeItemArr[_loc10_];
            _loc5_.setPrize(_loc3_[_loc10_],6 - _loc10_ + 1);
            _loc10_++;
         }
         var _loc9_:String = ServiceChallengeState.getCurWholeProgressState(_loc4_);
         var _loc6_:int = MainData.getInstance().serviceChallengeData.group_prize_id;
         var _loc2_:Object = ActivityXmlManager.getPrizeInfoByIndex(_loc4_,0,this._prizeTypeTab.selectedIndex,_loc6_);
         var _loc7_:int = MainData.getInstance().serviceChallengeData.world_prize_id;
         var _loc8_:Object = ActivityXmlManager.getPrizeInfoByIndex(_loc4_,1,this._prizeTypeTab.selectedIndex,_loc7_);
         if(this._prizeTab.selectedIndex == 0)
         {
            this.updatePrizeState(_loc2_,_loc9_,MainData.getInstance().serviceChallengeData.is_get_group_prize);
         }
         else if(this._prizeTab.selectedIndex == 1)
         {
            this.updatePrizeState(_loc8_,_loc9_,MainData.getInstance().serviceChallengeData.is_get_world_prize);
         }
      }
      
      private function updatePrizeState(param1:Object, param2:String, param3:Boolean) : void
      {
         if(param1.isHasPrize)
         {
            this._prizeItemArr[param1.index].addChildAt(this._selectBg,2);
            if(param3)
            {
               if(param2 == "serviceChallenge_promotion_inner" || param2 == "serviceChallenge_promotion_stride")
               {
                  this._prizeRankLabel.text = Globalization.getString("ServiceChallenge.53");
               }
               else
               {
                  this._prizeRankLabel.text = StringUtil.substitute(Globalization.getString("ServiceChallenge.54"),param1.desc);
               }
               this._getPrizeBtn.text = Globalization.getString("openprize.9");
               this._getPrizeBtn.mouseEnabled = false;
               this._getPrizeBtn.filters = [FilterLib.enbaleFilter];
            }
            else
            {
               this._prizeRankLabel.text = StringUtil.substitute(Globalization.getString("ServiceChallenge.54"),param1.desc);
               this._getPrizeBtn.text = Globalization.getString("Gl.77");
               this._getPrizeBtn.mouseEnabled = true;
               this._getPrizeBtn.filters = [];
            }
            this._prizeItemPane.nowUpdateUI();
            this._prizeItemPane.scrollTo(param1.index / (this._prizeItemArr.length - 1));
         }
         else
         {
            this._selectBg.parent && this._selectBg.parent.removeChild(this._selectBg);
            if(param2 == "serviceChallenge_promotion_inner" || param2 == "serviceChallenge_promotion_stride")
            {
               this._prizeRankLabel.text = Globalization.getString("ServiceChallenge.53");
            }
            else
            {
               this._prizeRankLabel.text = Globalization.getString("ServiceChallenge.55");
            }
            this._getPrizeBtn.text = Globalization.getString("Gl.77");
            this._getPrizeBtn.mouseEnabled = false;
            this._getPrizeBtn.filters = [FilterLib.enbaleFilter];
         }
      }
      
      public function updateWin() : void
      {
         this._getPrizeBtn.text = Globalization.getString("openprize.9");
         this._getPrizeBtn.mouseEnabled = false;
         this._getPrizeBtn.filters = [FilterLib.enbaleFilter];
      }
      
      override public function get posHeight() : Number
      {
         return 554;
      }
   }
}

