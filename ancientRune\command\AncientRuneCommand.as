package ancientRune.command
{
   import ancientRune.proxy.AncientRuneProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class AncientRuneCommand extends SimpleCommand
   {
      public function AncientRuneCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc3_:AncientRuneProxy = facade.retrieveProxy("AncientRuneProxy") as AncientRuneProxy;
         var _loc2_:Object = param1.getBody();
         switch(param1.getName())
         {
            case "CS_ANCIENT_RUNE_GET_INFO":
               _loc3_.getInfo();
               break;
            case "CS_ANCIENT_RUNE_STONE_ADVANCED":
               _loc3_.advancedStone(_loc2_.pos);
               break;
            case "CS_ANCIENT_RUNE_STONE_STRENGTHEN":
               _loc3_.strengthStone(_loc2_.pos);
               break;
            case "CS_ANCIENT_RUNE_BORE":
               _loc3_.bore(_loc2_.pos);
               break;
            case "CS_ANCIENT_RUNE_HOLE_STRENGTHEN":
               _loc3_.holeStreng(_loc2_.pos,_loc2_.layer);
               break;
            case "CS_ANCIENT_RUNE_DRAW_RUNE":
               _loc3_.draw(_loc2_.pos,_loc2_.type,_loc2_.layer);
               break;
            case "CS_ANCIENT_RUNE_REPLACE_RUNE":
               _loc3_.replace(_loc2_.type,_loc2_.pos,_loc2_.layer);
               break;
            case "CS_ANCIENT_RUNE_ABSORB":
               _loc3_.absorb(_loc2_.type);
         }
      }
   }
}

