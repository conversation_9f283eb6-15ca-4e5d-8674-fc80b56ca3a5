package ancientRune.mediator
{
   import ancientRune.command.AncientRuneCommand;
   import ancientRune.proxy.AncientRuneProxy;
   import ancientRune.view.RuneStonePanel;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.data.ancientRune.AncientRuneData;
   import org.puremvc.as3.interfaces.INotification;
   
   public class Rune<PERSON>toneMediator extends PirateMediator
   {
      public static const NAME:String = "RuneStoneMediator";
      
      private var _runeStonePanel:RuneStonePanel;
      
      public function RuneStoneMediator(param1:Object = null)
      {
         super("RuneStoneMediator",param1);
         this._runeStonePanel = param1 as RuneStonePanel;
         var _loc2_:AncientRuneData = MainData.getInstance().ancientRuneData;
         _loc2_.resetAvailable();
         this.checkDataAvialable(this.onComplete);
      }
      
      private function onComplete() : void
      {
         this._runeStonePanel.initUI();
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().ancientRuneData];
      }
      
      override public function onRegister() : void
      {
         facade.registerProxy(new AncientRuneProxy());
         facade.registerCommand("CS_ANCIENT_RUNE_BORE",AncientRuneCommand);
         facade.registerCommand("CS_ANCIENT_RUNE_DRAW_RUNE",AncientRuneCommand);
         facade.registerCommand("CS_ANCIENT_RUNE_GET_INFO",AncientRuneCommand);
         facade.registerCommand("CS_ANCIENT_RUNE_HOLE_STRENGTHEN",AncientRuneCommand);
         facade.registerCommand("CS_ANCIENT_RUNE_STONE_ADVANCED",AncientRuneCommand);
         facade.registerCommand("CS_ANCIENT_RUNE_STONE_STRENGTHEN",AncientRuneCommand);
         facade.registerCommand("CS_ANCIENT_RUNE_REPLACE_RUNE",AncientRuneCommand);
         facade.registerCommand("CS_ANCIENT_RUNE_ABSORB",AncientRuneCommand);
      }
      
      override public function onRemove() : void
      {
         facade.removeProxy("AncientRuneProxy");
         facade.removeCommand("CS_ANCIENT_RUNE_BORE");
         facade.removeCommand("CS_ANCIENT_RUNE_DRAW_RUNE");
         facade.removeCommand("CS_ANCIENT_RUNE_GET_INFO");
         facade.removeCommand("CS_ANCIENT_RUNE_HOLE_STRENGTHEN");
         facade.removeCommand("CS_ANCIENT_RUNE_STONE_ADVANCED");
         facade.removeCommand("CS_ANCIENT_RUNE_STONE_STRENGTHEN");
         facade.removeCommand("CS_ANCIENT_RUNE_REPLACE_RUNE");
         facade.removeCommand("CS_ANCIENT_RUNE_ABSORB");
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc3_:Object = param1.getBody();
         var _loc2_:AncientRuneData = MainData.getInstance().ancientRuneData;
         switch(param1.getName())
         {
            case "SC_ANCIENT_RUNE_STONE_STRENGTHEN":
               _loc2_.strengthenStone -= _loc3_.cost;
               _loc2_.stone[_loc3_.pos].level++;
               _loc2_.updateEquipFight();
               this._runeStonePanel.posStrentgth(_loc3_.pos);
               this._runeStonePanel._attributeSp.updateEquipProperty();
               sendNotification("CS_FRESH__PROPERTY");
               break;
            case "SC_ANCIENT_RUNE_STONE_ADVANCED":
               _loc2_.advancedStone -= _loc3_.cost;
               _loc2_.stone[_loc3_.pos].level = 0;
               _loc2_.stone[_loc3_.pos].quality++;
               _loc2_.updateEquipFight();
               this._runeStonePanel.posStrentgth(_loc3_.pos);
               this._runeStonePanel._attributeSp.updateEquipProperty();
               sendNotification("CS_FRESH__PROPERTY");
               break;
            case "SC_ANCIENT_RUNE_BORE":
               _loc2_.stone[_loc3_.pos].bore++;
               this._runeStonePanel.posDrawRefresh(_loc3_.pos);
               break;
            case "SC_ANCIENT_RUNE_DRAW_RUNE":
               _loc2_.stone[_loc3_.pos].rune[_loc3_.layer] = _loc3_.rune;
               if(_loc3_.rune.isNew)
               {
                  _loc2_.updateEquipFight();
                  this._runeStonePanel.posDrawRefresh(_loc3_.pos);
                  this._runeStonePanel._attributeSp.updateEquipProperty();
                  sendNotification("CS_FRESH__PROPERTY");
               }
               else
               {
                  this._runeStonePanel.posDrawReplace(_loc3_.rune.newRune,_loc3_.pos,_loc3_.layer);
               }
               break;
            case "SC_ANCIENT_RUNE_REPLACE_RUNE":
               if(_loc3_.type)
               {
                  _loc2_.stone[_loc3_.pos].rune[_loc3_.layer] = _loc3_.rune;
                  _loc2_.updateEquipFight();
                  this._runeStonePanel.posDrawRefresh(_loc3_.pos);
                  this._runeStonePanel._attributeSp.updateEquipProperty();
                  sendNotification("CS_FRESH__PROPERTY");
               }
               else
               {
                  _loc2_.boreExp += int(_loc3_.add);
                  _loc2_.stone[_loc3_.pos].rune[_loc3_.layer].newRune = null;
               }
               this._runeStonePanel.posDrawReplaceOk();
               break;
            case "SC_ANCIENT_RUNE_HOLE_STRENGTHEN":
               _loc2_.boreExp -= _loc3_.cost;
               _loc2_.stone[_loc3_.pos].borelevel = _loc3_.borelevel;
               _loc2_.updateEquipFight();
               this._runeStonePanel.boreStrengUpdate();
               this._runeStonePanel._attributeSp.updateEquipProperty();
               sendNotification("CS_FRESH__PROPERTY");
               break;
            case "SC_ANCIENT_RUNE_ABSORB":
               _loc2_.boreExp += int(_loc3_.add);
               this._runeStonePanel.boreStrengUpdate(0);
               this._runeStonePanel.posDrawReplaceOk();
         }
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_ANCIENT_RUNE_ABSORB","SC_ANCIENT_RUNE_REPLACE_RUNE","SC_ANCIENT_RUNE_BORE","SC_ANCIENT_RUNE_DRAW_RUNE","SC_ANCIENT_RUNE_GET_INFO","SC_ANCIENT_RUNE_HOLE_STRENGTHEN","SC_ANCIENT_RUNE_STONE_ADVANCED","SC_ANCIENT_RUNE_STONE_STRENGTHEN"];
      }
   }
}

