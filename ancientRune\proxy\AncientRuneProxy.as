package ancientRune.proxy
{
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class AncientRuneProxy extends Proxy
   {
      public static const NAME:String = "AncientRuneProxy";
      
      public function AncientRuneProxy(param1:Object = null)
      {
         super("AncientRuneProxy",param1);
      }
      
      override public function onRegister() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.ancientrune.getInfo",this.handleGetInfo);
         BabelTimeSocket.getInstance().regCallback("re.ancientrune.bore",this.handleBore);
         BabelTimeSocket.getInstance().regCallback("re.ancientrune.drawRune",this.handleDraw);
         BabelTimeSocket.getInstance().regCallback("re.ancientrune.holeStrengthen",this.handleHoleStreng);
         BabelTimeSocket.getInstance().regCallback("re.ancientrune.stoneAdvanced",this.handleAdvancedStone);
         BabelTimeSocket.getInstance().regCallback("re.ancientrune.stoneStrengthen",this.handleStrengthStone);
         BabelTimeSocket.getInstance().regCallback("re.ancientrune.replaceRune",this.handleReplace);
         BabelTimeSocket.getInstance().regCallback("re.ancientrune.absorb",this.handleAbsorb);
      }
      
      override public function onRemove() : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.battlepass.getInfo",this.handleGetInfo);
         BabelTimeSocket.getInstance().removeCallback("re.ancientrune.bore",this.handleBore);
         BabelTimeSocket.getInstance().removeCallback("re.ancientrune.drawRune",this.handleDraw);
         BabelTimeSocket.getInstance().removeCallback("re.ancientrune.holeStrengthen",this.handleHoleStreng);
         BabelTimeSocket.getInstance().removeCallback("re.ancientrune.stoneAdvanced",this.handleAdvancedStone);
         BabelTimeSocket.getInstance().removeCallback("re.ancientrune.stoneStrengthen",this.handleStrengthStone);
         BabelTimeSocket.getInstance().removeCallback("re.ancientrune.replaceRune",this.handleReplace);
         BabelTimeSocket.getInstance().removeCallback("re.ancientrune.absorb",this.handleAbsorb);
      }
      
      public function updateBag(param1:Object) : void
      {
         var _loc2_:* = undefined;
         var _loc3_:Item = null;
         for(_loc2_ in param1)
         {
            _loc3_ = ItemFactory.creatItem(param1[_loc2_]);
            MainData.getInstance().bagData.setGridItem(_loc2_,_loc3_);
         }
      }
      
      public function textTip(param1:String, param2:int = 1) : void
      {
         var _loc3_:TextFormat = null;
         if(param2)
         {
            _loc3_ = TextFormatLib.greenbig_12px;
         }
         else
         {
            _loc3_ = TextFormatLib.format_0xFF0000_12px;
         }
         sendNotification("POP_TEXT_TIPS",{
            "text":param1,
            "textFormat":_loc3_
         });
      }
      
      public function absorb(param1:int) : void
      {
         BabelTimeSocket.getInstance().sendMessage("ancientrune.absorb",new SocketCallback("re.ancientrune.absorb"),param1);
      }
      
      private function handleAbsorb(param1:SocketDataEvent) : void
      {
         if(param1.data.err == "ok")
         {
            this.textTip("一键吸收成功,符文经验+" + param1.data.add);
            this.updateBag(param1.data.bag);
            sendNotification("SC_ANCIENT_RUNE_ABSORB",{"add":param1.data.add});
         }
         else
         {
            this.textTip("一键吸收失败!",0);
         }
      }
      
      public function holeStreng(param1:int, param2:int) : void
      {
         BabelTimeSocket.getInstance().sendMessage("ancientrune.holeStrengthen",new SocketCallback("re.ancientrune.holeStrengthen",[param1,param2]),param1,param2);
      }
      
      private function handleHoleStreng(param1:SocketDataEvent) : void
      {
         if(param1.data.err == "ok")
         {
            this.textTip("升级成功");
            sendNotification("SC_ANCIENT_RUNE_HOLE_STRENGTHEN",{
               "pos":param1.callbackParames[0],
               "layer":param1.callbackParames[1],
               "cost":param1.data.cost,
               "borelevel":param1.data.borelevel
            });
         }
         else
         {
            this.textTip("升级失败!",0);
         }
      }
      
      public function replace(param1:int, param2:int, param3:int) : void
      {
         BabelTimeSocket.getInstance().sendMessage("ancientrune.replaceRune",new SocketCallback("re.ancientrune.replaceRune",[param1,param2,param3]),param1,param2,param3);
      }
      
      private function handleReplace(param1:SocketDataEvent) : void
      {
         var _loc2_:String = null;
         if(param1.data.err == "ok")
         {
            if(param1.callbackParames[0])
            {
               _loc2_ = "替换成功";
            }
            else
            {
               _loc2_ = "吸收成功,符文经验+" + param1.data.add;
            }
            this.textTip(_loc2_);
            sendNotification("SC_ANCIENT_RUNE_REPLACE_RUNE",{
               "type":param1.callbackParames[0],
               "pos":param1.callbackParames[1],
               "rune":param1.data.rune,
               "add":param1.data.add,
               "layer":param1.callbackParames[2]
            });
         }
         else
         {
            this.textTip("操作失败!",0);
         }
      }
      
      public function draw(param1:int, param2:int, param3:int) : void
      {
         BabelTimeSocket.getInstance().sendMessage("ancientrune.drawRune",new SocketCallback("re.ancientrune.drawRune",[param1,param3]),param1,param2,param3);
      }
      
      private function handleDraw(param1:SocketDataEvent) : void
      {
         if(param1.data.err == "ok")
         {
            this.textTip("绘制符文成功");
            this.updateBag(param1.data.bag);
            sendNotification("SC_ANCIENT_RUNE_DRAW_RUNE",{
               "pos":param1.callbackParames[0],
               "rune":param1.data.rune,
               "layer":param1.callbackParames[1]
            });
         }
         else
         {
            this.textTip("操作失败!",0);
         }
      }
      
      public function getInfo() : void
      {
         BabelTimeSocket.getInstance().sendMessage("ancientrune.getInfo",new SocketCallback("re.ancientrune.getInfo"));
      }
      
      private function handleGetInfo(param1:SocketDataEvent) : void
      {
      }
      
      public function strengthStone(param1:int) : void
      {
         BabelTimeSocket.getInstance().sendMessage("ancientrune.stoneStrengthen",new SocketCallback("re.ancientrune.stoneStrengthen",[param1]),param1);
      }
      
      private function handleStrengthStone(param1:SocketDataEvent) : void
      {
         if(param1.data.err == "ok")
         {
            this.textTip("强化成功");
            sendNotification("SC_ANCIENT_RUNE_STONE_STRENGTHEN",{
               "cost":param1.data.cost,
               "pos":param1.callbackParames[0]
            });
         }
         else
         {
            this.textTip("强化失败!",0);
         }
      }
      
      public function advancedStone(param1:int) : void
      {
         BabelTimeSocket.getInstance().sendMessage("ancientrune.stoneAdvanced",new SocketCallback("re.ancientrune.stoneAdvanced",[param1]),param1);
      }
      
      private function handleAdvancedStone(param1:SocketDataEvent) : void
      {
         if(param1.data.err == "ok")
         {
            this.textTip("进阶成功");
            sendNotification("SC_ANCIENT_RUNE_STONE_ADVANCED",{
               "cost":param1.data.cost,
               "pos":param1.callbackParames[0]
            });
         }
         else
         {
            this.textTip("进阶失败!",0);
         }
      }
      
      public function bore(param1:int) : void
      {
         BabelTimeSocket.getInstance().sendMessage("ancientrune.bore",new SocketCallback("re.ancientrune.bore",[param1]),param1);
      }
      
      private function handleBore(param1:SocketDataEvent) : void
      {
         if(param1.data.err == "ok")
         {
            this.textTip("开孔成功");
            this.updateBag(param1.data.bag);
            sendNotification("SC_ANCIENT_RUNE_BORE",{"pos":param1.callbackParames[0]});
         }
         else
         {
            this.textTip("开孔失败!",0);
         }
      }
   }
}

