package ancientRune.view
{
   import flash.events.MouseEvent;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class HelpWin extends PopUpWindow
   {
      public static const NAME:String = "HelpWin";
      
      public var node:XML;
      
      public function HelpWin()
      {
         super(520,372);
         setTitleImageData(UIManager.getUISkin("helpTitle").bitmapData);
         var _loc1_:UISkin = UIManager.getUISkin("group_bg");
         _loc1_.x = 20;
         _loc1_.y = 40;
         _loc1_.setSize(480,290);
         addChild(_loc1_);
         var _loc4_:ScrollPane = new ScrollPane(455,250);
         _loc4_.x = 30;
         _loc4_.y = 45;
         this.addChild(_loc4_);
         var _loc2_:UISprite = new UISprite();
         _loc4_.addToPane(_loc2_);
         var _loc5_:Label = new Label("",TextFormatLib.format_0xFFB932_12px);
         _loc5_.width = 440;
         _loc5_.wordWrap = true;
         _loc5_.text = "1、该系统由符石跟符文共同组成。\n\n2、符石通过强化石升级与进阶，并且可以通过打孔器进行打孔。\n\n3、打孔后该位置可通过各种符石进行绘制符文，第一次绘制后直接绘制成功，之后绘制可以替换或者分解，分解会获得符文经验。\n\n4、符文经验可以对孔位进行强化，会对该孔位的符文属性进行增幅。\n\n5、符石与符文如果属于同一系列[仅限基础属性]，例如永生系列，符文属性会加成百分之20，反之降低百分之20。\n\n6、不同颜色的符石头加成不同,但获得符印属性概率相同。\n\n";
         _loc2_.addChild(_loc5_);
         var _loc3_:Button = new Button(Globalization.getString("matchRule.1"),null,75,UIManager.getMultiUISkin("button_big"));
         _loc3_.x = 220;
         _loc3_.y = 335;
         addChild(_loc3_);
         _loc3_.addEventListener("click",this.onClick);
         this.isLive = false;
      }
      
      private function onClick(param1:MouseEvent) : void
      {
         this.close();
      }
   }
}

