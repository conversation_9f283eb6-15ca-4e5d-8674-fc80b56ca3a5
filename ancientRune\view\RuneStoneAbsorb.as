package ancientRune.view
{
   import ancientRune.view.draw.RuneStoneDrawChooseItem;
   import flash.events.MouseEvent;
   import flash.utils.ByteArray;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.window.PopUpWindow;
   
   public class RuneStoneAbsorb extends PopUpWindow
   {
      public static const NAME:String = "RuneStoneAbsorb";
      
      public var box:UIBox;
      
      public var itemArr:Array = [];
      
      public var data:ByteArray;
      
      public var clearBtn:Button;
      
      public var itemData:Object;
      
      public var chooseId:int;
      
      private var scrollPane:ScrollPane;
      
      public var _container:UISprite;
      
      public function RuneStoneAbsorb()
      {
         super(350,280);
         this.title = "宝石一键吸收";
         var _loc1_:UISkin = UIManager.getUISkin("group_bg");
         _loc1_.setSize(300,200);
         _loc1_.x = 30;
         _loc1_.y = 38;
         addChild(_loc1_);
         this.scrollPane = new ScrollPane(300,160);
         this.addChild(this.scrollPane);
         this._container = new UISprite();
         this.scrollPane.x = -9;
         this.scrollPane.y = 42;
         this.scrollPane.addToPane(this._container);
         this.scrollPane.updateUI();
         this.box = new UIBox();
         this.box.x = 20;
         this.box.y = -15;
         this.box.rowMaxChildrenNumber = 2;
         this.box.lineMaxChildrenNumber = 4;
         this.box.rowSpace = 20;
         this.box.lineSpace = 10;
         this._container.addChild(this.box);
         this.clearBtn = new Button("吸收",TextFormatLib.format_0xFFB932_12px_songti,85,UIManager.getMultiUISkin("btn_normal"));
         this.clearBtn.x = 150;
         this.clearBtn.y = 240;
         addChild(this.clearBtn);
         this.clearBtn.addEventListener("click",this.clearHandler);
      }
      
      private function clearHandler(param1:MouseEvent) : void
      {
         var chooseid:int = this.chooseId;
         var oneexp:int = int(MainData.getInstance().ancientRuneData.runeExpArray[chooseid]);
         var stoneid:int = int(MainData.getInstance().ancientRuneData.runeIdsArray[chooseid]);
         var stonenum:int = MainData.getInstance().bagData.getNumItemByItemTemplate(stoneid);
         this.close();
         PopUpCenter.confirmWin("是否消耗所有选中符石以换取 +" + oneexp * stonenum + "经验？",(function():*
         {
            var confirmOpen:Function;
            return confirmOpen = function():void
            {
               AppFacade.instance.sendNotification("CS_ANCIENT_RUNE_ABSORB",{"type":chooseid});
            };
         })(),null,0,true);
      }
      
      override public function show(param1:Object) : void
      {
         var _loc2_:* = 0;
         var _loc3_:RuneStoneDrawChooseItem = null;
         while(this.box.numChildren > 0)
         {
            this.box.removeChildAt(0);
         }
         this.itemData = param1.choose;
         var _loc4_:int = 0;
         for(_loc2_ in this.itemData)
         {
            _loc3_ = new RuneStoneDrawChooseItem();
            _loc3_.setData(this.itemData[_loc2_],_loc4_);
            _loc3_.addEventListener("click",this.chooseHandler);
            this.box.addChild(_loc3_);
            if(_loc4_ == 0)
            {
               this.chooseId = int(this.itemData[_loc2_].id);
            }
            _loc4_++;
         }
      }
      
      private function chooseHandler(param1:MouseEvent) : void
      {
         var _loc2_:RuneStoneDrawChooseItem = RuneStoneDrawChooseItem(param1.currentTarget);
         _loc2_.chooseBtn.isCheck = true;
         trace(_loc2_.id);
         this.chooseId = _loc2_.id;
      }
   }
}

