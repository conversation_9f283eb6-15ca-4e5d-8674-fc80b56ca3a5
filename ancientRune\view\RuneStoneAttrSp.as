package ancientRune.view
{
   import flash.display.Sprite;
   import game.data.MainData;
   import game.data.ancientRune.AncientRuneData;
   import game.manager.UIManager;
   import game.modules.rideDecoration.view.component.DecorationEquipDropArea;
   import game.modules.rideDecoration.view.component.DragDecorationIcon;
   import game.xmlParsers.affix.AffixManager;
   import game.xmlParsers.affix.IAffix;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   
   public class RuneStoneAttrSp extends Sprite
   {
      private var _iconArr:Array;
      
      private var _equipDropArea:DecorationEquipDropArea;
      
      private var _currentPosIcon:DragDecorationIcon;
      
      private var _selectPos:DragDecorationIcon;
      
      private var _psyAttackLabel:Label;
      
      private var _psyDefendLabel:Label;
      
      private var _magicAttackLabel:Label;
      
      private var _magicDefendLabel:Label;
      
      private var _killAttackLabel:Label;
      
      private var _killDefendLabel:Label;
      
      private var _hpLabel:Label;
      
      private var _hprate:Label;
      
      private var _psyAttackLabel_rate:Label;
      
      private var _psyDefendLabel_rate:Label;
      
      private var _magicAttackLabel_rate:Label;
      
      private var _magicDefendLabel_rate:Label;
      
      private var _killAttackLabel_rate:Label;
      
      private var _killDefendLabel_rate:Label;
      
      private var _phyAttRate:Label;
      
      private var _kilAttRate:Label;
      
      private var _magAttRate:Label;
      
      private var _phyAttDefRate:Label;
      
      private var _kilAttDefRate:Label;
      
      private var _magAttDefRate:Label;
      
      private var _str:Label;
      
      private var _agi:Label;
      
      private var _int:Label;
      
      public var strengthHandler:Function;
      
      public function RuneStoneAttrSp()
      {
         super();
         this.initUI();
      }
      
      private function initUI() : void
      {
         var _loc6_:int = 40;
         var _loc5_:int = 6;
         var _loc1_:int = 30;
         var _loc3_:Label = null;
         var _loc4_:UISkin = null;
         var _loc2_:Sprite = new Sprite();
         this.addChild(_loc2_);
         _loc4_ = UIManager.getUISkin("pane_bg");
         _loc4_.setSize(354,154);
         _loc2_.addChild(_loc4_);
         _loc3_ = new Label("符石属性加成",TextFormatLib.format_0xffb932_12px_verdana);
         _loc3_.x = 140;
         _loc3_.y = 1;
         _loc2_.addChild(_loc3_);
         this._psyAttackLabel = new Label("",TextFormatLib.format_0xebce82_10px);
         this._psyAttackLabel.x = 8;
         this._psyAttackLabel.y = 20;
         _loc2_.addChild(this._psyAttackLabel);
         this._killAttackLabel = new Label("",TextFormatLib.format_0xebce82_10px);
         this._killAttackLabel.x = 8;
         this._killAttackLabel.y = 36;
         _loc2_.addChild(this._killAttackLabel);
         this._magicAttackLabel = new Label("",TextFormatLib.format_0xebce82_10px);
         this._magicAttackLabel.x = 8;
         this._magicAttackLabel.y = 52;
         _loc2_.addChild(this._magicAttackLabel);
         this._psyDefendLabel = new Label("",TextFormatLib.format_0xebce82_10px);
         this._psyDefendLabel.x = 8;
         this._psyDefendLabel.y = 68;
         _loc2_.addChild(this._psyDefendLabel);
         this._killDefendLabel = new Label("",TextFormatLib.format_0xebce82_10px);
         this._killDefendLabel.x = 8;
         this._killDefendLabel.y = 84;
         _loc2_.addChild(this._killDefendLabel);
         this._magicDefendLabel = new Label("",TextFormatLib.format_0xebce82_10px);
         this._magicDefendLabel.x = 8;
         this._magicDefendLabel.y = 100;
         _loc2_.addChild(this._magicDefendLabel);
         this._hpLabel = new Label("",TextFormatLib.format_0xebce82_10px);
         this._hpLabel.x = 8;
         this._hpLabel.y = 116;
         _loc2_.addChild(this._hpLabel);
         this._hprate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._hprate.x = 240;
         this._hprate.y = 116;
         _loc2_.addChild(this._hprate);
         this._phyAttRate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._phyAttRate.x = 8;
         this._phyAttRate.y = 90;
         this._magAttRate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._magAttRate.x = 120;
         this._magAttRate.y = 90;
         this._kilAttRate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._kilAttRate.x = 230;
         this._kilAttRate.y = 90;
         this._psyAttackLabel_rate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._psyAttackLabel_rate.x = 120;
         this._psyAttackLabel_rate.y = 20;
         _loc2_.addChild(this._psyAttackLabel_rate);
         this._killAttackLabel_rate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._killAttackLabel_rate.x = 120;
         this._killAttackLabel_rate.y = 36;
         _loc2_.addChild(this._killAttackLabel_rate);
         this._magicAttackLabel_rate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._magicAttackLabel_rate.x = 120;
         this._magicAttackLabel_rate.y = 52;
         _loc2_.addChild(this._magicAttackLabel_rate);
         this._psyDefendLabel_rate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._psyDefendLabel_rate.x = 120;
         this._psyDefendLabel_rate.y = 68;
         _loc2_.addChild(this._psyDefendLabel_rate);
         this._killDefendLabel_rate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._killDefendLabel_rate.x = 120;
         this._killDefendLabel_rate.y = 84;
         _loc2_.addChild(this._killDefendLabel_rate);
         this._magicDefendLabel_rate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._magicDefendLabel_rate.x = 120;
         this._magicDefendLabel_rate.y = 100;
         _loc2_.addChild(this._magicDefendLabel_rate);
         this._phyAttRate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._phyAttRate.x = 240;
         this._phyAttRate.y = 20;
         _loc2_.addChild(this._phyAttRate);
         this._magAttRate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._magAttRate.x = 240;
         this._magAttRate.y = 36;
         _loc2_.addChild(this._magAttRate);
         this._kilAttRate = new Label("",TextFormatLib.format_0xebce82_10px);
         this._kilAttRate.x = 240;
         this._kilAttRate.y = 52;
         _loc2_.addChild(this._kilAttRate);
         this._str = new Label("",TextFormatLib.format_0xebce82_10px);
         this._str.x = 240;
         this._str.y = 68;
         _loc2_.addChild(this._str);
         this._agi = new Label("",TextFormatLib.format_0xebce82_10px);
         this._agi.x = 240;
         this._agi.y = 84;
         _loc2_.addChild(this._agi);
         this._int = new Label("",TextFormatLib.format_0xebce82_10px);
         this._int.x = 240;
         this._int.y = 100;
         _loc2_.addChild(this._int);
         this.updateEquipProperty();
      }
      
      public function updateEquipProperty() : void
      {
         var _loc2_:AncientRuneData = MainData.getInstance().ancientRuneData;
         var _loc3_:Number = _loc2_.getAttrAll(6);
         var _loc1_:IAffix = AffixManager.creatAffix(6,_loc3_);
         this._psyAttackLabel.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(9);
         _loc1_ = AffixManager.creatAffix(9,_loc3_);
         this._psyDefendLabel.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(9);
         _loc1_ = AffixManager.creatAffix(9,_loc3_);
         this._psyDefendLabel.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(7);
         _loc1_ = AffixManager.creatAffix(7,_loc3_);
         this._killAttackLabel.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(10);
         _loc1_ = AffixManager.creatAffix(10,_loc3_);
         this._killDefendLabel.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(8);
         _loc1_ = AffixManager.creatAffix(8,_loc3_);
         this._magicAttackLabel.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(11);
         _loc1_ = AffixManager.creatAffix(11,_loc3_);
         this._magicDefendLabel.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(1);
         _loc1_ = AffixManager.creatAffix(1,_loc3_);
         this._hpLabel.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(2);
         _loc1_ = AffixManager.creatAffix(2,_loc3_);
         this._hprate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(35);
         _loc1_ = AffixManager.creatAffix(35,_loc3_);
         this._phyAttRate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(37);
         _loc1_ = AffixManager.creatAffix(37,_loc3_);
         this._kilAttRate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(39);
         _loc1_ = AffixManager.creatAffix(39,_loc3_);
         this._magAttRate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(25);
         _loc1_ = AffixManager.creatAffix(25,_loc3_);
         this._psyAttackLabel_rate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(29);
         _loc1_ = AffixManager.creatAffix(29,_loc3_);
         this._magicAttackLabel_rate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(27);
         _loc1_ = AffixManager.creatAffix(27,_loc3_);
         this._killAttackLabel_rate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(26);
         _loc1_ = AffixManager.creatAffix(26,_loc3_);
         this._psyDefendLabel_rate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(30);
         _loc1_ = AffixManager.creatAffix(30,_loc3_);
         this._magicDefendLabel_rate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(28);
         _loc1_ = AffixManager.creatAffix(28,_loc3_);
         this._killDefendLabel_rate.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(3);
         _loc1_ = AffixManager.creatAffix(3,_loc3_);
         this._str.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(4);
         _loc1_ = AffixManager.creatAffix(4,_loc3_);
         this._agi.text = _loc1_.print1();
         _loc3_ = _loc2_.getAttrAll(5);
         _loc1_ = AffixManager.creatAffix(5,_loc3_);
         this._int.text = _loc1_.print1();
      }
   }
}

