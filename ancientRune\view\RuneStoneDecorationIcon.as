package ancientRune.view
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.geom.Point;
   import flash.utils.Timer;
   import game.drag.DragManager;
   import game.drag.DragSprite;
   import game.drag.ICanDragable;
   import game.drag.IDropable;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import game.modules.rideDecoration.manager.DecorationManager;
   import game.modules.rideDecoration.manager.EquipPositionInfo;
   import mmo.Core;
   import mmo.ext.filter.FilterLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.customTooltip.IToolTipAble;
   import mmo.ui.control.customTooltip.SingleTextToolTipContent;
   
   public class RuneStoneDecorationIcon extends UISprite implements IDropable, ICanDragable, IToolTipAble
   {
      public static const STARTDRAG:String = "game.modules.rideDecoration.view.component.startDrag";
      
      public static const STOPDRAG:String = "game.modules.rideDecoration.view.component.stopDrag";
      
      private var _bg:UISkin;
      
      private var _content:DisplayObject;
      
      private var _id:int;
      
      private var _positionId:int;
      
      private var _isShowAddProperty:Boolean;
      
      private var _dir:String = "left";
      
      private var _overSkin:UISkin;
      
      private var _effect:MovieClip;
      
      private var _tips:RuneStoneDecorationIconTips;
      
      private var _canDrag:Boolean;
      
      private var _canReceiveDrag:Boolean;
      
      private var _canDoubleClick:Boolean;
      
      private var _timer:Timer;
      
      public var dealDropFun:Function;
      
      public var stopDropFun:Function;
      
      public var doubleClickFun:Function;
      
      public function RuneStoneDecorationIcon(param1:UISkin = null)
      {
         super();
         this._bg = Boolean(param1) ? param1 : UIManager.getUISkin("formation_bg");
         this._bg.width = 48;
         this._bg.height = 48;
         this.addChild(this._bg);
         this._overSkin = UIManager.getUISkin("selected_general");
         this._overSkin.width = 56;
         this._overSkin.height = 56;
         this._overSkin.x = -4;
         this._overSkin.y = -4;
         this._overSkin.visible = false;
         this.addChild(this._overSkin);
         this._timer = new Timer(250,1);
         this._timer.addEventListener("timerComplete",this.completeHandler);
         this.addEventListener("mouseDown",this.mouseDownHandler);
         this.addEventListener("rollOver",this.onRollOverHandler);
         this.addEventListener("rollOut",this.onRollOutHandler);
         this.mouseChildren = false;
      }
      
      private function onRollOverHandler(param1:MouseEvent) : void
      {
         if(this._id == 0)
         {
            return;
         }
         this._tips = new RuneStoneDecorationIconTips();
         var _loc2_:Point = this.localToGlobal(new Point(48,0));
         this._tips.setData(this._id,this._positionId,_loc2_,this._dir);
      }
      
      private function onRollOutHandler(param1:MouseEvent) : void
      {
         if(!this._tips)
         {
            return;
         }
         this._tips.parent && this._tips.parent.removeChild(this._tips);
         this._tips.dispose();
         this._tips = null;
      }
      
      private function mouseDownHandler(param1:MouseEvent) : void
      {
         param1.stopImmediatePropagation();
         removeEventListener("mouseDown",this.mouseDownHandler);
         this._canDrag && addEventListener("mouseMove",this.mouseMoveHandler);
         this.addEventListener("mouseUp",this.mouseUpHandler);
      }
      
      private function mouseUpHandler(param1:MouseEvent) : void
      {
         param1.stopImmediatePropagation();
         removeEventListener("mouseMove",this.mouseMoveHandler);
         removeEventListener("mouseUp",this.mouseUpHandler);
         this._canDrag && addEventListener("mouseDown",this.mouseDownHandler);
      }
      
      private function mouseMoveHandler(param1:MouseEvent) : void
      {
         trace("划过");
         param1.stopImmediatePropagation();
         if(!param1.buttonDown)
         {
            removeEventListener("mouseMove",this.mouseMoveHandler);
            this._canDrag && addEventListener("mouseDown",this.mouseDownHandler);
            return;
         }
         removeEventListener("mouseMove",this.mouseMoveHandler);
         removeEventListener("mouseUp",this.mouseUpHandler);
         if(!this._canDrag)
         {
            return;
         }
         if(this._id == 0)
         {
            return;
         }
         this.addFilters();
         var _loc2_:DragSprite = this.createDragSp();
         DragManager.dragBase.addChild(_loc2_);
         DragManager.setDefaultDropHandler(this.setDefaultDropHandler);
         DragManager.setDefaultOverHandler(this.setDefaultOverHandler);
         DragManager.startDrag(_loc2_);
         dispatchEvent(new Event("game.modules.rideDecoration.view.component.startDrag"));
      }
      
      private function completeHandler(param1:TimerEvent) : void
      {
         this._timer.stop();
         this._timer.reset();
      }
      
      public function setData(param1:*, param2:Boolean = false, param3:Boolean = false, param4:Boolean = false) : void
      {
         var data:* = param1;
         var canDrag:Boolean = param2;
         var canReceiveDrag:Boolean = param3;
         var enableDoubleClick:Boolean = param4;
         this._canDrag = canDrag;
         this._canReceiveDrag = canReceiveDrag;
         this._canDoubleClick = enableDoubleClick;
         this.clear();
         if(data is String)
         {
            if(Core.dataLib.chkData(data.toString()))
            {
               this.setData(Core.dataLib.getImg(data.toString()),this._canDrag,this._canReceiveDrag,this._canDoubleClick);
            }
            else
            {
               Core.dataLib.load([data.toString()],function():void
               {
                  setData(Core.dataLib.getImg(data.toString()),_canDrag,_canReceiveDrag,_canDoubleClick);
               });
            }
         }
         else if(data is BitmapData)
         {
            this.setData(new Bitmap(data),this._canDrag,this._canReceiveDrag,this._canDoubleClick);
         }
         else if(data is DisplayObject)
         {
            this._content = addChild(data);
            if(this._effect)
            {
               addChild(this._effect);
            }
         }
         if(this._canDrag)
         {
            this.buttonMode = true;
            !hasEventListener("mouseDown") && this.addEventListener("mouseDown",this.mouseDownHandler);
         }
      }
      
      public function enableMouseOver(param1:Boolean) : void
      {
         if(param1)
         {
            this.addEventListener("rollOver",this.onRollOverHandler);
            this.addEventListener("rollOut",this.onRollOutHandler);
         }
      }
      
      protected function setDefaultOverHandler() : void
      {
      }
      
      protected function setDefaultDropHandler() : void
      {
         if(this._canReceiveDrag)
         {
            this._id = 0;
            this.setData(null,true,true);
            this.stopDropFun && this.stopDropFun();
         }
      }
      
      private function createDragSp() : DragSprite
      {
         var _loc1_:DragSprite = new DragSprite();
         var _loc2_:Bitmap = new Bitmap(Bitmap(DisplayObject(this._content)).bitmapData.clone());
         _loc1_.sourceSp = this;
         _loc2_.x = -_loc2_.width / 2;
         _loc2_.y = -_loc2_.height / 2;
         _loc1_.addChild(_loc2_);
         _loc1_.x = Core.mainView.mouseX;
         _loc1_.y = Core.mainView.mouseY;
         return _loc1_;
      }
      
      public function dragHandler(param1:String, param2:DragSprite, param3:IDropable) : void
      {
         var _loc4_:RuneStoneDecorationIcon = null;
         var _loc5_:RuneStoneDecorationIcon = null;
         var _loc6_:* = param1;
         if("dragDrop" !== _loc6_)
         {
            return;
         }
         if(param2.sourceSp == this)
         {
            this.removeFilters();
            addEventListener("mouseDown",this.mouseDownHandler);
            param2.dragOver();
            return;
         }
         _loc4_ = param2.sourceSp as RuneStoneDecorationIcon;
         _loc5_ = param3 as RuneStoneDecorationIcon;
         this.dealDropFun && this.dealDropFun(_loc4_,_loc5_);
         param2.sourceSp.dragOver();
         param2.dragOver();
      }
      
      public function addFilters() : void
      {
         this._content.filters = [FilterLib.enbaleFilter];
      }
      
      public function removeFilters() : void
      {
         this._content.filters = [];
      }
      
      public function getData() : Object
      {
         return this._content;
      }
      
      public function dragOver() : void
      {
         this.removeFilters();
         this.addEventListener("mouseDown",this.mouseDownHandler);
         this.dispatchEvent(new Event("game.modules.rideDecoration.view.component.stopDrag"));
      }
      
      public function clear() : void
      {
         if(this._content is MovieClip)
         {
            MovieClip(this._content).stop();
         }
         this._content && this._content.parent && removeChild(this._content);
      }
      
      override public function dispose() : void
      {
         this.clear();
         super.dispose();
      }
      
      public function clone() : Bitmap
      {
         if(!this._content)
         {
            return null;
         }
         return new Bitmap((this._content as Bitmap).bitmapData.clone());
      }
      
      public function get toolTipData() : Object
      {
         var _loc1_:EquipPositionInfo = DecorationManager.getPositionInfoById(this._positionId);
         return _loc1_.positionName;
      }
      
      public function get toolTipContentClass() : Class
      {
         return SingleTextToolTipContent;
      }
      
      public function get id() : int
      {
         return this._id;
      }
      
      public function set id(param1:int) : void
      {
         this._id = param1 <= 0 ? 0 : param1;
      }
      
      public function get positionId() : int
      {
         return this._positionId;
      }
      
      public function set positionId(param1:int) : void
      {
         this._positionId = param1;
      }
      
      public function updateBg(param1:UISkin = null) : void
      {
         if(this._bg)
         {
            this._bg.parent && this._bg.parent.removeChild(this._bg);
            this._bg.dispose();
            this._bg = null;
         }
         if(param1)
         {
            this._bg = param1;
         }
         else
         {
            this._bg = UIManager.getUISkin("formation_bg");
         }
         this._bg.setSize(48,48);
         this.addChildAt(this._bg,0);
      }
      
      public function addEffect(param1:int) : void
      {
         if(this._effect)
         {
            this._effect.gotoAndStop(1);
            this._effect.parent && this._effect.parent.removeChild(this._effect);
            this._effect = null;
         }
         if(param1 < 5)
         {
            return;
         }
         this._effect = AssetManager.getMc("DecorationQuality7");
         if(this._effect)
         {
            this._effect.gotoAndPlay(1);
            if(param1 == 5)
            {
               this._effect.x = -10;
            }
            else if(param1 == 6 || param1 == 7)
            {
               this._effect.x = 24;
               this._effect.y = 22;
            }
         }
      }
      
      public function set isShowAdd(param1:Boolean) : void
      {
         this._isShowAddProperty = param1;
      }
      
      public function set dir(param1:String) : void
      {
         this._dir = param1;
      }
      
      public function showSelect(param1:Boolean) : void
      {
         this._overSkin.visible = param1;
      }
   }
}

