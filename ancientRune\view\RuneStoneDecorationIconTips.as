package ancientRune.view
{
   import flash.geom.Point;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.data.ancientRune.manager.RuneStoneColorManager;
   import game.data.ancientRune.manager.RuneStoneInfo;
   import game.data.ancientRune.manager.RuneStoneXmlManager;
   import game.items.ItemQualityInfo;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.manager.XmlManager;
   import game.xmlParsers.affix.AffixManager;
   import game.xmlParsers.affix.IAffix;
   import mmo.Core;
   import mmo.ext.color.ColorLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class RuneStoneDecorationIconTips extends UISprite
   {
      private var _bg:UISkin;
      
      private var _iconBg:UISkin;
      
      private var _icon:Icon;
      
      public function RuneStoneDecorationIconTips()
      {
         super();
         this.initUI();
      }
      
      private function initUI() : void
      {
         this._bg = UIManager.getUISkin("info_bg");
         this._bg.width = 200;
         this.addChild(this._bg);
         this._icon = new Icon();
         this._icon.x = 145;
         this._icon.y = 4;
      }
      
      private function parseColor(param1:int) : uint
      {
         var _loc2_:int = 0;
         switch(param1)
         {
            case 0:
               _loc2_ = 16775920;
               break;
            case 1:
               _loc2_ = 65407;
               break;
            case 2:
               _loc2_ = 49151;
               break;
            case 3:
               _loc2_ = 16776960;
               break;
            case 4:
               _loc2_ = 14423100;
               break;
            case 5:
               _loc2_ = 9699539;
               break;
            case 6:
               _loc2_ = 16747520;
         }
         return _loc2_;
      }
      
      private function addItem(param1:String, param2:int, param3:int, param4:TextFormat, param5:uint = 0, param6:Boolean = false, param7:int = 0) : Label
      {
         param4.leading = param7;
         var _loc8_:Label = new Label("",param4,[]);
         param4.leading = 0;
         if(param5)
         {
            _loc8_.textColor = param5;
         }
         _loc8_.multiline = param6;
         _loc8_.wordWrap = param6;
         _loc8_.selectable = false;
         if(param6)
         {
            _loc8_.width = 175;
         }
         _loc8_.htmlText = param1;
         this.addChild(_loc8_);
         _loc8_.x = param2;
         _loc8_.y = param3;
         _loc8_.height = _loc8_.textHeight;
         return _loc8_;
      }
      
      public function setData(param1:int, param2:int, param3:Point, param4:String = "left") : void
      {
         var _loc15_:int = 0;
         var _loc25_:UISkin = null;
         var _loc7_:Array = null;
         var _loc24_:String = null;
         var _loc9_:UISkin = null;
         var _loc12_:XML = null;
         var _loc11_:int = 0;
         var _loc16_:Number = NaN;
         var _loc27_:Array = null;
         var _loc10_:IAffix = null;
         var _loc17_:int = 0;
         var _loc19_:uint = 0;
         var _loc13_:Label = null;
         var _loc18_:String = null;
         var _loc21_:XML = null;
         var _loc26_:int = 0;
         var _loc23_:Object = MainData.getInstance().ancientRuneData.stone[param2];
         var _loc6_:RuneStoneInfo = RuneStoneXmlManager.getRuneStoneInfoById(param2);
         var _loc14_:int = 30;
         this._iconBg = RuneStoneSlotSp.getQualityBorder(_loc23_.quality);
         this._iconBg.x = 145;
         this._iconBg.y = 4;
         this.addChild(this._iconBg);
         var _loc22_:int = int(_loc23_.quality);
         var _loc8_:int = int(_loc23_.level);
         if(param4 == "right")
         {
            if(_loc8_ >= _loc22_ * 10)
            {
               _loc22_++;
               _loc8_ = 0;
            }
            else
            {
               _loc8_++;
            }
         }
         this._icon.setData(UrlManager.getAncientRuneUrl(_loc6_.stoneUrl + _loc23_.quality));
         this.addChild(this._icon);
         this.addItem(_loc6_.stoneName,5,5,TextFormatLib.format_0xFFB932_14px,16776960);
         this.addItem(Globalization.getString("infoMc.22"),5,_loc14_,TextFormatLib.format_0xFFF5CE_12px);
         _loc14_ += this.addItem(_loc6_.stoneName,50,_loc14_,TextFormatLib.format_0xFFF5CE_12px).textHeight + 5;
         this.addItem(Globalization.getString("infoMc.2"),5,_loc14_,TextFormatLib.format_0xFFF5CE_12px);
         _loc14_ += this.addItem(RuneStoneColorManager.getQualityName(_loc22_,_loc8_),50,_loc14_,TextFormatLib.white_12px,ItemQualityInfo.getQualityColor(1)).textHeight + 5;
         _loc14_ = _loc14_ + (this.addItem(Globalization.getString("infoMc.41"),5,_loc14_,TextFormatLib.format_0xFFB932_12px).textHeight + 5);
         var _loc20_:Array = _loc6_.attrType;
         while(_loc15_ < _loc20_.length)
         {
            _loc18_ = "";
            _loc24_ = _loc6_.getAttrNum(_loc15_,_loc22_,_loc8_);
            if(int(_loc20_[_loc15_]) == 1)
            {
               _loc18_ += StringUtil.substitute(Globalization.getString("decotation.18"),_loc24_) + "\n";
            }
            if(int(_loc20_[_loc15_]) == 6)
            {
               _loc18_ += StringUtil.substitute(Globalization.getString("decotation.19"),_loc24_) + "\n";
            }
            if(int(_loc20_[_loc15_]) == 9)
            {
               _loc18_ += StringUtil.substitute(Globalization.getString("decotation.20"),_loc24_) + "\n";
            }
            if(int(_loc20_[_loc15_]) == 7)
            {
               _loc18_ += StringUtil.substitute(Globalization.getString("decotation.21"),_loc24_) + "\n";
            }
            if(int(_loc20_[_loc15_]) == 10)
            {
               _loc18_ += StringUtil.substitute(Globalization.getString("decotation.22"),_loc24_) + "\n";
            }
            if(int(_loc20_[_loc15_]) == 8)
            {
               _loc18_ += StringUtil.substitute(Globalization.getString("decotation.23"),_loc24_) + "\n";
            }
            if(int(_loc20_[_loc15_]) == 11)
            {
               _loc18_ += StringUtil.substitute(Globalization.getString("decotation.24"),_loc24_) + "\n";
            }
            _loc14_ += this.addItem(_loc18_,25,_loc14_,TextFormatLib.format_0xFFB932_12px).textHeight + 5;
            _loc15_ += 1;
         }
         _loc25_ = UIManager.getUISkin("line2");
         _loc25_.width = 170;
         _loc25_.x = 5;
         _loc25_.y = _loc14_;
         _loc14_ += 6;
         addChild(_loc25_);
         _loc15_ = 1;
         var _loc5_:int = 0;
         while(_loc15_ <= _loc23_.bore)
         {
            if(_loc23_.rune[_loc15_])
            {
               _loc5_++;
            }
            _loc15_++;
         }
         _loc14_ += this.addItem(StringUtil.substitute("[符文绘制]： {0}/{1}   ",_loc5_,_loc23_.bore),5,_loc14_,TextFormatLib.format_0xFFF5CE_12px,ColorLib.purple).textHeight + 5;
         _loc15_ = 1;
         while(_loc15_ <= _loc23_.bore)
         {
            if(_loc23_.rune[_loc15_])
            {
               _loc21_ = XmlManager.ancientRuneStoneAttrXml.children()[int(_loc23_.rune[_loc15_]["id"])];
               _loc11_ = int(_loc21_.@attrType);
               _loc16_ = Number(String(_loc21_.@attrNum).split(";")[_loc23_.rune[_loc15_]["quality"]]);
               if(_loc23_.rune[_loc15_]["id"] == param2)
               {
                  _loc16_ *= 1;
               }
               else
               {
                  _loc16_ *= 1;
               }
               _loc26_ = 0;
               if(_loc23_["borelevel"][_loc15_])
               {
                  _loc26_ = int(_loc23_["borelevel"][_loc15_].level);
                  _loc16_ *= 1 + _loc26_ / 100;
                  _loc16_ = Number(_loc16_.toFixed(2));
               }
               _loc10_ = AffixManager.creatAffix(_loc11_,_loc16_);
               _loc19_ = this.parseColor(_loc23_.rune[_loc15_].quality);
               _loc18_ = _loc10_.print();
               _loc14_ += this.addItem("lv." + _loc26_ + " " + _loc21_.@name + " " + _loc18_,10,_loc14_,TextFormatLib.format_0xDE23AA_12px,_loc19_).textHeight + 5;
            }
            else
            {
               _loc19_ = ItemQualityInfo.getQualityColor(9);
               _loc14_ += this.addItem("未绘制符文",25,_loc14_,TextFormatLib.format_0xDE23AA_12px,_loc19_).textHeight + 5;
            }
            _loc15_ += 1;
         }
         _loc25_ = UIManager.getUISkin("line2");
         _loc25_.width = 170;
         _loc25_.x = 5;
         _loc25_.y = _loc14_;
         _loc14_ += 6;
         addChild(_loc25_);
         _loc14_ += this.addItem("这是来自天界阿卡丽送你的一个牛逼符石",5,_loc14_,TextFormatLib.format_0xFFF5CE_12px,0,true).textHeight + 5;
         this._bg.height = _loc14_ + 5;
         this.x = param3.x;
         this.y = param3.y;
         if(param3.y + this.height > Core.stgH)
         {
            this.y = param3.y - this.height + 53;
         }
         if(param3.x + this.width > Core.stgW)
         {
            this.x = param3.x - this.width - 48;
         }
         this.x < 0 && (this.x = 0);
         this.y < 0 && (this.y = 0);
         Core.mainView.addChild(this);
      }
   }
}

