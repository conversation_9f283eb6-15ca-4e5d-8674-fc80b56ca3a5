package ancientRune.view
{
   import ancientRune.view.boreStreng.BoreStrengPanel;
   import ancientRune.view.draw.RuneStoneDrawPanel;
   import ancientRune.view.streng.RuneStoneStrengPanel;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.manager.UIManager;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.tab.TabPane;
   import mmo.ui.event.TabEvent;
   import util.Globalization;
   
   public class RuneStonePanel extends UISprite
   {
      private var _tab:TabPane;
      
      private var _nowPos:int;
      
      private var _rightLayer:Sprite;
      
      public var _attributeSp:RuneStoneAttrSp;
      
      private var _suitSp:RuneStoneSuitSp;
      
      public var strengthUI:RuneStoneStrengPanel;
      
      public var drawUI:RuneStoneDrawPanel;
      
      public var boreStrengUI:BoreStrengPanel;
      
      public var helpBtn:Button;
      
      public function RuneStonePanel()
      {
         super();
         this.isLive = false;
      }
      
      public function initUI() : void
      {
         this._nowPos = 0;
         this.helpBtn = new Button(Globalization.getString("Globalization.60"),null,60,UIManager.getMultiUISkin("btn_topMenu"));
         this.helpBtn.setTextOffset(0,-1);
         this.helpBtn.x = 598;
         this.helpBtn.y = 18;
         this.addChild(this.helpBtn);
         this._rightLayer = new Sprite();
         this._rightLayer.x = 360;
         this._rightLayer.y = 35;
         this.addChild(this._rightLayer);
         this.helpBtn.addEventListener("click",this.onOpenWindowHandler);
         this._tab = new TabPane(["刻印","符文","强化"],0,56);
         this._tab.y = 6;
         this._tab.addEventListener(TabEvent.Tab_IndexChange,this.onTapChangeHandler);
         this._rightLayer.addChild(this._tab);
         this._attributeSp = new RuneStoneAttrSp();
         this._attributeSp.x = 0;
         this._attributeSp.y = 261;
         this.addChild(this._attributeSp);
         this._suitSp = new RuneStoneSuitSp();
         this.addChild(this._suitSp);
         this._suitSp.strengthHandler = this.posStrentgth;
         this._suitSp.drawHandler = this.posDraw;
         this._suitSp.boreStrenngHandler = this.boreStrength;
         this.strengthUI = new RuneStoneStrengPanel();
         this.strengthUI.y = 5;
         this._rightLayer.addChild(this.strengthUI);
         this.strengthUI.updateStrengthData(0);
         this.drawUI = new RuneStoneDrawPanel();
         this.drawUI.y = 5;
         this.boreStrengUI = new BoreStrengPanel();
         this.boreStrengUI.y = 5;
         this._suitSp.showClickIcon(1);
      }
      
      public function refreshSuit() : void
      {
      }
      
      private function onOpenWindowHandler(param1:MouseEvent) : void
      {
         var _loc2_:* = null;
         var _loc3_:* = param1.target;
         if(this.helpBtn === _loc3_)
         {
            _loc2_ = new HelpWin();
            PopUpCenter.addPopUp("HelpWin",_loc2_,true,true);
         }
      }
      
      private function onTapChangeHandler(param1:Event) : void
      {
         if(this._tab.selectedIndex == 0)
         {
            this.drawUI.parent && this.drawUI.parent.removeChild(this.drawUI);
            this.boreStrengUI.parent && this.boreStrengUI.parent.removeChild(this.boreStrengUI);
            this._rightLayer.addChild(this.strengthUI);
            this._suitSp.showClickIcon(1);
            this.strengthUI.updateStrengthData(this._nowPos);
            this._suitSp.setSelect(0);
            this._suitSp.visible = true;
         }
         else if(this._tab.selectedIndex == 1)
         {
            this.strengthUI.parent && this.strengthUI.parent.removeChild(this.strengthUI);
            this.boreStrengUI.parent && this.boreStrengUI.parent.removeChild(this.boreStrengUI);
            this._rightLayer.addChild(this.drawUI);
            this._suitSp.showClickIcon(2);
            this.drawUI.initData(this._nowPos);
            this._suitSp.visible = true;
         }
         else if(this._tab.selectedIndex == 2)
         {
            this.strengthUI.parent && this.strengthUI.parent.removeChild(this.strengthUI);
            this.drawUI.parent && this.drawUI.parent.removeChild(this.drawUI);
            this._suitSp.showClickIcon(3);
            this._rightLayer.addChild(this.boreStrengUI);
            this.boreStrengUI.initData(this._nowPos);
            this._suitSp.visible = true;
         }
      }
      
      public function posStrentgth(param1:int) : void
      {
         this._nowPos = param1;
         this.strengthUI.updateStrengthData(param1);
      }
      
      public function posDraw(param1:int) : void
      {
         this._nowPos = param1;
         this.drawUI.initData(param1);
      }
      
      public function boreStrength(param1:int) : void
      {
         this._nowPos = param1;
         this.boreStrengUI.initData(param1);
      }
      
      public function posDrawRefresh(param1:int) : void
      {
         this.drawUI.initData(param1);
      }
      
      public function posDrawReplace(param1:Object, param2:int, param3:int) : void
      {
         this.drawUI.replaceInit(param1,param2,param3);
      }
      
      public function posDrawReplaceOk() : void
      {
         this.drawUI.replaceOk();
      }
      
      public function boreStrengUpdate(param1:int = 1) : void
      {
         this.boreStrengUI.update(param1);
      }
   }
}

