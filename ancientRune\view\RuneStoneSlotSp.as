package ancientRune.view
{
   import flash.display.Bitmap;
   import flash.display.Sprite;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.manager.XmlManager;
   import mmo.Core;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   
   public class RuneStoneSlotSp extends Sprite
   {
      public var _itemImg:Bitmap;
      
      public var _slot_back:UISkin;
      
      public var _slotbg:UISkin;
      
      public var _itemSp:UISprite;
      
      public var _data:Object;
      
      public var _url:String;
      
      public var _xml:XML;
      
      private var _positionId:int;
      
      public function RuneStoneSlotSp(param1:int)
      {
         super();
         doubleClickEnabled = false;
         this._slotbg = UIManager.getUISkin("slot_bg");
         addChild(this._slotbg);
         this._itemSp = new UISprite();
         addChild(this._itemSp);
         this.setSize(48,48);
         this._xml = XmlManager.ancientRuneStoneXml.children()[param1];
         this._positionId = param1;
      }
      
      public static function getQualityBorder(param1:int) : UISkin
      {
         var _loc2_:UISkin = null;
         switch(param1 - 1)
         {
            case 0:
               _loc2_ = UIManager.getUISkin("white");
               break;
            case 1:
               _loc2_ = UIManager.getUISkin("green");
               break;
            case 2:
               _loc2_ = UIManager.getUISkin("blue");
               break;
            case 3:
               _loc2_ = UIManager.getUISkin("orange");
               break;
            case 4:
               _loc2_ = UIManager.getUISkin("red");
               break;
            case 5:
               _loc2_ = UIManager.getUISkin("purple");
               break;
            case 6:
               _loc2_ = UIManager.getUISkin("sunGold");
               break;
            case 7:
               _loc2_ = UIManager.getUISkin("darkGold");
               break;
            case 8:
               _loc2_ = UIManager.getUISkin("darkGold");
         }
         return _loc2_;
      }
      
      private function setQualityBox(param1:int) : void
      {
         this._slot_back = getQualityBorder(param1);
         this._slot_back && this._itemSp.addChildAt(this._slot_back,0);
      }
      
      public function setSize(param1:int, param2:int) : void
      {
         super.width = param1;
         super.height = param2;
      }
      
      public function initData(param1:Object, param2:int) : void
      {
         this._data = param1;
         this._url = String(this._xml.@url) + this._data.quality;
         this._itemSp.setToolTip(String(this._xml.@name),"rightMiddle");
         this.setQualityBox(int(this._data.quality));
         this.freshInfo();
      }
      
      private function complete_Handler() : void
      {
         this._itemImg ||= new Bitmap(Core.dataLib.getImgData(UrlManager.getAncientRuneUrl(this._url)));
         if(this._itemSp.numChildren > 0)
         {
            this._itemImg.parent || this._itemSp.addChildAt(this._itemImg,1);
         }
      }
      
      private function freshInfo() : void
      {
         if(Core.dataLib.chkData(this._url))
         {
            if(!this._itemImg)
            {
               this._itemImg = new Bitmap(Core.dataLib.getImgData(this._url));
               this._itemImg.parent || this._itemSp.addChildAt(this._itemImg,1);
            }
         }
         else
         {
            Core.dataLib.load([UrlManager.getAncientRuneUrl(this._url)],this.complete_Handler);
         }
      }
      
      public function get positionId() : int
      {
         return this._positionId;
      }
      
      public function set positionId(param1:int) : void
      {
         this._positionId = param1;
      }
   }
}

