package ancientRune.view
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   
   public class RuneStoneSuitSp extends Sprite
   {
      public var _bgMiddle:MovieClip;
      
      public var _baseBg:Sprite;
      
      private var _iconArr:Array;
      
      public var strengthHandler:Function;
      
      public var drawHandler:Function;
      
      public var boreStrenngHandler:Function;
      
      private var _itemAbsorb:RuneStoneAbsorb;
      
      public function RuneStoneSuitSp()
      {
         super();
         this._iconArr = [];
         this.initUI();
      }
      
      private function initUI() : void
      {
         var _loc1_:RuneStoneSlotSp = null;
         var _loc2_:UISkin = UIManager.getUISkin("ancient.stoneBg");
         this.addChild(_loc2_);
         _loc2_.y = 10;
         this._bgMiddle = AssetManager.getMc("ancientRune.stoneBgMiddle");
         this.addChild(this._bgMiddle);
         this._bgMiddle.x = 180;
         this._bgMiddle.y = 140;
         this._bgMiddle.addEventListener("click",this.onClickHeart);
         this._baseBg = new Sprite();
         this.addChild(this._baseBg);
         var _loc3_:int = 0;
         while(_loc3_ < 8)
         {
            _loc1_ = new RuneStoneSlotSp(_loc3_);
            _loc1_.x = _loc3_ % 2 == 0 ? 0 : 272;
            _loc1_.x += 15;
            _loc1_.y = int(_loc3_ / 2) * 65;
            _loc1_.y += 15;
            trace("FFFFFFFFFFFFFFF+" + MainData.getInstance().ancientRuneData.stone);
            _loc1_.initData(MainData.getInstance().ancientRuneData.stone[_loc3_],_loc3_);
            this._iconArr.push(_loc1_);
            this._baseBg.addChild(_loc1_);
            _loc3_++;
         }
      }
      
      private function onClickHeart(param1:MouseEvent) : void
      {
         var _loc7_:Object = null;
         this._itemAbsorb = new RuneStoneAbsorb();
         var _loc2_:uint = 0;
         var _loc8_:uint = 0;
         var _loc3_:uint = 0;
         var _loc11_:uint = 0;
         var _loc10_:Object = {};
         var _loc9_:int = 0;
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:Array = MainData.getInstance().ancientRuneData.runeIdsArray;
         trace("ffffffffffffffffffffffL:" + _loc5_);
         while(_loc6_ < _loc5_.length)
         {
            _loc2_ = uint(_loc6_);
            _loc8_ = uint(_loc5_[_loc6_]);
            _loc3_ = uint(MainData.getInstance().bagData.userBag.numItems(_loc8_));
            if(_loc3_ >= 1)
            {
               _loc7_ = {
                  "id":_loc2_,
                  "itemId":_loc8_,
                  "num":_loc3_
               };
               _loc10_[_loc6_] = _loc7_;
            }
            _loc6_++;
         }
         PopUpCenter.addPopUp("RuneStoneAbsorb",this._itemAbsorb,true,true,{
            "data":"RuneStoneAbsorb",
            "func":null,
            "choose":_loc10_
         });
      }
      
      private function refreshAll() : void
      {
         var _loc1_:RuneStoneSlotSp = null;
         while(this._baseBg.numChildren > 0)
         {
            this._baseBg.removeChildAt(0);
         }
         var _loc2_:int = 0;
         while(_loc2_ < 8)
         {
            _loc1_ = new RuneStoneSlotSp(_loc2_);
            _loc1_.x = _loc2_ % 2 == 0 ? 0 : 272;
            _loc1_.x += 15;
            _loc1_.y = int(_loc2_ / 2) * 65;
            _loc1_.y += 15;
            _loc1_.initData(MainData.getInstance().ancientRuneData.stone[_loc2_],_loc2_);
            this._iconArr.push(_loc1_);
            this._baseBg.addChild(_loc1_);
            _loc2_++;
         }
      }
      
      private function onStrengthClick(param1:MouseEvent) : void
      {
         var _loc2_:RuneStoneSlotSp = RuneStoneSlotSp(param1.currentTarget);
         this.setSelect(_loc2_.positionId);
         this.strengthHandler && this.strengthHandler(_loc2_.positionId);
      }
      
      private function onDrawClick(param1:MouseEvent) : void
      {
         var _loc2_:RuneStoneSlotSp = RuneStoneSlotSp(param1.currentTarget);
         this.setSelect(_loc2_.positionId);
         this.drawHandler && this.drawHandler(_loc2_.positionId);
      }
      
      private function onBoreStrengClick(param1:MouseEvent) : void
      {
         var _loc2_:RuneStoneSlotSp = RuneStoneSlotSp(param1.currentTarget);
         this.setSelect(_loc2_.positionId);
         this.boreStrenngHandler && this.boreStrenngHandler(_loc2_.positionId);
      }
      
      public function showClickIcon(param1:int) : void
      {
         var _loc2_:RuneStoneSlotSp = null;
         for each(_loc2_ in this._iconArr)
         {
            _loc2_.buttonMode = true;
            if(param1 == 1)
            {
               _loc2_.removeEventListener("click",this.onDrawClick);
               _loc2_.removeEventListener("click",this.onBoreStrengClick);
               _loc2_.addEventListener("click",this.onStrengthClick);
            }
            else if(param1 == 2)
            {
               _loc2_.removeEventListener("click",this.onStrengthClick);
               _loc2_.removeEventListener("click",this.onBoreStrengClick);
               _loc2_.addEventListener("click",this.onDrawClick);
            }
            else if(param1 == 3)
            {
               _loc2_.removeEventListener("click",this.onStrengthClick);
               _loc2_.removeEventListener("click",this.onDrawClick);
               _loc2_.addEventListener("click",this.onBoreStrengClick);
            }
         }
      }
      
      public function setSelect(param1:int) : void
      {
      }
   }
}

