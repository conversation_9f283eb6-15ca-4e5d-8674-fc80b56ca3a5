package ancientRune.view.boreStreng
{
   import ancientRune.view.draw.RuneStoneDrawItem;
   import ancientRune.view.draw.RuneStoneDrawPropertyMC;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.events.PageNavigatorEvent;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   
   public class BoreStrengPanel extends Sprite
   {
      public static const MAX_PROPERTY_NUM:int = 6;
      
      public static const MAX_PAGE:int = 10;
      
      public static const BG_WIDTH:int = 250;
      
      public static const BG_WIDTH2:int = 295;
      
      public static const BG_HEIGHT:int = 370;
      
      public var bore:int;
      
      public var _drawPos:int;
      
      public var _drawClickPos:int;
      
      public var _boreClickPos:int;
      
      public var boreLevel:int;
      
      public var boreCost:int;
      
      private var _stoneSlot:RuneStoneDrawItem;
      
      private var _treasureName:Label;
      
      private var _propertyLabel:Label;
      
      private var _upBtn:Button;
      
      private var _propertyMcArr:Array;
      
      public var changePropertyFun:Function;
      
      private var _box:UISprite;
      
      private var _costMc1:CostComponent;
      
      private var _costMc2:CostComponent;
      
      public var pageNavigator:PageNavigator;
      
      public var nowPage:int;
      
      public function BoreStrengPanel()
      {
         super();
         this.initUI();
      }
      
      private function initUI() : void
      {
         this._propertyMcArr = [];
         var _loc1_:UISkin = UIManager.getUISkin("board_bg");
         _loc1_.setSize(295,370);
         this.addChild(_loc1_);
         this._stoneSlot = new RuneStoneDrawItem();
         this._stoneSlot.x = 70;
         this._stoneSlot.y = 8;
         this.addChild(this._stoneSlot);
         this._treasureName = new Label("测试符石",TextFormatLib.format_0xffb932_12px);
         this._treasureName.x = 124;
         this._treasureName.y = 22;
         this.pageNavigator = new PageNavigator();
         addChild(this.pageNavigator);
         this.pageNavigator.x = 100;
         this.pageNavigator.y = 250;
         this.pageNavigator.addEventListener("pageChange",this.changePage_Handler);
         this.pageNavigator.init(1,10);
         this.addChild(this._treasureName);
         var _loc3_:UISkin = UIManager.getUISkin("line2");
         _loc3_.width = 285;
         _loc3_.x = 6;
         _loc3_.y = 80;
         addChild(_loc3_);
         var _loc2_:UISkin = UIManager.getUISkin("line2");
         _loc2_.width = 285;
         _loc2_.x = 6;
         _loc2_.y = 275;
         addChild(_loc2_);
         this._upBtn = new Button("升级",null,70,UIManager.getMultiUISkin("button_big"));
         this._upBtn.x = 115;
         this._upBtn.y = 330;
         this.addChild(this._upBtn);
         this._upBtn.visible = false;
         this._box = new UISprite();
         addChild(this._box);
         this._upBtn.addEventListener("click",this.upHandle);
         this._costMc1 = new CostComponent("经验");
         this._costMc1.x = 80;
         this._costMc1.y = 280;
         addChild(this._costMc1);
         this._costMc2 = new CostComponent("消耗");
         this._costMc2.x = 80;
         this._costMc2.y = 305;
         addChild(this._costMc2);
         this.nowPage = 1;
      }
      
      private function changePage_Handler(param1:PageNavigatorEvent) : void
      {
         this.nowPage = param1.currentPage;
         this.initData(this._drawPos);
      }
      
      private function upHandle(param1:MouseEvent) : void
      {
         var pos:int = 0;
         var layer:int = 0;
         pos = this._drawPos;
         layer = this._drawClickPos + 1;
         PopUpCenter.confirmWin("是否消耗" + this.boreCost + "经验进行升级？",(function():*
         {
            var confirmOpen:Function;
            return confirmOpen = function():void
            {
               if(MainData.getInstance().ancientRuneData.boreExp < boreCost)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":"经验不足！！",
                     "textFormat":TextFormatLib.format_0xFF0000_12px
                  });
                  return;
               }
               AppFacade.instance.sendNotification("CS_ANCIENT_RUNE_HOLE_STRENGTHEN",{
                  "pos":pos,
                  "layer":layer
               });
            };
         })(),null,0,true);
      }
      
      private function onClickUp(param1:MouseEvent) : void
      {
         var _loc4_:int = 0;
         this._costMc2.updateLabelValue("");
         var _loc3_:RuneStoneDrawPropertyMC = null;
         if(this._drawClickPos >= 0)
         {
            _loc3_ = this._propertyMcArr[this._drawClickPos % 6];
            _loc3_.propertyCheckBox.isCheck = false;
         }
         var _loc2_:RuneStoneDrawPropertyMC = RuneStoneDrawPropertyMC(param1.target.parent);
         if(_loc2_.pos == this._drawClickPos)
         {
            this._drawClickPos = -1;
            _loc2_.propertyCheckBox.isCheck = false;
            this._upBtn.visible = false;
         }
         else
         {
            this.boreLevel = _loc2_.boreStrengLevel;
            _loc4_ = Math.floor(this.boreLevel / 10);
            this.boreCost = MainData.getInstance().ancientRuneData.boreNeedExpArray[_loc4_];
            this._costMc2.updateLabelValue(String(this.boreCost));
            this._drawClickPos = _loc2_.pos;
            _loc2_.propertyCheckBox.isCheck = true;
            this._upBtn.visible = true;
         }
      }
      
      public function update(param1:int = 1) : void
      {
         if(param1)
         {
            this._costMc1.updateLabelValue(String(MainData.getInstance().ancientRuneData.boreExp));
            this.boreLevel++;
            param1 = Math.floor(this.boreLevel / 10);
            this.boreCost = MainData.getInstance().ancientRuneData.boreNeedExpArray[param1];
            this._costMc2.updateLabelValue(String(this.boreCost));
            this.refreshMc();
         }
         else
         {
            this._costMc1.updateLabelValue(String(MainData.getInstance().ancientRuneData.boreExp));
         }
      }
      
      public function initData(param1:int) : void
      {
         this._drawClickPos = -1;
         this._upBtn.visible = false;
         var _loc3_:RuneStoneDrawPropertyMC = null;
         var _loc2_:* = (this.nowPage - 1) * 6;
         this._propertyMcArr = [];
         while(_loc2_ < this.nowPage * 6)
         {
            _loc3_ = new RuneStoneDrawPropertyMC(_loc2_ + 1);
            _loc3_.x = 7;
            _loc3_.y = 90 + _loc2_ % 6 * 20;
            this._propertyMcArr.push(_loc3_);
            _loc2_++;
         }
         this._treasureName.text = String(XmlManager.ancientRuneStoneXml.children()[param1].@name);
         this._stoneSlot.initData(param1);
         this._drawPos = param1;
         this.bore = MainData.getInstance().ancientRuneData.stone[param1].bore;
         this.showPropertyMc();
         this._costMc1.updateLabelValue(String(MainData.getInstance().ancientRuneData.boreExp));
      }
      
      private function refreshMc() : void
      {
         var _loc1_:RuneStoneDrawPropertyMC = null;
         var _loc2_:int = (this.nowPage - 1) * 6;
         while(_loc2_ < this.nowPage * 6)
         {
            _loc1_ = this._propertyMcArr[_loc2_ % 6];
            if(_loc2_ < this.bore)
            {
               _loc1_.showProperty(3,this._drawPos);
            }
            _loc2_++;
         }
      }
      
      private function showPropertyMc() : void
      {
         this._box.clearAllChild(this._box);
         var _loc1_:RuneStoneDrawPropertyMC = null;
         var _loc2_:int = (this.nowPage - 1) * 6;
         while(_loc2_ < this.nowPage * 6)
         {
            _loc1_ = this._propertyMcArr[_loc2_ % 6];
            if(_loc2_ < this.bore)
            {
               _loc1_.showProperty(3,this._drawPos);
               _loc1_.propertyCheckBox.addEventListener("click",this.onClickUp);
               this._box.addChild(_loc1_);
            }
            else
            {
               _loc1_.parent && _loc1_.parent.removeChild(_loc1_);
            }
            _loc2_++;
         }
      }
   }
}

import flash.display.Sprite;
import game.manager.UIManager;
import mmo.ext.font.TextFormatLib;
import mmo.ui.control.UISkin;
import mmo.ui.control.label.Label;

class CostComponent extends Sprite
{
   public var elementLable:Label;
   
   private var _labelValue:Label;
   
   public function CostComponent(param1:String, param2:String = "")
   {
      var _loc4_:UISkin = null;
      super();
      var _loc3_:UISkin = UIManager.getUISkin("level_bg");
      _loc3_.setSize(160,20);
      addChild(_loc3_);
      this.elementLable = new Label(param1,TextFormatLib.format_0xFFB932_12px);
      addChild(this.elementLable);
      if(param2 != "")
      {
         _loc4_ = UIManager.getUISkin(param2);
         _loc4_.x = 3;
         _loc4_.y = 1;
         addChild(_loc4_);
         this.elementLable.x = 20;
      }
      else
      {
         this.elementLable.width = 60;
         this.elementLable.autoSize = "center";
      }
      this._labelValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center);
      this._labelValue.x = 70;
      this._labelValue.width = 70;
      this._labelValue.autoSize = "center";
      addChild(this._labelValue);
   }
   
   public function updateLabelValue(param1:String) : void
   {
      this._labelValue.text = param1;
   }
}
