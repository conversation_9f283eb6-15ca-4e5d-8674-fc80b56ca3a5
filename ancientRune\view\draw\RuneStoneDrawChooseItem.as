package ancientRune.view.draw
{
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.RadioButton;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   
   public class RuneStoneDrawChooseItem extends UISprite
   {
      public var item:Slot;
      
      public var chooseBtn:RadioButton;
      
      public var itemId:String;
      
      public var num:int;
      
      public var id:int;
      
      public function RuneStoneDrawChooseItem()
      {
         super();
         this.item = new Slot();
         this.item.x = 20;
         this.item.y = 20;
         addChild(this.item);
         this.chooseBtn = new RadioButton("");
         this.chooseBtn.x = 35;
         this.chooseBtn.y = 72;
         addChild(this.chooseBtn);
         this.chooseBtn.gpName = "choose";
      }
      
      public function setData(param1:Object, param2:int = 0) : void
      {
         var _loc3_:SlotTemplete = new SlotTemplete();
         _loc3_.tempID = param1.itemId;
         this.itemId = param1.itemId;
         this.num = param1.num;
         this.id = param1.id;
         this.item.setItem(_loc3_,false,false,false,false);
         this.item._itemNum_txt.text = String(param1.num);
         if(param2 == 0)
         {
            this.chooseBtn.isCheck = true;
         }
         else
         {
            this.chooseBtn.isCheck = false;
         }
      }
   }
}

