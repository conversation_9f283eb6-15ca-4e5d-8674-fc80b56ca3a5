package ancientRune.view.draw
{
   import flash.events.MouseEvent;
   import flash.utils.ByteArray;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.window.PopUpWindow;
   
   public class RuneStoneDrawChooseWin extends PopUpWindow
   {
      public static const NAME:String = "RuneStoneDrawChooseItem";
      
      public var box:UIBox;
      
      public var itemArr:Array = [];
      
      public var data:ByteArray;
      
      public var clearBtn:Button;
      
      public var itemData:Object;
      
      public var _pos:int;
      
      public var chooseId:int;
      
      public var _layer:int;
      
      private var scrollPane:ScrollPane;
      
      public var _container:UISprite;
      
      private var _optional:RuneStoneDrawOptional;
      
      public function RuneStoneDrawChooseWin(param1:int, param2:int)
      {
         super(350,280);
         this.title = "符石选择";
         this._pos = param1;
         this._layer = param2;
         var _loc3_:UISkin = UIManager.getUISkin("group_bg");
         _loc3_.setSize(300,200);
         _loc3_.x = 30;
         _loc3_.y = 38;
         addChild(_loc3_);
         this.scrollPane = new ScrollPane(300,160);
         this.addChild(this.scrollPane);
         this._container = new UISprite();
         this.scrollPane.x = -9;
         this.scrollPane.y = 42;
         this.scrollPane.addToPane(this._container);
         this.scrollPane.updateUI();
         this.box = new UIBox();
         this.box.x = 20;
         this.box.y = -15;
         this.box.rowMaxChildrenNumber = 2;
         this.box.lineMaxChildrenNumber = 4;
         this.box.rowSpace = 20;
         this.box.lineSpace = 10;
         this._container.addChild(this.box);
         this.clearBtn = new Button("绘制",TextFormatLib.format_0xFFB932_12px_songti,85,UIManager.getMultiUISkin("btn_normal"));
         this.clearBtn.x = 150;
         this.clearBtn.y = 240;
         addChild(this.clearBtn);
         this.clearBtn.addEventListener("click",this.clearHandler);
         this._optional = new RuneStoneDrawOptional();
         this._optional.refreshFunc = this.onAutoRefreshCallback;
         this._optional.visible = false;
         this.addChild(this._optional);
      }
      
      private function onAutoRefreshCallback() : void
      {
         this.close();
         AppFacade.instance.sendNotification("CS_ANCIENT_RUNE_DRAW_RUNE",{
            "pos":this._pos,
            "type":this.chooseId,
            "layer":this._layer
         });
      }
      
      private function openOptional() : void
      {
         this._optional.show();
         this._optional.x = 30;
         this._optional.y = 30;
      }
      
      private function clearHandler(param1:MouseEvent) : void
      {
         this.close();
         AppFacade.instance.sendNotification("CS_ANCIENT_RUNE_DRAW_RUNE",{
            "pos":this._pos,
            "type":this.chooseId,
            "layer":this._layer
         });
      }
      
      override public function show(param1:Object) : void
      {
         var _loc2_:* = 0;
         var _loc3_:RuneStoneDrawChooseItem = null;
         while(this.box.numChildren > 0)
         {
            this.box.removeChildAt(0);
         }
         this.itemData = param1.choose;
         var _loc4_:int = 0;
         for(_loc2_ in this.itemData)
         {
            _loc3_ = new RuneStoneDrawChooseItem();
            _loc3_.setData(this.itemData[_loc2_],_loc4_);
            _loc3_.addEventListener("click",this.chooseHandler);
            this.box.addChild(_loc3_);
            if(_loc4_ == 0)
            {
               this.chooseId = int(this.itemData[_loc2_].id);
            }
            _loc4_++;
         }
      }
      
      private function chooseHandler(param1:MouseEvent) : void
      {
         var _loc2_:RuneStoneDrawChooseItem = RuneStoneDrawChooseItem(param1.currentTarget);
         _loc2_.chooseBtn.isCheck = true;
         trace(_loc2_.id);
         this.chooseId = _loc2_.id;
      }
   }
}

