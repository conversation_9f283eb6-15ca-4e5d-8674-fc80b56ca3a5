package ancientRune.view.draw
{
   import ancientRune.view.RuneStoneDecorationIcon;
   import ancientRune.view.RuneStoneSlotSp;
   import flash.display.Sprite;
   import game.data.MainData;
   import game.data.ancientRune.AncientRuneData;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.manager.XmlManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.label.Label;
   
   public class RuneStoneDrawItem extends Sprite
   {
      private var _nameLabel:Label;
      
      private var _icon:RuneStoneDecorationIcon;
      
      private var _levelLabel:Label;
      
      private var _propertyLabel:Label;
      
      public function RuneStoneDrawItem()
      {
         super();
         this._nameLabel = new Label("",TextFormatLib.format_0xebce82_12px);
         this._nameLabel.x = 10;
         this.addChild(this._nameLabel);
         this._icon = new RuneStoneDecorationIcon();
         this._icon.y = 5;
         this.addChild(this._icon);
         this._propertyLabel = new Label("",TextFormatLib.format_Verdana_0xFFB932_12px);
         this._propertyLabel.x = -21;
         this._propertyLabel.y = 94;
         this.addChild(this._propertyLabel);
      }
      
      public function initData(param1:int, param2:String = "left") : void
      {
         var _loc8_:String = null;
         var _loc4_:Number = NaN;
         var _loc3_:AncientRuneData = MainData.getInstance().ancientRuneData;
         var _loc6_:Object = _loc3_.stone[param1];
         var _loc5_:int = 1;
         var _loc7_:String = String(XmlManager.ancientRuneStoneXml.children()[param1].@url) + _loc6_.quality;
         if(_loc5_ == 0)
         {
            this._icon.isShowAdd = false;
            this._icon.id = 0;
            this._icon.positionId = 0;
            this._icon.updateBg(UIManager.getUISkin("decorationPosition" + param1));
            this._icon.addEffect(0);
            this._icon.setData(null);
         }
         else
         {
            this._icon.isShowAdd = true;
            this._icon.id = _loc5_;
            this._icon.positionId = param1;
            _loc8_ = UrlManager.getAncientRuneUrl(_loc7_);
            this._icon.updateBg(RuneStoneSlotSp.getQualityBorder(_loc6_.quailty));
            this._icon.addEffect(_loc6_.quality);
            this._icon.setData(_loc8_);
         }
      }
   }
}

