package ancientRune.view.draw
{
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.items.framework.items.Item;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.label.Label;
   
   public class RuneStoneDrawOptional extends Sprite
   {
      private static const WIDTH:int = 400;
      
      private static const HEIGHT:int = 200;
      
      protected var closeBtn:ImgButton;
      
      private var panelBG:UISkin;
      
      private var rows:Array = [];
      
      private var startBtn:Button;
      
      private var _refreshFunc:Function = null;
      
      private var _selectItem:Item = null;
      
      private var _isRunning:Boolean = false;
      
      public function RuneStoneDrawOptional()
      {
         super();
         this.panelBG = UIManager.getUISkin("win_bg");
         this.panelBG.setSize(400,200);
         this.addChild(this.panelBG);
         var _loc1_:Label = new Label("自定义选择属性",TextFormatLib.format_0xebce82_12px);
         _loc1_.width = 400;
         _loc1_.autoSize = "center";
         _loc1_.y = 3;
         this.addChild(_loc1_);
         this.closeBtn = new ImgButton(UIManager.getMultiUISkin("btn_close2"));
         this.addChild(this.closeBtn);
         this.closeBtn.addEventListener("click",this.onExitBtnClick);
         this.closeBtn.x = this.panelBG.width - this.closeBtn.width - 3;
         this.startBtn = new Button("确定",null,70);
         this.startBtn.x = 400 / 2 - this.startBtn.width / 2;
         this.startBtn.y = 200;
         this.addChild(this.startBtn);
         this.startBtn.addEventListener("click",this.onStartBtnClick);
      }
      
      public function show() : void
      {
         this.visible = true;
         var _loc1_:int = 3;
         this.startBtn.y = _loc1_ * 25 + 45;
         this.panelBG.setSize(400,_loc1_ * 25 + 85);
      }
      
      private function onExitBtnClick(param1:MouseEvent) : void
      {
         this.visible = false;
      }
      
      private function onStartBtnClick(param1:MouseEvent) : void
      {
         this.visible = false;
         this._refreshFunc();
      }
      
      public function get isRunning() : Boolean
      {
         return this._isRunning;
      }
      
      public function get selectedLayers() : Array
      {
         return [];
      }
      
      public function set refreshFunc(param1:Function) : void
      {
         this._refreshFunc = param1;
      }
      
      public function checkStopCondition(param1:Array) : void
      {
      }
   }
}

