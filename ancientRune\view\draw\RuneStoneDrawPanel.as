package ancientRune.view.draw
{
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.events.PageNavigatorEvent;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   
   public class RuneStoneDrawPanel extends Sprite
   {
      public static const MAX_PROPERTY_NUM:int = 6;
      
      public static const MAX_PAGE:int = 3;
      
      public static const BG_WIDTH:int = 250;
      
      public static const BG_WIDTH2:int = 295;
      
      public static const BG_HEIGHT:int = 280;
      
      public var bore:int;
      
      public var _drawPos:int;
      
      public var _drawClickPos:int;
      
      public var _boreClickPos:int;
      
      private var _stoneSlot:RuneStoneDrawItem;
      
      private var _replacePanel:RuneStoneDrawReplacePanel;
      
      private var _treasureName:Label;
      
      private var _propertyLabel:Label;
      
      private var _boreBtn:Button;
      
      private var _drawBtn:Button;
      
      private var _propertyMcArr:Array;
      
      private var _itemSellChoose:RuneStoneDrawChooseWin;
      
      public var changePropertyFun:Function;
      
      private var _box:UISprite;
      
      public var replaceFun:Function;
      
      public var pageNavigator:PageNavigator;
      
      public var nowPage:int;
      
      public function RuneStoneDrawPanel()
      {
         var _loc2_:Label = null;
         var _loc4_:UISkin = null;
         _loc2_ = null;
         var _loc3_:RuneStoneDrawPropertyMC = null;
         this._propertyMcArr = [];
         super();
         var _loc1_:UISkin = UIManager.getUISkin("board_bg");
         _loc1_.setSize(295,280);
         this.addChild(_loc1_);
         this._replacePanel = new RuneStoneDrawReplacePanel();
         this._replacePanel.y = 290;
         this.addChild(this._replacePanel);
         this._stoneSlot = new RuneStoneDrawItem();
         this._stoneSlot.x = 70;
         this._stoneSlot.y = 8;
         this.addChild(this._stoneSlot);
         this._treasureName = new Label("测试符石",TextFormatLib.format_0xffb932_12px);
         this._treasureName.x = 124;
         this._treasureName.y = 22;
         this.addChild(this._treasureName);
         this.pageNavigator = new PageNavigator();
         addChild(this.pageNavigator);
         this.pageNavigator.x = 100;
         this.pageNavigator.y = 250;
         this.pageNavigator.addEventListener("pageChange",this.changePage_Handler);
         this.pageNavigator.init(1,3);
         _loc4_ = UIManager.getUISkin("line2");
         _loc4_.width = 285;
         _loc4_.x = 6;
         _loc4_.y = 80;
         addChild(_loc4_);
         this._boreBtn = new Button("开孔",null,70,UIManager.getMultiUISkin("button_big"));
         this._boreBtn.x = 115;
         this._boreBtn.y = 220;
         this.addChild(this._boreBtn);
         this._boreBtn.visible = false;
         this._drawBtn = new Button("绘制",null,70,UIManager.getMultiUISkin("button_big"));
         this._drawBtn.x = 115;
         this._drawBtn.y = this._boreBtn.y;
         this.addChild(this._drawBtn);
         this._drawBtn.visible = false;
         this._boreBtn.addEventListener("click",this.boreHandle);
         this._drawBtn.addEventListener("click",this.drawHandle);
         this._box = new UISprite();
         addChild(this._box);
         this.nowPage = 1;
      }
      
      private function changePage_Handler(param1:PageNavigatorEvent) : void
      {
         this.nowPage = param1.currentPage;
         this.initData(this._drawPos);
      }
      
      private function boreHandle(param1:MouseEvent) : void
      {
         var pos:int = 0;
         var costNum:int = 0;
         var itemNum:int = 0;
         pos = this._drawPos;
         var layer:int = this._drawClickPos;
         costNum = int(MainData.getInstance().ancientRuneData.boreNumArray[layer]);
         itemNum = MainData.getInstance().bagData.userBag.numItems(MainData.getInstance().ancientRuneData.boreId);
         PopUpCenter.confirmWin("是否消耗" + costNum + "个打孔器对该符石进行打孔？",(function():*
         {
            var confirmOpen:Function;
            return confirmOpen = function():void
            {
               if(itemNum < costNum)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":"打孔器不足！！",
                     "textFormat":TextFormatLib.format_0xFF0000_12px
                  });
                  return;
               }
               AppFacade.instance.sendNotification("CS_ANCIENT_RUNE_BORE",{"pos":pos});
            };
         })(),null,0,true);
      }
      
      private function drawHandle(param1:MouseEvent) : void
      {
         var _loc7_:Object = null;
         this._itemSellChoose = new RuneStoneDrawChooseWin(this._drawPos,this._drawClickPos + 1);
         var _loc2_:uint = 0;
         var _loc8_:uint = 0;
         var _loc3_:uint = 0;
         var _loc11_:uint = 0;
         var _loc10_:Object = {};
         var _loc9_:int = 0;
         var _loc6_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:Array = MainData.getInstance().ancientRuneData.runeIdsArray;
         while(_loc6_ < _loc5_.length)
         {
            _loc2_ = uint(_loc6_);
            _loc8_ = uint(_loc5_[_loc6_]);
            _loc3_ = uint(MainData.getInstance().bagData.userBag.numItems(_loc8_));
            if(_loc3_ >= 1)
            {
               _loc7_ = {
                  "id":_loc2_,
                  "itemId":_loc8_,
                  "num":_loc3_
               };
               _loc10_[_loc6_] = _loc7_;
            }
            _loc6_++;
         }
         PopUpCenter.addPopUp("RuneStoneDrawChooseItem",this._itemSellChoose,true,true,{
            "data":"RuneStoneDrawChooseItem",
            "func":null,
            "choose":_loc10_
         });
      }
      
      private function onChangeLayer(param1:MouseEvent) : void
      {
         this._replacePanel.replaceOk();
         var _loc3_:RuneStoneDrawPropertyMC = null;
         if(this._drawClickPos >= 0)
         {
            _loc3_ = this._propertyMcArr[this._drawClickPos % 6];
            _loc3_.propertyCheckBox.isCheck = false;
         }
         this._drawBtn.visible = false;
         var _loc2_:RuneStoneDrawPropertyMC = RuneStoneDrawPropertyMC(param1.target.parent);
         if(_loc2_.pos == this._drawClickPos)
         {
            this._drawClickPos = -1;
            _loc2_.propertyCheckBox.isCheck = false;
            this._boreBtn.visible = false;
         }
         else
         {
            this._drawClickPos = _loc2_.pos;
            _loc2_.propertyCheckBox.isCheck = true;
            this._boreBtn.visible = true;
         }
         this.changePropertyFun && this.changePropertyFun();
      }
      
      private function onClickDraw(param1:MouseEvent) : void
      {
         var _loc4_:Object = null;
         this._replacePanel.replaceOk();
         var _loc3_:RuneStoneDrawPropertyMC = null;
         if(this._drawClickPos >= 0)
         {
            _loc3_ = this._propertyMcArr[this._drawClickPos % 6];
            _loc3_.propertyCheckBox.isCheck = false;
         }
         var _loc2_:RuneStoneDrawPropertyMC = RuneStoneDrawPropertyMC(param1.target.parent);
         this._boreBtn.visible = false;
         if(_loc2_.pos == this._drawClickPos)
         {
            this._drawClickPos = -1;
            _loc2_.propertyCheckBox.isCheck = false;
            this._drawBtn.visible = false;
         }
         else
         {
            this._drawClickPos = _loc2_.pos;
            _loc2_.propertyCheckBox.isCheck = true;
            this._drawBtn.visible = true;
            if(MainData.getInstance().ancientRuneData.stone[this._drawPos]["rune"][_loc2_.nowLayer])
            {
               _loc4_ = MainData.getInstance().ancientRuneData.stone[this._drawPos]["rune"][_loc2_.nowLayer];
               if(_loc4_.newRune)
               {
                  this._replacePanel.init(_loc4_.newRune,this._drawPos,_loc2_.nowLayer);
               }
            }
         }
      }
      
      public function replaceInit(param1:Object, param2:int, param3:int) : void
      {
         this._replacePanel.init(param1,param2,param3);
      }
      
      public function replaceOk() : void
      {
         this._replacePanel.replaceOk();
      }
      
      public function initData(param1:int) : void
      {
         this._replacePanel.replaceOk();
         this._drawClickPos = -1;
         this._drawBtn.visible = false;
         this._boreBtn.visible = false;
         var _loc3_:RuneStoneDrawPropertyMC = null;
         var _loc2_:* = (this.nowPage - 1) * 6;
         this._propertyMcArr = [];
         while(_loc2_ < this.nowPage * 6)
         {
            _loc3_ = new RuneStoneDrawPropertyMC(_loc2_ + 1);
            _loc3_.x = 7;
            _loc3_.y = 90 + _loc2_ % 6 * 20;
            this._propertyMcArr.push(_loc3_);
            _loc2_++;
         }
         this._stoneSlot.initData(param1);
         this._drawPos = param1;
         this._treasureName.text = String(XmlManager.ancientRuneStoneXml.children()[param1].@name);
         this.bore = MainData.getInstance().ancientRuneData.stone[param1].bore;
         this.showPropertyMc();
      }
      
      public function refresh(param1:int) : void
      {
         this._drawClickPos = -1;
         this._drawBtn.visible = false;
         this._boreBtn.visible = false;
         this.bore = MainData.getInstance().ancientRuneData.stone[param1].bore;
         this.showPropertyMc();
      }
      
      private function showPropertyMc() : void
      {
         this._box.clearAllChild(this._box);
         var _loc1_:RuneStoneDrawPropertyMC = null;
         var _loc2_:int = (this.nowPage - 1) * 6;
         while(_loc2_ < this.nowPage * 6)
         {
            _loc1_ = this._propertyMcArr[_loc2_ % 6];
            if(_loc2_ <= this.bore)
            {
               trace("i:" + _loc2_);
               trace(_loc1_);
               if(this.bore < 6 * 3 && _loc2_ == this.bore)
               {
                  trace("显示已开孔");
                  _loc1_.showProperty(0);
                  _loc1_.propertyCheckBox.removeEventListener("click",this.onClickDraw);
                  _loc1_.propertyCheckBox.addEventListener("click",this.onChangeLayer);
               }
               else
               {
                  _loc1_.showProperty(1,this._drawPos);
                  _loc1_.propertyCheckBox.removeEventListener("click",this.onChangeLayer);
                  _loc1_.propertyCheckBox.addEventListener("click",this.onClickDraw);
               }
               this._box.addChild(_loc1_);
            }
            else
            {
               _loc1_.parent && _loc1_.parent.removeChild(_loc1_);
            }
            _loc2_++;
         }
      }
   }
}

