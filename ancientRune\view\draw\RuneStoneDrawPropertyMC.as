package ancientRune.view.draw
{
   import flash.display.Sprite;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.manager.XmlManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.button.CheckBox;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class RuneStoneDrawPropertyMC extends Sprite
   {
      private var _nowLayer:int;
      
      private var _pos:int;
      
      public var boreStrengLevel:int;
      
      private var _stoneId:int;
      
      public var _text2:Label;
      
      private var _text3:Label;
      
      public var maxLabel:Label;
      
      public var propertyCheckBox:CheckBox;
      
      private var _tfFFb932:TextFormat;
      
      private var _tfFcff00:TextFormat;
      
      private var _tfDe23aa:TextFormat;
      
      public function RuneStoneDrawPropertyMC(param1:int)
      {
         super();
         this._nowLayer = param1;
         this._pos = param1 - 1;
         this._tfFFb932 = TextFormatLib.format_0xFFB932_12px;
         this._tfFcff00 = TextFormatLib.format_0xfcff00_12px;
         this._tfDe23aa = TextFormatLib.format_0xDE23AA_12px;
         var _loc2_:Label = new Label(StringUtil.substitute(Globalization.getString("treasureSmith.9"),this._nowLayer),TextFormatLib.format_0xFFB932_12px);
         this.addChild(_loc2_);
         this._text2 = new Label("",TextFormatLib.format_0xFFB932_12px);
         this._text2.x = _loc2_.width + 2;
         this.addChild(this._text2);
         this._text2.visible = true;
         this.maxLabel = new Label(Globalization.getString("infoMc.69"),TextFormatLib.format_0xfcff00_12px);
         this.maxLabel.x = 244;
         this.addChild(this.maxLabel);
         this.maxLabel.visible = false;
         this.propertyCheckBox = new CheckBox("");
         this.propertyCheckBox.x = 295 - this.propertyCheckBox.width - 8;
         this.propertyCheckBox.y = 2;
         this.addChild(this.propertyCheckBox);
      }
      
      private function parseColor(param1:int) : String
      {
         var _loc2_:int = 0;
         switch(param1)
         {
            case 0:
               _loc2_ = 16775920;
               break;
            case 1:
               _loc2_ = 65407;
               break;
            case 2:
               _loc2_ = 49151;
               break;
            case 3:
               _loc2_ = 16776960;
               break;
            case 4:
               _loc2_ = 14423100;
               break;
            case 5:
               _loc2_ = 9699539;
               break;
            case 6:
               _loc2_ = 16747520;
         }
         return "#" + _loc2_.toString(16);
      }
      
      public function showProperty(param1:int, param2:int = 0) : void
      {
         var _loc8_:Object = null;
         var _loc3_:XML = null;
         var _loc4_:String = null;
         var _loc6_:Number = NaN;
         var _loc5_:int = 0;
         var _loc7_:String = null;
         switch(param1)
         {
            case 0:
               this._text2.visible = true;
               this._text2.defaultTextFormat = this._tfFFb932;
               this._text2.text = "可开孔";
               break;
            case 1:
               this._text2.visible = true;
               this._text2.defaultTextFormat = this._tfFFb932;
               if(MainData.getInstance().ancientRuneData.stone[param2]["rune"][this._nowLayer])
               {
                  _loc8_ = MainData.getInstance().ancientRuneData.stone[param2]["rune"][this._nowLayer];
                  _loc3_ = XmlManager.ancientRuneStoneAttrXml.children()[int(_loc8_["id"])];
                  _loc4_ = String(XmlManager.affixConf.children()[_loc3_.@attrType - 1].@propertyName);
                  _loc6_ = Number(String(_loc3_.@attrNum).split(";")[_loc8_["quality"]]);
                  if(_loc8_["id"] == param2)
                  {
                     _loc6_ *= 1;
                  }
                  else
                  {
                     _loc6_ *= 1;
                  }
                  _loc5_ = 0;
                  if(MainData.getInstance().ancientRuneData.stone[param2]["borelevel"][this._nowLayer])
                  {
                     _loc5_ = int(MainData.getInstance().ancientRuneData.stone[param2]["borelevel"][this._nowLayer].level);
                     _loc6_ *= 1 + _loc5_ / 100;
                     _loc6_ = Number(_loc6_.toFixed(2));
                  }
                  this._text2.htmlText = "<font color=\'" + this.parseColor(_loc8_["quality"]) + "\'>  lv." + _loc5_ + " " + String(_loc3_.@name) + "  " + _loc4_ + "  " + _loc6_ + "</font>";
               }
               else
               {
                  this._text2.text = "可绘制符文";
               }
               break;
            case 2:
               this._text2.visible = true;
               this._text2.defaultTextFormat = this._tfFFb932;
               this._text2.text = "未开孔，不能强化。";
               break;
            case 3:
               this._text2.visible = true;
               if(MainData.getInstance().ancientRuneData.stone[param2]["borelevel"][this._nowLayer])
               {
                  _loc8_ = MainData.getInstance().ancientRuneData.stone[param2]["borelevel"][this._nowLayer];
                  this.boreStrengLevel = _loc8_.level;
                  this._text2.text = StringUtil.substitute(" LV.{0} 属性加成  {1}%",_loc8_.level,_loc8_.level);
                  break;
               }
               this._text2.text = " LV.0 属性加成  0%";
               break;
         }
      }
      
      public function showReplaceProperty(param1:Object) : void
      {
         this._text2.visible = true;
         this._text3.visible = false;
         this._text2.defaultTextFormat = this._tfFFb932;
         var _loc3_:XML = XmlManager.ancientRuneStoneAttrXml.children()[int(param1.id)];
         var _loc4_:String = String(XmlManager.affixConf.children()[_loc3_.@attrType - 1].@propertyName);
         var _loc2_:int = int(String(_loc3_.@attrNum).split(";")[param1["quality"]]);
         if(param1.id == this.pos)
         {
            _loc2_ *= 1;
         }
         else
         {
            _loc2_ *= 1;
         }
         this._text2.htmlText = "<font color=\'" + this.parseColor(param1.quality) + "\'>   " + String(_loc3_.@name) + "  " + _loc4_ + "  " + _loc2_ + "</font>";
      }
      
      public function get nowLayer() : int
      {
         return this._nowLayer;
      }
      
      public function get pos() : int
      {
         return this._pos;
      }
   }
}

