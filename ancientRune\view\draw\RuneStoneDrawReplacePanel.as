package ancientRune.view.draw
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.data.ancientRune.manager.RuneStoneColorManager;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   
   public class RuneStoneDrawReplacePanel extends Sprite
   {
      public var _text2:Label;
      
      private var _replaceBtn:Button;
      
      private var _cancelBtn:Button;
      
      private var _tfFFb932:TextFormat;
      
      private var _effect:MovieClip;
      
      private var _pos:int;
      
      private var _layer:int;
      
      private var quality:int;
      
      public function RuneStoneDrawReplacePanel(param1:Boolean = true)
      {
         super();
         this._tfFFb932 = TextFormatLib.format_0xFFB932_12px;
         var _loc2_:UISkin = UIManager.getUISkin("board_bg");
         _loc2_.setSize(295,80);
         this.addChild(_loc2_);
         this._replaceBtn = new Button("替换",null,70,UIManager.getMultiUISkin("button_big"));
         this._replaceBtn.x = 50;
         this._replaceBtn.y = 55;
         this.addChild(this._replaceBtn);
         this._cancelBtn = new Button("吸收",null,70,UIManager.getMultiUISkin("button_big"));
         this._cancelBtn.x = 150;
         this._cancelBtn.y = this._replaceBtn.y;
         this.addChild(this._cancelBtn);
         this._cancelBtn.visible = this._replaceBtn.visible = false;
         this._text2 = new Label("",TextFormatLib.format_0xFFB932_12px);
         this._text2.x = 50;
         this._text2.y = 15;
         this.addChild(this._text2);
         this._text2.visible = false;
         this._cancelBtn.addEventListener("click",this.onClickCancel);
         this._replaceBtn.addEventListener("click",this.onClickReplace);
      }
      
      private function onClickCancel(param1:MouseEvent) : void
      {
         var costNum:int = int(MainData.getInstance().ancientRuneData.runeExpArray[this.quality]);
         PopUpCenter.confirmWin("你确定吸收该条符文吗？可获得符文经验" + costNum,(function():*
         {
            var confirmOpen:Function;
            return confirmOpen = function():void
            {
               AppFacade.instance.sendNotification("CS_ANCIENT_RUNE_REPLACE_RUNE",{
                  "pos":_pos,
                  "layer":_layer,
                  "type":0
               });
            };
         })(),null,0,true);
      }
      
      private function onClickReplace(param1:MouseEvent) : void
      {
         PopUpCenter.confirmWin("你确定用该条符文替换旧的符文吗？",(function():*
         {
            var confirmOpen:Function;
            return confirmOpen = function():void
            {
               AppFacade.instance.sendNotification("CS_ANCIENT_RUNE_REPLACE_RUNE",{
                  "pos":_pos,
                  "layer":_layer,
                  "type":1
               });
            };
         })(),null,0,true);
      }
      
      public function init(param1:Object, param2:int, param3:int) : void
      {
         this._layer = param3;
         this._pos = param2;
         this._cancelBtn.visible = this._replaceBtn.visible = true;
         this._text2.visible = true;
         trace(XmlManager.ancientRuneStoneAttrXml);
         var _loc5_:XML = XmlManager.ancientRuneStoneAttrXml.children()[int(param1.id)];
         var _loc6_:String = String(XmlManager.affixConf.children()[_loc5_.@attrType - 1].@propertyName);
         this.quality = param1["quality"];
         var _loc4_:int = int(String(_loc5_.@attrNum).split(";")[this.quality]);
         if(param1.id == param2)
         {
            _loc4_ *= 1;
         }
         else
         {
            _loc4_ *= 1;
         }
         this._text2.htmlText = "<font color=\'" + RuneStoneColorManager.parseColor(this.quality + 1) + "\'>   " + String(_loc5_.@name) + "  " + _loc6_ + "  " + _loc4_ + "</font>";
         this.addEffect(6);
      }
      
      public function replaceOk() : void
      {
         this._cancelBtn.visible = this._replaceBtn.visible = false;
         this._text2.visible = false;
         if(this._effect)
         {
            this._effect.gotoAndStop(1);
            this._effect.parent && this._effect.parent.removeChild(this._effect);
            this._effect = null;
         }
      }
      
      public function addEffect(param1:int) : void
      {
         if(this._effect)
         {
            this._effect.gotoAndStop(1);
            this._effect.parent && this._effect.parent.removeChild(this._effect);
            this._effect = null;
         }
         if(param1 < 5)
         {
            return;
         }
         this._effect = AssetManager.getMc("DecorationQuality" + param1);
         this._effect.height = this._text2.height + 6;
         this._effect.width = this._text2.width + 30;
         this._effect.gotoAndPlay(1);
         this._effect.x = 110;
         this._effect.y = 25;
         addChild(this._effect);
      }
   }
}

