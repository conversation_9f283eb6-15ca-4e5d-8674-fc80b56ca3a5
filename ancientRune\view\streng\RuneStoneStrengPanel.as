package ancientRune.view.streng
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.data.ancientRune.AncientRuneData;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class RuneStoneStrengPanel extends Sprite
   {
      private var _leftPosition:RuneStoneStrengthItem;
      
      private var _rightPosition:RuneStoneStrengthItem;
      
      private var _costLabel:Label;
      
      private var _curResource:Label;
      
      private var _curResourceName:Label;
      
      private var _strengthBtn:Button;
      
      private var _advancedBtn:Button;
      
      private var _strengthPos:int;
      
      private var _isSuccess:Boolean;
      
      private var _successEffect:MovieClip;
      
      private var _failEffect:MovieClip;
      
      private var costNum:int;
      
      public function RuneStoneStrengPanel()
      {
         super();
         this.initUI();
      }
      
      private function initUI() : void
      {
         var _loc8_:UISkin = UIManager.getUISkin("group_bg");
         _loc8_.setSize(300,376);
         this.addChild(_loc8_);
         this._leftPosition = new RuneStoneStrengthItem();
         this._leftPosition.x = 30;
         this._leftPosition.y = 18;
         this.addChild(this._leftPosition);
         var _loc9_:Label = new Label(Globalization.getString("decotation.9"),TextFormatLib.format_0x00FF00_12px);
         _loc9_.x = 115;
         _loc9_.y = 45;
         this.addChild(_loc9_);
         var _loc2_:UISkin = UIManager.getUISkin("arrow_green_3");
         _loc2_.x = 115;
         _loc2_.y = 60;
         this.addChild(_loc2_);
         this._rightPosition = new RuneStoneStrengthItem();
         this._rightPosition.x = 207;
         this._rightPosition.y = 18;
         this.addChild(this._rightPosition);
         var _loc1_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc1_.setSize(202,26);
         _loc1_.x = 46;
         _loc1_.y = 140;
         this.addChild(_loc1_);
         this._costLabel = new Label("",TextFormatLib.format_verdana_0xffed89_12px);
         this._costLabel.x = 96;
         this._costLabel.y = 144;
         this.addChild(this._costLabel);
         this._curResourceName = new Label("当前符石进阶石",TextFormatLib.format_0x00FF00_12px);
         this._curResourceName.x = 70;
         this._curResourceName.y = 174;
         this.addChild(this._curResourceName);
         var _loc3_:UISkin = UIManager.getUISkin("crystal");
         _loc3_.x = this._curResourceName.width + 72;
         _loc3_.y = 176;
         this._curResource = new Label("",TextFormatLib.format_0x00FF00_12px);
         this._curResource.x = _loc3_.x + _loc3_.width;
         this._curResource.y = 174;
         this.addChild(this._curResource);
         this._strengthBtn = new Button(Globalization.getString("Strengthen.33"),null,70,UIManager.getMultiUISkin("button_big"));
         this._strengthBtn.x = 115;
         this._strengthBtn.y = 196;
         this.addChild(this._strengthBtn);
         this._advancedBtn = new Button("进阶",null,70,UIManager.getMultiUISkin("button_big"));
         this._advancedBtn.x = 115;
         this._advancedBtn.y = 196;
         this.addChild(this._advancedBtn);
         this._advancedBtn.visible = false;
         var _loc5_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc5_.setSize(282,120);
         _loc5_.x = 6;
         _loc5_.y = 235;
         this.addChild(_loc5_);
         var _loc4_:Label = new Label(Globalization.getString("decotation.13"),TextFormatLib.format_0xffb932_12px);
         _loc4_.x = 24;
         _loc4_.y = 242;
         this.addChild(_loc4_);
         var _loc6_:TextFormat = new TextFormat("Verdana",12,16772489);
         _loc6_.leading = 4;
         var _loc7_:Label = new Label("1、以左边面板加成为准，右边存在换算关系。\n2、强化到一定等级后可以进阶。\n3、消耗进阶石进阶，进阶后会改变符石品质。\n4、不同品质可解锁更高属性\n5、在各种活动中可以获得符石强化石与进阶石",_loc6_);
         _loc7_.x = 24;
         _loc7_.y = 260;
         _loc7_.width = 260;
         _loc7_.wordWrap = true;
         this.addChild(_loc7_);
         this._strengthBtn.addEventListener("click",this.onStrengthHandler);
         this._advancedBtn.addEventListener("click",this.onAdvancedHandler);
      }
      
      private function stopMc() : void
      {
         var _loc1_:MovieClip = this._isSuccess ? this._successEffect : this._failEffect;
         _loc1_.parent && _loc1_.parent.removeChild(_loc1_);
         _loc1_.stop();
      }
      
      private function onStrengthHandler(param1:MouseEvent) : void
      {
         var _loc3_:AncientRuneData = MainData.getInstance().ancientRuneData;
         var _loc2_:Object = _loc3_.stone[this._strengthPos];
         if(_loc3_.strengthenStone < this.costNum)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":"符石强化石不足",
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         AppFacade.instance.sendNotification("CS_ANCIENT_RUNE_STONE_STRENGTHEN",{"pos":this._strengthPos});
      }
      
      private function onAdvancedHandler(param1:MouseEvent) : void
      {
         var _loc3_:AncientRuneData = MainData.getInstance().ancientRuneData;
         var _loc2_:Object = _loc3_.stone[this._strengthPos];
         if(_loc3_.advancedStone < this.costNum)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":"符石进阶石不足",
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         AppFacade.instance.sendNotification("CS_ANCIENT_RUNE_STONE_ADVANCED",{"pos":this._strengthPos});
      }
      
      public function updateStrengthData(param1:int) : void
      {
         var _loc2_:Object = MainData.getInstance().ancientRuneData.stone[param1];
         this._strengthPos = param1;
         this._leftPosition.initData(param1);
         this._rightPosition.initData(param1,"right");
         if(_loc2_.level >= _loc2_.quality * 10)
         {
            this._advancedBtn.visible = true;
            this.costNum = MainData.getInstance().ancientRuneData.advancedNumArray[_loc2_.quality - 1];
            this._costLabel.htmlText = StringUtil.substitute("进阶消耗资源：<font color=\'#fff600\'>{0}</font>",this.costNum);
            this._curResourceName.text = "当前符石进阶石";
            this._curResource.text = MainData.getInstance().ancientRuneData.advancedStone + "";
         }
         else
         {
            this.costNum = MainData.getInstance().ancientRuneData.strengthNumArray[_loc2_.quality - 1];
            this._advancedBtn.visible = false;
            this._costLabel.htmlText = StringUtil.substitute(Globalization.getString("decotation.10"),this.costNum);
            this._curResourceName.text = "当前符石强化石";
            this._curResource.text = MainData.getInstance().ancientRuneData.strengthenStone + "";
         }
      }
      
      public function playEffect(param1:Boolean) : void
      {
         this._isSuccess = param1;
         if(param1)
         {
            this.addChild(this._successEffect);
            this._successEffect.gotoAndPlay(1);
            this._failEffect.stop();
            this._failEffect.parent && this._failEffect.parent.removeChild(this._failEffect);
         }
         else
         {
            this.addChild(this._failEffect);
            this._failEffect.gotoAndPlay(1);
            this._successEffect.stop();
            this._successEffect.parent && this._successEffect.parent.removeChild(this._successEffect);
         }
      }
   }
}

