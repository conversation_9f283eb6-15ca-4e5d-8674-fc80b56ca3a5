package ancientRune.view.streng
{
   import ancientRune.view.RuneStoneDecorationIcon;
   import ancientRune.view.RuneStoneSlotSp;
   import flash.display.Sprite;
   import game.data.MainData;
   import game.data.ancientRune.AncientRuneData;
   import game.data.ancientRune.manager.RuneStoneColorManager;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.manager.XmlManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class RuneStoneStrengthItem extends Sprite
   {
      private var _nameLabel:Label;
      
      private var _icon:RuneStoneDecorationIcon;
      
      private var _levelLabel:Label;
      
      private var _propertyLabel:Label;
      
      public function RuneStoneStrengthItem()
      {
         super();
         this._nameLabel = new Label("",TextFormatLib.format_0xebce82_12px);
         this._nameLabel.x = 2;
         this.addChild(this._nameLabel);
         this._icon = new RuneStoneDecorationIcon();
         this._icon.y = 21;
         this.addChild(this._icon);
         var _loc1_:UISkin = UIManager.getUISkin("state_strengthen");
         _loc1_.x = -9;
         _loc1_.y = 74;
         this.addChild(_loc1_);
         this._levelLabel = new Label("",TextFormatLib.format_0x00FF00_12px);
         this._levelLabel.x = 11;
         this._levelLabel.y = 75;
         this.addChild(this._levelLabel);
         this._propertyLabel = new Label("",TextFormatLib.format_Verdana_0xFFB932_12px);
         this._propertyLabel.x = -21;
         this._propertyLabel.y = 94;
         this.addChild(this._propertyLabel);
      }
      
      private function getQualityName(param1:int) : String
      {
         var _loc2_:String = null;
         switch(param1 - 1)
         {
            case 0:
               _loc2_ = "白色";
               break;
            case 1:
               _loc2_ = "绿色";
               break;
            case 2:
               _loc2_ = "蓝色";
               break;
            case 3:
               _loc2_ = "黄色";
               break;
            case 4:
               _loc2_ = "红色";
               break;
            case 5:
               _loc2_ = "紫色";
               break;
            case 6:
               _loc2_ = "橙色";
         }
         return _loc2_;
      }
      
      private function parseColor(param1:int) : String
      {
         var _loc2_:int = 0;
         switch(param1 - 1)
         {
            case 0:
               _loc2_ = 16775920;
               break;
            case 1:
               _loc2_ = 65407;
               break;
            case 2:
               _loc2_ = 49151;
               break;
            case 3:
               _loc2_ = 16776960;
               break;
            case 4:
               _loc2_ = 14423100;
               break;
            case 5:
               _loc2_ = 9699539;
               break;
            case 6:
               _loc2_ = 16747520;
         }
         return "#" + _loc2_.toString(16);
      }
      
      public function initData(param1:int, param2:String = "left") : void
      {
         var _loc12_:String = null;
         var _loc4_:Number = NaN;
         var _loc3_:AncientRuneData = MainData.getInstance().ancientRuneData;
         var _loc7_:Object = _loc3_.stone[param1];
         trace(_loc7_);
         var _loc5_:int = 1;
         var _loc9_:String = String(XmlManager.ancientRuneStoneXml.children()[param1].@url) + _loc7_.quality;
         this._icon.isShowAdd = true;
         this._icon.id = _loc5_;
         this._icon.positionId = param1;
         _loc12_ = UrlManager.getAncientRuneUrl(_loc9_);
         this._icon.updateBg(RuneStoneSlotSp.getQualityBorder(_loc7_.quailty));
         this._icon.addEffect(_loc7_.quality);
         this._icon.setData(_loc12_);
         var _loc6_:int = int(_loc7_.level);
         var _loc10_:int = int(_loc7_.quality);
         var _loc8_:String = this.getQualityName(_loc10_);
         if(param2 == "left")
         {
            this._levelLabel.htmlText = RuneStoneColorManager.getQualityName(_loc10_,_loc6_);
         }
         else if(_loc6_ >= _loc10_ * 10)
         {
            this._icon.dir = param2;
            this._levelLabel.htmlText = RuneStoneColorManager.getQualityName(_loc10_ + 1,0);
         }
         else
         {
            this._icon.dir = param2;
            this._levelLabel.htmlText = RuneStoneColorManager.getQualityName(_loc10_,_loc6_ + 1);
         }
         var _loc11_:String = "100";
         this._propertyLabel.visible = false;
         this._propertyLabel.htmlText = StringUtil.substitute(Globalization.getString("decotation.8"),_loc11_);
         this._nameLabel.text = String(XmlManager.ancientRuneStoneXml.children()[param1].@name);
      }
   }
}

