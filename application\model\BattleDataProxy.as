package application.model
{
   import application.view.components.AlertInfo;
   import battleConfig.BattleConfiger;
   import com.hurlant.crypto.Crypto;
   import flash.utils.ByteArray;
   import game.Environment;
   import game.clientTrace;
   import game.data.battle.BattleLoadSourceAnalyzer;
   import game.modules.card.manager.CardManager;
   import game.net.BabelTimeSocket;
   import game.net.SocketDataEvent;
   import game.xmlParsers.copy.Army;
   import game.xmlParsers.copy.CopyManager;
   import game.xmlParsers.copy.Monstersquad;
   import mmo.Config;
   import mmo.Core;
   import mmo.ui.control.PopUpCenter;
   import model.GameResultModel;
   import model.PlayerListModel;
   import model.PlayerModel;
   import model.TeamInfoModel;
   import org.puremvc.as3.interfaces.IProxy;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import sourceManager.SourceManager;
   
   public class BattleDataProxy extends Proxy implements IProxy
   {
      public static const NAME:String = "Battle9GirdDataProxy";
      
      private var _socketData:String;
      
      private var _battleData:Object;
      
      private var _loadSource:BattleLoadSourceAnalyzer;
      
      private var _gameResult:GameResultModel;
      
      private var _playerTeamModel:TeamInfoModel;
      
      private var _armyTeamModel:TeamInfoModel;
      
      private var _battleRecordURL:String;
      
      private var _cardModule:Boolean;
      
      public var bossid:int = 0;
      
      public var backGroundid:uint = 0;
      
      public var musicid:int = 0;
      
      public var bossMode:Boolean;
      
      public var blood_mastername:*;
      
      public var worldteam_name_left:*;
      
      public var worldteam_name_right:*;
      
      public var currentSelectedHeroInfoModel:PlayerModel;
      
      public function BattleDataProxy()
      {
         super("Battle9GirdDataProxy");
      }
      
      override public function onRegister() : void
      {
         try
         {
            if(!SourceManager.record)
            {
               BabelTimeSocket.getInstance().regCallback("handleBattelData",this.getBattleDataBack);
            }
            return;
         }
         catch(e:Error)
         {
         }
      }
      
      override public function onRemove() : void
      {
         this.bossid = 0;
         if(!SourceManager.record)
         {
            BabelTimeSocket.getInstance().removeCallback("handleBattelData",this.getBattleDataBack);
         }
      }
      
      private function getBattleDataBack(param1:SocketDataEvent) : void
      {
         var _loc4_:ByteArray = null;
         var _loc2_:Array = null;
         var _loc3_:int = 0;
         clientTrace("==============接受到数据:");
         if(param1.error == "ok" || SourceManager.instance.fakeBattleData)
         {
            if(!this._loadSource)
            {
               this._loadSource = new BattleLoadSourceAnalyzer();
            }
            else
            {
               this._loadSource.reset();
            }
            _loc4_ = new ByteArray();
            clientTrace(param1.data as String);
            this.socketData = param1.data as String;
            _loc4_ = Base64Coder.decodeToByteArray(this.socketData);
            _loc4_.position = 0;
            _loc4_.uncompress();
            _loc4_.position = 0;
            this._battleData = _loc4_.readObject();
            this._loadSource.data = this._battleData;
            if(SourceManager.instance.fakeBattleData && this._battleData.battle)
            {
               _loc2_ = this._battleData.battle;
               _loc3_ = 0;
               while(_loc3_ < _loc2_.length)
               {
                  if(_loc2_[_loc3_].arrReaction && _loc2_[_loc3_].arrReaction[0].reaction)
                  {
                     _loc2_[_loc3_].arrReaction[0].reaction = 3;
                  }
                  _loc3_++;
               }
            }
            sendNotification("BATTLE_JUMP_TO_BATTTEL",this._battleData);
            return;
         }
         PopUpCenter.addPopUp("alertInfo",new AlertInfo("socket 返回数据状态错误,错误代码:" + param1.error),true,true);
         throw new Error("socket error = " + param1.error);
      }
      
      public function getRoleModelList() : Array
      {
         if(!this._loadSource)
         {
            this._loadSource = new BattleLoadSourceAnalyzer();
         }
         else
         {
            this._loadSource.reset();
         }
         this._loadSource.data = this.regetBattleData();
         return this._loadSource.roleIdList;
      }
      
      public function getAnimationList() : Array
      {
         if(!this._loadSource)
         {
            this._loadSource = new BattleLoadSourceAnalyzer();
         }
         else
         {
            this._loadSource.reset();
         }
         var _loc1_:Array = [];
         this._loadSource.data = this.regetBattleData();
         return this._loadSource.allSkillAnimationName;
      }
      
      public function getSourceNeedLoadList() : Array
      {
         if(!this._loadSource)
         {
            this._loadSource = new BattleLoadSourceAnalyzer();
         }
         else
         {
            this._loadSource.reset();
         }
         this._loadSource.data = this.regetBattleData();
         return this._loadSource.needLoadList;
      }
      
      public function getConfigsNeedLoadList() : Array
      {
         if(!this._loadSource)
         {
            this._loadSource = new BattleLoadSourceAnalyzer();
         }
         else
         {
            this._loadSource.reset();
         }
         return this._loadSource.getConfigList(this.regetBattleData());
      }
      
      public function regetBattleData() : Object
      {
         var _loc8_:Object = null;
         var _loc4_:Object = null;
         var _loc10_:String = null;
         var _loc3_:String = null;
         var _loc5_:Array = null;
         var _loc12_:int = 0;
         var _loc2_:String = null;
         var _loc11_:Array = null;
         var _loc9_:int = 0;
         var _loc7_:Monstersquad = null;
         var _loc1_:Army = null;
         var _loc6_:ByteArray = new ByteArray();
         try
         {
            _loc6_ = Base64Coder.decodeToByteArray(this._socketData);
            _loc6_.position = 0;
            _loc6_.uncompress();
            _loc8_ = _loc6_.readObject();
            trace("ddddd-0:" + _loc8_.equip);
            if(SourceManager.instance.fakeBattleData && _loc8_.battle)
            {
               _loc5_ = _loc8_.battle;
               _loc12_ = 0;
               while(_loc12_ < _loc5_.length)
               {
                  if(_loc5_[_loc12_].arrReaction && _loc5_[_loc12_].arrReaction[0].reaction)
                  {
                     _loc5_[_loc12_].arrReaction[0].reaction = 3;
                  }
                  _loc12_++;
               }
            }
            _loc4_ = Environment.loadingParams;
            if(_loc4_ && _loc4_.hasOwnProperty("recordUrl"))
            {
               _loc10_ = _loc4_.recordUrl;
            }
            else
            {
               _loc10_ = "";
            }
            if(!_loc8_.hasOwnProperty("url_brid") || _loc8_["url_brid"] == null || String(_loc8_["url_brid"]) == "null" || String(_loc8_["url_brid"]) == "")
            {
               _loc8_["url_brid"] = Crypto.encryptNumber(Number(_loc8_.brid));
            }
            BattleConfiger.getInstance().battlePara.brid = _loc8_.brid;
            _loc3_ = BattleConfiger.getInstance().battlePara.serverid;
            if(this.cardModule)
            {
               this._battleRecordURL = _loc10_ + "?serverid=" + Core.cardServer + "&bid=" + _loc8_.url_brid;
            }
            else
            {
               this._battleRecordURL = _loc10_ + "?serverid=" + Config.serverID + "&bid=" + _loc8_.url_brid;
            }
         }
         catch(e:*)
         {
            return null;
         }
         if(int(_loc8_.type) == 18)
         {
            _loc2_ = _loc8_.team1.name;
            _loc11_ = _loc2_.split(",");
            try
            {
               _loc8_.team1.name = CardManager.getName(_loc11_[0],_loc11_[1]);
            }
            catch(e:*)
            {
               _loc8_.team1.name = "";
            }
            _loc2_ = _loc8_.team2.name;
            _loc11_ = _loc2_.split(",");
            try
            {
               _loc8_.team2.name = CardManager.getName(_loc11_[0],_loc11_[1]);
            }
            catch(e:*)
            {
               _loc8_.team2.name = "";
            }
            if(SourceManager.record)
            {
               BattleConfiger.getInstance().battlePara.selfLeaderName = _loc8_.team1.name;
               BattleConfiger.getInstance().battlePara.armyLeaderName = _loc8_.team2.name;
            }
            this._cardModule = true;
         }
         try
         {
            this.bossid = 0;
            this.backGroundid = _loc8_.bgId;
            if(_loc8_ != null)
            {
               _loc9_ = int(_loc8_.type);
               if(_loc9_ == 7 || _loc9_ == 9 || _loc9_ == 11 || _loc9_ == 24)
               {
                  if(_loc9_ == 24)
                  {
                     _loc7_ = CopyManager.getMonstersquad(_loc8_.team2.uid);
                  }
                  else
                  {
                     _loc1_ = CopyManager.getArmy(_loc8_.team2.uid);
                     if(_loc1_ != null)
                     {
                        _loc7_ = CopyManager.getMonstersquad(_loc1_.monstersquadID);
                     }
                  }
                  if(_loc7_)
                  {
                     this.bossid = int(_loc7_.bossID);
                  }
               }
            }
         }
         catch(e:*)
         {
            this.bossid = 0;
         }
         this.musicid = _loc8_.musicId;
         if(!_loc8_ || !_loc8_.hasOwnProperty("team1") || !_loc8_.hasOwnProperty("team2") || !_loc8_.hasOwnProperty("battle"))
         {
            Config.clientDebug && PopUpCenter.alertWin("原始战斗数据错误,未包含team1,team2或battle必要属性,socketData:" + this._socketData);
         }
         if(this._playerTeamModel == null)
         {
            this._playerTeamModel = new TeamInfoModel();
         }
         this._playerTeamModel.data = _loc8_.team1;
         if(this._armyTeamModel == null)
         {
            this._armyTeamModel = new TeamInfoModel();
         }
         this._armyTeamModel.data = _loc8_.team2;
         return _loc8_;
      }
      
      public function iniBattleData() : void
      {
         if(!this._gameResult)
         {
            this._gameResult = new GameResultModel();
         }
         this._gameResult.data = this.regetBattleData();
         this.blood_mastername = null;
         this.worldteam_name_left = null;
         this.worldteam_name_right = null;
         this._battleData.reward && this._battleData.reward.blood_mastername && (this.blood_mastername = this._battleData.reward.blood_mastername);
         this._battleData.reward && this._battleData.reward.worldteam_name_left && (this.worldteam_name_left = this._battleData.reward.worldteam_name_left);
         this._battleData.reward && this._battleData.reward.worldteam_name_right && (this.worldteam_name_right = this._battleData.reward.worldteam_name_right);
         PlayerListModel.instance.grid = SourceManager.instance.grid;
         PlayerListModel.instance.teamAData = this._battleData.team1.arrHero;
         PlayerListModel.instance.teamBData = this._battleData.team2.arrHero;
         if(this._battleData.team2 && this._battleData.team2.hasOwnProperty("isPlayer"))
         {
            PlayerListModel.instance.teamBIsHero = this._battleData.team2.isPlayer;
         }
         if(this._battleData.team1 && this._battleData.team1.hasOwnProperty("isPlayer"))
         {
            PlayerListModel.instance.teamAIsHero = this._battleData.team1.isPlayer;
         }
         var _loc1_:int = int(this._battleData.team1.formation);
         PlayerListModel.instance.teamAFormationID = _loc1_;
         _loc1_ = int(this._battleData.team2.formation);
         PlayerListModel.instance.teamBFormationID = _loc1_;
      }
      
      public function iniGameResultModel() : void
      {
         if(!this._gameResult)
         {
            this._gameResult = new GameResultModel();
         }
         this._gameResult.data = this.regetBattleData();
      }
      
      public function fakeData() : void
      {
         this._battleData = {};
         this._battleData.battle = [{
            "action":1,
            "attacker":1,
            "defender":2,
            "rage":25,
            "arrReaction":[{
               "arrDamage":[{
                  "damageType":1,
                  "damageValue":111
               },{
                  "damageType":4,
                  "damageValue":444
               },{
                  "damageType":5,
                  "damageValue":555
               },{
                  "damageType":6,
                  "damageValue":666
               },{
                  "damageType":7,
                  "damageValue":777
               }],
               "defender":2,
               "reaction":1
            }]
         }];
         this._battleData.team1 = [{
            "name":"A1",
            "position":0,
            "uid":1,
            "currHp":10001,
            "maxHp":10001
         }];
         this._battleData.team2 = [{
            "name":"B1",
            "position":2,
            "uid":2,
            "currHp":10001,
            "maxHp":10001
         }];
         this.sendNotification("BATTLE_JUMP_TO_BATTTEL",this._battleData);
      }
      
      public function get backGroundURL() : String
      {
         return this._loadSource.backGroundURL;
      }
      
      public function get gameResult() : GameResultModel
      {
         return this._gameResult;
      }
      
      public function get battleData() : Object
      {
         return this._battleData;
      }
      
      public function set battleData(param1:Object) : void
      {
         this._battleData = param1;
      }
      
      public function get socketData() : String
      {
         return this._socketData;
      }
      
      public function set socketData(param1:String) : void
      {
         this._socketData = param1;
         if(SourceManager.instance.fakeBattleData)
         {
            SourceManager.mode = "MODE_ONLINE";
            this._socketData = "eJylVU1v00AQzdpTt2snEU5SkGl7apGiqqogVNATFxBQiQMtCCEhIbbxmlh1ErOxJeCCXKreOKcH/gR/pH8p7EeaxBtQk/RgaWYyO2/mvcmu7aDSMUmSiOINZDsIM/KZgufyEGmeUAanf/p916cB7fjcO/+1VSLNJOx2IPMc1k07PqA7hLEjqsLYFFUM8CyR67JhGKDKk56RNq+OHZGy6kvn7beY8grKeUeilMIL8XMFoAYFZS2NLGtkLQsLCRsEkCX7XAFsAyqqHlSoDAYadiQSN2X0YgWyxjhTdivCZUC3VHcVQDX4PnMjTkASEpljJFFNMsCR1iZ74qEywCTK6qLjXkyPq0/weL7aQ5JGbGaH0yT1NYhXM0PczvPTv+LHs8Eo5vdGw8jchebIiW1cI3ZzAQhNZSOvcq58/eZK6BPoSjyZF2IwGPzOS3HXBrOIl66G2JxEMGpwem92iH/xwEtkP24upXmNlJ8WIOJnXs31McrUFGYNerMjuPzee9oKI19Vy8NsjGFKx2kQUKYcVzkHPnzAibgh17HPbxjwRrca+v+e6PToe/J6rn9slcQxI2GPRJb53EkoaT9QC7LcCn1ZHrcSbl26Tpt8fRnD2ftSM2VMWq6wjsSLUqjSL2kYH3SCLnLjbi+Ur4Jk581JGEW8ZX4TVMXrI32+iwKkLunahsvKDpx93OXffSg00B7AI3libZ9rhWRbDdVWXWzLtmU+3IFzY5d/wwMFdaCxryYUaVs8bU9LQyrtUKShv8EM97M=";
         }
         this.regetBattleData();
      }
      
      public function get playerTeamModel() : TeamInfoModel
      {
         return this._playerTeamModel;
      }
      
      public function get armyTeamModel() : TeamInfoModel
      {
         return this._armyTeamModel;
      }
      
      public function set armyTeamModel(param1:TeamInfoModel) : void
      {
         this._armyTeamModel = param1;
      }
      
      public function get battleRecordURL() : String
      {
         return this._battleRecordURL;
      }
      
      public function get cardModule() : Boolean
      {
         return this._cardModule;
      }
   }
}

