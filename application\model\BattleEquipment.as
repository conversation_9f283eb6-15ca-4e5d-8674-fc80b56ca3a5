package application.model
{
   import game.items.framework.items.Equipment;
   import game.items.framework.items.Gem;
   import game.items.framework.items.ItemFactory;
   
   public class BattleEquipment extends Equipment
   {
      public function BattleEquipment(param1:*)
      {
         super(param1);
      }
      
      override public function set arm_enchanse(param1:Object) : void
      {
         var _loc2_:Object = null;
         for each(_loc2_ in param1)
         {
            arm_enchanse.push(ItemFactory.creatItem(_loc2_,false,false) as Gem);
         }
      }
   }
}

