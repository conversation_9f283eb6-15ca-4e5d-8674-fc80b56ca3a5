package application.model
{
   import game.items.ItemManager;
   import game.items.framework.interfaces.IBasicInterface;
   import game.items.framework.interfaces.IEquipmentInterface;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   
   public class BattleItemFactory
   {
      public function BattleItemFactory()
      {
         super();
      }
      
      public static function creatItem(param1:Object, param2:Boolean = true, param3:<PERSON>olean = true) : Item
      {
         var _loc4_:Item = null;
         if(!param1)
         {
            return null;
         }
         if(param1 is Array && param1.length == 0)
         {
            return null;
         }
         var _loc5_:IBasicInterface = ItemManager.getInstance().getItemTemplate(param1.item_template_id);
         if(_loc5_ is IEquipmentInterface)
         {
            return new BattleEquipment(param1);
         }
         return ItemFactory.creatItem(param1,param2,param3);
      }
   }
}

