package application.utils
{
   import com.greensock.TweenLite;
   import flash.display.MovieClip;
   import flash.display.Shape;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.progressBar.ProgressBar;
   
   public class BattleBloodBar extends UISprite
   {
      private var bar:ProgressBar;
      
      private var lbl:Label;
      
      private var bg:UISkin;
      
      private var mc:MovieClip;
      
      public function BattleBloodBar()
      {
         super();
         this.bg = UIManager.getUISkin("bloodBarBg");
         addChild(this.bg);
         this.bg.x = -11;
         this.bg.y = -2;
         this.mc = AssetManager.getMc("strongWorldBloodMC");
         addChild(this.mc);
         this.mc.x = -10;
         this.mc.y = 10;
         var _loc1_:Shape = new Shape();
         _loc1_.graphics.beginFill(0);
         _loc1_.graphics.drawRoundRect(0,0,85,25,20,20);
         _loc1_.graphics.endFill();
         _loc1_.y = 0;
         _loc1_.x = -1;
         addChild(_loc1_);
         this.mc.mask = _loc1_;
         this.updateValue(0,1);
      }
      
      public function updateValue(param1:int, param2:int, param3:Boolean = false) : void
      {
         var _loc4_:int = 0;
         TweenLite.killTweensOf(this.mc);
         if(param1 <= 0)
         {
            param1 = 0;
         }
         if(param1 == param2)
         {
            _loc4_ = 30;
         }
         else
         {
            _loc4_ = 30 - 60 * (1 - param1 / param2);
         }
         if(param3)
         {
            TweenLite.to(this.mc,0.6,{"x":_loc4_});
         }
         else
         {
            this.mc.x = _loc4_;
         }
      }
      
      override public function dispose() : void
      {
         TweenLite.killTweensOf(this.mc);
         super.dispose();
      }
   }
}

