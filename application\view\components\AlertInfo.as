package application.view.components
{
   import flash.events.MouseEvent;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   
   public class AlertInfo extends PopUpWindow
   {
      private var _label:Label;
      
      private var _button:Button;
      
      public function AlertInfo(param1:String = "战斗结束")
      {
         super(200,150);
         this._button = new Button("确定",null,60);
         this._label = new Label(param1);
         this.addChild(this._label);
         this.addChild(this._button);
         this._label.x = (this.width - this._label.width) / 2;
         this._label.y = this._label.height * 2;
         this._button.x = (this.width - this._button.width) / 2;
         this._button.y = this.height - this._button.height * 1.5;
         this._button.addEventListener("click",this.onClickOK);
      }
      
      protected function onClickOK(param1:MouseEvent) : void
      {
         this._label && this.removeChild(this._label);
         this._button && this.removeChild(this._button);
         PopUpCenter.removePopUp(this.winName);
      }
   }
}

