package attackongeneralsfightmodule.mvc.model
{
   import attackongeneralsfightmodule.mvc.model.vo.FightVO;
   import attackongeneralsfightmodule.mvc.model.vo.LevelVO;
   import attackongeneralsfightmodule.mvc.model.vo.MemberVO;
   import attackongeneralsfightmodule.mvc.model.vo.NPCVO;
   import attackongeneralsfightmodule.mvc.model.vo.ReportVO;
   import attackongeneralsfightmodule.mvc.model.vo.RoleVO;
   import flash.utils.Dictionary;
   import flash.utils.describeType;
   import game.data.MainData;
   import game.data.group.HeroDataUtil;
   import game.data.group.HeroDetailData;
   import game.manager.UrlManager;
   import game.manager.XmlManager;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.IProxy;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   import util.time.TimeManager;
   
   public class DataProxy extends Proxy implements IProxy
   {
      public static const NAME:String = "attackongeneralsfightmodule.mvc.model.DataProxy";
      
      private var _fightVO:FightVO;
      
      public function DataProxy()
      {
         super("attackongeneralsfightmodule.mvc.model.DataProxy");
         this._fightVO = new FightVO();
      }
      
      public function xmlConfig() : void
      {
         var _loc2_:LevelVO = null;
         var _loc3_:XMLList = null;
         var _loc8_:XML = null;
         var _loc5_:XMLList = null;
         var _loc7_:int = 0;
         var _loc4_:int = 0;
         var _loc6_:NPCVO = null;
         this._fightVO.firstFight = true;
         var _loc1_:XML = XmlManager.boatBattle.children()[0];
         this._fightVO.readyTime = Number(_loc1_.@readyTime) * 1000;
         this._fightVO.buyFailGold = Vector.<int>(String(_loc1_.@buyFailPrice).split("|"));
         this._fightVO.canBuyFailTimes = this._fightVO.buyFailGold.length;
         this._fightVO.userSpeed = Number(_loc1_.@unitSpeed);
         this._fightVO.squadNumSingle = uint(_loc1_.@unitNumber);
         this._fightVO.squadNumMulti = uint(_loc1_.@zuduiunitNumber);
         this._fightVO.endFightCD = Number(_loc1_.@endcount);
         this._fightVO.CDTime = Number(_loc1_.@CDTime);
         this._fightVO.AllCDTime = Number(_loc1_.@allCD);
         this._fightVO.integral = uint(_loc1_.@integral);
         this._fightVO.killNPCTimes = uint(_loc1_.@SuperNumber);
         this._fightVO.killNPCGold = Vector.<uint>(String(_loc1_.@SuperGold).split("|"));
         this._fightVO.killAllNPCTimes = uint(_loc1_.@SuperLineNumber);
         this._fightVO.killAllNPCGold = Vector.<uint>(String(_loc1_.@SuperLineGold).split("|"));
         this._fightVO.killNPCFree = uint(_loc1_.@freeSuper) + int(XmlManager.vipConfing.children().(@level == MainData.getInstance().userData.vip)[0].@freeSuper);
         this._fightVO.killAllNPCFree = uint(_loc1_.@freeSuperLine);
         this._fightVO.levels = new Dictionary();
         _loc5_ = XmlManager.boatBattle_customs.children();
         _loc7_ = _loc5_.length() - 1;
         while(_loc7_ > -1)
         {
            _loc1_ = _loc5_[_loc7_];
            _loc2_ = new LevelVO();
            _loc2_.name = String(_loc1_.@name);
            _loc2_.id = uint(_loc1_.@id);
            _loc2_.next = uint(_loc1_.@next);
            _loc2_.last = uint(_loc1_.@last);
            _loc2_.finalTime = Number(_loc1_.@finalTime) * 1000;
            _loc2_.passInfo = String(_loc1_.@passInfo);
            if(_loc2_.passInfo == null || _loc2_.passInfo == "null")
            {
               _loc2_.passInfo = "";
            }
            _loc2_.passIntegral = uint(_loc1_.@passIntegral);
            _loc2_.passTime = uint(_loc1_.@passTime);
            _loc2_.passKill = uint(_loc1_.@passKill);
            _loc2_.blood = uint(_loc1_.@blood);
            _loc2_.eliteTime = Number(_loc1_.@eliteTime) * 1000;
            _loc2_.eliteCountdown = Number(_loc1_.@eliteCountdown) * 1000;
            _loc2_.eliteInfo = String(_loc1_.@eliteInfo);
            _loc2_.lineNumber = uint(_loc1_.@lineNumber);
            _loc2_.extraintegral = uint(_loc1_.@extraintegral);
            _loc2_.extraCutBlood = uint(_loc1_.@extraCutBlood);
            _loc2_.ourspeak = new Vector.<String>();
            _loc3_ = _loc1_.@*;
            _loc4_ = _loc3_.length() - 1;
            while(_loc4_ > -1)
            {
               _loc8_ = _loc3_[_loc4_];
               if(String(_loc8_.name()).indexOf("ourspeak") != -1)
               {
                  _loc2_.ourspeak.unshift(String(_loc8_));
               }
               _loc4_--;
            }
            this._fightVO.levels[String(_loc2_.id)] = _loc2_;
            _loc7_--;
         }
         this._fightVO.NPCs = new Dictionary();
         _loc5_ = XmlManager.boatBattle_army.children();
         _loc7_ = _loc5_.length() - 1;
         while(_loc7_ > -1)
         {
            _loc1_ = _loc5_[_loc7_];
            _loc6_ = new NPCVO();
            _loc6_.id = uint(_loc1_.@id);
            _loc6_.speed = Number(_loc1_.@Speed);
            _loc6_.modelName = String(_loc1_.@modelName);
            _loc6_.model = String(_loc1_.@model);
            _loc6_.name = String(_loc1_.@name);
            _loc6_.level = uint(_loc1_.@level);
            _loc6_.info = String(_loc1_.@info);
            _loc6_.speak = new Vector.<String>();
            _loc3_ = _loc1_.@*;
            _loc4_ = _loc3_.length() - 1;
            while(_loc4_ > -1)
            {
               _loc8_ = _loc3_[_loc4_];
               if(String(_loc8_.name()).indexOf("speak") != -1)
               {
                  _loc6_.speak.unshift(String(_loc8_));
               }
               _loc4_--;
            }
            _loc6_.killIntegral = uint(_loc1_.@killIntegral);
            _loc6_.cutblood = uint(_loc1_.@cutblood);
            this._fightVO.NPCs[String(_loc6_.id)] = _loc6_;
            _loc7_--;
         }
      }
      
      public function initData(param1:Object) : void
      {
         this._fightVO.roleAll = new Dictionary();
         this._fightVO.reports = new Vector.<ReportVO>();
         this._fightVO.selectedSquad = -1;
         this._fightVO.passCD = -1;
         this._fightVO.textNPC = false;
         this._fightVO.textFightStart = false;
         if(param1.round)
         {
            if(this._fightVO.currentLevel && this._fightVO.currentLevel.id == param1.round)
            {
               if(this._fightVO.canBuyFailTimes > 0)
               {
                  this._fightVO.canBuyFailTimes--;
               }
            }
            this._fightVO.currentLevel = this._fightVO.levels[String(param1.round)];
            sendNotification("ATTACK_ON_GENERALS_INIT_LANES");
         }
         if(param1.teamid)
         {
            this._fightVO.battleID = param1.teamid;
         }
         if(param1.captainId && param1.userinfo && this._fightVO.members == null)
         {
            this._teamData(param1.captainId,param1.userinfo);
            if(this._fightVO.members.length > 1)
            {
               this._fightVO.squadNum = this._fightVO.squadNumMulti;
            }
            else
            {
               this._fightVO.squadNum = this._fightVO.squadNumSingle;
            }
         }
         if(this._fightVO.squadNum)
         {
            this._updateSquadIcon(this._fightVO.members[this._fightVO.myInMemberIndex].squadFormationDetail,true);
         }
      }
      
      private function _teamData(param1:uint, param2:Object) : void
      {
         var _loc8_:int = 0;
         var _loc9_:MemberVO = null;
         var _loc3_:Object = null;
         var _loc4_:int = 0;
         var _loc7_:MemberVO = null;
         var _loc6_:Array = null;
         var _loc5_:Array = null;
         this._fightVO.totalScore = 0;
         this._fightVO.members = new Vector.<MemberVO>();
         for each(_loc3_ in param2)
         {
            _loc9_ = new MemberVO();
            _loc9_.id = _loc3_.uid;
            _loc9_.name = _loc3_.uname;
            _loc9_.level = _loc3_.level;
            _loc9_.killNum = _loc3_.killNum;
            _loc9_.score = _loc3_.point;
            this._fightVO.totalScore += _loc9_.score;
            _loc9_.squadFormation = _loc3_.formation;
            _loc5_ = this._formationDictionary(_loc3_.formation);
            _loc9_.squadFormationDetail = _loc5_[0];
            _loc9_.squadHerosName = _loc5_[1];
            if(_loc9_.id == MainData.getInstance().userData.uid)
            {
               this._fightVO.killNPCRemainFree = this._fightVO.killNPCFree - _loc3_.killOneNpcFreeTimes;
               this._fightVO.killNPCRemain = this._fightVO.killNPCTimes - _loc3_.killOneNpcTimes;
               this._fightVO.killAllNPCRemainFree = this._fightVO.killAllNPCFree - _loc3_.killAllNpcFreeTimes;
               this._fightVO.killAllNPCRemain = this._fightVO.killAllNPCTimes - _loc3_.killAllNpcTimes;
            }
            if(_loc9_.id == param1)
            {
               _loc8_ = int(this._fightVO.members.length);
            }
            this._fightVO.members[this._fightVO.members.length] = _loc9_;
         }
         _loc7_ = this._fightVO.members.splice(_loc8_,1)[0];
         this._fightVO.members.unshift(_loc7_);
         this._fightVO.leader = _loc7_.id == MainData.getInstance().userData.uid;
         _loc6_ = [13764359,5173760,61170];
         this._fightVO.membersIndex = new Dictionary();
         _loc4_ = this._fightVO.members.length - 1;
         while(_loc4_ > -1)
         {
            if(_loc4_ < 3)
            {
               this._fightVO.members[_loc4_].nameColor = _loc6_[_loc4_];
            }
            if(this._fightVO.members[_loc4_].id == MainData.getInstance().userData.uid)
            {
               this._fightVO.myInMemberIndex = _loc4_;
               this._fightVO.myBoat = "attackongeneralsfightmodule.User" + _loc4_;
            }
            this._fightVO.membersIndex[String(this._fightVO.members[_loc4_].id)] = _loc4_;
            _loc4_--;
         }
      }
      
      private function _formationDictionary(param1:Object) : Array
      {
         var _loc9_:* = null;
         var _loc2_:int = 0;
         var _loc4_:int = 0;
         var _loc7_:Array = null;
         var _loc6_:Vector.<HeroDetailData> = null;
         var _loc5_:HeroDetailData = null;
         var _loc3_:String = null;
         var _loc10_:Dictionary = new Dictionary();
         var _loc8_:Dictionary = new Dictionary();
         for(_loc9_ in param1)
         {
            _loc7_ = param1[_loc9_];
            _loc4_ = int(_loc7_.length);
            _loc6_ = new Vector.<HeroDetailData>();
            _loc3_ = "";
            _loc2_ = 0;
            while(_loc2_ < _loc4_)
            {
               if(_loc7_[_loc2_])
               {
                  _loc5_ = MainData.getInstance().tavernData.getHeroInfo(_loc7_[_loc2_]);
                  if(_loc5_)
                  {
                     _loc6_[_loc6_.length] = _loc5_;
                     if(_loc2_)
                     {
                        _loc3_ += "," + _loc5_.name;
                     }
                     else
                     {
                        _loc3_ += _loc5_.name;
                     }
                  }
               }
               _loc2_++;
            }
            _loc10_[_loc9_] = _loc6_;
            _loc8_[_loc9_] = _loc3_;
         }
         return [_loc10_,_loc8_];
      }
      
      private function _updateSquadIcon(param1:Dictionary, param2:Boolean = false) : void
      {
         var _loc3_:int = 0;
         var _loc4_:Vector.<HeroDetailData> = null;
         this._fightVO.squadIcon = new Vector.<String>();
         _loc3_ = 0;
         while(_loc3_ < this._fightVO.squadNum)
         {
            this._fightVO.squadIcon[_loc3_] = null;
            _loc4_ = param1[String(_loc3_ + 1)];
            if(_loc4_ && _loc4_.length && _loc4_[0])
            {
               this._fightVO.squadIcon[_loc3_] = UrlManager.getHeroUrl(HeroDataUtil.indexHeroSmallHeadImgBytid(_loc4_[0].htid));
            }
            _loc3_++;
         }
         sendNotification("ATTACK_ON_GENERALS_SQUAD",param2);
      }
      
      public function updateSquad(param1:uint, param2:Object) : void
      {
         var _loc3_:MemberVO = this._fightVO.members[this._fightVO.membersIndex[String(param1)]];
         _loc3_.squadFormation = param2;
         var _loc4_:Array = this._formationDictionary(param2);
         _loc3_.squadFormationDetail = _loc4_[0];
         _loc3_.squadHerosName = _loc4_[1];
         if(_loc3_.id == MainData.getInstance().userData.uid)
         {
            this._updateSquadIcon(_loc3_.squadFormationDetail);
         }
      }
      
      public function enterData(param1:Object) : void
      {
         this._fightVO.attackerGroupID = param1.attacker.groupId;
         this._fightVO.attackerMaxHP = param1.attacker.maxHp;
         this._fightVO.attackerCurrentHP = param1.attacker.hp;
         if(this._fightVO.attackerMaxHP < this._fightVO.attackerCurrentHP)
         {
            this._fightVO.attackerMaxHP = this._fightVO.attackerCurrentHP;
         }
         this._fightVO.defenderGroupID = param1.defender.groupId;
         if(this._fightVO.currentLevel.blood)
         {
            this._fightVO.defenderMaxHP = param1.defender.maxHp;
            this._fightVO.defenderCurrentHP = param1.defender.hp;
            if(this._fightVO.defenderMaxHP < this._fightVO.defenderCurrentHP)
            {
               this._fightVO.defenderMaxHP = this._fightVO.defenderCurrentHP;
            }
         }
         else
         {
            this._fightVO.defenderMaxHP = this._fightVO.defenderCurrentHP = 1;
         }
         this._fightVO.myGroupID = param1.user.groupId;
         this._fightVO.endTime = TimeManager.setTimezoneOffset(param1.field.endTime).time;
         this._fightVO.startTime = this._fightVO.endTime - this._fightVO.currentLevel.finalTime;
         this._fightVO.eliteNoShowed = true;
         this._fightVO.eliteCountdown = this._fightVO.startTime + this._fightVO.currentLevel.eliteCountdown;
         this._fightVO.eliteTime = this._fightVO.startTime + this._fightVO.currentLevel.eliteTime;
         var _loc2_:Object = param1.field;
         _loc2_.refreshMs = param1.refreshMs;
         sendNotification("ATTACK_ON_GENERALS_ROLE_REFRESH",_loc2_);
         sendNotification("ATTACK_ON_GENERALS_ENTER_FIGHT");
      }
      
      public function setTransferWalk() : void
      {
         var _loc2_:int = this._fightVO.roadsNum * 2;
         this._fightVO.transferWalk = new Vector.<Boolean>(_loc2_,true);
         var _loc1_:int = _loc2_ - 1;
         while(_loc1_ > -1)
         {
            this._fightVO.transferWalk[_loc1_] = true;
            _loc1_--;
         }
      }
      
      public function getTransferID(param1:int) : int
      {
         var _loc2_:int = 0;
         if(this._fightVO.myGroupID == this._fightVO.defenderGroupID)
         {
            _loc2_ = this._fightVO.roadsNum;
         }
         return _loc2_ + param1;
      }
      
      public function killAllNeedGold() : int
      {
         var _loc3_:int = this._fightVO.killAllNPCGold.length - 1;
         var _loc2_:int = int(this._fightVO.killAllNPCGold[_loc3_]);
         var _loc1_:int = this._fightVO.killAllNPCTimes - this._fightVO.killAllNPCRemain;
         if(_loc1_ < _loc3_)
         {
            _loc2_ = int(this._fightVO.killAllNPCGold[_loc1_]);
         }
         return _loc2_;
      }
      
      public function killNeedGold() : int
      {
         var _loc3_:int = this._fightVO.killNPCGold.length - 1;
         var _loc2_:int = int(this._fightVO.killNPCGold[_loc3_]);
         var _loc1_:int = this._fightVO.killNPCTimes - this._fightVO.killNPCRemain;
         if(_loc1_ < _loc3_)
         {
            _loc2_ = int(this._fightVO.killNPCGold[_loc1_]);
         }
         return _loc2_;
      }
      
      public function buyFailTimesNeedGold() : int
      {
         var _loc4_:int = int(this._fightVO.buyFailGold.length);
         var _loc3_:int = _loc4_ - 1;
         var _loc1_:int = this._fightVO.buyFailGold[_loc3_];
         var _loc2_:int = _loc4_ - this._fightVO.canBuyFailTimes;
         if(_loc2_ < _loc3_)
         {
            _loc1_ = this._fightVO.buyFailGold[_loc2_];
         }
         return _loc1_;
      }
      
      public function report(param1:int, param2:int, param3:int) : void
      {
         var _loc7_:String = null;
         var _loc6_:XML = null;
         var _loc8_:ReportVO = new ReportVO();
         _loc8_.id = param1;
         _loc8_.win = new RoleVO();
         var _loc4_:RoleVO = this._fightVO.roleAll["_" + param2];
         var _loc5_:XMLList = describeType(_loc4_).variable;
         for each(_loc6_ in _loc5_)
         {
            _loc7_ = _loc6_.@name;
            _loc8_.win[_loc7_] = _loc4_[_loc7_];
         }
         _loc8_.lose = new RoleVO();
         _loc4_ = this._fightVO.roleAll["_" + param3];
         _loc5_ = describeType(_loc4_).variable;
         for each(_loc6_ in _loc5_)
         {
            _loc7_ = _loc6_.@name;
            _loc8_.lose[_loc7_] = _loc4_[_loc7_];
         }
         if(_loc8_)
         {
            this._fightVO.reports.push(_loc8_);
         }
         sendNotification("ATTACK_ON_GENERALS_REPORT");
      }
      
      public function secKill(param1:int, param2:int) : void
      {
         if(param1)
         {
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - param1;
            if(param2)
            {
               this._fightVO.killAllNPCRemain--;
            }
            else
            {
               this._fightVO.killNPCRemain--;
            }
         }
         else if(param2)
         {
            this._fightVO.killAllNPCRemainFree--;
         }
         else
         {
            this._fightVO.killNPCRemainFree--;
         }
      }
      
      public function fightResults() : String
      {
         return StringUtil.substitute(Globalization.getString("AttackOnGenerals.11"),this._fightVO.currentLevel.id - 1,this._fightVO.totalScore);
      }
      
      public function score(param1:Object) : void
      {
         var _loc2_:RoleVO = null;
         var _loc3_:int = int(this._fightVO.membersIndex[String(param1.uid)]);
         this._fightVO.members[_loc3_].killNum += param1.killNum;
         this._fightVO.members[_loc3_].score += param1.point;
         this._fightVO.totalScore += param1.point;
         if(param1.winner)
         {
            _loc2_ = this._fightVO.roleAll["_" + param1.winner];
            if(_loc2_)
            {
               sendNotification("ATTACK_ON_GENERALS_SCORE",[param1.point,_loc2_.realX + 100,_loc2_.realY - 50]);
            }
         }
         else
         {
            sendNotification("ATTACK_ON_GENERALS_SCORE",param1.point);
         }
      }
      
      public function sayTime() : void
      {
         var _loc3_:int = 10000;
         var _loc2_:int = 20000;
         var _loc1_:int = 4000;
         this._fightVO.sayStartTime = TimeManager.getInstance().getTime() + Math.floor(Math.random() * (_loc2_ - _loc3_ + 1)) + _loc3_;
         this._fightVO.sayEndTime = this._fightVO.sayStartTime + _loc1_;
      }
      
      public function passInfo() : String
      {
         var _loc1_:* = "";
         if(this._fightVO.currentLevel.passInfo)
         {
            _loc1_ += "<font size=\'14\' color=\'#00ff00\'>" + Globalization.getString("townteamfight.6") + "</font>" + this._fightVO.currentLevel.passInfo;
         }
         if(this._fightVO.passCD > -1)
         {
            if(_loc1_ != "")
            {
               _loc1_ += "，";
            }
            _loc1_ += StringUtil.substitute(Globalization.getString("AttackOnGenerals.46"),this._fightVO.passCD);
            this._fightVO.passCD--;
         }
         return _loc1_;
      }
      
      public function get fightVO() : FightVO
      {
         return this._fightVO;
      }
   }
}

