package attackongeneralsfightmodule.mvc.model
{
   import game.data.MainData;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.IProxy;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   
   public class ServiceProxy extends Proxy implements IProxy
   {
      public static const NAME:String = "attackongeneralsfightmodule.mvc.model.ServiceProxy";
      
      private var _dataProxy:DataProxy;
      
      private var escState:int;
      
      public function ServiceProxy()
      {
         super("attackongeneralsfightmodule.mvc.model.ServiceProxy");
      }
      
      override public function onRegister() : void
      {
         this._dataProxy = facade.retrieveProxy("attackongeneralsfightmodule.mvc.model.DataProxy") as DataProxy;
         this._regSocketCallback();
         super.onRegister();
      }
      
      override public function onRemove() : void
      {
         this._removeSocketCallback();
         super.onRemove();
      }
      
      private function _regSocketCallback() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.boatbattle.nextRound",this._reNextRound);
         BabelTimeSocket.getInstance().regCallback("re.boatbattle.recivePoint",this._reRecivePoint);
         BabelTimeSocket.getInstance().regCallback("re.boatbattle.reload",this._reFightAgain);
         BabelTimeSocket.getInstance().regCallback("re.boatbattle.getEnterInfo",this._reEntered);
         BabelTimeSocket.getInstance().regCallback("re.boatbattle.secKill",this._reKill);
         BabelTimeSocket.getInstance().regCallback("re.boatbattle.joinBattle",this._reJoin);
         BabelTimeSocket.getInstance().regCallback("sc.boatbattle.formationChange",this._formationChangeSC);
         BabelTimeSocket.getInstance().regCallback("sc.boatbattle.leave",this._leaveSC);
         BabelTimeSocket.getInstance().regCallback("sc.boatbattle.start",this._startSC);
         BabelTimeSocket.getInstance().regCallback("sc.boatbattle.secKill",this._killSC);
         BabelTimeSocket.getInstance().regCallback("sc.boatbattle.refresh",this._refreshSC);
         BabelTimeSocket.getInstance().regCallback("sc.boatbattle.fightResult",this._fightResultSC);
         BabelTimeSocket.getInstance().regCallback("sc.boatbattle.score",this._scoreSC);
         BabelTimeSocket.getInstance().regCallback("sc.boatbattle.battleEnd",this._battleEndSC);
         BabelTimeSocket.getInstance().regCallback("sc.boatbattle.fightEnd",this._leaveSC);
      }
      
      private function _removeSocketCallback() : void
      {
         this.quit();
         BabelTimeSocket.getInstance().removeCallback("re.boatbattle.nextRound",this._reNextRound);
         BabelTimeSocket.getInstance().removeCallback("re.boatbattle.recivePoint",this._reRecivePoint);
         BabelTimeSocket.getInstance().removeCallback("re.boatbattle.reload",this._reFightAgain);
         BabelTimeSocket.getInstance().removeCallback("re.boatbattle.getEnterInfo",this._reEntered);
         BabelTimeSocket.getInstance().removeCallback("re.boatbattle.secKill",this._reKill);
         BabelTimeSocket.getInstance().removeCallback("re.boatbattle.joinBattle",this._reJoin);
         BabelTimeSocket.getInstance().removeCallback("sc.boatbattle.formationChange",this._formationChangeSC);
         BabelTimeSocket.getInstance().removeCallback("sc.boatbattle.leave",this._leaveSC);
         BabelTimeSocket.getInstance().removeCallback("sc.boatbattle.start",this._startSC);
         BabelTimeSocket.getInstance().removeCallback("sc.boatbattle.secKill",this._killSC);
         BabelTimeSocket.getInstance().removeCallback("sc.boatbattle.refresh",this._refreshSC);
         BabelTimeSocket.getInstance().removeCallback("sc.boatbattle.fightResult",this._fightResultSC);
         BabelTimeSocket.getInstance().removeCallback("sc.boatbattle.score",this._scoreSC);
         BabelTimeSocket.getInstance().removeCallback("sc.boatbattle.battleEnd",this._battleEndSC);
         BabelTimeSocket.getInstance().removeCallback("sc.boatbattle.fightEnd",this._leaveSC);
      }
      
      public function quit(param1:int = 2) : void
      {
         if(param1 == 2 && this.escState == 3)
         {
            return;
         }
         BabelTimeSocket.getInstance().sendMessage("boatbattle.quit",new SocketCallback("re.boatbattle.quit"),param1);
      }
      
      public function nextRound() : void
      {
         BabelTimeSocket.getInstance().sendMessage("boatbattle.nextRound",new SocketCallback("re.boatbattle.nextRound"),this._dataProxy.fightVO.battleID);
      }
      
      private function _reNextRound(param1:SocketDataEvent) : void
      {
      }
      
      public function recivePoint() : void
      {
         BabelTimeSocket.getInstance().sendMessage("boatbattle.recivePoint",new SocketCallback("re.boatbattle.recivePoint"),this._dataProxy.fightVO.battleID);
      }
      
      private function _reRecivePoint(param1:SocketDataEvent) : void
      {
         sendNotification("POP_TEXT_TIPS",{
            "text":StringUtil.substitute(Globalization.getString("AttackOnGenerals.21"),this._dataProxy.fightVO.totalScore),
            "textFormat":TextFormatLib.format_0x00FF00_16px
         });
         sendNotification("ATTACK_ON_GENERALS_QUIT");
      }
      
      public function buyReceive(param1:int, param2:int = 1) : void
      {
         BabelTimeSocket.getInstance().sendMessage("boatbattle.buyRecivePointTimes",new SocketCallback("re.boatbattle.buyRecivePointTimes",[param1,param2]),param2);
      }
      
      public function fightAgain(param1:int = 0) : void
      {
         BabelTimeSocket.getInstance().sendMessage("boatbattle.reload",new SocketCallback("re.boatbattle.reload",[param1]),this._dataProxy.fightVO.battleID);
      }
      
      private function _reFightAgain(param1:SocketDataEvent) : void
      {
         var _loc2_:int = int(param1.callbackParames[0]);
         if(_loc2_)
         {
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - param1.callbackParames[0];
         }
      }
      
      public function entered() : void
      {
         BabelTimeSocket.getInstance().sendMessage("boatbattle.getEnterInfo",new SocketCallback("re.boatbattle.getEnterInfo"),this._dataProxy.fightVO.battleID);
      }
      
      private function _reEntered(param1:SocketDataEvent) : void
      {
         this._dataProxy.enterData(param1.data.res);
      }
      
      public function kill(param1:int, param2:int, param3:int = 0) : void
      {
         if(this._dataProxy.fightVO.fightState)
         {
            BabelTimeSocket.getInstance().sendMessage("boatbattle.secKill",new SocketCallback("re.boatbattle.secKill",[param1,param2]),this._dataProxy.fightVO.battleID,param2,param3);
         }
      }
      
      private function _reKill(param1:SocketDataEvent) : void
      {
         this._dataProxy.secKill(param1.callbackParames[0],param1.callbackParames[1]);
      }
      
      public function join(param1:int) : void
      {
         if(this._dataProxy.fightVO.fightState)
         {
            BabelTimeSocket.getInstance().sendMessage("boatbattle.joinBattle",new SocketCallback("re.boatbattle.joinBattle",[this._dataProxy.fightVO.selectedSquad]),this._dataProxy.fightVO.battleID,param1,this._dataProxy.fightVO.selectedSquad + 1);
            sendNotification("ATTACK_ON_GENERALS_SQUAD_CD",[true,this._dataProxy.fightVO.selectedSquad]);
         }
      }
      
      private function _reJoin(param1:SocketDataEvent) : void
      {
         if(param1.data == null || !(param1.data is Number))
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":"<font size=\'14\' color=\'#ff0000\'>" + Globalization.getString("AttackOnGenerals.32") + "</font>",
               "textFormat":TextFormatLib.format_0x00FF00_16px
            });
            sendNotification("ATTACK_ON_GENERALS_SQUAD_CD",[false,param1.callbackParames[0]]);
         }
      }
      
      private function _formationChangeSC(param1:SocketDataEvent) : void
      {
         this._dataProxy.updateSquad(param1.data.uid,param1.data.formation);
      }
      
      private function _leaveSC(param1:SocketDataEvent) : void
      {
         sendNotification("ATTACK_ON_GENERALS_LEAVE",param1.data);
      }
      
      private function _startSC(param1:SocketDataEvent) : void
      {
         this._dataProxy.initData(param1.data);
         this.entered();
      }
      
      private function _killSC(param1:SocketDataEvent) : void
      {
         sendNotification("ATTACK_ON_GENERALS_ROLE_DELETE",[2,param1.data.arrId]);
      }
      
      private function _refreshSC(param1:SocketDataEvent) : void
      {
         var _loc2_:Object = param1.data.field;
         sendNotification("ATTACK_ON_GENERALS_ROLE_REFRESH",_loc2_);
         if(_loc2_.touchdown && _loc2_.touchdown.length)
         {
            sendNotification("ATTACK_ON_GENERALS_ROLE_DELETE",[0,_loc2_.touchdown]);
         }
         if(_loc2_.leave && _loc2_.leave.length)
         {
            sendNotification("ATTACK_ON_GENERALS_ROLE_DELETE",[1,_loc2_.leave]);
         }
         if(param1.data.attacker != null && param1.data.attacker.hp)
         {
            sendNotification("ATTACK_ON_GENERALS_HP",[param1.data.attacker.hp,true]);
         }
         if(param1.data.defender != null && param1.data.defender.hp)
         {
            sendNotification("ATTACK_ON_GENERALS_HP",[param1.data.defender.hp,false]);
         }
      }
      
      private function _fightResultSC(param1:SocketDataEvent) : void
      {
         this._dataProxy.report(param1.data.brid,param1.data.winId,param1.data.loseId);
         sendNotification("ATTACK_ON_GENERALS_ROLE_DELETE",[2,[param1.data.loseId]]);
      }
      
      private function _scoreSC(param1:SocketDataEvent) : void
      {
         if(this._dataProxy.fightVO.fightState)
         {
            this._dataProxy.score(param1.data);
         }
      }
      
      private function _battleEndSC(param1:SocketDataEvent) : void
      {
         var _loc4_:int = 0;
         var _loc2_:* = null;
         var _loc3_:int = 0;
         this._dataProxy.fightVO.firstFight = false;
         this._dataProxy.fightVO.fightState = false;
         this.escState = param1.data.state;
         if(param1.data.point)
         {
            for(_loc2_ in param1.data.point)
            {
               _loc4_ = int(this._dataProxy.fightVO.membersIndex[_loc2_]);
               this._dataProxy.fightVO.members[_loc4_].score = param1.data.point[_loc2_];
            }
            this._dataProxy.fightVO.totalScore = 0;
            _loc3_ = this._dataProxy.fightVO.members.length - 1;
            while(_loc3_ > -1)
            {
               this._dataProxy.fightVO.totalScore += this._dataProxy.fightVO.members[_loc3_].score;
               _loc3_--;
            }
         }
         if(param1.data.state == 1 || param1.data.state == 3)
         {
            this._dataProxy.fightVO.winNum++;
            if(param1.data.state == 1)
            {
               this.nextRound();
            }
            if(param1.data.hp == 1)
            {
               sendNotification("ATTACK_ON_GENERALS_HP",[0,false]);
            }
         }
         else if(param1.data.hp == 1)
         {
            sendNotification("ATTACK_ON_GENERALS_HP",[0,true]);
         }
         sendNotification("ATTACK_ON_GENERALS_END",param1.data.state);
      }
   }
}

