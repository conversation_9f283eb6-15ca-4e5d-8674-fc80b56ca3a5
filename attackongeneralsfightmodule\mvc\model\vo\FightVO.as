package attackongeneralsfightmodule.mvc.model.vo
{
   import flash.utils.Dictionary;
   
   public class FightVO
   {
      public var textNPC:Boolean;
      
      public var textFightStart:Boolean;
      
      public var firstFight:Boolean;
      
      public var fightState:Boolean;
      
      public var cdState:int;
      
      public var sayStartTime:Number;
      
      public var sayEndTime:Number;
      
      public var sayRole:RoleVO;
      
      public var winNum:int;
      
      public var readyTime:Number;
      
      public var buyFailGold:Vector.<int>;
      
      public var canBuyFailTimes:int;
      
      public var userSpeed:Number;
      
      public var squadNum:uint;
      
      public var squadNumSingle:uint;
      
      public var squadNumMulti:uint;
      
      public var CDTime:Number;
      
      public var AllCDTime:Number;
      
      public var integral:uint;
      
      public var killNPCTimes:uint;
      
      public var killNPCRemain:int;
      
      public var killNPCGold:Vector.<uint>;
      
      public var killAllNPCTimes:uint;
      
      public var killAllNPCRemain:int;
      
      public var killAllNPCGold:Vector.<uint>;
      
      public var killNPCFree:uint;
      
      public var killNPCRemainFree:int;
      
      public var killAllNPCFree:uint;
      
      public var killAllNPCRemainFree:int;
      
      public var levels:Dictionary;
      
      public var currentLevel:LevelVO;
      
      public var totalScore:int;
      
      public var endFightCD:Number;
      
      public var passCD:int;
      
      public var NPCs:Dictionary;
      
      public var battleID:uint;
      
      public var members:Vector.<MemberVO>;
      
      public var membersIndex:Dictionary;
      
      public var myInMemberIndex:int;
      
      public var myBoat:String;
      
      public var leader:Boolean;
      
      public var squadIcon:Vector.<String>;
      
      public var selectedSquad:int;
      
      public var startTime:Number;
      
      public var endTime:Number;
      
      public var eliteTime:Number;
      
      public var eliteCountdown:Number;
      
      public var eliteNoShowed:Boolean;
      
      public var attackerGroupID:uint;
      
      public var attackerMaxHP:uint;
      
      public var attackerCurrentHP:uint;
      
      public var defenderGroupID:uint;
      
      public var defenderMaxHP:uint;
      
      public var defenderCurrentHP:uint;
      
      public var myGroupID:uint;
      
      public var reports:Vector.<ReportVO>;
      
      public var paths:Dictionary;
      
      public var roadsNum:int;
      
      public var roadLength:Number;
      
      public var stagePathDis:Number;
      
      public var roleAll:Dictionary;
      
      public var isAttack:Boolean;
      
      public var transfer:Array;
      
      public var winStreakNameColorTransform:Vector.<int>;
      
      public var refreshMs:Number;
      
      public var refreshStageFrames:int;
      
      public var correct:Boolean;
      
      public var transferWalk:Vector.<Boolean>;
      
      public function FightVO()
      {
         super();
      }
   }
}

