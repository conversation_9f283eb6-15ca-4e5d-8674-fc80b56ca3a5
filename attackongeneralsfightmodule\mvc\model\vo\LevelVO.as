package attackongeneralsfightmodule.mvc.model.vo
{
   public class LevelVO
   {
      public var name:String;
      
      public var id:uint = 0;
      
      public var next:uint;
      
      public var last:uint;
      
      public var finalTime:Number;
      
      public var passInfo:String;
      
      public var passIntegral:uint;
      
      public var passTime:uint;
      
      public var passKill:uint;
      
      public var blood:uint = 0;
      
      public var eliteTime:Number;
      
      public var eliteCountdown:Number;
      
      public var eliteInfo:String;
      
      public var lineNumber:uint;
      
      public var ourspeak:Vector.<String>;
      
      public var extraintegral:uint;
      
      public var extraCutBlood:uint;
      
      public function LevelVO()
      {
         super();
      }
   }
}

