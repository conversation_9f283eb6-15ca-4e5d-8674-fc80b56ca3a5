package attackongeneralsfightmodule.mvc.model.vo
{
   import flash.geom.Point;
   
   public class RoleVO
   {
      public var id:int;
      
      public var viewName:String;
      
      public var keyName:String;
      
      public var tid:int;
      
      public var type:int;
      
      public var curHp:int;
      
      public var maxHp:int;
      
      public var transferId:int;
      
      public var roadIds:Array;
      
      public var roadX:Number;
      
      public var stopX:Number;
      
      public var winStreak:int;
      
      public var speed:Number;
      
      public var stopA:Point;
      
      public var stopB:Point;
      
      public var boatId:int;
      
      public var stageSpeed:Number;
      
      public var refreshDis:Number;
      
      public var isAttackOffsetX:Number;
      
      public var isAttackOffsetY:Number;
      
      public var isAttackVX:Number;
      
      public var isAttackVY:Number;
      
      public var vx:Number;
      
      public var vy:Number;
      
      public var realX:Number;
      
      public var realY:Number;
      
      public var stageStop:Point;
      
      public var textureName:String;
      
      public var roadID:int;
      
      public var nameColor:Object;
      
      public var isAttack:Boolean;
      
      public var tipName:String;
      
      public var tipLevel:String;
      
      public var tipNPCInfo:String;
      
      public var tipNPCNum1:String;
      
      public var tipNPCNum2:String;
      
      public var tipUserSquadName:String;
      
      public var tipUserSquadHerosName:String;
      
      public var speak:Vector.<String>;
      
      public function RoleVO()
      {
         super();
      }
   }
}

