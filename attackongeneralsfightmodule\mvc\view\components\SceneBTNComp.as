package attackongeneralsfightmodule.mvc.view.components
{
   import flash.display.Sprite;
   import game.manager.UIManager;
   import mmo.ui.control.button.ImgButton;
   import util.VectorUtilities;
   
   public class SceneBTNComp extends Sprite
   {
      public static const BTN_EDIT_HERO:String = "BTNEDITHEROSceneBTNComp";
      
      public static const BTN_HELP:String = "BTNHELPSceneBTNComp";
      
      public static const BTN_QUIT:String = "BTNQUITSceneBTNComp";
      
      private var _imgBTN:Vector.<ImgButton>;
      
      public function SceneBTNComp(param1:Number = 10, param2:int = 1)
      {
         super();
         this._imgBTN = new Vector.<ImgButton>(3,true);
         var _loc4_:Vector.<String> = VectorUtilities.getFixedString(["AttackOnGeneralsTeamBTN2","soul_Help_button","btn_back"]);
         var _loc5_:Vector.<String> = VectorUtilities.getFixedString(["BTNEDITHEROSceneBTNComp","BTNH<PERSON>PSceneBTNComp","BTNQUITSceneBTNComp"]);
         var _loc3_:int = 0;
         while(_loc3_ < 3)
         {
            this._imgBTN[_loc3_] = addChild(new ImgButton(UIManager.getMultiUISkin(_loc4_[_loc3_]))) as ImgButton;
            if(_loc3_)
            {
               if(param2)
               {
                  this._imgBTN[_loc3_].x = this._imgBTN[_loc3_ - 1].x + this._imgBTN[_loc3_ - 1].width + param1;
               }
               else
               {
                  this._imgBTN[_loc3_].y = this._imgBTN[_loc3_ - 1].y + this._imgBTN[_loc3_ - 1].height + param1;
               }
            }
            else
            {
               this._imgBTN[_loc3_].y = 6;
            }
            this._imgBTN[_loc3_].name = _loc5_[_loc3_];
            _loc3_++;
         }
      }
      
      public function btnEnabled(param1:Boolean, param2:int) : void
      {
         this._imgBTN[param2].enabled = param1;
      }
   }
}

