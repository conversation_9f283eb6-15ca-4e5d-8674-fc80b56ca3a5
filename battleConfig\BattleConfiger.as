package battleConfig
{
   import flash.events.EventDispatcher;
   import flash.utils.Dictionary;
   import game.data.battle.BattleModuleParams;
   import game.data.skill.SkillAndBuffXMLManager;
   import mmo.Config;
   import mmo.ui.control.PopUpCenter;
   import sourceManager.AnimationJumpFrameConfigFile;
   import sourceManager.SwfAnimationConfigMap;
   
   public class BattleConfiger extends EventDispatcher
   {
      public static var assetPath:String = "";
      
      public static var showAnimation:Boolean = true;
      
      public static var isdoubleSpeed:Boolean = false;
      
      public static var linkMax:int = 4;
      
      public static var imagePath:String = "battle/";
      
      private static var _instance:BattleConfiger;
      
      public static var showRoleStand:Boolean = false;
      
      private static var _scale:Number = 1;
      
      public static const RAGE_MAX_VALUE:int = 100;
      
      public static const TAG_SKILL_TYPE_NAME:String = "iniComplete";
      
      public static const TAG_SKILL_1_NAME:String = "iniComplete";
      
      public static const TAG_SKILL_2_NAME:String = "iniComplete";
      
      public static const TAG_SKILL_3_NAME:String = "iniComplete";
      
      public static const EVT_INI_COMPLETE:String = "iniComplete";
      
      public static const EVT_INI_ERROR:String = "iniComplete";
      
      private static const ROLE_PARTICULAR_INTELL:String = "battle_role_intelligencetype";
      
      private static const ROLE_PARTICULAR_ATT:String = "battle_role_attacktype";
      
      private static const ROLE_PARTICULAR_DEF:String = "battle_role_defensetype";
      
      private static const ROLE_PARTICULAR_KILL:String = "battle_role_killtype";
      
      private static var reActionSkill:Array = [2,3];
      
      public var battlePara:BattleModuleParams;
      
      private var _buff:XML;
      
      private var _skill:XML;
      
      private var _roleMoedel:XML;
      
      private var _lastIndexSkill:XML;
      
      private var _backGroundImage:XML;
      
      private var _requireSource:XML;
      
      private var _backGroundImgNumber:uint = 0;
      
      private var _backGroundImageMap:Dictionary;
      
      private var _swfJumpFrameConifg:SwfAnimationConfigMap;
      
      public var defaultModelType:uint;
      
      private const STATE_LOADING:String = "loading";
      
      public function BattleConfiger()
      {
         super();
      }
      
      public static function isReactionSkill(param1:uint) : Boolean
      {
         if(reActionSkill.indexOf(param1) >= 0)
         {
            return true;
         }
         return false;
      }
      
      public static function set scale(param1:Number) : void
      {
         _scale = param1;
      }
      
      public static function get scale() : Number
      {
         return _scale;
      }
      
      public static function getInstance() : BattleConfiger
      {
         if(_instance == null)
         {
            _instance = new BattleConfiger();
            _instance.ini();
         }
         return _instance as BattleConfiger;
      }
      
      private static function indexRoleData(param1:String, param2:XML) : XML
      {
         var _loc4_:XMLList = param2.children();
         var _loc5_:uint = uint(_loc4_.length());
         var _loc3_:uint = 0;
         while(_loc3_ < _loc5_)
         {
            if(String(_loc4_[_loc3_].@id) == param1)
            {
               return _loc4_[_loc3_];
            }
            _loc3_++;
         }
         Config.clientDebug && PopUpCenter.alertWin("BattleConfiger->未检美工素材模型xml::bug id : 2012-1-16 14:43:26");
         return null;
      }
      
      private static function getTag(param1:XML, param2:String) : XMLList
      {
         return param1.attribute(param2);
      }
      
      public function ini() : void
      {
         this._backGroundImageMap = new Dictionary();
         this._swfJumpFrameConifg = new SwfAnimationConfigMap();
      }
      
      public function pushAnimationJumpFrameConfig(param1:XML) : void
      {
         this._swfJumpFrameConifg.pushConfigFile(param1);
      }
      
      public function hasJumpFrameConfig(param1:String) : Boolean
      {
         return this._swfJumpFrameConifg.hasActionConfig(param1);
      }
      
      public function getJumpFrameConfig(param1:String) : AnimationJumpFrameConfigFile
      {
         return this._swfJumpFrameConifg.getActionConfig(param1);
      }
      
      public function isRowRageSkill(param1:uint) : Boolean
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1 > 0)
         {
            _loc3_ = int(SkillAndBuffXMLManager.indexSkillType(param1));
            if(_loc3_ != 2)
            {
               return false;
            }
            _loc2_ = int(SkillAndBuffXMLManager.indexRageSkillType(param1));
            if(_loc2_ == 2)
            {
               return true;
            }
         }
         return false;
      }
      
      public function isMulityAttackRageSkill(param1:uint) : Boolean
      {
         var _loc3_:int = 0;
         var _loc2_:int = 0;
         if(param1 > 0)
         {
            _loc3_ = int(SkillAndBuffXMLManager.indexSkillType(param1));
            if(_loc3_ != 2)
            {
               return false;
            }
            _loc2_ = int(SkillAndBuffXMLManager.indexRageSkillType(param1));
            if(_loc2_ == 1)
            {
               return true;
            }
         }
         return false;
      }
      
      public function isMulityRemoteAttackSkill(param1:uint) : Boolean
      {
         var _loc2_:int = 0;
         if(param1 > 0)
         {
            _loc2_ = int(SkillAndBuffXMLManager.indexRageSkillType(param1));
            if(_loc2_ == 10)
            {
               return true;
            }
            return false;
         }
         return false;
      }
      
      public function isRageSkill(param1:uint) : Boolean
      {
         var _loc2_:int = 0;
         if(param1 > 0)
         {
            _loc2_ = int(SkillAndBuffXMLManager.indexSkillType(param1));
            if(_loc2_ == 2)
            {
               return true;
            }
            return false;
         }
         return false;
      }
      
      public function getRoleParticularName(param1:String) : String
      {
         var _loc2_:String = null;
         switch(param1)
         {
            case "1":
               _loc2_ = "battle_role_attacktype";
               break;
            case "2":
               _loc2_ = "battle_role_killtype";
               break;
            case "3":
               _loc2_ = "battle_role_intelligencetype";
               break;
            case "4":
               _loc2_ = "battle_role_defensetype";
         }
         return _loc2_;
      }
      
      public function getDamageArray(param1:int, param2:int, param3:int) : Array
      {
         var _loc4_:Array = [];
         switch(param3 - 1)
         {
            case 0:
               break;
            default:
               _loc4_.push(param1);
         }
         return _loc4_;
      }
      
      public function indexRoleDirection(param1:uint) : uint
      {
         if(param1 == 7)
         {
            return 1;
         }
         return 0;
      }
      
      public function indexAnimationPreEndFrame(param1:String) : uint
      {
         var _loc2_:uint = 0;
         if(param1.indexOf("attacks") >= 0)
         {
            _loc2_ = 5;
         }
         else if(param1 == "monkey_magic_addBlood" || param1 == "monkey_magic_fire" || param1 == "monkey_magic_blood")
         {
            _loc2_ = 10;
         }
         else if(param1 == "Lightning")
         {
            _loc2_ = 10;
         }
         else if(param1 == "monkey_beAttack" || param1 == "m_beAttack" || param1 == "Criticals" || param1 == "monkey_attEff")
         {
            _loc2_ = 2;
         }
         else
         {
            _loc2_ = 4;
         }
         return _loc2_;
      }
      
      public function get roleMoedel() : XML
      {
         return this._roleMoedel;
      }
      
      public function set roleMoedel(param1:XML) : void
      {
         this._roleMoedel = param1;
      }
      
      public function get requireSource() : XML
      {
         return this._requireSource;
      }
      
      public function set requireSource(param1:XML) : void
      {
         this._requireSource = param1;
      }
      
      public function dispose() : void
      {
         if(this._swfJumpFrameConifg)
         {
            this._swfJumpFrameConifg.dispose();
         }
      }
   }
}

