package battleConfig
{
   public class BattleNotificationsNames
   {
      public static const BATTLE_START_PRELOAD:String = "BATTLE_START_PRELOAD";
      
      public static const BATTLE_PRELOAD_COMPLETE:String = "BATTLE_PRELOAD_COMPLETE";
      
      public static const BATTLE_START_LOAD_BATTLE_MODEL_SOURCE:String = "BATTLE_START_LOAD_BATTLE_MODEL_SOURCE";
      
      public static const BATTLE_PRELOAD_ERROR:String = "BATTLE_PRELOAD_ERROR";
      
      public static const BATTLE_JUMP_TO_CONFIG_BATTTEL:String = "BATTLE_JUMP_TO_CONFIG_BATTTEL";
      
      public static const BATTLE_JUMP_TO_BATTTEL:String = "BATTLE_JUMP_TO_BATTTEL";
      
      public static const BATTLE_SHOW_INFO_UI:String = "BATTLE_SHOW_INFO_UI";
      
      public static const BATTLE_REMOVE_INFO_UI:String = "BATTLE_REMOVE_INFO_UI";
      
      public static const BATTLE_SHOW_GAME_OVER_UI:String = "BATTLE_SHOW_GAME_OVER_UI";
      
      public static const BATTLE_REMOVE_GAME_OVER_UI:String = "BATTLE_REMOVE_GAME_OVER_UI";
      
      public static const BATTLE_REQUEST_REPLAY:String = "BATTLE_REPLAY";
      
      public static const BATTLE_ANIMATION_END:String = "BATTLE_ANIMATION_END";
      
      public static const BATTLE_FAKE_DATA:String = "BATTLE_FAKE_DATA";
      
      public static const BATTLE_REQUEST_QUIT:String = "BATTLE_REQUEST_QUIT";
      
      public static const BATTLE_ROUND_CHANGE:String = "BATTLE_ROUND_CHANGE";
      
      public static const BATTLE_INI_COMPLETE:String = "BATTLE_INI_COMPLETE";
      
      public static const BATTLE_ONE_HERO_ROUND_END:String = "BATTLE_ONE_HERO_ROUND_END";
      
      public static const BATTLE_CLOSE_RESULT_WINDOW:String = "BATTLE_CLOSE_RESULT_WINDOW";
      
      public static const HANDLE_BATTLE_DATA:String = "handleBattelData";
      
      public static const ADD_BENCH_TO_FORMATION:String = "ADD_BENCH_TO_FORMATION";
      
      public function BattleNotificationsNames()
      {
         super();
      }
   }
}

