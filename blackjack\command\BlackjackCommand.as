package blackjack.command
{
   import blackjack.proxy.BlackjackProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class BlackjackCommand extends SimpleCommand
   {
      public function BlackjackCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc3_:BlackjackProxy = facade.retrieveProxy("BlackjackProxy") as BlackjackProxy;
         var _loc2_:Object = param1.getBody();
         switch(param1.getName())
         {
            case "CS_TWENTYONESCORES_GETUSERINFO":
               _loc3_.getInfo(_loc2_);
               break;
            case "CS_TWENTYONESCORES_SENDCARDS":
               _loc3_.sendCards(_loc2_);
               break;
            case "CS_TWENTYONESCORES_CONTINUESENDCARD":
               _loc3_.continueSendCards(_loc2_);
               break;
            case "CS_TWENTYONESCORES_SENDCARDSAGAIN":
               _loc3_.sendCardsAgain(_loc2_);
               break;
            case "CS_TWENTYONESCORES_FETCHREWARD":
               _loc3_.fetchReward(_loc2_);
               break;
            case "CS_TWENTYONESCORES_REFRESHREWARD":
               _loc3_.refreshReward(_loc2_);
         }
      }
   }
}

