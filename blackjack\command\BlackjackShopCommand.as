package blackjack.command
{
   import blackjack.proxy.BlackjackShopProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class BlackjackShopCommand extends SimpleCommand
   {
      public function BlackjackShopCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc3_:BlackjackShopProxy = facade.retrieveProxy("BlackjackShopProxy") as BlackjackShopProxy;
         var _loc2_:Object = param1.getBody();
         switch(param1.getName())
         {
            case "CS_BLACKJACK_SHOP_BUYITEM":
               _loc3_.buyItem(_loc2_);
               break;
            case "CS_BLACKJACK_SHOP_GETINFO":
               _loc3_.getInfo();
         }
      }
   }
}

