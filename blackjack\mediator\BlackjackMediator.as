package blackjack.mediator
{
   import blackjack.command.BlackjackCommand;
   import blackjack.proxy.BlackjackProxy;
   import blackjack.view.BlackjackWindow;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.Core;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.SwitchButton;
   import mmo.ui.control.window.ConfirmSwitchShowWindow;
   import mmo.ui.event.ButtonEvent;
   import mmo.ui.event.TabEvent;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   
   public class BlackjackMediator extends PirateMediator
   {
      public static const NAME:String = "BlackjackMediator";
      
      public var isCanClick:Boolean = true;
      
      public function BlackjackMediator(param1:Object = null)
      {
         super("BlackjackMediator",param1);
         this.getView().showHander = this.show;
         this.getView().tab.addEventListener(TabEvent.Tab_IndexChange,this.tabHandler);
         this.getView().nomalSprite.startGame.addEventListener("click",this.startGameHandler);
         this.getView().highSprite.startGame.addEventListener("click",this.startHighGameHandler);
         this.getView().nomalSprite.faPaiBtn.addEventListener("click",this.faPaiBtnHandler);
         this.getView().highSprite.faPaiBtn.addEventListener("click",this.faPaiHighBtnHandler);
         this.getView().nomalSprite.nextGame.addEventListener("click",this.nextGameHandler);
         this.getView().highSprite.nextGame.addEventListener("click",this.nextHighGameHandler);
         this.getView().nomalSprite.getAwardBtn.addEventListener("click",this.getAwardHandler);
         this.getView().highSprite.getAwardBtn.addEventListener("click",this.getHighAwardHandler);
         this.getView().nomalSprite.oneItem.refreshBtn.addEventListener("click",this.refreshOneHandler);
         this.getView().nomalSprite.twoItem.refreshBtn.addEventListener("click",this.refreshTwoHandler);
         this.getView().nomalSprite.threeItem.refreshBtn.addEventListener("click",this.refreshThreeHandler);
         this.getView().highSprite.oneItem.refreshBtn.addEventListener("click",this.refreshHighOneHandler);
         this.getView().highSprite.twoItem.refreshBtn.addEventListener("click",this.refreshHighTwoHandler);
         this.getView().highSprite.threeItem.refreshBtn.addEventListener("click",this.refreshHighThreeHandler);
      }
      
      private function refreshHighThreeHandler(param1:MouseEvent) : void
      {
         var _loc4_:XML = null;
         var _loc2_:int = 0;
         var _loc3_:Array = null;
         if(this.getView().highSprite.arr.length > 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("peakednessModule.42"),
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
         }
         else
         {
            _loc4_ = XmlManager.twentyone_lucky.children()[0];
            _loc2_ = this.getView().highSprite.normalRefreshTime;
            if(_loc2_ < int(_loc4_.@seniorGameRefreshNum))
            {
               sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[2,3]);
            }
            else
            {
               _loc3_ = String(_loc4_.@seniorGameRefreshCost).split("|");
               if(int(_loc3_[2]) > MainData.getInstance().userData.gold_num)
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("peakednessModule.41"),
                     "textFormat":TextFormatLib.format_0x00FF00_12px
                  });
               }
               else
               {
                  sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[2,3]);
               }
            }
         }
      }
      
      private function refreshHighTwoHandler(param1:MouseEvent) : void
      {
         var _loc4_:XML = null;
         var _loc2_:int = 0;
         var _loc3_:Array = null;
         if(this.getView().highSprite.arr.length > 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("peakednessModule.42"),
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
         }
         else
         {
            _loc4_ = XmlManager.twentyone_lucky.children()[0];
            _loc2_ = this.getView().highSprite.normalRefreshTime;
            if(_loc2_ < int(_loc4_.@seniorGameRefreshNum))
            {
               sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[2,2]);
            }
            else
            {
               _loc3_ = String(_loc4_.@seniorGameRefreshCost).split("|");
               if(int(_loc3_[1]) > MainData.getInstance().userData.gold_num)
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("peakednessModule.41"),
                     "textFormat":TextFormatLib.format_0x00FF00_12px
                  });
               }
               else
               {
                  sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[2,2]);
               }
            }
         }
      }
      
      private function refreshHighOneHandler(param1:MouseEvent) : void
      {
         var _loc4_:XML = null;
         var _loc2_:int = 0;
         var _loc3_:Array = null;
         if(this.getView().highSprite.arr.length > 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("peakednessModule.42"),
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
         }
         else
         {
            _loc4_ = XmlManager.twentyone_lucky.children()[0];
            _loc2_ = this.getView().highSprite.normalRefreshTime;
            if(_loc2_ < int(_loc4_.@seniorGameRefreshNum))
            {
               sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[2,1]);
            }
            else
            {
               _loc3_ = String(_loc4_.@seniorGameRefreshCost).split("|");
               if(int(_loc3_[0]) > MainData.getInstance().userData.gold_num)
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("peakednessModule.41"),
                     "textFormat":TextFormatLib.format_0x00FF00_12px
                  });
               }
               else
               {
                  sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[2,1]);
               }
            }
         }
      }
      
      private function refreshThreeHandler(param1:MouseEvent) : void
      {
         var _loc4_:XML = null;
         var _loc2_:int = 0;
         var _loc3_:Array = null;
         if(this.getView().nomalSprite.arr.length > 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("peakednessModule.42"),
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
         }
         else
         {
            _loc4_ = XmlManager.twentyone_lucky.children()[0];
            _loc2_ = this.getView().nomalSprite.normalRefreshTime;
            if(_loc2_ < int(_loc4_.@juniorGameRefreshNum))
            {
               sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[1,3]);
            }
            else
            {
               _loc3_ = String(_loc4_.@juniorGameRefreshCost).split("|");
               if(int(_loc3_[2]) > MainData.getInstance().userData.gold_num)
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("peakednessModule.41"),
                     "textFormat":TextFormatLib.format_0x00FF00_12px
                  });
               }
               else
               {
                  sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[1,3]);
               }
            }
         }
      }
      
      private function refreshTwoHandler(param1:MouseEvent) : void
      {
         var _loc4_:XML = null;
         var _loc2_:int = 0;
         var _loc3_:Array = null;
         if(this.getView().nomalSprite.arr.length > 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("peakednessModule.42"),
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
         }
         else
         {
            _loc4_ = XmlManager.twentyone_lucky.children()[0];
            _loc2_ = this.getView().nomalSprite.normalRefreshTime;
            if(_loc2_ < int(_loc4_.@juniorGameRefreshNum))
            {
               sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[1,2]);
            }
            else
            {
               _loc3_ = String(_loc4_.@juniorGameRefreshCost).split("|");
               if(int(_loc3_[1]) > MainData.getInstance().userData.gold_num)
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("peakednessModule.41"),
                     "textFormat":TextFormatLib.format_0x00FF00_12px
                  });
               }
               else
               {
                  sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[1,2]);
               }
            }
         }
      }
      
      private function refreshOneHandler(param1:MouseEvent) : void
      {
         var _loc4_:XML = null;
         var _loc2_:int = 0;
         var _loc3_:Array = null;
         if(this.getView().nomalSprite.arr.length > 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("peakednessModule.42"),
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
         }
         else
         {
            _loc4_ = XmlManager.twentyone_lucky.children()[0];
            _loc2_ = this.getView().nomalSprite.normalRefreshTime;
            if(_loc2_ < int(_loc4_.@juniorGameRefreshNum))
            {
               sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[1,1]);
            }
            else
            {
               _loc3_ = String(_loc4_.@juniorGameRefreshCost).split("|");
               if(int(_loc3_[0]) > MainData.getInstance().userData.gold_num)
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("peakednessModule.41"),
                     "textFormat":TextFormatLib.format_0x00FF00_12px
                  });
               }
               else
               {
                  sendNotification("CS_TWENTYONESCORES_REFRESHREWARD",[1,1]);
               }
            }
         }
      }
      
      private function getHighAwardHandler(param1:MouseEvent) : void
      {
         if(this.isCanClick || this.getView().highSprite.isCanLook)
         {
            if(this.getView().highSprite.arr.length > 0)
            {
               this.isCanClick = false;
               this.getView().highSprite.isCanLook = false;
               sendNotification("CS_TWENTYONESCORES_FETCHREWARD",2);
            }
            else
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("peakednessModule.40"),
                  "textFormat":TextFormatLib.format_0x00FF00_12px
               });
            }
         }
      }
      
      private function getAwardHandler(param1:MouseEvent) : void
      {
         if(this.isCanClick || this.getView().nomalSprite.isCanLook)
         {
            if(this.getView().nomalSprite.arr.length > 0)
            {
               this.isCanClick = false;
               this.getView().nomalSprite.isCanLook = false;
               sendNotification("CS_TWENTYONESCORES_FETCHREWARD",1);
            }
            else
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("peakednessModule.40"),
                  "textFormat":TextFormatLib.format_0x00FF00_12px
               });
            }
         }
      }
      
      private function nextHighGameHandler(param1:MouseEvent) : void
      {
         if(this.isCanClick || this.getView().highSprite.isCanLook)
         {
            if(this.getView().highSprite.arr.length > 0)
            {
               this.isCanClick = false;
               this.getView().highSprite.isCanLook = false;
               sendNotification("CS_TWENTYONESCORES_SENDCARDSAGAIN",2);
            }
            else
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("peakednessModule.39"),
                  "textFormat":TextFormatLib.format_0x00FF00_12px
               });
            }
         }
      }
      
      private function nextGameHandler(param1:MouseEvent) : void
      {
         if(this.isCanClick || this.getView().nomalSprite.isCanLook)
         {
            if(this.getView().nomalSprite.arr.length > 0)
            {
               this.isCanClick = false;
               this.getView().nomalSprite.isCanLook = false;
               sendNotification("CS_TWENTYONESCORES_SENDCARDSAGAIN",1);
            }
            else
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("peakednessModule.39"),
                  "textFormat":TextFormatLib.format_0x00FF00_12px
               });
            }
         }
      }
      
      private function faPaiHighBtnHandler(param1:MouseEvent) : void
      {
         if(this.isCanClick || this.getView().highSprite.isCanLook)
         {
            this.isCanClick = false;
            this.getView().highSprite.isCanLook = false;
            sendNotification("CS_TWENTYONESCORES_CONTINUESENDCARD",2);
         }
      }
      
      private function faPaiBtnHandler(param1:MouseEvent) : void
      {
         if(this.isCanClick || this.getView().nomalSprite.isCanLook)
         {
            this.isCanClick = false;
            this.getView().nomalSprite.isCanLook = false;
            sendNotification("CS_TWENTYONESCORES_CONTINUESENDCARD",1);
         }
      }
      
      private function startGameHandler(param1:MouseEvent) : void
      {
         var _loc2_:XML = null;
         if(this.isCanClick || this.getView().nomalSprite.isCanLook)
         {
            _loc2_ = XmlManager.twentyone_lucky.children()[0];
            if(this.getView().nomalSprite.playTime > 0)
            {
               this.isCanClick = false;
               this.getView().nomalSprite.isCanLook = false;
               sendNotification("CS_TWENTYONESCORES_SENDCARDS",1);
            }
            else if(this.getView().nomalSprite.goldTime > 0)
            {
               if(MainData.getInstance().userData.belly_num < int(_loc2_.@juniorGameNumCost))
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("peakednessModule.38"),
                     "textFormat":TextFormatLib.format_0x00FF00_12px
                  });
               }
               else
               {
                  this.isCanClick = false;
                  this.getView().nomalSprite.isCanLook = false;
                  sendNotification("CS_TWENTYONESCORES_SENDCARDS",1);
               }
            }
            else
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("peakednessModule.35"),
                  "textFormat":TextFormatLib.format_0x00FF00_12px
               });
            }
         }
      }
      
      private function startHighGameHandler(param1:MouseEvent) : void
      {
         var _confirmPOP:Function;
         var xml:XML = null;
         var e:MouseEvent = param1;
         if(this.isCanClick || this.getView().highSprite.isCanLook)
         {
            xml = XmlManager.twentyone_lucky.children()[0];
            if(this.getView().highSprite.playTime > 0)
            {
               this.isCanClick = false;
               this.getView().highSprite.isCanLook = false;
               sendNotification("CS_TWENTYONESCORES_SENDCARDS",2);
            }
            else if(this.getView().highSprite.goldTime > 0)
            {
               if(MainData.getInstance().userData.gold_num < int(xml.@seniorGameNumCost))
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("peakednessModule.37"),
                     "textFormat":TextFormatLib.format_0x00FF00_12px
                  });
               }
               else if(!MainData.getInstance().closeGoldNoticeData.isCloseGoldNotice(83))
               {
                  _confirmPOP = function(param1:String, param2:String, param3:int, param4:String = ""):void
                  {
                     var pop:ConfirmSwitchShowWindow = null;
                     var dt:Array = null;
                     var _check:Function = null;
                     var popTXT:String = param1;
                     var note:String = param2;
                     var node:int = param3;
                     var onlyName:String = param4;
                     _check = function():void
                     {
                        dt = MainData.getInstance().closeGoldNoticeData.setCloseGoldNotic(node,(pop.cbAlertAgain as SwitchButton).isCheck);
                        AppFacade.instance.sendNotification("CS_SET_COSTNOTICE",dt);
                     };
                     pop = PopUpCenter.confirmSwitchShowWin(Core.guideView,onlyName,popTXT,compositionFun,null,0,true);
                     pop && (pop.cbAlertAgain.isCheck = false);
                     (pop.cbAlertAgain as SwitchButton).addEventListener(ButtonEvent.Button_Update,_check);
                  };
                  _confirmPOP(StringUtil.substitute("是否使用1个“黑杰克勋章”进行游戏？",int(xml.@seniorGameNumCost)),"CS_COMPOSITIONFRUIT",83,"NODE_FRUITCOMPOSITION_COSTGOLD");
               }
               else
               {
                  this.compositionFun();
               }
            }
            else
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("peakednessModule.35"),
                  "textFormat":TextFormatLib.format_0x00FF00_12px
               });
            }
         }
      }
      
      public function compositionFun() : void
      {
         this.isCanClick = false;
         this.getView().highSprite.isCanLook = false;
         sendNotification("CS_TWENTYONESCORES_SENDCARDS",2);
      }
      
      private function tabHandler(param1:Event) : void
      {
         var _loc2_:int = this.getView().tab.selectedIndex + 1;
         if(_loc2_ == 2)
         {
            if(this.getView().nomalSprite.arr.length > 0)
            {
               this.getView().tab.selectedIndex = this.getView().tab.selectedIndex - 1;
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("peakednessModule.34"),
                  "textFormat":TextFormatLib.format_0x00FF00_12px
               });
            }
            else
            {
               sendNotification("CS_TWENTYONESCORES_GETUSERINFO",_loc2_);
            }
         }
         else if(this.getView().highSprite.arr.length > 0)
         {
            this.getView().tab.selectedIndex = this.getView().tab.selectedIndex + 1;
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("peakednessModule.34"),
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
         }
         else
         {
            sendNotification("CS_TWENTYONESCORES_GETUSERINFO",_loc2_);
         }
      }
      
      public function show(param1:Object) : void
      {
         sendNotification("CS_TWENTYONESCORES_GETUSERINFO",1);
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_TWENTYONESCORES_GETUSERINFO","SC_TWENTYONESCORES_CONTINUESENDCARD","SC_TWENTYONESCORES_SENDCARDS","SC_TWENTYONESCORES_FETCHREWARD","SC_TWENTYONESCORES_REFRESHREWARD","SC_TWENTYONESCORES_SENDCARDSAGAIN"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc6_:Object = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:XML = null;
         switch(param1.getName())
         {
            case "SC_TWENTYONESCORES_GETUSERINFO":
               _loc6_ = param1.getBody();
               _loc4_ = this.getView().tab.selectedIndex;
               if(_loc4_ != int(_loc6_.type - 1))
               {
                  _loc4_ == int(_loc6_.type - 1);
                  this.getView().tab.selectedIndex = this.getView().tab.selectedIndex + 1;
               }
               if(_loc4_ == 0)
               {
                  this.getView().nomalSprite.setData(_loc6_);
                  this.setNextGame(_loc6_.va_info.round.scores,1);
               }
               else
               {
                  _loc6_ = param1.getBody();
                  this.getView().highSprite.setData(_loc6_);
                  this.setNextGame(_loc6_.va_info.round.scores,2);
               }
               this.isCanClick = true;
               break;
            case "SC_TWENTYONESCORES_SENDCARDS":
               _loc4_ = this.getView().tab.selectedIndex;
               if(_loc4_ == 0)
               {
                  _loc3_ = XmlManager.twentyone_lucky.children()[0];
                  if(this.getView().nomalSprite.playTime < 1)
                  {
                     MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num - int(_loc3_.@juniorGameNumCost);
                  }
                  _loc6_ = param1.getBody();
                  this.getView().nomalSprite.setCardsData(_loc6_);
                  this.setNextGame(_loc6_.totalPoints,1);
                  this.isCanClick = this.getView().nomalSprite.isCanLook;
               }
               else
               {
                  _loc3_ = XmlManager.twentyone_lucky.children()[0];
                  if(this.getView().highSprite.playTime < 1)
                  {
                     MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - int(_loc3_.@seniorGameNumCost);
                  }
                  _loc6_ = param1.getBody();
                  this.getView().highSprite.setCardsData(_loc6_);
                  this.setNextGame(_loc6_.totalPoints,2);
                  this.isCanClick = this.getView().highSprite.isCanLook;
               }
               break;
            case "SC_TWENTYONESCORES_CONTINUESENDCARD":
               _loc4_ = this.getView().tab.selectedIndex;
               _loc6_ = param1.getBody();
               if(_loc4_ == 0)
               {
                  this.getView().nomalSprite.faPaiSetData(_loc6_);
                  this.setNextGame(_loc6_.totalPoints,1);
                  this.isCanClick = this.getView().nomalSprite.isCanLook;
               }
               else
               {
                  this.getView().highSprite.faPaiSetData(_loc6_);
                  this.setNextGame(_loc6_.totalPoints,2);
                  this.isCanClick = this.getView().highSprite.isCanLook;
               }
               break;
            case "SC_TWENTYONESCORES_FETCHREWARD":
               _loc4_ = this.getView().tab.selectedIndex;
               if(_loc4_ == 0)
               {
                  this.getView().nomalSprite.clearData();
               }
               else
               {
                  this.getView().highSprite.clearData();
               }
               this.isCanClick = true;
               break;
            case "SC_TWENTYONESCORES_REFRESHREWARD":
               _loc5_ = int(param1.getBody()[0][0]);
               _loc2_ = int(param1.getBody()[0][1]);
               _loc6_ = param1.getBody()[1];
               if(_loc5_ == 1)
               {
                  this.getView().nomalSprite.normalRefreshTime = this.getView().nomalSprite.normalRefreshTime + 1;
                  this.getView().nomalSprite.oneItem.setTips(this.getView().nomalSprite.normalRefreshTime,_loc5_,0);
                  this.getView().nomalSprite.twoItem.setTips(this.getView().nomalSprite.normalRefreshTime,_loc5_,1);
                  this.getView().nomalSprite.threeItem.setTips(this.getView().nomalSprite.normalRefreshTime,_loc5_,2);
                  if(_loc2_ == 1)
                  {
                     this.getView().nomalSprite.oneItem.setItemData(_loc6_);
                     MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - this.getView().nomalSprite.oneItem.costGold(this.getView().nomalSprite.normalRefreshTime - 1,_loc5_,0);
                  }
                  else if(_loc2_ == 2)
                  {
                     this.getView().nomalSprite.twoItem.setItemData(_loc6_);
                     MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - this.getView().nomalSprite.twoItem.costGold(this.getView().nomalSprite.normalRefreshTime - 1,_loc5_,1);
                  }
                  else
                  {
                     this.getView().nomalSprite.threeItem.setItemData(_loc6_);
                     MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - this.getView().nomalSprite.threeItem.costGold(this.getView().nomalSprite.normalRefreshTime - 1,_loc5_,2);
                  }
               }
               else
               {
                  this.getView().highSprite.normalRefreshTime = this.getView().highSprite.normalRefreshTime + 1;
                  this.getView().highSprite.oneItem.setTips(this.getView().highSprite.normalRefreshTime,_loc5_,0);
                  this.getView().highSprite.twoItem.setTips(this.getView().highSprite.normalRefreshTime,_loc5_,1);
                  this.getView().highSprite.threeItem.setTips(this.getView().highSprite.normalRefreshTime,_loc5_,2);
                  if(_loc2_ == 1)
                  {
                     this.getView().highSprite.oneItem.setItemData(_loc6_);
                     MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - this.getView().highSprite.oneItem.costGold(this.getView().highSprite.normalRefreshTime - 1,_loc5_,0);
                  }
                  else if(_loc2_ == 2)
                  {
                     this.getView().highSprite.twoItem.setItemData(_loc6_);
                     MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - this.getView().highSprite.twoItem.costGold(this.getView().highSprite.normalRefreshTime - 1,_loc5_,1);
                  }
                  else
                  {
                     this.getView().highSprite.threeItem.setItemData(_loc6_);
                     MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - this.getView().highSprite.threeItem.costGold(this.getView().highSprite.normalRefreshTime - 1,_loc5_,2);
                  }
               }
               this.isCanClick = true;
               break;
            case "SC_TWENTYONESCORES_SENDCARDSAGAIN":
               _loc4_ = this.getView().tab.selectedIndex;
               if(_loc4_ == 0)
               {
                  this.getView().nomalSprite.clearData();
                  this.getView().nomalSprite.curRound = this.getView().nomalSprite.curRound + 1;
                  _loc6_ = param1.getBody();
                  this.getView().nomalSprite.setCardsData(_loc6_,false);
                  this.setNextGame(_loc6_.totalPoints,1);
               }
               else
               {
                  this.getView().highSprite.clearData();
                  this.getView().highSprite.curRound = this.getView().highSprite.curRound + 1;
                  _loc6_ = param1.getBody();
                  this.getView().highSprite.setCardsData(_loc6_,false);
                  this.setNextGame(_loc6_.totalPoints,2);
               }
               this.isCanClick = true;
         }
      }
      
      public function setNextGame(param1:int, param2:int) : void
      {
         if(param2 == 1)
         {
            if(param1 > 18 && param1 < 22)
            {
               this.getView().nomalSprite.nextGame.filters = [];
               this.getView().nomalSprite.nextGame.addEventListener("click",this.nextGameHandler);
            }
            else
            {
               this.getView().nomalSprite.nextGame.filters = [FilterLib.enbaleFilter];
               this.getView().nomalSprite.nextGame.removeEventListener("click",this.nextGameHandler);
            }
            if(this.getView().nomalSprite.curRound > 2)
            {
               this.getView().nomalSprite.nextGame.filters = [FilterLib.enbaleFilter];
               this.getView().nomalSprite.nextGame.removeEventListener("click",this.nextGameHandler);
            }
         }
         else
         {
            if(param1 > 18 && param1 < 22)
            {
               this.getView().highSprite.nextGame.filters = [];
               this.getView().highSprite.nextGame.addEventListener("click",this.nextHighGameHandler);
            }
            else
            {
               this.getView().highSprite.nextGame.filters = [FilterLib.enbaleFilter];
               this.getView().highSprite.nextGame.removeEventListener("click",this.nextHighGameHandler);
            }
            if(this.getView().highSprite.curRound > 2)
            {
               this.getView().highSprite.nextGame.filters = [FilterLib.enbaleFilter];
               this.getView().highSprite.nextGame.removeEventListener("click",this.nextHighGameHandler);
            }
         }
      }
      
      override public function onRegister() : void
      {
         facade.registerProxy(new BlackjackProxy());
         facade.registerCommand("CS_TWENTYONESCORES_GETUSERINFO",BlackjackCommand);
         facade.registerCommand("CS_TWENTYONESCORES_SENDCARDS",BlackjackCommand);
         facade.registerCommand("CS_TWENTYONESCORES_CONTINUESENDCARD",BlackjackCommand);
         facade.registerCommand("CS_TWENTYONESCORES_SENDCARDSAGAIN",BlackjackCommand);
         facade.registerCommand("CS_TWENTYONESCORES_FETCHREWARD",BlackjackCommand);
         facade.registerCommand("CS_TWENTYONESCORES_REFRESHREWARD",BlackjackCommand);
      }
      
      override public function onRemove() : void
      {
         facade.removeCommand("CS_TWENTYONESCORES_GETUSERINFO");
         facade.removeCommand("CS_TWENTYONESCORES_SENDCARDS");
         facade.removeCommand("CS_TWENTYONESCORES_CONTINUESENDCARD");
         facade.removeCommand("CS_TWENTYONESCORES_SENDCARDSAGAIN");
         facade.removeCommand("CS_TWENTYONESCORES_FETCHREWARD");
         facade.removeCommand("CS_TWENTYONESCORES_REFRESHREWARD");
      }
      
      public function getView() : BlackjackWindow
      {
         return this.viewComponent as BlackjackWindow;
      }
   }
}

