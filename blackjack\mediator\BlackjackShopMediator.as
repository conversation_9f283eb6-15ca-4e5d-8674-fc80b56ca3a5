package blackjack.mediator
{
   import blackjack.command.BlackjackShopCommand;
   import blackjack.proxy.BlackjackShopProxy;
   import blackjack.view.BlackJackShopWindow;
   import blackjack.view.PrizeExchangeWin;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.events.BaseEvent;
   import game.items.ItemManager;
   import game.items.ItemQualityInfo;
   import game.items.framework.interfaces.IBasicInterface;
   import game.manager.XmlManager;
   import game.modules.chat.msgInfo.MessageReceive;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   
   public class BlackjackShopMediator extends PirateMediator
   {
      public static const NAME:String = "BlackjackShopMediator";
      
      private var currentIntegral:int;
      
      private var exchangeItemInfo:Object;
      
      public function BlackjackShopMediator(param1:BlackJackShopWindow = null)
      {
         super("BlackjackShopMediator",param1);
         this.view.showHander = this.showHander;
         this.view.addEventListener("BlackJackShopWindow.PROXY.EXCHANGEPRIZE",this.exchangePrize);
      }
      
      private function exchangePrize(param1:BaseEvent) : void
      {
         var itemInfo:Object;
         var tempId:int;
         var unitIntegral:int;
         var lv:int;
         var prestige:int;
         var userLv:int;
         var userPrestige:int;
         var userBagGridNum:int;
         var temp:IBasicInterface;
         var itemName:String;
         var color:uint;
         var itemColor:String;
         var exchangeId:int = 0;
         var exchangeNum:int = 0;
         var popText:String = null;
         var event:BaseEvent = param1;
         exchangeId = int(event.data[0]);
         exchangeNum = int(event.data[2]);
         var exchangeTipsStr:String = Globalization.getString("peakednessModule.33");
         var exchangeTimes:int = 0;
         if(this.exchangeItemInfo[exchangeId])
         {
            exchangeTimes = int(this.exchangeItemInfo[exchangeId]);
         }
         itemInfo = PrizeExchangeWin.getExchangeInfoByExchangeId(exchangeId);
         tempId = int(itemInfo.tempId);
         unitIntegral = int(itemInfo.integral);
         lv = int(itemInfo.lv);
         prestige = int(itemInfo.prestige);
         userLv = MainData.getInstance().groupData.roleModle.level;
         userPrestige = MainData.getInstance().userData.prestige_num;
         if(itemInfo.limitTimes > 0)
         {
            if(exchangeTimes >= itemInfo.limitTimes)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("AttackTeamGroupUpMoudle.2"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
         }
         if(this.currentIntegral < unitIntegral * exchangeNum)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":exchangeTipsStr,
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(userLv < lv)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("AttackTeamGroupUpMoudle.3"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(userPrestige < prestige)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("AttackTeamGroupUpMoudle.4"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         userBagGridNum = MainData.getInstance().bagData.userBag.getLastGridsNum();
         if(userBagGridNum == 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("AttackTeamGroupUpMoudle.5"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         temp = ItemManager.getInstance().getItemTemplate(tempId.toString());
         itemName = temp.name;
         color = ItemQualityInfo.getQualityColor(temp.quality);
         itemColor = MessageReceive.parseColor(color);
         if(itemInfo.limitTimes > 0)
         {
            popText = StringUtil.substitute(StringUtil.substitute(Globalization.getString("AttackTeamGroupUpMoudle.6"),itemName,itemColor),itemInfo.limitTimes - exchangeTimes,itemInfo.getNum * exchangeNum);
         }
         else
         {
            popText = StringUtil.substitute(Globalization.getString("AttackTeamGroupUpMoudle.7"),itemName,itemColor,itemInfo.getNum * exchangeNum);
         }
         PopUpCenter.confirmWin2(popText,function():void
         {
            sendNotification("CS_BLACKJACK_SHOP_BUYITEM",{
               "id":exchangeId,
               "num":exchangeNum
            });
         },null,Globalization.getString("Globalization.37"),Globalization.getString("Globalization.38"),true);
      }
      
      override public function onRegister() : void
      {
         facade.registerProxy(new BlackjackShopProxy());
         facade.registerCommand("CS_BLACKJACK_SHOP_BUYITEM",BlackjackShopCommand);
         facade.registerCommand("CS_BLACKJACK_SHOP_GETINFO",BlackjackShopCommand);
      }
      
      override public function onRemove() : void
      {
         facade.removeProxy("BlackjackShopProxy");
         facade.removeCommand("CS_BLACKJACK_SHOP_BUYITEM");
         facade.removeCommand("CS_BLACKJACK_SHOP_GETINFO");
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_BLACKJACK_SHOP_BUYITEM","SC_BLACKJACK_SHOP_GETINFO"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc2_:Object = param1.getBody();
         switch(param1.getName())
         {
            case "SC_BLACKJACK_SHOP_BUYITEM":
               if(PopUpCenter.containsWin("PrizeExchangeWin"))
               {
                  PopUpCenter.removePopUp("PrizeExchangeWin");
               }
               sendNotification("CS_BLACKJACK_SHOP_GETINFO");
               break;
            case "SC_BLACKJACK_SHOP_GETINFO":
               this.initData(_loc2_);
         }
      }
      
      private function initData(param1:Object) : void
      {
         this.setShopItem(param1);
      }
      
      private function setShopItem(param1:Object) : void
      {
         var _loc4_:XML = null;
         var _loc2_:Object = null;
         this.view.surplusNum.text = param1.score;
         this.currentIntegral = param1.score;
         this.exchangeItemInfo = param1.exiteminfo;
         var _loc5_:XMLList = XmlManager.twentyone_shop.twentyone_shop;
         var _loc3_:Array = [];
         for each(_loc4_ in _loc5_)
         {
            _loc2_ = {};
            _loc2_.integral = int(_loc4_.@consumeScore);
            _loc2_.getNum = int(_loc4_.@limitTime);
            _loc2_.lv = int(_loc4_.@neeLevel);
            _loc2_.tempId = int(_loc4_.@itemTempId);
            _loc2_.exchangeId = int(_loc4_.@id);
            _loc2_.canBatch = int(_loc4_.@canBatch);
            _loc2_.limitTime = int(_loc4_.@limitTime);
            _loc2_.exchangeId = int(_loc4_.@id);
            if(this.exchangeItemInfo[_loc4_.@id])
            {
               _loc2_.exchangeTimes = int(this.exchangeItemInfo[_loc4_.@id]);
            }
            _loc3_.push(_loc2_);
         }
         this.view.setData(_loc3_);
      }
      
      private function showHander(param1:Object) : void
      {
         checkDataAvialable(this.showCallBack);
      }
      
      private function showCallBack() : void
      {
         this.initItem();
         sendNotification("CS_BLACKJACK_SHOP_GETINFO");
      }
      
      private function initItem() : void
      {
         var _loc1_:XML = null;
         var _loc2_:Object = null;
         var _loc4_:XMLList = XmlManager.twentyone_shop.twentyone_shop;
         var _loc3_:Array = [];
         for each(_loc1_ in _loc4_)
         {
            _loc2_ = {};
            _loc2_.integral = int(_loc1_.@consumeScore);
            _loc2_.getNum = int(_loc1_.@limitTime);
            _loc2_.lv = int(_loc1_.@neeLevel);
            _loc2_.tempId = int(_loc1_.@itemTempId);
            _loc2_.exchangeId = int(_loc1_.@id);
            _loc2_.canBatch = int(_loc1_.@canBatch);
            _loc2_.limitTime = int(_loc1_.@limitTime);
            _loc2_.exchangeId = int(_loc1_.@id);
            _loc3_.push(_loc2_);
         }
         this.view.setData(_loc3_);
      }
      
      public function get view() : BlackJackShopWindow
      {
         return viewComponent as BlackJackShopWindow;
      }
   }
}

