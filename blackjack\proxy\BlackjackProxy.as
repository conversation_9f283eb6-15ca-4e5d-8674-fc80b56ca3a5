package blackjack.proxy
{
   import game.data.MainData;
   import game.items.ItemQualityInfo;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.mvc.AppFacade;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   
   public class BlackjackProxy extends Proxy
   {
      public static const NAME:String = "BlackjackProxy";
      
      public function BlackjackProxy()
      {
         super("BlackjackProxy");
      }
      
      public function getInfo(param1:Object) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.twentyOneScores.getUserInfo",this.getInfoReturn);
         BabelTimeSocket.getInstance().sendMessage("twentyOneScores.getUserInfo",new SocketCallback("re.twentyOneScores.getUserInfo",[param1]),param1);
      }
      
      private function getInfoReturn(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.twentyOneScores.getUserInfo",this.getInfoReturn);
         if(param1.error == "ok")
         {
            sendNotification("SC_TWENTYONESCORES_GETUSERINFO",param1.data);
         }
      }
      
      public function sendCards(param1:Object) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.twentyOneScores.sendCards",this.sendCardsReturn);
         BabelTimeSocket.getInstance().sendMessage("twentyOneScores.sendCards",new SocketCallback("re.twentyOneScores.sendCards",[param1]),param1);
      }
      
      public function sendCardsReturn(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.twentyOneScores.sendCards",this.sendCardsReturn);
         if(param1.data.info == "error")
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":param1.data.mess,
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            return;
         }
         if(param1.error == "ok")
         {
            sendNotification("SC_TWENTYONESCORES_SENDCARDS",param1.data);
         }
      }
      
      public function continueSendCards(param1:Object) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.twentyOneScores.continueSendCards",this.continueSendCardsReturn);
         BabelTimeSocket.getInstance().sendMessage("twentyOneScores.continueSendCards",new SocketCallback("re.twentyOneScores.continueSendCards",[param1]),param1);
      }
      
      private function continueSendCardsReturn(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.twentyOneScores.continueSendCards",this.continueSendCardsReturn);
         if(param1.error == "ok")
         {
            sendNotification("SC_TWENTYONESCORES_CONTINUESENDCARD",param1.data);
         }
      }
      
      public function sendCardsAgain(param1:Object) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.twentyOneScores.sendCardsAgain",this.sendCardsAgainReturn);
         BabelTimeSocket.getInstance().sendMessage("twentyOneScores.sendCardsAgain",new SocketCallback("re.twentyOneScores.sendCardsAgain",[param1]),param1);
      }
      
      private function sendCardsAgainReturn(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.twentyOneScores.sendCardsAgain",this.sendCardsAgainReturn);
         if(param1.error == "ok")
         {
            sendNotification("SC_TWENTYONESCORES_SENDCARDSAGAIN",param1.data);
         }
      }
      
      public function fetchReward(param1:Object) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.twentyOneScores.fetchRewards",this.fetchRewardReturn);
         BabelTimeSocket.getInstance().sendMessage("twentyOneScores.fetchRewards",new SocketCallback("re.twentyOneScores.fetchRewards",[param1]),param1);
      }
      
      private function fetchRewardReturn(param1:SocketDataEvent) : void
      {
         var _loc7_:String = null;
         var _loc5_:* = undefined;
         var _loc6_:int = 0;
         var _loc2_:Item = null;
         var _loc3_:uint = 0;
         var _loc4_:String = null;
         BabelTimeSocket.getInstance().removeCallback("re.twentyOneScores.fetchRewards",this.fetchRewardReturn);
         if(param1.error == "ok")
         {
            sendNotification("SC_TWENTYONESCORES_FETCHREWARD");
            _loc7_ = Globalization.getString("peakednessModule.32");
            for(_loc5_ in param1.data)
            {
               _loc6_ = MainData.getInstance().bagData.userBag.numItem(param1.data[_loc5_].item_id);
               _loc2_ = ItemFactory.creatItem(param1.data[_loc5_]);
               MainData.getInstance().bagData.setGridItem(_loc5_,_loc2_);
               _loc3_ = ItemQualityInfo.getQualityColor(_loc2_.quality);
               _loc4_ = MessageReceive.parseColor(_loc3_);
               _loc7_ += "\n" + StringUtil.substitute("<font color = \'{0}\'>{1}*{2}</font>",_loc4_,_loc2_.name,String(param1.data[_loc5_].item_num - _loc6_));
            }
            sendNotification("POP_TEXT_TIPS",{
               "text":_loc7_,
               "textFormat":TextFormatLib.format_0x00FF00_14px,
               "runTime":4
            });
         }
      }
      
      public function refreshReward(param1:Object) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.twentyOneScores.refreshRewards",this.refreshRewardReturn);
         BabelTimeSocket.getInstance().sendMessage("twentyOneScores.refreshRewards",new SocketCallback("re.twentyOneScores.refreshRewards",[param1]),param1[0],param1[1]);
      }
      
      private function refreshRewardReturn(param1:SocketDataEvent) : void
      {
         var _loc2_:Array = null;
         BabelTimeSocket.getInstance().removeCallback("re.twentyOneScores.refreshRewards",this.refreshRewardReturn);
         if(param1.error == "ok")
         {
            _loc2_ = param1.callbackParames[0];
            sendNotification("SC_TWENTYONESCORES_REFRESHREWARD",[_loc2_,param1.data]);
         }
      }
   }
}

