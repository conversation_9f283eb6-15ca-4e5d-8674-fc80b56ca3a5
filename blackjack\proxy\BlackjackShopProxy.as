package blackjack.proxy
{
   import blackjack.view.PrizeExchangeWin;
   import game.data.MainData;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   
   public class BlackjackShopProxy extends Proxy
   {
      public static const NAME:String = "BlackjackShopProxy";
      
      public function BlackjackShopProxy(param1:Object = null)
      {
         super("BlackjackShopProxy",param1);
      }
      
      public function buyItem(param1:Object) : void
      {
         var _loc4_:uint = uint(param1.id);
         var _loc2_:uint = uint(param1.num);
         var _loc3_:Object = PrizeExchangeWin.getExchangeInfoByExchangeId(_loc4_);
         BabelTimeSocket.getInstance().regCallback("re.exchangeshop.exItem",this.onBuyItemCallBack);
         BabelTimeSocket.getInstance().sendMessage("exchangeshop.exItem",new SocketCallback("re.exchangeshop.exItem",[_loc3_,_loc2_]),"twentyoneshop",_loc4_,_loc2_);
      }
      
      private function onBuyItemCallBack(param1:SocketDataEvent) : void
      {
         var _loc7_:Object = null;
         var _loc5_:* = undefined;
         var _loc6_:Object = null;
         var _loc2_:Object = null;
         var _loc3_:String = null;
         var _loc4_:Item = null;
         BabelTimeSocket.getInstance().removeCallback("re.exchangeshop.exItem",this.onBuyItemCallBack);
         if(param1.error == "ok")
         {
            _loc7_ = param1.data;
            if(param1.data == "noPoint")
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("AttackTeamGroupUpMoudle.9"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px
               });
               return;
            }
            if(param1.data == "noPrestige")
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("AttackTeamGroupUpMoudle.10"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px
               });
               return;
            }
            if(param1.data == "noLevel")
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("AttackTeamGroupUpMoudle.11"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px
               });
               return;
            }
            if(param1.data == "noExTimes")
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("AttackTeamGroupUpMoudle.12"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px
               });
               return;
            }
            for(_loc5_ in _loc7_)
            {
               if(_loc7_[_loc5_].item_template_id)
               {
                  _loc4_ = ItemFactory.creatItem(_loc7_[_loc5_]);
                  MainData.getInstance().bagData.setGridItem(_loc5_,_loc4_);
               }
            }
            _loc6_ = param1.callbackParames[0];
            _loc2_ = param1.callbackParames[1];
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("AttackTeamGroupUpMoudle.13"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
            _loc3_ = MessageReceive.getItemColorName(_loc6_.tempId);
            sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("activity.67"),_loc3_,_loc2_),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            sendNotification("SC_BLACKJACK_SHOP_BUYITEM",_loc7_);
         }
      }
      
      public function getInfo() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.exchangeshop.exchangShopInfo",this.onGetInfoCallBack);
         BabelTimeSocket.getInstance().sendMessage("exchangeshop.exchangShopInfo",new SocketCallback("re.exchangeshop.exchangShopInfo"),"twentyoneshop");
      }
      
      private function onGetInfoCallBack(param1:SocketDataEvent) : void
      {
         var _loc2_:Object = null;
         if(param1.error == "ok")
         {
            _loc2_ = param1.data;
            BabelTimeSocket.getInstance().removeCallback("re.exchangeshop.exchangShopInfo",this.onGetInfoCallBack);
            sendNotification("SC_BLACKJACK_SHOP_GETINFO",_loc2_);
         }
      }
   }
}

