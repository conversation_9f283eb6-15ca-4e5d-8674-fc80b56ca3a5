package blackjack.view
{
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import game.events.PageNavigatorEvent;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.page.PageNavigator;
   import util.Globalization;
   
   public class AttackShopUI extends UISprite
   {
      public static const LIST_MAX_NUM:int = 9;
      
      private var _itemBox:UIBox;
      
      private var _pageBtn:PageNavigator;
      
      private var _itemDataArr:Array;
      
      private var _curPage:int = 1;
      
      private var _itemListArr:Array = [];
      
      public var exchangeFun:Function;
      
      public function AttackShopUI()
      {
         super();
         this._itemBox = new UIBox();
         this._itemBox.x = 12;
         this._itemBox.y = 40;
         this._itemBox.lineMaxChildrenNumber = 3;
         this._itemBox.rowMaxChildrenNumber = 3;
         this._itemBox.lineSpace = 8;
         this._itemBox.rowSpace = 4;
         this.addChild(this._itemBox);
         this._pageBtn = new PageNavigator();
         this._pageBtn.x = 244;
         this._pageBtn.y = 432;
         this.addChild(this._pageBtn);
         this._pageBtn.addEventListener("pageChange",this.onPageChange);
      }
      
      private function onPageChange(param1:PageNavigatorEvent) : void
      {
         this.freshItemList(this._itemDataArr);
      }
      
      private function freshItemList(param1:Array) : void
      {
         var _loc2_:Object = null;
         var _loc3_:int = 0;
         var _loc5_:PrizeExchangeItem = null;
         var _loc4_:PrizeExchangeItem = null;
         while(this._itemBox.numChildren)
         {
            _loc5_ = this._itemBox.removeChildAt(0) as PrizeExchangeItem;
            _loc5_.exchangeBtn.removeEventListener("click",this.onExchangeHandler);
         }
         this._curPage = this._pageBtn.currentPage;
         var _loc8_:int = int(this._pageBtn.totalPage);
         var _loc6_:int = (this._curPage - 1) * 9;
         var _loc7_:int = this._curPage >= _loc8_ ? int(param1.length) : this._curPage * 9;
         while(_loc6_ < _loc7_)
         {
            _loc2_ = param1[_loc6_];
            if(this._itemListArr[_loc6_ % 9])
            {
               _loc4_ = this._itemListArr[_loc6_ % 9];
            }
            else
            {
               _loc4_ = new PrizeExchangeItem();
               this._itemListArr.push(_loc4_);
            }
            _loc4_.setExchangeData(_loc2_.exchangeId,_loc2_.tempId,_loc2_.getNum,_loc2_.integral,_loc2_.lv,_loc2_.prestige);
            this._itemBox.addChild(_loc4_);
            _loc4_.exchangeBtn.addEventListener("click",this.onExchangeHandler);
            if(_loc2_.getNum > 0)
            {
               _loc3_ = int(_loc2_.exchangeTimes);
               if(_loc3_ >= _loc2_.limitTime)
               {
                  _loc4_.exchangeBtn.enabled = false;
                  _loc4_.exchangeBtn.text = Globalization.getString("activity.105");
               }
               else
               {
                  _loc4_.exchangeBtn.enabled = true;
                  _loc4_.exchangeBtn.text = Globalization.getString("equipmentExchange.5");
               }
            }
            else
            {
               _loc4_.exchangeBtn.enabled = true;
               _loc4_.exchangeBtn.text = Globalization.getString("equipmentExchange.5");
            }
            _loc4_.exchangeBtn.addEventListener("click",this.onExchangeHandler);
            this._itemBox.addChild(_loc4_);
            _loc6_++;
         }
      }
      
      private function onExchangeHandler(param1:MouseEvent) : void
      {
         var _loc4_:PrizeExchangeItem = param1.target.parent as PrizeExchangeItem;
         var _loc2_:Point = _loc4_.itemSlot.localToGlobal(new Point(0,0));
         var _loc3_:Object = {
            "exchangeId":_loc4_.exchangeId,
            "point":_loc2_
         };
         this.exchangeFun && this.exchangeFun(_loc3_);
      }
      
      public function setHonourShopItem(param1:Array) : void
      {
         if(param1 != null && param1.length != 0)
         {
            this._itemDataArr = param1;
            this._pageBtn.init(this._curPage,Math.ceil(param1.length / 9));
            this.freshItemList(param1);
         }
      }
   }
}

