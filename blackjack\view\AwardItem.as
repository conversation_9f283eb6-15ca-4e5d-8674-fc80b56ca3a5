package blackjack.view
{
   import flash.display.MovieClip;
   import game.items.ItemManager;
   import game.items.framework.interfaces.IBasicInterface;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class AwardItem extends UISprite
   {
      private var _blackEffect:MovieClip;
      
      public var textName:UISkin;
      
      public var slot:Slot;
      
      public var slot1:Slot;
      
      public var slot2:Slot;
      
      public var slot3:Slot;
      
      public var slot4:Slot;
      
      public var slot5:Slot;
      
      public var refreshBtn:Button;
      
      public function AwardItem(param1:int = 1)
      {
         var _loc2_:UISkin = null;
         super();
         var _loc3_:UISkin = UIManager.getUISkin("bg_hero_normal");
         _loc3_.x = 10;
         _loc3_.y = 10;
         _loc3_.setSize(568,65);
         addChild(_loc3_);
         if(param1 == 1)
         {
            this.textName = UIManager.getUISkin("ThreeNumErShiYi");
         }
         else if(param1 == 2)
         {
            this.textName = UIManager.getUISkin("OneNumErShiYi");
         }
         else
         {
            this.textName = UIManager.getUISkin("TwoNumErShiYi");
         }
         this.textName.x = 23;
         this.textName.y = 25;
         addChild(this.textName);
         _loc2_ = UIManager.getUISkin("unionShopItemBorder");
         _loc2_.x = 95;
         _loc2_.y = 10;
         _loc2_.setSize(130,66);
         addChild(_loc2_);
         this.slot = new Slot();
         this.slot.x = 105;
         this.slot.y = 20;
         addChild(this.slot);
         this.slot1 = new Slot();
         this.slot1.x = 165;
         this.slot1.y = 20;
         addChild(this.slot1);
         _loc2_ = UIManager.getUISkin("unionShopItemBorder");
         _loc2_.x = 235;
         _loc2_.y = 10;
         _loc2_.setSize(130,66);
         addChild(_loc2_);
         this.slot2 = new Slot();
         this.slot2.x = 245;
         this.slot2.y = 20;
         addChild(this.slot2);
         this.slot3 = new Slot();
         this.slot3.x = 300;
         this.slot3.y = 20;
         addChild(this.slot3);
         _loc2_ = UIManager.getUISkin("unionShopItemBorder");
         _loc2_.x = 375;
         _loc2_.y = 10;
         _loc2_.setSize(130,66);
         addChild(_loc2_);
         this.slot4 = new Slot();
         this.slot4.x = 385;
         this.slot4.y = 20;
         addChild(this.slot4);
         this.slot5 = new Slot();
         this.slot5.x = 445;
         this.slot5.y = 20;
         addChild(this.slot5);
         this.refreshBtn = new Button(Globalization.getString("peakednessModule.43"),TextFormatLib.format_0xFFB932_12px_songti,70,UIManager.getMultiUISkin("btn_normal"));
         this.refreshBtn.x = 505;
         this.refreshBtn.y = 25;
         addChild(this.refreshBtn);
      }
      
      public function clearEquipInfo() : void
      {
         if(this._blackEffect)
         {
            this._blackEffect.gotoAndStop(1);
            this._blackEffect.parent && this._blackEffect.parent.removeChild(this._blackEffect);
            this._blackEffect = null;
         }
      }
      
      public function costGold(param1:int, param2:int, param3:int) : int
      {
         var _loc5_:Array = null;
         var _loc6_:int = 0;
         var _loc4_:XML = XmlManager.twentyone_lucky.children()[0];
         if(param2 == 1)
         {
            if(param1 < int(_loc4_.@juniorGameRefreshNum))
            {
               _loc6_ = 0;
            }
            else
            {
               _loc5_ = String(_loc4_.@juniorGameRefreshCost).split("|");
               _loc6_ = int(_loc5_[param3]);
            }
         }
         else if(param1 < int(_loc4_.@seniorGameRefreshNum))
         {
            _loc6_ = 0;
         }
         else
         {
            _loc5_ = String(_loc4_.@seniorGameRefreshCost).split("|");
            _loc6_ = int(_loc5_[param3]);
         }
         return _loc6_;
      }
      
      public function setTips(param1:int, param2:int, param3:int) : void
      {
         var _loc4_:Array = null;
         var _loc5_:XML = XmlManager.twentyone_lucky.children()[0];
         if(param2 == 1)
         {
            if(param1 < int(_loc5_.@juniorGameRefreshNum))
            {
               this.refreshBtn.setToolTip(StringUtil.substitute(Globalization.getString("peakednessModule.44"),int(_loc5_.@juniorGameRefreshNum) - param1));
            }
            else
            {
               _loc4_ = String(_loc5_.@juniorGameRefreshCost).split("|");
               this.refreshBtn.setToolTip(StringUtil.substitute(Globalization.getString("peakednessModule.45"),_loc4_[param3]));
            }
         }
         else if(param1 < int(_loc5_.@seniorGameRefreshNum))
         {
            this.refreshBtn.setToolTip(StringUtil.substitute(Globalization.getString("peakednessModule.44"),int(_loc5_.@seniorGameRefreshNum) - param1));
         }
         else
         {
            _loc4_ = String(_loc5_.@seniorGameRefreshCost).split("|");
            this.refreshBtn.setToolTip(StringUtil.substitute(Globalization.getString("peakednessModule.45"),_loc4_[param3]));
         }
      }
      
      public function clearInfo() : void
      {
         this.slot.clearEquipInfo();
         this.slot1.clearEquipInfo();
         this.slot2.clearEquipInfo();
         this.slot3.clearEquipInfo();
         this.slot4.clearEquipInfo();
         this.slot5.clearEquipInfo();
      }
      
      public function setItemData(param1:Object) : void
      {
         var _loc3_:IBasicInterface = null;
         var _loc2_:SlotTemplete = null;
         this.slot.clearInfo();
         this.slot1.clearInfo();
         this.slot2.clearInfo();
         this.slot3.clearInfo();
         this.slot4.clearInfo();
         this.slot5.clearInfo();
         _loc3_ = ItemManager.getInstance().getItemTemplate(param1.first[0].itemId);
         _loc2_ = new SlotTemplete();
         _loc2_.tempID = param1.first[0].itemId;
         _loc2_.num = param1.first[0].num;
         this.slot.setItem(_loc2_,false,false,true,false);
         _loc3_ = ItemManager.getInstance().getItemTemplate(param1.first[1].itemId);
         _loc2_ = new SlotTemplete();
         _loc2_.tempID = param1.first[1].itemId;
         _loc2_.num = param1.first[1].num;
         this.slot1.setItem(_loc2_,false,false,true,false);
         _loc3_ = ItemManager.getInstance().getItemTemplate(param1.second[0].itemId);
         _loc2_ = new SlotTemplete();
         _loc2_.tempID = param1.second[0].itemId;
         _loc2_.num = param1.second[0].num;
         this.slot2.setItem(_loc2_,false,false,true,false);
         _loc3_ = ItemManager.getInstance().getItemTemplate(param1.second[1].itemId);
         _loc2_ = new SlotTemplete();
         _loc2_.tempID = param1.second[1].itemId;
         _loc2_.num = param1.second[1].num;
         this.slot3.setItem(_loc2_,false,false,true,false);
         _loc3_ = ItemManager.getInstance().getItemTemplate(param1.third[0].itemId);
         _loc2_ = new SlotTemplete();
         _loc2_.tempID = param1.third[0].itemId;
         _loc2_.num = param1.third[0].num;
         this.slot4.setItem(_loc2_,false,false,true,false);
         _loc3_ = ItemManager.getInstance().getItemTemplate(param1.third[1].itemId);
         _loc2_ = new SlotTemplete();
         _loc2_.tempID = param1.third[1].itemId;
         _loc2_.num = param1.third[1].num;
         this.slot5.setItem(_loc2_,false,false,true,false);
      }
   }
}

