package blackjack.view
{
   import blackjack.mediator.BlackjackShopMediator;
   import flash.geom.Point;
   import game.data.group.HeroDetailData;
   import game.events.BaseEvent;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class BlackJackShopWindow extends PopUpWindow
   {
      public static const NAME:String = "BlackJackShopWindow";
      
      public static const EXCHANGEPRIZE:String = "BlackJackShopWindow.PROXY.EXCHANGEPRIZE";
      
      public var prizeShopWin:AttackShopUI;
      
      private var titleSkin:UISkin;
      
      public var surplusTxt:Label;
      
      public var surplusNum:Label;
      
      public var _exchangeWin:PrizeExchangeWin;
      
      public function BlackJackShopWindow()
      {
         super(620,480);
         this.isLive = false;
         this.titleSkin = UIManager.getUISkin("convertCenter");
         this.titleSkin.x = 234;
         this.titleSkin.y = -25;
         this.addChild(this.titleSkin);
         pane.y += 22;
         pane.x = 10;
         var _loc1_:UISkin = UIManager.getUISkin("group_bg");
         _loc1_.setSize(601,398);
         _loc1_.y = -6;
         pane.addChild(_loc1_);
         this.surplusTxt = new Label(Globalization.getString("peakednessModule.46"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.surplusTxt.x = 200;
         this.surplusTxt.y = -28;
         pane.addChild(this.surplusTxt);
         this.surplusNum = new Label("",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.surplusNum.x = 298;
         this.surplusNum.y = -28;
         pane.addChild(this.surplusNum);
         this.prizeShopWin = new AttackShopUI();
         this.prizeShopWin.x = -4;
         this.prizeShopWin.y = -40;
         this.prizeShopWin.exchangeFun = this.exchangeHandler;
         pane.addChild(this.prizeShopWin);
         this._exchangeWin = new PrizeExchangeWin();
         this._exchangeWin.confirmExchange = this.confirmExchangeHandler;
         this.initMediator();
      }
      
      public static function getPrizeInfo() : Array
      {
         var _loc1_:XML = null;
         var _loc2_:Object = null;
         var _loc4_:Array = [];
         var _loc3_:XML = XmlManager.twentyone_shop;
         for each(_loc1_ in _loc3_.children())
         {
            _loc2_ = {};
            _loc2_.integral = int(_loc1_.@consumeScore);
            _loc2_.exchangeId = int(_loc1_.@id);
            _loc2_.tempId = int(_loc1_.@itemTempId);
            _loc2_.lv = int(_loc1_.@neeLevel);
            _loc2_.prestige = int(_loc1_.@needPrestige);
            _loc2_.getNum = int(_loc1_.@limitTime);
            _loc2_.canBatch = int(_loc1_.@canBatch);
            _loc4_.push(_loc2_);
         }
         return _loc4_;
      }
      
      private function confirmExchangeHandler(param1:int, param2:Point, param3:int) : void
      {
         this.dispatchEvent(new BaseEvent("BlackJackShopWindow.PROXY.EXCHANGEPRIZE",[param1,param2,param3]));
      }
      
      private function exchangeHandler(param1:Object) : void
      {
         var _loc2_:Object = PrizeExchangeWin.getExchangeInfoByExchangeId(param1.exchangeId);
         if(_loc2_.canBatch == 0)
         {
            this.dispatchEvent(new BaseEvent("BlackJackShopWindow.PROXY.EXCHANGEPRIZE",[param1.exchangeId,param1.point,1]));
            return;
         }
         PopUpCenter.addPopUp("PrizeExchangeWin",this._exchangeWin,true,true);
         this._exchangeWin.setExchangeData(param1.exchangeId,int(this.surplusNum.text));
      }
      
      private function initMediator() : void
      {
         AppFacade.instance.registerMediator(new BlackjackShopMediator(this));
      }
      
      public function setData(param1:Array) : void
      {
         this.prizeShopWin.setHonourShopItem(param1);
      }
      
      private function getInviteHerosData(param1:Array) : Array
      {
         var filterReLiveNum:Function = null;
         var filterLevel:Function = null;
         var filterID:Function = null;
         var data:Array = param1;
         filterReLiveNum = function(param1:*, param2:int, param3:Array):Boolean
         {
            return HeroDetailData(param1).rebirthNum > 0;
         };
         filterLevel = function(param1:*, param2:int, param3:Array):Boolean
         {
            return HeroDetailData(param1).rebirthNum == 0 && HeroDetailData(param1).level > 0;
         };
         filterID = function(param1:*, param2:int, param3:Array):Boolean
         {
            return HeroDetailData(param1).rebirthNum == 0 && HeroDetailData(param1).level == 0;
         };
         var reLiveData:Array = data.filter(filterReLiveNum).sortOn("rebirthNum",2 | 0x10);
         var lvData:Array = data.filter(filterLevel).sortOn("level",2 | 0x10);
         var idData:Array = data.filter(filterID).sortOn("hid",2 | 0x10);
         return reLiveData.concat(lvData).concat(idData);
      }
      
      override public function dispose() : void
      {
         super.dispose();
         AppFacade.instance.removeMediator("BlackjackShopMediator");
      }
   }
}

