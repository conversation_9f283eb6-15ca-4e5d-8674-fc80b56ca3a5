package blackjack.view
{
   import blackjack.mediator.BlackjackMediator;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.tab.TabPane;
   import mmo.ui.control.window.PopUpWindow;
   import mmo.utils.CountTimer;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.ModelUtilities;
   import util.time.TimeManager;
   
   public class BlackjackWindow extends PopUpWindow
   {
      public var tab:TabPane;
      
      public var nomalSprite:NomalWindow;
      
      public var highSprite:NomalWindow;
      
      public var blackJackShop:ImgButton;
      
      public var btn_help:Button;
      
      public var btn_look:Button;
      
      private var timer:CountTimer;
      
      private var timerTxt:Label;
      
      private var startTime:Number;
      
      private var endTime:Number;
      
      private var nowTime:Number;
      
      public function BlackjackWindow()
      {
         super(600,620,UIManager.getUISkin("sendCard_bg"));
         setTitleImageData(UIManager.getUISkin("TitleNumErShiYi").bitmapData);
         var _loc3_:UISkin = UIManager.getUISkin("intro_bg");
         _loc3_.x = 10;
         _loc3_.y = 53;
         _loc3_.setSize(580,245);
         addChild(_loc3_);
         this.timerTxt = new Label("00:00:00",TextFormatLib.format_0x00FF00_12px_s);
         this.timerTxt.autoSize = "left";
         var _loc2_:UISprite = new UISprite();
         _loc2_.x = 410;
         _loc2_.y = 35;
         _loc2_.addChild(this.timerTxt);
         this.addChild(_loc2_);
         this.tab = new TabPane([Globalization.getString("peakednessModule.52"),Globalization.getString("peakednessModule.53")],0,75,20);
         this.tab.Gap = 0;
         this.tab.x = 12;
         this.tab.y = 55;
         addChild(this.tab);
         this.nomalSprite = new NomalWindow(1);
         this.nomalSprite.x = -15;
         this.nomalSprite.y = -48;
         this.tab.addToTab(this.nomalSprite,0);
         this.highSprite = new NomalWindow(2);
         this.highSprite.x = -15;
         this.highSprite.y = -48;
         this.tab.addToTab(this.highSprite,1);
         this.btn_help = new Button(Globalization.getString("peakednessModule.49"),TextFormatLib.format_0xFFB932_12px_songti,60,UIManager.getMultiUISkin("btn_topMenu"));
         this.btn_help.x = 440;
         this.btn_help.y = 305;
         this.btn_help.setTextOffset(0,-2);
         addChild(this.btn_help);
         this.btn_help.addEventListener("click",this.btn_helpHandler);
         this.btn_look = new Button(Globalization.getString("peakednessModule.48"),TextFormatLib.format_0xFFB932_12px_songti,60,UIManager.getMultiUISkin("btn_topMenu"));
         this.btn_look.x = 520;
         this.btn_look.y = 305;
         this.btn_look.setTextOffset(0,-2);
         addChild(this.btn_look);
         this.btn_look.addEventListener("click",this.btn_LookHandler);
         this.blackJackShop = new ImgButton(UIManager.getMultiUISkin("blackJackShop"));
         this.blackJackShop.x = 515;
         this.blackJackShop.y = 550;
         addChild(this.blackJackShop);
         this.blackJackShop.addEventListener("click",this.blackJackShopHandler);
         this.timer = new CountTimer();
         this.timer.addEventListener("CountTimer_EVENT_TIMER",this.onCountTimerHandler);
         var _loc1_:XML = XmlManager.twentyone_lucky.children()[0];
         this.startTime = ModelUtilities.timeStringToNumber(_loc1_.@startTime);
         this.endTime = ModelUtilities.timeStringToNumber(_loc1_.@endTime);
         this.nowTime = TimeManager.getInstance().getTime();
         if(this.nowTime > this.startTime && this.nowTime < this.endTime)
         {
            this.timer.start((this.endTime - this.nowTime) / 1000);
            this.getTimerText();
         }
         AppFacade.instance.registerMediator(new BlackjackMediator(this));
      }
      
      private function onCountTimerHandler(param1:Event) : void
      {
         this.getTimerText();
      }
      
      private function getTimerText() : void
      {
         var _loc3_:* = this.timer.nextTime / 3600 >> 0;
         var _loc2_:* = _loc3_ / 24 >> 0;
         var _loc1_:Number = this.timer.nextTime % 86400;
         this.timerTxt.htmlText = StringUtil.substitute(Globalization.getString("peakednessModule.47"),_loc2_,"  " + CountTimer.formatTime(_loc1_,this.timer.showType));
      }
      
      private function btn_LookHandler(param1:MouseEvent) : void
      {
         var _loc5_:XML = XmlManager.twentyone_lucky.children()[0];
         var _loc3_:* = String(_loc5_.@seniorReview).split("|");
         var _loc4_:* = String(_loc5_.@juniorReview).split("|");
         var _loc2_:Array = [_loc4_,_loc3_];
         PopUpCenter.addPopUp("DropItemWindows",new DropItemWindows(),true,true,_loc2_);
      }
      
      private function btn_helpHandler(param1:MouseEvent) : void
      {
         PopUpCenter.removePopUp("HelpWindows");
         var _loc2_:HelpWindows = new HelpWindows();
         PopUpCenter.addPopUp("HelpWindows",_loc2_,true,true);
      }
      
      private function blackJackShopHandler(param1:MouseEvent) : void
      {
         var _loc2_:BlackJackShopWindow = new BlackJackShopWindow();
         PopUpCenter.addPopUp("BlackJackShopWindow",_loc2_,true,true);
      }
      
      override public function dispose() : void
      {
         super.dispose();
         AppFacade.instance.removeMediator("BlackjackMediator");
      }
   }
}

