package blackjack.view
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   
   public class Card extends UISprite
   {
      private var nameTF:Label;
      
      private var headImg:UISkin;
      
      private var htid:int;
      
      private var bgSP:Sprite;
      
      private var bgSK:UISkin;
      
      public function Card()
      {
         super();
         addChild(new Bitmap(new BitmapData(132,167,true,0)));
         this.buttonMode = true;
         this.bgSP = new Sprite();
         this.addChild(this.bgSP);
         this.nameTF = new Label("",TextFormatLib.format_0xFFED89_12px_center,[FilterLib.glow_0x272727],false);
         this.nameTF.width = 100;
         this.nameTF.height = 20;
         this.nameTF.x = 18;
         this.nameTF.y = 123;
         this.bgSP.addChild(this.nameTF);
         this.bgSP.mouseChildren = false;
         this.bgSP.mouseEnabled = false;
      }
      
      public function setData(param1:int, param2:int) : void
      {
         var _loc3_:TextFormat = new TextFormat("Verdana",12,16772489,null,null,null,null,null,"center");
         this.headImg = UIManager.getUISkin("card_" + param1 + "_" + param2);
         this.bgSP.addChild(this.headImg);
      }
   }
}

