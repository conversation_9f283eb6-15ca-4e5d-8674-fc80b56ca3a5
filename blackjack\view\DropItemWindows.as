package blackjack.view
{
   import flash.events.Event;
   import game.manager.UIManager;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   import mmo.ui.control.tab.TabPane;
   import mmo.ui.control.window.PopUpWindow;
   import mmo.ui.event.TabEvent;
   import util.Globalization;
   
   public class DropItemWindows extends PopUpWindow
   {
      public static const NAME:String = "DropItemWindows";
      
      private var _container:UISprite;
      
      private var tabs:Array;
      
      private var tabBar:TabPane;
      
      private const ADVANCED:String = "advanced";
      
      private const NORMAL:String = "normal";
      
      private var _scrollArr:Array;
      
      private var _scrollPane:ScrollPane;
      
      public function DropItemWindows()
      {
         super(376,300);
         title = Globalization.getString("peakednessModule.48");
         pane.y -= 10;
         var _loc1_:UISkin = UIManager.getUISkin("group_bg");
         _loc1_.setSize(356,210);
         _loc1_.y = 38;
         _loc1_.x = 5;
         pane.addChild(_loc1_);
         this._scrollPane = new ScrollPane(346,184);
         this._scrollPane.x = 10;
         this._scrollPane.y = 50;
         pane.addChild(this._scrollPane);
         this._container = new UISprite();
         this._container.x = 6;
         this._scrollPane.addToPane(this._container);
      }
      
      override public function show(param1:Object) : void
      {
         super.show(param1);
         this._scrollArr = param1 as Array;
         var _loc3_:UISkin = UIManager.getUISkin("tab_bg");
         pane.addChild(_loc3_);
         this.tabs = [];
         var _loc2_:Array = [];
         _loc2_.push(Globalization.getString("peakednessModule.50"));
         this.tabs.push("advanced");
         _loc2_.push(Globalization.getString("peakednessModule.51"));
         this.tabs.push("normal");
         this.tabBar = new TabPane(_loc2_,0,68,20);
         this.tabBar.Gap = 0;
         this.tabBar.x = 9;
         this.tabBar.y = 30;
         pane.addChild(this.tabBar);
         _loc3_.setSize(this.width - 8,this.tabBar.height);
         _loc3_.x = 0;
         _loc3_.y = 5;
         this.tabBar.addEventListener(TabEvent.Tab_IndexChange,this.changeData_Handler);
         this.changeData_Handler(null);
         this.x = 442;
         this.y = 180;
      }
      
      protected function changeData_Handler(param1:Event) : void
      {
         var _loc2_:Slot = null;
         var _loc3_:SlotTemplete = null;
         while(this._container.numChildren)
         {
            this._container.removeChildAt(0);
         }
         var _loc6_:Array = this._scrollArr[this.tabBar.selectedIndex];
         var _loc4_:UIBox = new UIBox();
         _loc4_.lineMaxChildrenNumber = 5;
         _loc4_.rowSpace = 20;
         var _loc5_:int = 0;
         while(_loc5_ < _loc6_.length)
         {
            _loc2_ = new Slot();
            _loc3_ = new SlotTemplete();
            _loc3_.tempID = _loc6_[_loc5_];
            _loc3_.num = 1;
            _loc2_.setItem(_loc3_,false,false,false,false);
            _loc4_.addChild(_loc2_);
            _loc5_++;
         }
         this._container.addChild(_loc4_);
         this._scrollPane.nowUpdateUI();
         this._scrollPane.updateMaxHeight(184);
      }
      
      override public function close() : void
      {
         while(this._container.numChildren)
         {
            this._container.removeChildAt(0);
         }
         super.close();
      }
      
      override public function dispose() : void
      {
         super.dispose();
      }
      
      override public function get posHeight() : Number
      {
         return 300;
      }
   }
}

