package blackjack.view
{
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class HelpWindows extends PopUpWindow
   {
      public static const NAME:String = "HelpWindows";
      
      private var _scrollPane:ScrollPane;
      
      public function HelpWindows()
      {
         super(480,325);
         setTitleImageData(UIManager.getUISkin("titleTip").bitmapData,-10);
         isLive = false;
         this.initDis();
      }
      
      private function initDis() : void
      {
         var _loc5_:UISkin = UIManager.getUISkin("underGroundLightbg");
         _loc5_.setSize(460,245);
         this.addChild(_loc5_);
         _loc5_.x = this.width - _loc5_.width >> 1;
         _loc5_.y = 33;
         var _loc4_:XML = XmlManager.twentyone_lucky.children()[0];
         var _loc2_:Label = new Label();
         _loc2_.mouseEnabled = false;
         _loc2_.mouseWheelEnabled = false;
         var _loc3_:TextFormat = new TextFormat("Verdana",12,16772489);
         _loc3_.leading = 10;
         _loc2_.defaultTextFormat = _loc3_;
         _loc2_.htmlText = decodeURIComponent(String(_loc4_.@help).replace(/\\n/g,"\n"));
         _loc2_.x = 20;
         _loc2_.y = 50;
         _loc2_.width = 420;
         _loc2_.wordWrap = true;
         _loc2_.multiline = true;
         this.addChild(_loc2_);
         var _loc1_:Button = addChild(new Button(Globalization.getString("Gl.161"),null,64,UIManager.getMultiUISkin("button_big"))) as Button;
         _loc1_.textFormat.letterSpacing = 5;
         _loc1_.x = this.width - _loc1_.width >> 1;
         _loc1_.y = this.height - _loc1_.height - 20;
         _loc1_.addEventListener("click",onExitBtnClick);
      }
   }
}

