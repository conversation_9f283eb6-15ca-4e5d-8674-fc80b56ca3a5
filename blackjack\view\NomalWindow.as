package blackjack.view
{
   import com.greensock.TweenMax;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import game.data.MainData;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class NomalWindow extends UISprite
   {
      public var curTimes:UISprite;
      
      public var timeNum:int;
      
      private var cardlistSK:UISkin;
      
      public var getAwardBtn:ImgButton;
      
      public var startGame:ImgButton;
      
      public var faPaiBtn:ImgButton;
      
      public var nextGame:ImgButton;
      
      public var freeTimes:Label;
      
      public var goldTimes:Label;
      
      public var myCard1:Card;
      
      public var myCard2:Card;
      
      public var oneItem:AwardItem;
      
      public var twoItem:AwardItem;
      
      public var threeItem:AwardItem;
      
      public var arr:Array;
      
      public var sp:UISprite;
      
      public var playTime:int;
      
      public var goldTime:int;
      
      public var typeInt:int;
      
      private var curNumUI:UISkin;
      
      public var curRound:int;
      
      public var normalRefreshTime:int;
      
      public var isCanLook:Boolean = true;
      
      public function NomalWindow(param1:int = 1)
      {
         var _loc4_:UISkin = null;
         var _loc3_:Label = null;
         this.arr = [];
         super();
         this.typeInt = param1;
         _loc4_ = UIManager.getUISkin("split_vertical_short");
         _loc4_.x = 105;
         _loc4_.y = 50;
         _loc4_.height = 25;
         addChild(_loc4_);
         _loc3_ = new Label(Globalization.getString("peakednessModule.58"),TextFormatLib.format_0xFFB932_12px_songti,[FilterLib.glow_0x272727]);
         _loc3_.x = 30;
         _loc3_.y = 53;
         addChild(_loc3_);
         _loc4_ = UIManager.getUISkin("split_vertical_short");
         _loc4_.x = 235;
         _loc4_.y = 50;
         _loc4_.height = 25;
         addChild(_loc4_);
         _loc3_ = new Label(Globalization.getString("peakednessModule.59"),TextFormatLib.format_0xFF0000_12px_s,[FilterLib.glow_0x272727]);
         _loc3_.x = 145;
         _loc3_.y = 53;
         addChild(_loc3_);
         _loc4_ = UIManager.getUISkin("split_vertical_short");
         _loc4_.x = 380;
         _loc4_.y = 50;
         _loc4_.height = 25;
         addChild(_loc4_);
         _loc3_ = new Label(Globalization.getString("peakednessModule.60"),TextFormatLib.format_0xd30de5_12px_s,[FilterLib.glow_0x272727]);
         _loc3_.x = 277;
         _loc3_.y = 53;
         addChild(_loc3_);
         _loc4_ = UIManager.getUISkin("split_vertical_short");
         _loc4_.x = 510;
         _loc4_.y = 50;
         _loc4_.height = 25;
         addChild(_loc4_);
         _loc3_ = new Label(Globalization.getString("peakednessModule.61"),TextFormatLib.format_0xFF6600_12px_s,[FilterLib.glow_0x272727]);
         _loc3_.x = 425;
         _loc3_.y = 53;
         addChild(_loc3_);
         this.oneItem = new AwardItem(1);
         this.oneItem.x = 10;
         this.oneItem.y = 70;
         addChild(this.oneItem);
         this.twoItem = new AwardItem(2);
         this.twoItem.x = 10;
         this.twoItem.y = 140;
         addChild(this.twoItem);
         this.threeItem = new AwardItem(3);
         this.threeItem.x = 10;
         this.threeItem.y = 210;
         addChild(this.threeItem);
         this.curNumUI = UIManager.getUISkin("curNumErShiYi");
         this.curNumUI.x = 20;
         this.curNumUI.y = 295;
         addChild(this.curNumUI);
         this.curTimes = new UISprite();
         this.curTimes.x = 140;
         this.curTimes.y = 297;
         addChild(this.curTimes);
         var _loc2_:Sprite = new Sprite();
         _loc2_.graphics.beginFill(3742721);
         _loc2_.graphics.drawRect(0,0,587,200);
         _loc2_.graphics.lineStyle(2,16711680);
         _loc2_.graphics.endFill();
         _loc2_.x = 10;
         _loc2_.y = 320;
         addChild(_loc2_);
         this.cardlistSK = UIManager.getUISkin("cardListBlack");
         this.cardlistSK.x = 405;
         this.cardlistSK.y = 340;
         this.addChild(this.cardlistSK);
         this.myCard1 = new Card();
         this.myCard1.x = 20;
         this.myCard1.y = 340;
         this.myCard1.visible = false;
         addChild(this.myCard1);
         this.myCard2 = new Card();
         this.myCard2.x = 40;
         this.myCard2.y = 340;
         this.myCard2.visible = false;
         addChild(this.myCard2);
         this.sp = new UISprite();
         addChild(this.sp);
         this.getAwardBtn = new ImgButton(UIManager.getMultiUISkin("RewardNumErShiYi"));
         this.getAwardBtn.x = 70;
         this.getAwardBtn.y = 530;
         addChild(this.getAwardBtn);
         this.startGame = new ImgButton(UIManager.getMultiUISkin("StartNumErShiYi"));
         this.startGame.x = 220;
         this.startGame.y = 530;
         addChild(this.startGame);
         this.faPaiBtn = new ImgButton(UIManager.getMultiUISkin("SendCardNumErShiYi"));
         this.faPaiBtn.x = 220;
         this.faPaiBtn.y = 530;
         addChild(this.faPaiBtn);
         this.faPaiBtn.visible = false;
         this.nextGame = new ImgButton(UIManager.getMultiUISkin("NextNumErShiYi"));
         this.nextGame.x = 400;
         this.nextGame.y = 530;
         this.nextGame.filters = [FilterLib.enbaleFilter];
         addChild(this.nextGame);
         this.nextGame.setToolTip(Globalization.getString("peakednessModule.62"));
         this.freeTimes = new Label("",TextFormatLib.format_0xFFED89_12px_s,[FilterLib.glow_0x272727]);
         this.freeTimes.x = 130;
         this.freeTimes.y = 580;
         addChild(this.freeTimes);
         this.goldTimes = new Label("",TextFormatLib.format_0xFFED89_12px_s,[FilterLib.glow_0x272727]);
         this.goldTimes.x = 313;
         this.goldTimes.y = 580;
         addChild(this.goldTimes);
      }
      
      public function setData(param1:Object) : void
      {
         var _loc5_:XML = null;
         var _loc2_:Array = null;
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         var _loc4_:* = undefined;
         var _loc3_:* = param1;
         this.arr = [];
         while(this.sp.numChildren > 0)
         {
            this.sp.removeChildAt(0);
         }
         _loc5_ = XML(XmlManager.vipConfing.vip.(@level == MainData.getInstance().userData.vip));
         this.setNum(_loc3_.va_info.round.scores);
         this.timeNum = int(_loc3_.va_info.round.scores);
         this.freeTimes.htmlText = StringUtil.substitute(Globalization.getString("peakednessModule.63"),_loc3_.playTimes);
         this.playTime = _loc3_.playTimes;
         this.curRound = _loc3_.currentRound;
         if(this.typeInt == 1)
         {
            this.goldTime = int(String(_loc5_.@twentyOneGameNum).split("|")[0]) - _loc3_.normal_playTimes;
            this.goldTimes.htmlText = StringUtil.substitute(Globalization.getString("peakednessModule.64"),int(String(_loc5_.@twentyOneGameNum).split("|")[0]) - _loc3_.normal_playTimes);
         }
         else
         {
            this.goldTime = int(String(_loc5_.@twentyOneGameNum).split("|")[1]) - _loc3_.senior_playTimes;
            this.goldTimes.htmlText = StringUtil.substitute(Globalization.getString("peakednessModule.65"),int(String(_loc5_.@twentyOneGameNum).split("|")[1]) - _loc3_.senior_playTimes);
         }
         if(_loc3_.hasOwnProperty("norHasRefresh"))
         {
            this.normalRefreshTime = _loc3_.norHasRefresh;
         }
         else
         {
            this.normalRefreshTime = _loc3_.senHasRefresh;
         }
         this.oneItem.setItemData(_loc3_.va_info.reward[0]);
         this.oneItem.setTips(this.normalRefreshTime,this.typeInt,0);
         this.twoItem.setItemData(_loc3_.va_info.reward[1]);
         this.twoItem.setTips(this.normalRefreshTime,this.typeInt,1);
         this.threeItem.setItemData(_loc3_.va_info.reward[2]);
         this.threeItem.setTips(this.normalRefreshTime,this.typeInt,2);
         _loc2_ = _loc3_.va_info.round.baseNum;
         if(_loc2_.length > 0)
         {
            this.myCard1.visible = true;
            this.myCard1.setData(_loc2_[0].color,_loc2_[0].pokerid);
            this.arr.push(this.myCard1);
            this.myCard2.visible = true;
            this.myCard2.setData(_loc2_[1].color,_loc2_[1].pokerid);
            this.arr.push(this.myCard2);
         }
         _loc6_ = _loc3_.va_info.round.number;
         _loc7_ = 0;
         while(_loc7_ < _loc6_.length)
         {
            _loc4_ = new Card();
            _loc4_.x = 20 + 20 * this.arr.length;
            _loc4_.y = 340;
            _loc4_.setData(_loc6_[_loc7_].color,_loc6_[_loc7_].pokerid);
            this.sp.addChild(_loc4_);
            this.arr.push(_loc4_);
            _loc7_++;
         }
         if(_loc2_.length > 0)
         {
            this.startGame.visible = false;
            this.faPaiBtn.visible = true;
         }
         else
         {
            this.myCard1.visible = false;
            this.myCard2.visible = false;
            this.startGame.visible = true;
            this.faPaiBtn.visible = false;
         }
         _loc5_ = XmlManager.twentyone_lucky.children()[0];
         if(this.playTime == 0)
         {
            if(this.typeInt == 1)
            {
               this.startGame.setToolTip(StringUtil.substitute(Globalization.getString("peakednessModule.67"),int(_loc5_.@juniorGameNumCost)));
            }
            else
            {
               this.startGame.setToolTip("消耗1个“黑杰克勋章”");
            }
         }
         else
         {
            this.startGame.setToolTip(StringUtil.substitute(Globalization.getString("peakednessModule.69"),this.playTime));
         }
         this.addEffect(this.timeNum,this.curRound);
      }
      
      public function faPaiSetData(param1:Object) : void
      {
         var dealMC5:MovieClip = null;
         var movieComplete:Function = null;
         var data:Object = param1;
         movieComplete = function():void
         {
            var myCard3:*;
            var turningMC2:MovieClip = null;
            dealMC5.stop();
            dealMC5.parent && dealMC5.parent.removeChild(dealMC5);
            turningMC2 = AssetManager.getMc("turningMCCard");
            turningMC2.x = dealMC5.x;
            turningMC2.y = dealMC5.y;
            myCard2.visible = true;
            turningMC2.play();
            turningMC2.addFrameScript(turningMC2.totalFrames - 1,(function():*
            {
               var okFun:Function;
               return okFun = function():void
               {
                  var mc:MovieClip = null;
                  var curRound:int = 0;
                  var xml:XML = null;
                  var arr:Array = null;
                  turningMC2.addFrameScript(turningMC2.totalFrames - 1,null);
                  turningMC2.stop();
                  turningMC2.parent && turningMC2.parent.removeChild(turningMC2);
                  turningMC2 = null;
                  isCanLook = true;
                  if(data.totalPoints > 21)
                  {
                     mc = AssetManager.getMc("blackJackEffect");
                     mc.x = 170;
                     mc.y = 420;
                     addChild(mc);
                     mc.play();
                     curRound = int(param1.curRound);
                     xml = XmlManager.twentyone_lucky.children()[0];
                     if(typeInt == 1)
                     {
                        arr = String(xml.@juniorLostScore).split("|");
                     }
                     else
                     {
                        arr = String(xml.@seniorLostScore).split("|");
                     }
                     AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                        "text":StringUtil.substitute(Globalization.getString("peakednessModule.66"),arr[curRound - 1]),
                        "textFormat":TextFormatLib.format_0x00FF00_12px
                     });
                     mc.addFrameScript(mc.totalFrames - 1,(function():*
                     {
                        var okFun:Function;
                        return okFun = function():void
                        {
                           mc.addFrameScript(mc.totalFrames - 1,null);
                           mc.stop();
                           mc.parent && mc.parent.removeChild(mc);
                           mc = null;
                        };
                     })());
                     clearData();
                  }
               };
            })());
            myCard3 = new Card();
            myCard3.x = 20 + 20 * arr.length;
            myCard3.y = 340;
            sp.addChild(myCard3);
            myCard3.setData(data.curPoker.color,data.curPoker.pokerid);
            arr.push(myCard3);
         };
         this.setNum(data.totalPoints);
         this.timeNum = int(data.totalPoints);
         dealMC5 = AssetManager.getMc("dealMCCard");
         dealMC5.x = 440;
         dealMC5.y = 420;
         dealMC5.play();
         addChild(dealMC5);
         TweenMax.to(dealMC5,0.6,{
            "x":80 + 20 * this.arr.length,
            "y":420,
            "onComplete":movieComplete
         });
         this.addEffect(this.timeNum,this.curRound);
      }
      
      public function clearData() : void
      {
         this.setNum(0);
         this.timeNum = 0;
         this.startGame.visible = true;
         this.faPaiBtn.visible = false;
         this.arr = [];
         this.curRound = 1;
         while(this.sp.numChildren > 0)
         {
            this.sp.removeChildAt(0);
         }
         this.myCard1.visible = false;
         this.myCard2.visible = false;
         this.oneItem.clearInfo();
         this.twoItem.clearInfo();
         this.threeItem.clearInfo();
      }
      
      public function addEffect(param1:int, param2:int) : void
      {
         this.oneItem.clearInfo();
         this.twoItem.clearInfo();
         this.threeItem.clearInfo();
         if(param2 == 1)
         {
            if(param1 > 0 && param1 < 19)
            {
               this.oneItem.slot.blackEffect();
               this.oneItem.slot1.blackEffect();
            }
            else if(param1 > 18 && param1 < 21)
            {
               this.oneItem.slot2.blackEffect();
               this.oneItem.slot3.blackEffect();
            }
            else if(param1 == 21)
            {
               this.oneItem.slot4.blackEffect();
               this.oneItem.slot5.blackEffect();
            }
         }
         else if(param2 == 2)
         {
            if(param1 > 0 && param1 < 19)
            {
               this.twoItem.slot.blackEffect();
               this.twoItem.slot1.blackEffect();
            }
            else if(param1 > 18 && param1 < 21)
            {
               this.twoItem.slot2.blackEffect();
               this.twoItem.slot3.blackEffect();
            }
            else if(param1 == 21)
            {
               this.twoItem.slot4.blackEffect();
               this.twoItem.slot5.blackEffect();
            }
         }
         else if(param2 == 3)
         {
            if(param1 > 0 && param1 < 19)
            {
               this.threeItem.slot.blackEffect();
               this.threeItem.slot1.blackEffect();
            }
            else if(param1 > 18 && param1 < 21)
            {
               this.threeItem.slot2.blackEffect();
               this.threeItem.slot3.blackEffect();
            }
            else if(param1 == 21)
            {
               this.threeItem.slot4.blackEffect();
               this.threeItem.slot5.blackEffect();
            }
         }
      }
      
      public function setCardsData(param1:Object, param2:Boolean = true) : void
      {
         var xml:XML = null;
         var dealMC3:MovieClip = null;
         var dealMC4:MovieClip = null;
         var movieComplete:Function = null;
         var data:Object = param1;
         var isCan:Boolean = param2;
         var send:Function = function():void
         {
            var turningMC1:MovieClip = null;
            turningMC1 = AssetManager.getMc("turningMCCard");
            turningMC1.x = dealMC3.x;
            turningMC1.y = dealMC3.y;
            myCard1.visible = true;
            myCard1.setData(data.poker[0].color,data.poker[0].pokerid);
            arr.push(myCard1);
            addChild(turningMC1);
            turningMC1.play();
            turningMC1.addFrameScript(turningMC1.totalFrames - 1,(function():*
            {
               var okFun:Function;
               return okFun = function():void
               {
                  turningMC1.addFrameScript(turningMC1.totalFrames - 1,null);
                  turningMC1.stop();
                  turningMC1.parent && turningMC1.parent.removeChild(turningMC1);
                  turningMC1 = null;
               };
            })());
            dealMC3.stop();
            dealMC3.parent && dealMC3.parent.removeChild(dealMC3);
            dealMC4.play();
            TweenMax.to(dealMC4,0.6,{
               "x":140,
               "y":420,
               "onComplete":movieComplete
            });
         };
         movieComplete = function():void
         {
            var turningMC2:MovieClip = null;
            turningMC2 = AssetManager.getMc("turningMCCard");
            turningMC2.x = dealMC4.x;
            turningMC2.y = dealMC4.y;
            addChild(turningMC2);
            turningMC2.play();
            turningMC2.addFrameScript(turningMC2.totalFrames - 1,(function():*
            {
               var okFun:Function;
               return okFun = function():void
               {
                  turningMC2.addFrameScript(turningMC2.totalFrames - 1,null);
                  turningMC2.stop();
                  turningMC2.parent && turningMC2.parent.removeChild(turningMC2);
                  turningMC2 = null;
               };
            })());
            myCard2.visible = true;
            myCard2.setData(data.poker[1].color,data.poker[1].pokerid);
            arr.push(myCard2);
            dealMC4.stop();
            dealMC4.parent && dealMC4.parent.removeChild(dealMC4);
            isCanLook = true;
         };
         this.arr = [];
         while(this.sp.numChildren > 0)
         {
            this.sp.removeChildAt(0);
         }
         this.setNum(data.totalPoints);
         this.timeNum = int(data.totalPoints);
         if(data.hasOwnProperty("currentRound"))
         {
            this.curRound = data.currentRound;
         }
         xml = XmlManager.vipConfing.vip.(@level == MainData.getInstance().userData.vip)[0];
         if(isCan)
         {
            if(this.playTime == 0)
            {
               if(this.typeInt == 1)
               {
                  this.goldTime -= 1;
                  if(this.goldTime < 0)
                  {
                     this.goldTime = 0;
                  }
                  this.goldTimes.htmlText = StringUtil.substitute(Globalization.getString("peakednessModule.64"),this.goldTime);
               }
               else
               {
                  this.goldTime -= 1;
                  if(this.goldTime < 0)
                  {
                     this.goldTime = 0;
                  }
                  this.goldTimes.htmlText = StringUtil.substitute(Globalization.getString("peakednessModule.65"),this.goldTime);
               }
            }
            else
            {
               this.playTime -= 1;
               if(this.playTime < 0)
               {
                  this.playTime = 0;
               }
               this.freeTimes.htmlText = StringUtil.substitute(Globalization.getString("peakednessModule.63"),this.playTime);
            }
         }
         this.startGame.visible = false;
         this.faPaiBtn.visible = true;
         dealMC3 = AssetManager.getMc("dealMCCard");
         dealMC3.x = 440;
         dealMC3.y = 420;
         dealMC3.stop();
         addChild(dealMC3);
         dealMC4 = AssetManager.getMc("dealMCCard");
         dealMC4.x = 440;
         dealMC4.y = 420;
         dealMC4.stop();
         addChild(dealMC4);
         dealMC3.play();
         TweenMax.to(dealMC3,0.6,{
            "x":60,
            "y":420,
            "onComplete":send
         });
         xml = XmlManager.twentyone_lucky.children()[0];
         if(this.playTime == 0)
         {
            if(this.typeInt == 1)
            {
               this.startGame.setToolTip(StringUtil.substitute(Globalization.getString("peakednessModule.67"),int(xml.@juniorGameNumCost)));
            }
            else
            {
               this.startGame.setToolTip("消耗1个“黑杰克勋章”");
            }
         }
         else
         {
            this.startGame.setToolTip(StringUtil.substitute(Globalization.getString("peakednessModule.69"),this.playTime));
         }
         this.addEffect(this.timeNum,this.curRound);
      }
      
      public function setNum(param1:int) : void
      {
         var _loc3_:UISkin = null;
         while(this.curTimes.numChildren > 0)
         {
            this.curTimes.removeChildAt(0);
         }
         var _loc4_:int = param1 / 10;
         if(_loc4_ != 0)
         {
            _loc3_ = UIManager.getUISkin("gold_" + String(_loc4_));
            this.curTimes.addChild(_loc3_);
         }
         var _loc2_:int = param1 % 10;
         _loc3_ = UIManager.getUISkin("gold_" + String(_loc2_));
         _loc3_.x = 10;
         this.curTimes.addChild(_loc3_);
      }
   }
}

