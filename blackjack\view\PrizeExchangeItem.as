package blackjack.view
{
   import game.items.ItemManager;
   import game.items.ItemQualityInfo;
   import game.items.framework.interfaces.IBasicInterface;
   import game.manager.UIManager;
   import game.modules.chat.msgInfo.MessageReceive;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class PrizeExchangeItem extends UISprite
   {
      public var itemSlot:Slot;
      
      private var _slotTemp:SlotTemplete;
      
      private var _itemName:Label;
      
      private var _integral:Label;
      
      private var _level:Label;
      
      private var _split:UISkin;
      
      public var exchangeBtn:Button;
      
      private var _exchangeId:int;
      
      public function PrizeExchangeItem()
      {
         super();
         var _loc2_:UISkin = UIManager.getUISkin("bg_hero_normal");
         _loc2_.setSize(193,122);
         this.addChild(_loc2_);
         var _loc1_:UISkin = UIManager.getUISkin("honourBorder");
         _loc1_.setSize(193,122);
         this.addChild(_loc1_);
         this.itemSlot = new Slot();
         this.itemSlot.x = 9;
         this.itemSlot.y = 11;
         this.addChild(this.itemSlot);
         this._slotTemp = new SlotTemplete();
         this._itemName = new Label("",TextFormatLib.format_0xffb932_12px);
         this._itemName.x = 66;
         this._itemName.y = 9;
         this.addChild(this._itemName);
         this._integral = new Label("",TextFormatLib.format_0xffb932_12px);
         this._integral.x = 66;
         this._integral.y = 28;
         this.addChild(this._integral);
         this._level = new Label("",TextFormatLib.format_0xffb932_12px);
         this._level.x = 66;
         this._level.y = 47;
         this.addChild(this._level);
         this._split = UIManager.getUISkin("line2");
         this._split.width = 186;
         this._split.x = 3;
         this._split.y = 73;
         this.addChild(this._split);
         this.exchangeBtn = new Button(Globalization.getString("equipmentExchange.5"),null,64,UIManager.getMultiUISkin("button_big"));
         this.exchangeBtn.x = 64;
         this.exchangeBtn.y = 85;
         this.addChild(this.exchangeBtn);
      }
      
      public function setExchangeData(param1:int, param2:int, param3:int, param4:int, param5:int, param6:int) : void
      {
         this._exchangeId = param1;
         this._slotTemp.tempID = param2;
         this.itemSlot.setItem(this._slotTemp,false,false,false,false);
         if(param3 <= 1)
         {
            this.itemSlot._itemNum_txt.text = "";
         }
         else
         {
            this.itemSlot._itemNum_txt.text = String(param3);
         }
         var _loc10_:IBasicInterface = ItemManager.getInstance().getItemTemplate(String(param2)) as IBasicInterface;
         var _loc9_:String = _loc10_.name;
         var _loc8_:int = _loc10_.quality;
         var _loc7_:String = MessageReceive.parseColor(ItemQualityInfo.getQualityColor(_loc8_));
         this._itemName.htmlText = StringUtil.substitute(Globalization.getString("activity.62"),_loc9_,_loc7_);
         this._integral.htmlText = StringUtil.substitute(Globalization.getString("peakednessModule.57"),param4);
         this._level.htmlText = StringUtil.substitute(Globalization.getString("activity.51"),param5);
      }
      
      public function get exchangeId() : int
      {
         return this._exchangeId;
      }
   }
}

