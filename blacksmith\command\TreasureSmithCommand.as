package blacksmith.command
{
   import blacksmith.proxy.TreasureSmithProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class TreasureSmithCommand extends SimpleCommand
   {
      public function TreasureSmithCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         if(!facade.hasProxy("BlackTreasureSmithProxy"))
         {
            facade.registerProxy(new TreasureSmithProxy());
         }
         var _loc2_:TreasureSmithProxy = facade.retrieveProxy("BlackTreasureSmithProxy") as TreasureSmithProxy;
         _loc2_.handNotice(param1.getName(),param1.getBody());
      }
   }
}

