package blacksmith.events.treasure
{
   import flash.events.Event;
   
   public class TreasureReplaceEvent extends Event
   {
      public static const TREASURE_SMITH_REPLACE:String = "TreasureSmithReplace";
      
      private var _itemId:int;
      
      private var _replaceLayer:int;
      
      public function TreasureReplaceEvent(param1:String, param2:int, param3:int)
      {
         super(param1,false,false);
         this._itemId = param2;
         this._replaceLayer = param3;
      }
      
      public function get itemId() : int
      {
         return this._itemId;
      }
      
      override public function clone() : Event
      {
         return new TreasureReplaceEvent(type,this.itemId,this.replaceLayer);
      }
      
      override public function toString() : String
      {
         return formatToString("TreasureReplaceEvent","itemId","replaceLayer");
      }
      
      public function get replaceLayer() : int
      {
         return this._replaceLayer;
      }
   }
}

