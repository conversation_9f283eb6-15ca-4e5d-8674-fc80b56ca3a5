package blacksmith.events.treasure
{
   import flash.events.Event;
   
   public class TreasureSmithAutoLogEvent extends Event
   {
      public static const TREASURE_SMITH_AUTO_WRITE_LOG:String = "TreasureSmithAutoWriteLogEvent";
      
      private var _itemId:int;
      
      private var _itemTemplateId:int;
      
      private var _layer:int;
      
      private var _star:int;
      
      private var _affix:int;
      
      private var _counter:int;
      
      public function TreasureSmithAutoLogEvent(param1:String, param2:int, param3:int, param4:int, param5:int, param6:int, param7:int)
      {
         super(param1,true,false);
         this._itemId = param2;
         this._itemTemplateId = param3;
         this._layer = param4;
         this._star = param5;
         this._affix = param6;
         this._counter = param7;
      }
      
      public function get itemId() : int
      {
         return this._itemId;
      }
      
      public function get itemTemplateId() : int
      {
         return this._itemTemplateId;
      }
      
      public function get layer() : int
      {
         return this._layer;
      }
      
      public function get star() : int
      {
         return this._star;
      }
      
      public function get affix() : int
      {
         return this._affix;
      }
      
      public function get counter() : int
      {
         return this._counter;
      }
      
      override public function clone() : Event
      {
         return new TreasureSmithAutoLogEvent(type,this.itemId,this.itemTemplateId,this.layer,this.star,this.affix,this.counter);
      }
      
      override public function toString() : String
      {
         return formatToString("TreasureSmithAutoLogEvent","itemId","itemTemplateId","layer","star","affix","counter");
      }
   }
}

