package blacksmith.events.treasure
{
   import flash.events.Event;
   
   public class TreasureSmithEvent extends Event
   {
      public static const TREASURE_SMITH:String = "TreasureSmithEvent";
      
      public static const TREASURE_SMITH_AUTO:String = "AutoTreasureSmithEvent";
      
      public static const TREASURE_SMITH_AUTO_HISTORY:String = "AutoHistoryTreasureSmithEvent";
      
      public static const TREASURE_SMITH_BUY_AUTO:String = "BuyAutoTreasureSmithEvent";
      
      private var _itemId:int;
      
      private var _smithType:int;
      
      private var _layers:Array;
      
      public function TreasureSmithEvent(param1:String, param2:int, param3:int, param4:Array)
      {
         super(param1,false,false);
         this._itemId = param2;
         this._smithType = param3;
         this._layers = param4;
      }
      
      public function get itemId() : int
      {
         return this._itemId;
      }
      
      public function get smithType() : int
      {
         return this._smithType;
      }
      
      public function get layers() : Array
      {
         return this._layers;
      }
      
      override public function clone() : Event
      {
         return new TreasureSmithEvent(type,this.itemId,this.smithType,this.layers);
      }
      
      override public function toString() : String
      {
         return formatToString("TreasureSmithEvent","itemId","smithType","layers");
      }
   }
}

