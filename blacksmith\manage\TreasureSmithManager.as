package blacksmith.manage
{
   import flash.utils.Dictionary;
   import game.data.MainData;
   import game.data.group.GroupData;
   import game.data.group.HeroDetailData;
   import game.items.framework.items.Gem;
   import game.items.framework.items.TreasureItem;
   import game.items.framework.templates.Template_Gem;
   import game.items.framework.templates.Template_Treasure;
   import game.manager.XmlManager;
   
   public class TreasureSmithManager
   {
      public static var properyNameDic:Dictionary = new Dictionary();
      
      public function TreasureSmithManager()
      {
         super();
      }
      
      public static function getSmithCostByGold(param1:TreasureItem, param2:Array, param3:int) : Array
      {
         var _loc8_:int = 0;
         if(param1 == null)
         {
            return null;
         }
         var _loc4_:Template_Treasure = param1.template as Template_Treasure;
         var _loc5_:Array = param3 == 0 ? _loc4_.goldSmithCost : _loc4_.energySmithCost;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         for each(_loc8_ in param2)
         {
            _loc7_ += int(_loc5_[_loc8_ - 1].split("|")[0]);
            _loc6_ += int(_loc5_[_loc8_ - 1].split("|")[1]);
         }
         return [_loc7_,_loc6_];
      }
      
      public static function getSmithCostByItem(param1:TreasureItem, param2:Array) : Array
      {
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         if(param1 == null)
         {
            return null;
         }
         var _loc3_:Template_Treasure = param1.template as Template_Treasure;
         var _loc4_:Array = _loc3_.itemSmithCost;
         var _loc5_:Array = [];
         for each(_loc7_ in param2)
         {
            _loc6_ = _loc4_[_loc7_ - 1].split("|");
            _loc5_.push({
               "itemId":int(_loc6_[0]),
               "num":int(_loc6_[1])
            });
         }
         return _loc5_;
      }
      
      public static function getHerosEquipmentNumByTempId(param1:int) : int
      {
         var _loc5_:HeroDetailData = null;
         var _loc3_:GroupData = MainData.getInstance().groupData;
         var _loc4_:Array = _loc3_.list;
         var _loc2_:int = 0;
         for each(_loc5_ in _loc4_)
         {
            if(_loc5_.equipments.isHasEquipmentByTempId(param1))
            {
               _loc2_++;
            }
         }
         return _loc2_;
      }
      
      public static function setPropertyNameDic() : void
      {
         var _loc3_:XML = null;
         var _loc2_:int = 0;
         var _loc1_:XML = XmlManager.getXml("affix");
         for each(_loc3_ in _loc1_.children())
         {
            _loc2_ = int(_loc3_.@id);
            if(!properyNameDic[_loc2_])
            {
               properyNameDic[_loc2_] = _loc3_.@propertyName;
            }
         }
      }
      
      public static function deletePropertyNameDic() : void
      {
         var _loc1_:* = undefined;
         for(_loc1_ in properyNameDic)
         {
            delete properyNameDic[_loc1_];
            _loc1_ = null;
         }
      }
      
      public static function getProperyNameById(param1:int) : String
      {
         var _loc2_:* = undefined;
         for(_loc2_ in properyNameDic)
         {
            if(_loc2_ == param1)
            {
               return properyNameDic[_loc2_];
            }
         }
         return "";
      }
      
      public static function getGemSmithCost(param1:Gem, param2:Array, param3:int) : Array
      {
         var _loc8_:int = 0;
         if(param1 == null)
         {
            return null;
         }
         var _loc4_:Template_Gem = param1.template as Template_Gem;
         var _loc5_:Array = param3 == 0 ? _loc4_.goldSmithCost : _loc4_.creamSmithCost;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         for each(_loc8_ in param2)
         {
            _loc7_ += int(_loc5_[_loc8_ - 1].split("|")[0]);
            _loc6_ += int(_loc5_[_loc8_ - 1].split("|")[1]);
         }
         return [_loc7_,_loc6_];
      }
      
      public static function getGemSmithCostByItem(param1:Gem, param2:Array) : Array
      {
         var _loc6_:Array = null;
         var _loc7_:int = 0;
         if(param1 == null)
         {
            return null;
         }
         var _loc3_:Template_Gem = param1.template as Template_Gem;
         var _loc4_:Array = _loc3_.itemSmithCost;
         var _loc5_:Array = [];
         for each(_loc7_ in param2)
         {
            _loc6_ = _loc4_[_loc7_ - 1].split("|");
            _loc5_.push({
               "itemId":int(_loc6_[0]),
               "num":int(_loc6_[1])
            });
         }
         return _loc5_;
      }
   }
}

