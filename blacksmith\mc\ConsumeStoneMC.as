package blacksmith.mc
{
   import flash.display.Sprite;
   import game.items.ItemManager;
   import game.items.framework.interfaces.IBasicInterface;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class ConsumeStoneMC extends Sprite
   {
      private var _icon:Icon;
      
      private var _textLabel:Label;
      
      public function ConsumeStoneMC()
      {
         super();
         this._icon = new Icon();
         this._icon.y = 2;
         this.addChild(this._icon);
         this._textLabel = new Label("",TextFormatLib.format_0xFFB932_12px);
         this._textLabel.x = 16;
         this.addChild(this._textLabel);
      }
      
      public function setConsumeData(param1:int, param2:int, param3:int) : void
      {
         var _loc5_:IBasicInterface = ItemManager.getInstance().getItemTemplate(String(param1));
         var _loc4_:String = "bitmaps/" + _loc5_.smallSmallitem;
         this._icon.setData(_loc4_);
         this._textLabel.text = StringUtil.substitute(Globalization.getString("treasureSmith.8"),_loc5_.name,param2,param3);
      }
   }
}

