package blacksmith.mc
{
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   import util.Globalization;
   
   public class HeroPanelMC extends UISprite
   {
      private var pane_bg:UISkin;
      
      public var title:Label;
      
      public var heroList:UIBox;
      
      public var btn_page:PageNavigator;
      
      public function HeroPanelMC(param1:int, param2:int)
      {
         super();
         this.pane_bg = UIManager.getUISkin("pane_bg");
         this.pane_bg.setSize(114,param1);
         addChild(this.pane_bg);
         this.title = new Label(Globalization.getString("Strengthen.52"),TextFormatLib.format_0xebce82_12px,[FilterLib.glow_0x272727]);
         this.title.autoSize = "center";
         this.title.x = 12;
         this.title.y = 3;
         this.title.width = 90;
         this.title.height = 20;
         addChild(this.title);
         this.heroList = new UIBox();
         this.heroList.lineMaxChildrenNumber = 2;
         this.heroList.rowMaxChildrenNumber = 5;
         this.heroList.leftSpace = 0;
         this.heroList.topSpace = 2;
         this.heroList.lineSpace = 6;
         this.heroList.rowSpace = 2;
         this.heroList.x = 5;
         this.heroList.y = 26;
         addChild(this.heroList);
         this.btn_page = new PageNavigator();
         this.btn_page.x = 6;
         this.btn_page.y = param2;
         addChild(this.btn_page);
      }
   }
}

