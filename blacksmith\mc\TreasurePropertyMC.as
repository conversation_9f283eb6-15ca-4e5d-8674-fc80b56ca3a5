package blacksmith.mc
{
   import flash.display.Sprite;
   import flash.text.TextFormat;
   import game.items.SealInfo;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.CheckBox;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class TreasurePropertyMC extends Sprite
   {
      private var _propertyLayer:int;
      
      private var _text2:Label;
      
      private var _text3:Label;
      
      private var _star:UISkin;
      
      public var maxLabel:Label;
      
      public var propertyCheckBox:CheckBox;
      
      public var replaceBtn:Button;
      
      private var _maxStarValue:Label;
      
      private var _starMax:UISkin;
      
      public var isCanSelect:Boolean;
      
      private var _tfFFb932:TextFormat;
      
      private var _tfFcff00:TextFormat;
      
      private var _tfDe23aa:TextFormat;
      
      public function TreasurePropertyMC(param1:int, param2:Boolean = true)
      {
         super();
         this._propertyLayer = param1;
         this._tfFFb932 = TextFormatLib.format_0xFFB932_12px;
         this._tfFcff00 = TextFormatLib.format_0xfcff00_12px;
         this._tfDe23aa = TextFormatLib.format_0xDE23AA_12px;
         var _loc3_:Label = new Label(StringUtil.substitute(Globalization.getString("treasureSmith.9"),param1),TextFormatLib.format_0xFFB932_12px);
         this.addChild(_loc3_);
         this._text2 = new Label("",TextFormatLib.format_0xFFB932_12px);
         this._text2.x = _loc3_.width + 2;
         this.addChild(this._text2);
         this._text2.visible = false;
         this._text3 = new Label("",TextFormatLib.format_0xFFF600_12px);
         this.addChild(this._text3);
         this._text3.visible = false;
         this._star = UIManager.getUISkin("star");
         this._star.y = -2;
         this.addChild(this._star);
         this._star.visible = false;
         if(param2)
         {
            this._text3.x = 208;
            this._star.x = 224;
            this.maxLabel = new Label(Globalization.getString("infoMc.69"),TextFormatLib.format_0xfcff00_12px);
            this.maxLabel.x = 244;
            this.addChild(this.maxLabel);
            this.maxLabel.visible = false;
            this.propertyCheckBox = new CheckBox("");
            this.propertyCheckBox.x = 295 - this.propertyCheckBox.width - 8;
            this.propertyCheckBox.y = 2;
            this.addChild(this.propertyCheckBox);
         }
         else
         {
            this._text3.x = 156;
            this._star.x = 172;
            this.replaceBtn = new Button(Globalization.getString("fresh.5"),null,32,UIManager.getMultiUISkin("btn_topMenu"));
            this.replaceBtn.x = 193;
            this.addChild(this.replaceBtn);
            this.replaceBtn.visible = false;
            this._maxStarValue = new Label("",TextFormatLib.format_verdana_0xff0000_12px);
            this._maxStarValue.x = 227;
            this.addChild(this._maxStarValue);
            this._starMax = UIManager.getUISkin("star");
            this._starMax.x = 295 - this._starMax.width - 8;
            this._starMax.y = -2;
            this.addChild(this._starMax);
         }
      }
      
      private function parseColor(param1:uint, param2:int) : String
      {
         var _loc3_:int = int(SealInfo.getSealInfoColorByQuality(param1,param2));
         return "#" + _loc3_.toString(16);
      }
      
      public function showProperty(param1:int, param2:String = "", param3:int = 0, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:String = null;
         switch(param1)
         {
            case 0:
               this.isCanSelect = true;
               this._text2.visible = true;
               this._text3.visible = true;
               this._star.visible = true;
               this._text2.defaultTextFormat = this._tfFFb932;
               _loc6_ = this.parseColor(param3,param4);
               this._text2.htmlText = "<font color=\'" + _loc6_ + "\'>" + param2 + "</font>";
               this._text3.text = String(param3);
               this._text3.x = this._star.x - this._text3.width + 4;
               if(this.maxLabel)
               {
                  this.maxLabel.visible = param3 >= param4 ? true : false;
               }
               if(this.propertyCheckBox)
               {
                  this.propertyCheckBox.visible = true;
               }
               if(this._starMax)
               {
                  this._starMax.visible = true;
               }
               if(this._maxStarValue)
               {
                  this._maxStarValue.visible = true;
                  this._maxStarValue.text = StringUtil.substitute(Globalization.getString("treasureSmith.10"),param4);
               }
               break;
            case 1:
               this.isCanSelect = param5;
               this._text2.visible = true;
               this._text2.defaultTextFormat = param5 ? this._tfFcff00 : this._tfDe23aa;
               this._text2.text = param5 ? Globalization.getString("treasureSmith.11") : Globalization.getString("treasureSmith.12");
               this._text3.visible = false;
               this._star.visible = false;
               if(this.maxLabel)
               {
                  this.maxLabel.visible = false;
               }
               if(this.propertyCheckBox)
               {
                  this.propertyCheckBox.visible = param5;
               }
               if(this.replaceBtn)
               {
                  this.replaceBtn.visible = false;
               }
               if(this._starMax)
               {
                  this._starMax.visible = true;
               }
               if(this._maxStarValue)
               {
                  this._maxStarValue.visible = true;
                  this._maxStarValue.text = StringUtil.substitute(Globalization.getString("treasureSmith.10"),param4);
               }
               break;
            case 2:
               this.isCanSelect = false;
               this._text2.visible = true;
               this._text2.defaultTextFormat = this._tfDe23aa;
               this._text2.text = param2;
               this._text3.visible = false;
               this._star.visible = false;
               if(this.maxLabel)
               {
                  this.maxLabel.visible = false;
               }
               if(this.propertyCheckBox)
               {
                  this.propertyCheckBox.visible = false;
               }
               if(this.replaceBtn)
               {
                  this.replaceBtn.visible = false;
               }
               if(this._starMax)
               {
                  this._starMax.visible = false;
               }
               if(this._maxStarValue)
               {
                  this._maxStarValue.visible = false;
               }
               break;
            default:
               this.isCanSelect = false;
               this._text2.visible = false;
               this._text3.visible = false;
               this._star.visible = false;
               if(this.propertyCheckBox)
               {
                  this.propertyCheckBox.visible = false;
               }
               if(this.replaceBtn)
               {
                  this.replaceBtn.visible = false;
               }
               if(this._starMax)
               {
                  this._starMax.visible = false;
               }
               if(this._maxStarValue)
               {
                  this._maxStarValue.visible = false;
                  break;
               }
         }
      }
      
      public function get propertyLayer() : int
      {
         return this._propertyLayer;
      }
   }
}

