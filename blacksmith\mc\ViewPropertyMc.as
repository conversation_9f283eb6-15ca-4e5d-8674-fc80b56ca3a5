package blacksmith.mc
{
   import blacksmith.manage.TreasureSmithManager;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class ViewPropertyMc extends UISprite
   {
      private static const _LABEL_NUM_MAX:int = 5;
      
      private var _bg:UISkin;
      
      private var _layerTitle:Label;
      
      private var _propertyLabelArr:Array = [];
      
      public function ViewPropertyMc()
      {
         super();
         this.initUI();
      }
      
      private function initUI() : void
      {
         var _loc3_:Label = null;
         this._bg = UIManager.getUISkin("board_bg");
         this._bg.width = 590;
         this.addChild(this._bg);
         this._layerTitle = new Label("",TextFormatLib.format_Verdana_0xFFB932_12px);
         this._layerTitle.x = 8;
         this._layerTitle.y = 3;
         this.addChild(this._layerTitle);
         var _loc2_:UISkin = UIManager.getUISkin("split_line");
         _loc2_.width = 572;
         _loc2_.x = 6;
         _loc2_.y = 22;
         this.addChild(_loc2_);
         var _loc1_:int = 0;
         while(_loc1_ < 5)
         {
            _loc3_ = new Label("",TextFormatLib.format_0xFFF5CE_12px_leading2);
            _loc3_.x = 8 + _loc1_ * 108;
            _loc3_.y = 26;
            _loc3_.width = 108;
            this.addChild(_loc3_);
            this._propertyLabelArr.push(_loc3_);
            _loc1_++;
         }
      }
      
      public function setPropetyData(param1:int, param2:Array) : void
      {
         var _loc5_:String = null;
         this._layerTitle.text = StringUtil.substitute(Globalization.getString("treasureSmith.60"),param1);
         var _loc6_:int = int(param2.length);
         var _loc3_:int = 0;
         while(_loc3_ < _loc6_)
         {
            _loc5_ = TreasureSmithManager.getProperyNameById(param2[_loc3_]);
            this._propertyLabelArr[_loc3_ % 5].appendText(_loc5_ + "\n");
            _loc3_++;
         }
         var _loc4_:int = Math.ceil(_loc6_ / 5);
         this._bg.height = 30 + 18 * _loc4_;
      }
   }
}

