package blacksmith.mediator
{
   import blacksmith.command.TreasureSmithCommand;
   import blacksmith.events.treasure.TreasureReplaceEvent;
   import blacksmith.events.treasure.TreasureSmithAutoLogEvent;
   import blacksmith.events.treasure.TreasureSmithEvent;
   import blacksmith.manage.TreasureSmithManager;
   import blacksmith.proxy.BlacksmithProxy;
   import blacksmith.proxy.TreasureSmithProxy;
   import blacksmith.ui.TreasureBlackSmithWindow;
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.utils.setTimeout;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.data.group.HeroData;
   import game.items.ItemManager;
   import game.items.framework.items.TreasureItem;
   import game.items.framework.templates.Template_Treasure;
   import game.modules.chat.msgInfo.MessageReceive;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   
   public class TreasureBlackSmithMediator extends PirateMediator
   {
      public static const NAME:String = "TreasureBlackSmithMediator";
      
      private var _treasureSmithWin:TreasureBlackSmithWindow;
      
      private var _selectHeroId:int;
      
      private var _commandQueue:Array = [];
      
      public function TreasureBlackSmithMediator(param1:Object = null)
      {
         super("TreasureBlackSmithMediator",param1);
         this._treasureSmithWin = param1 as TreasureBlackSmithWindow;
         this._treasureSmithWin.showHander = this.showHandler;
         this._treasureSmithWin.heroList.addEventListener("selectHero",this.selectHeroHandler);
         this._treasureSmithWin.treasureRefreshPanel.addEventListener("TreasureSmithEvent",this.onTreasureSmithEvent);
         this._treasureSmithWin.treasureRefreshPanel.addEventListener("AutoTreasureSmithEvent",this.onTreasureSmithAutoEvent);
         this._treasureSmithWin.treasureRefreshPanel.addEventListener("AutoHistoryTreasureSmithEvent",this.onTreasureSmithAutoHistoryEvent);
         this._treasureSmithWin.treasureRefreshPanel.addEventListener("BuyAutoTreasureSmithEvent",this.onTreasureSmithBuyAuto);
         this._treasureSmithWin.treasureRefreshPanel.addEventListener("TreasureSmithAutoWriteLogEvent",this.onTreasureSmithAutoWriteLogEvent);
         this._treasureSmithWin.treasureRefreshPanel.addEventListener("TreasureSmithReplace",this.onTreasureSmithReplace);
         this._commandQueue = [];
      }
      
      private function showHandler(param1:Object) : void
      {
         checkDataAvialable(this.onComplete);
      }
      
      private function onComplete() : void
      {
         var _loc1_:BlacksmithProxy = facade.retrieveProxy("BlacksmithProxy") as BlacksmithProxy;
         this._treasureSmithWin.heroList.setHerosInfo(_loc1_.getHeroList());
         this._treasureSmithWin.unuseEquipment.setInfo(_loc1_.getTreasureFromUserBag());
         this._treasureSmithWin.heroList.clickRoleHero();
         this._treasureSmithWin.treasureEquipmentList.clickDefaultTreasure();
         sendNotification("CS_TREASURE_SMITH_INFO");
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().bagData,MainData.getInstance().groupData];
      }
      
      private function selectHeroHandler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         var _loc3_:BlacksmithProxy = facade.retrieveProxy("BlacksmithProxy") as BlacksmithProxy;
         var _loc2_:HeroData = _loc3_.getHeroInfoByHeroID(int(param1.data));
         this._treasureSmithWin.treasureEquipmentList.setTreasureEquipmentsInfo(_loc2_);
         this._selectHeroId = int(param1.data);
         this._treasureSmithWin.treasureRefreshPanel.clearRefreshData();
         this._treasureSmithWin.unuseEquipment.clearSelect();
      }
      
      private function onTreasureSmithAutoHistoryEvent(param1:Event) : void
      {
         sendNotification("CS_TREASURE_SMITH_AUTO_GET_LOG",{});
      }
      
      private function onTreasureSmithBuyAuto(param1:Event) : void
      {
         sendNotification("CS_TREASURE_SMITH_BUY_AUTO",{});
      }
      
      private function onTreasureSmithAutoWriteLogEvent(param1:TreasureSmithAutoLogEvent) : void
      {
         this.sendNotice("CS_TREASURE_SMITH_AUTO_WRITE_LOG",{
            "itemId":param1.itemId,
            "itemTemplateId":param1.itemTemplateId,
            "affix":param1.affix,
            "counter":param1.counter,
            "layer":param1.layer,
            "star":param1.star
         });
      }
      
      private function onTreasureSmithAutoEvent(param1:TreasureSmithEvent) : void
      {
         var _loc12_:int = 0;
         var _loc10_:int = 0;
         var _loc11_:int = 0;
         var _loc15_:int = 0;
         var _loc16_:Array = null;
         var _loc19_:String = null;
         var _loc17_:int = 0;
         var _loc18_:Object = null;
         var _loc3_:String = null;
         var _loc5_:int = param1.itemId;
         var _loc4_:TreasureItem = ItemManager.getInstance().getItemInstanceByID(_loc5_) as TreasureItem;
         var _loc7_:Template_Treasure = _loc4_.template as Template_Treasure;
         var _loc2_:TreasureSmithProxy = facade.retrieveProxy("BlackTreasureSmithProxy") as TreasureSmithProxy;
         if(_loc2_.honor <= 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.79"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            this._treasureSmithWin.treasureRefreshPanel.stopAutoRefresh();
            return;
         }
         if(_loc4_ == null)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.14"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            this._treasureSmithWin.treasureRefreshPanel.stopAutoRefresh();
            return;
         }
         if(_loc4_.openSealNum <= 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.15"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            this._treasureSmithWin.treasureRefreshPanel.stopAutoRefresh();
            return;
         }
         var _loc13_:Array = param1.layers;
         if(_loc13_ == null || _loc13_.length == 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.16"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            this._treasureSmithWin.treasureRefreshPanel.stopAutoRefresh();
            return;
         }
         var _loc14_:int = param1.smithType;
         var _loc9_:int = 0;
         var _loc6_:int = 0;
         var _loc20_:int = MainData.getInstance().userData.belly_num;
         var _loc8_:TreasureSmithProxy = facade.retrieveProxy("BlackTreasureSmithProxy") as TreasureSmithProxy;
         if(_loc14_ == 0)
         {
            _loc9_ = int(TreasureSmithManager.getSmithCostByGold(_loc4_,_loc13_,0)[0]);
            _loc6_ = int(TreasureSmithManager.getSmithCostByGold(_loc4_,_loc13_,0)[1]);
            _loc11_ = MainData.getInstance().userData.gold_num;
            if(_loc9_ > _loc11_)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("treasureSmith.17"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               this._treasureSmithWin.treasureRefreshPanel.stopAutoRefresh();
               return;
            }
            if(_loc6_ > _loc20_)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("treasureSmith.18"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               this._treasureSmithWin.treasureRefreshPanel.stopAutoRefresh();
               return;
            }
         }
         else if(_loc14_ == 1)
         {
            _loc9_ = int(TreasureSmithManager.getSmithCostByGold(_loc4_,_loc13_,1)[0]);
            _loc6_ = int(TreasureSmithManager.getSmithCostByGold(_loc4_,_loc13_,1)[1]);
            _loc15_ = _loc8_.energyNum;
            if(_loc9_ > _loc15_)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("treasureSmith.19"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               this._treasureSmithWin.treasureRefreshPanel.stopAutoRefresh();
               return;
            }
            if(_loc6_ > _loc20_)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("treasureSmith.18"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               this._treasureSmithWin.treasureRefreshPanel.stopAutoRefresh();
               return;
            }
         }
         else if(_loc14_ == 2)
         {
            _loc16_ = TreasureSmithManager.getSmithCostByItem(_loc4_,_loc13_);
            _loc19_ = "";
            _loc17_ = 0;
            for each(_loc18_ in _loc16_)
            {
               _loc10_ = MainData.getInstance().bagData.userBag.numItems(_loc18_.itemId);
               _loc12_ = int(_loc18_.num);
               if(_loc10_ < _loc12_)
               {
                  _loc3_ = MessageReceive.getItemColorName(_loc18_.itemId);
                  _loc19_ += StringUtil.substitute(Globalization.getString("treasureSmith.42"),_loc3_);
                  _loc17_ += _loc12_ - _loc10_;
               }
            }
            if(_loc17_ > 0)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":_loc19_,
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               this._treasureSmithWin.treasureRefreshPanel.stopAutoRefresh();
               return;
            }
         }
         this.sendNotice("CS_TREASURE_SMITH_AUTO",{
            "itemId":_loc5_,
            "smithType":_loc14_,
            "layer":_loc13_
         });
      }
      
      private function sendNotice(param1:String, param2:Object) : void
      {
         if(this._commandQueue == null)
         {
            this._commandQueue = [];
         }
         this._commandQueue.push({
            "notice":param1,
            "data":param2
         });
         if(this._commandQueue.length == 1)
         {
            setTimeout(this.processNextCommand,50);
         }
      }
      
      private function processNextCommand() : void
      {
         if(this._commandQueue == null || this._commandQueue.length == 0)
         {
            return;
         }
         var _loc1_:Object = this._commandQueue.shift();
         sendNotification(_loc1_.notice,_loc1_.data);
      }
      
      private function onTreasureSmithEvent(param1:TreasureSmithEvent) : void
      {
         var _loc11_:int = 0;
         var _loc9_:int = 0;
         var _loc10_:int = 0;
         var _loc14_:int = 0;
         var _loc15_:Array = null;
         var _loc18_:String = null;
         var _loc16_:int = 0;
         var _loc17_:Object = null;
         var _loc3_:String = null;
         var _loc5_:int = param1.itemId;
         var _loc4_:TreasureItem = ItemManager.getInstance().getItemInstanceByID(_loc5_) as TreasureItem;
         var _loc7_:Template_Treasure = _loc4_.template as Template_Treasure;
         if(_loc4_ == null)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.14"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(_loc4_.openSealNum <= 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.15"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         var _loc2_:Array = param1.layers;
         if(_loc2_ == null || _loc2_.length == 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.16"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         var _loc12_:int = param1.smithType;
         var _loc13_:int = 0;
         var _loc8_:int = 0;
         var _loc6_:int = MainData.getInstance().userData.belly_num;
         var _loc19_:TreasureSmithProxy = facade.retrieveProxy("BlackTreasureSmithProxy") as TreasureSmithProxy;
         if(_loc12_ == 0)
         {
            _loc13_ = int(TreasureSmithManager.getSmithCostByGold(_loc4_,_loc2_,0)[0]);
            _loc8_ = int(TreasureSmithManager.getSmithCostByGold(_loc4_,_loc2_,0)[1]);
            _loc10_ = MainData.getInstance().userData.gold_num;
            if(_loc13_ > _loc10_)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("treasureSmith.17"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
            if(_loc8_ > _loc6_)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("treasureSmith.18"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
         }
         else if(_loc12_ == 1)
         {
            _loc13_ = int(TreasureSmithManager.getSmithCostByGold(_loc4_,_loc2_,1)[0]);
            _loc8_ = int(TreasureSmithManager.getSmithCostByGold(_loc4_,_loc2_,1)[1]);
            _loc14_ = _loc19_.energyNum;
            if(_loc13_ > _loc14_)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("treasureSmith.19"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
            if(_loc8_ > _loc6_)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("treasureSmith.18"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
         }
         else if(_loc12_ == 2)
         {
            _loc15_ = TreasureSmithManager.getSmithCostByItem(_loc4_,_loc2_);
            _loc18_ = "";
            _loc16_ = 0;
            for each(_loc17_ in _loc15_)
            {
               _loc9_ = MainData.getInstance().bagData.userBag.numItems(_loc17_.itemId);
               _loc11_ = int(_loc17_.num);
               if(_loc9_ < _loc11_)
               {
                  _loc3_ = MessageReceive.getItemColorName(_loc17_.itemId);
                  _loc18_ += StringUtil.substitute(Globalization.getString("treasureSmith.42"),_loc3_);
                  _loc16_ += _loc11_ - _loc9_;
               }
            }
            if(_loc16_ > 0)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":_loc18_,
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
         }
         sendNotification("CS_TREASURE_SMITH",{
            "itemId":_loc5_,
            "smithType":_loc12_,
            "layer":_loc2_
         });
      }
      
      private function onTreasureSmithReplace(param1:TreasureReplaceEvent) : void
      {
         var _loc4_:int = param1.itemId;
         var _loc2_:TreasureItem = ItemManager.getInstance().getItemInstanceByID(_loc4_) as TreasureItem;
         var _loc3_:int = _loc2_.needRepalceNum;
         if(_loc3_ <= 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.21"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         sendNotification("CS_TREASURE_SMITH_REPLACE",{
            "itemId":_loc4_,
            "layer":param1.replaceLayer
         });
      }
      
      override public function onRegister() : void
      {
         if(!facade.retrieveProxy("BlacksmithProxy"))
         {
            facade.registerProxy(new BlacksmithProxy());
         }
         facade.registerProxy(new TreasureSmithProxy());
         facade.registerCommand("CS_TREASURE_SMITH_INFO",TreasureSmithCommand);
         facade.registerCommand("CS_TREASURE_SMITH",TreasureSmithCommand);
         facade.registerCommand("CS_TREASURE_SMITH_AUTO",TreasureSmithCommand);
         facade.registerCommand("CS_TREASURE_SMITH_BUY_AUTO",TreasureSmithCommand);
         facade.registerCommand("CS_TREASURE_SMITH_AUTO_WRITE_LOG",TreasureSmithCommand);
         facade.registerCommand("CS_TREASURE_SMITH_AUTO_GET_LOG",TreasureSmithCommand);
         facade.registerCommand("CS_TREASURE_SMITH_REPLACE",TreasureSmithCommand);
      }
      
      override public function onRemove() : void
      {
         facade.removeProxy("BlackTreasureSmithProxy");
         facade.removeCommand("CS_TREASURE_SMITH_INFO");
         facade.removeCommand("CS_TREASURE_SMITH");
         facade.removeCommand("CS_TREASURE_SMITH_AUTO");
         facade.removeCommand("CS_TREASURE_SMITH_BUY_AUTO");
         facade.removeCommand("CS_TREASURE_SMITH_AUTO_WRITE_LOG");
         facade.removeCommand("CS_TREASURE_SMITH_AUTO_GET_LOG");
         facade.removeCommand("CS_TREASURE_SMITH_REPLACE");
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc2_:TreasureSmithProxy = facade.retrieveProxy("BlackTreasureSmithProxy") as TreasureSmithProxy;
         switch(param1.getName())
         {
            case "SC_TREASURE_SMITH_AUTO_WRITE_LOG":
               this.processNextCommand();
               break;
            case "SC_TREASURE_SMITH_BUY_AUTO":
               this._treasureSmithWin.treasureRefreshPanel.updateHonorNum(_loc2_.honor);
               break;
            case "SC_TREASURE_SMITH_AUTO_GET_LOG":
               this._treasureSmithWin.treasureRefreshPanel.showAutoLog(param1.getBody() as Array);
               break;
            case "SC_TREASURE_SMITH_AUTO":
               this._treasureSmithWin.treasureRefreshPanel.updateRefreshData(param1.getBody() as Array);
               this._treasureSmithWin.treasureRefreshPanel.updateEnergyNum(_loc2_.energyNum);
               this._treasureSmithWin.treasureRefreshPanel.updateHonorNum(_loc2_.honor);
               break;
            case "SC_TREASURE_SMITH":
               this._treasureSmithWin.treasureRefreshPanel.updateRefreshData(param1.getBody() as Array);
               this._treasureSmithWin.treasureRefreshPanel.updateEnergyNum(_loc2_.energyNum);
               break;
            case "SC_TREASURE_SMITH_REPLACE":
               this._treasureSmithWin.treasureRefreshPanel.updateReplaceData(param1.getBody() as Array);
               if(this._selectHeroId != 0 && this._treasureSmithWin.isHeroTreasure)
               {
                  sendNotification("SC_STRENGTH_REFRESH_POWER",this._selectHeroId);
               }
               break;
            case "SC_TREASURE_SMITH_INFO":
               this._treasureSmithWin.treasureRefreshPanel.updateEnergyNum(_loc2_.energyNum);
               this._treasureSmithWin.treasureRefreshPanel.updateHonorNum(_loc2_.honor);
               this._treasureSmithWin.treasureRefreshPanel.updateAutoBuyConfig(_loc2_.cost,_loc2_.packages);
         }
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_TREASURE_SMITH_INFO","SC_TREASURE_SMITH","SC_TREASURE_SMITH_REPLACE","SC_TREASURE_SMITH_AUTO","SC_TREASURE_SMITH_BUY_AUTO","SC_TREASURE_SMITH_AUTO_WRITE_LOG","SC_TREASURE_SMITH_AUTO_GET_LOG"];
      }
   }
}

