package blacksmith.proxy
{
   import game.data.MainData;
   import game.data.group.HeroData;
   import game.items.framework.interfaces.IEquipmentInterface;
   import game.items.framework.interfaces.IGemInterface;
   import game.items.framework.interfaces.ITreasureInterface;
   import game.items.framework.items.Item;
   import game.items.framework.templates.Template_Equipment;
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class BlacksmithProxy extends Proxy
   {
      public static const NAME:String = "BlacksmithProxy";
      
      public var randomRefreshNeedBelly:int;
      
      public var randomRefreshNeedGold:int;
      
      public var fixRefreshNeedBelly:int;
      
      public var fixRefreshNeedGold:int;
      
      public function BlacksmithProxy()
      {
         super("BlacksmithProxy");
      }
      
      public function getHeroList() : Array
      {
         return MainData.getInstance().groupData.list;
      }
      
      public function numHeros() : int
      {
         return MainData.getInstance().groupData.numHeros();
      }
      
      public function getHeroInfoByIndex(param1:int) : HeroData
      {
         return MainData.getInstance().groupData.list[param1] as HeroData;
      }
      
      public function getHeroInfoByHeroID(param1:int) : HeroData
      {
         return MainData.getInstance().groupData.getHeroDataByHeroID(param1);
      }
      
      public function getEquipmentsFromUserBag() : Array
      {
         var condition:Function = null;
         condition = function(param1:Item):Boolean
         {
            return param1.template is IEquipmentInterface;
         };
         return MainData.getInstance().bagData.userBag.getItemsByCondition(condition);
      }
      
      public function getCanUpEquipmentsFromUserBag() : Array
      {
         var condition:Function = null;
         condition = function(param1:Item):Boolean
         {
            if(param1.template is IEquipmentInterface)
            {
               return Template_Equipment(param1.template).upID != 0 && Template_Equipment(param1.template).isDarkGold != 1;
            }
            return false;
         };
         return MainData.getInstance().bagData.userBag.getItemsByCondition(condition);
      }
      
      public function getCanGoldUpEquipmentsFromUserBag() : Array
      {
         var condition:Function = null;
         condition = function(param1:Item):Boolean
         {
            if(param1.template is IEquipmentInterface)
            {
               return Template_Equipment(param1.template).upID != 0 && Template_Equipment(param1.template).isDarkGold == 1;
            }
            return false;
         };
         return MainData.getInstance().bagData.userBag.getItemsByCondition(condition);
      }
      
      public function getGemFromUserBag() : Array
      {
         var condition:Function = null;
         condition = function(param1:Item):Boolean
         {
            return param1.template is IGemInterface;
         };
         return MainData.getInstance().bagData.userBag.getItemsByCondition(condition);
      }
      
      public function getTreasureFromUserBag() : Array
      {
         var condition:Function = null;
         condition = function(param1:Item):Boolean
         {
            return param1.template is ITreasureInterface;
         };
         return MainData.getInstance().bagData.userBag.getItemsByCondition(condition);
      }
      
      public function getEightGemFromUserBag() : Array
      {
         var condition:Function = null;
         condition = function(param1:Item):Boolean
         {
            return param1.template is IGemInterface && param1.quality >= 8;
         };
         return MainData.getInstance().bagData.userBag.getItemsByCondition(condition);
      }
   }
}

