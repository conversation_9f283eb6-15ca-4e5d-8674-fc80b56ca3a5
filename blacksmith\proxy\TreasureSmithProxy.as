package blacksmith.proxy
{
   import blacksmith.manage.TreasureSmithManager;
   import game.data.MainData;
   import game.items.ItemManager;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   import game.items.framework.items.TreasureItem;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   
   public class TreasureSmithProxy extends Proxy
   {
      public static const NAME:String = "BlackTreasureSmithProxy";
      
      public var honor:int;
      
      public var energyNum:int;
      
      public var freeNum:int;
      
      public var cost:Array;
      
      public var packages:Array;
      
      public function TreasureSmithProxy(param1:Object = null)
      {
         super("BlackTreasureSmithProxy",param1);
         BabelTimeSocket.getInstance().regCallback("re.jewelry.refreshInfo",this.refreshInfoHandler);
         BabelTimeSocket.getInstance().regCallback("re.jewelry.refresh",this.treasureSmithHandler);
         BabelTimeSocket.getInstance().regCallback("re.jewelry.refreshAuto",this.treasureSmithAutoHandler);
         BabelTimeSocket.getInstance().regCallback("re.jewelry.buyHonor",this.treasureSmithBuyAutoHandler);
         BabelTimeSocket.getInstance().regCallback("re.jewelry.writeAutoLog",this.treasureSmithAutoWriteLogHandler);
         BabelTimeSocket.getInstance().regCallback("re.jewelry.getAutoHistory",this.treasureSmithAutoGetLogHandler);
         BabelTimeSocket.getInstance().regCallback("re.jewelry.replace",this.treasureSmithReplaceHandler);
         BabelTimeSocket.getInstance().regCallback("re.jewelry.sealTransfer",this.treasureSealTransferHandler);
      }
      
      public function handNotice(param1:String, param2:Object) : void
      {
         switch(param1)
         {
            case "CS_TREASURE_SMITH_INFO":
               BabelTimeSocket.getInstance().sendMessage("jewelry.refreshInfo",new SocketCallback("re.jewelry.refreshInfo"));
               break;
            case "CS_TREASURE_SMITH_BUY_AUTO":
               BabelTimeSocket.getInstance().sendMessage("jewelry.buyHonor",new SocketCallback("re.jewelry.buyHonor",[param2.quantity]),param2.quantity);
               break;
            case "CS_TREASURE_SMITH_AUTO":
               BabelTimeSocket.getInstance().sendMessage("jewelry.refreshAuto",new SocketCallback("re.jewelry.refreshAuto",[param2.itemId,param2.layer,param2.smithType]),param2.itemId,param2.smithType,param2.layer);
               break;
            case "CS_TREASURE_SMITH_AUTO_GET_LOG":
               BabelTimeSocket.getInstance().sendMessage("jewelry.getAutoHistory",new SocketCallback("re.jewelry.getAutoHistory"));
               break;
            case "CS_TREASURE_SMITH_AUTO_WRITE_LOG":
               BabelTimeSocket.getInstance().sendMessage("jewelry.writeAutoLog",new SocketCallback("re.jewelry.writeAutoLog",[param2.itemId,param2.itemTemplateId,param2.affix,param2.counter,param2.layer,param2.star]),param2.itemId,param2.itemTemplateId,param2.affix,param2.counter,param2.layer,param2.star);
               break;
            case "CS_TREASURE_SMITH":
               BabelTimeSocket.getInstance().sendMessage("jewelry.refresh",new SocketCallback("re.jewelry.refresh",[param2.itemId,param2.layer,param2.smithType]),param2.itemId,param2.smithType,param2.layer);
               break;
            case "CS_TREASURE_SMITH_REPLACE":
               BabelTimeSocket.getInstance().sendMessage("jewelry.replace",new SocketCallback("re.jewelry.replace",[param2.itemId,param2.layer]),param2.itemId,param2.layer);
               break;
            case "CS_TREASURE_SEAL_TRANSFER":
               BabelTimeSocket.getInstance().sendMessage("jewelry.sealTransfer",new SocketCallback("re.jewelry.sealTransfer",[param2.srcItemID,param2.targetItemID,param2.type]),param2.srcItemID,param2.targetItemID,param2.type);
         }
      }
      
      private function refreshInfoHandler(param1:SocketDataEvent) : void
      {
         this.energyNum = int(param1.data.energy);
         this.freeNum = int(param1.data.vip_free_num);
         this.honor = int(param1.data.honor);
         this.cost = param1.data.auto_buy_cost;
         this.packages = param1.data.auto_buy_packages;
         sendNotification("SC_TREASURE_SMITH_INFO");
      }
      
      private function treasureSmithAutoGetLogHandler(param1:SocketDataEvent) : void
      {
         sendNotification("SC_TREASURE_SMITH_AUTO_GET_LOG",param1.data);
      }
      
      private function treasureSmithAutoWriteLogHandler(param1:SocketDataEvent) : void
      {
         sendNotification("SC_TREASURE_SMITH_AUTO_WRITE_LOG");
      }
      
      private function treasureSmithBuyAutoHandler(param1:SocketDataEvent) : void
      {
         this.honor = int(param1.data.honor);
         MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - int(param1.data.cost_gold);
         sendNotification("SC_TREASURE_SMITH_BUY_AUTO");
      }
      
      private function treasureSmithAutoHandler(param1:SocketDataEvent) : void
      {
         var _loc9_:int = 0;
         var _loc7_:Array = null;
         var _loc8_:int = 0;
         var _loc11_:TreasureItem = null;
         var _loc12_:Object = null;
         var _loc15_:Object = null;
         var _loc13_:* = undefined;
         var _loc14_:Object = null;
         var _loc3_:* = undefined;
         var _loc5_:Item = null;
         var _loc4_:Array = null;
         var _loc6_:String = null;
         var _loc2_:Object = null;
         var _loc10_:String = null;
         if(param1.data != false)
         {
            _loc9_ = int(param1.callbackParames[0]);
            _loc7_ = param1.callbackParames[1];
            _loc8_ = int(param1.callbackParames[2]);
            _loc11_ = ItemManager.getInstance().getItemInstanceByID(_loc9_) as TreasureItem;
            _loc12_ = _loc11_.seal_general_fixed;
            _loc15_ = param1.data.fresh;
            trace("======:" + _loc15_[1]);
            for(_loc13_ in _loc15_)
            {
               _loc12_[_loc13_] = _loc15_[_loc13_];
            }
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - int(param1.data.costgold);
            MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num - int(param1.data.costbelly);
            this.energyNum -= int(param1.data.costenergy);
            this.honor = int(param1.data.honor);
            _loc14_ = param1.data.baginfo;
            for(_loc3_ in _loc14_)
            {
               _loc5_ = ItemFactory.creatItem(_loc14_[_loc3_]);
               MainData.getInstance().bagData.setGridItem(_loc3_,_loc5_);
            }
            sendNotification("SC_TREASURE_SMITH_AUTO",_loc7_);
            if(_loc8_ == 2)
            {
               _loc4_ = TreasureSmithManager.getSmithCostByItem(_loc11_,_loc7_);
               _loc6_ = Globalization.getString("treasureSmith.43");
               for each(_loc2_ in _loc4_)
               {
                  _loc10_ = MessageReceive.getItemColorName(_loc2_.itemId);
                  _loc6_ += StringUtil.substitute(Globalization.getString("treasureSmith.44"),_loc10_,_loc2_.num);
               }
               sendNotification("POP_TEXT_TIPS",{
                  "text":_loc6_,
                  "textFormat":TextFormatLib.format_0x00FF00_14px
               });
            }
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.22"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      private function treasureSmithHandler(param1:SocketDataEvent) : void
      {
         var _loc9_:int = 0;
         var _loc7_:Array = null;
         var _loc8_:int = 0;
         var _loc11_:TreasureItem = null;
         var _loc12_:Object = null;
         var _loc15_:Object = null;
         var _loc13_:* = undefined;
         var _loc14_:Object = null;
         var _loc3_:* = undefined;
         var _loc5_:Item = null;
         var _loc4_:Array = null;
         var _loc6_:String = null;
         var _loc2_:Object = null;
         var _loc10_:String = null;
         if(param1.data != false)
         {
            _loc9_ = int(param1.callbackParames[0]);
            _loc7_ = param1.callbackParames[1];
            _loc8_ = int(param1.callbackParames[2]);
            _loc11_ = ItemManager.getInstance().getItemInstanceByID(_loc9_) as TreasureItem;
            _loc12_ = _loc11_.seal_general_fixed;
            _loc15_ = param1.data.fresh;
            trace("+++++++" + _loc15_[1]);
            for(_loc13_ in _loc15_)
            {
               _loc12_[_loc13_] = _loc15_[_loc13_];
            }
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - int(param1.data.costgold);
            MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num - int(param1.data.costbelly);
            this.energyNum -= int(param1.data.costenergy);
            _loc14_ = param1.data.baginfo;
            for(_loc3_ in _loc14_)
            {
               _loc5_ = ItemFactory.creatItem(_loc14_[_loc3_]);
               MainData.getInstance().bagData.setGridItem(_loc3_,_loc5_);
            }
            sendNotification("SC_TREASURE_SMITH",_loc7_);
            if(_loc8_ == 2)
            {
               _loc4_ = TreasureSmithManager.getSmithCostByItem(_loc11_,_loc7_);
               _loc6_ = Globalization.getString("treasureSmith.43");
               for each(_loc2_ in _loc4_)
               {
                  _loc10_ = MessageReceive.getItemColorName(_loc2_.itemId);
                  _loc6_ += StringUtil.substitute(Globalization.getString("treasureSmith.44"),_loc10_,_loc2_.num);
               }
               sendNotification("POP_TEXT_TIPS",{
                  "text":_loc6_,
                  "textFormat":TextFormatLib.format_0x00FF00_14px
               });
            }
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.22"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      private function treasureSmithReplaceHandler(param1:SocketDataEvent) : void
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:TreasureItem = null;
         var _loc2_:Array = null;
         if(param1.data.success)
         {
            _loc5_ = int(param1.callbackParames[0]);
            _loc3_ = int(param1.callbackParames[1]);
            _loc4_ = ItemManager.getInstance().getItemInstanceByID(_loc5_) as TreasureItem;
            if(_loc3_ == 0)
            {
               _loc2_ = _loc4_.needReplaceLayers;
            }
            else
            {
               _loc2_ = [_loc3_];
            }
            _loc4_.seal_general_fixed = param1.data.freshinfo as Object;
            _loc4_.freshAllSealInfo(param1.data.sealinfo);
            sendNotification("SC_TREASURE_SMITH_REPLACE",_loc2_);
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.23"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      private function treasureSealTransferHandler(param1:SocketDataEvent) : void
      {
         var _loc9_:int = 0;
         var _loc7_:int = 0;
         var _loc8_:TreasureItem = null;
         var _loc2_:TreasureItem = null;
         var _loc3_:Object = null;
         var _loc6_:* = undefined;
         var _loc5_:int = 0;
         var _loc4_:Item = null;
         if(param1.data.success)
         {
            _loc9_ = int(param1.callbackParames[0]);
            _loc7_ = int(param1.callbackParames[1]);
            _loc8_ = ItemManager.getInstance().getItemInstanceByID(_loc9_) as TreasureItem;
            _loc2_ = ItemManager.getInstance().getItemInstanceByID(_loc7_) as TreasureItem;
            _loc8_.clearSealInfo();
            _loc2_.freshAllSealInfo(param1.data.seal_info_b);
            _loc2_.freshOpenSealNum();
            this.energyNum -= int(param1.data.energy);
            MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num - int(param1.data.belly);
            _loc3_ = param1.data.baginfo;
            for(_loc6_ in _loc3_)
            {
               _loc4_ = ItemFactory.creatItem(_loc3_[_loc6_]);
               MainData.getInstance().bagData.setGridItem(_loc6_,_loc4_);
            }
            _loc5_ = int(param1.callbackParames[2]);
            if(_loc5_ == 0)
            {
               this.freeNum -= 1;
               this.freeNum = this.freeNum < 0 ? 0 : this.freeNum;
            }
            else if(_loc5_ == 1)
            {
               MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - int(param1.data.gold);
            }
            sendNotification("SC_TREASURE_SEAL_TRANSFER",_loc5_);
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.55"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.54"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
         }
      }
      
      override public function onRemove() : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.jewelry.refresh",this.treasureSmithHandler);
         BabelTimeSocket.getInstance().removeCallback("re.jewelry.refreshAuto",this.treasureSmithAutoHandler);
         BabelTimeSocket.getInstance().removeCallback("re.jewelry.buyHonor",this.treasureSmithBuyAutoHandler);
         BabelTimeSocket.getInstance().removeCallback("re.jewelry.writeAutoLog",this.treasureSmithAutoWriteLogHandler);
         BabelTimeSocket.getInstance().removeCallback("re.jewelry.getAutoHistory",this.treasureSmithAutoGetLogHandler);
         BabelTimeSocket.getInstance().removeCallback("re.jewelry.replace",this.treasureSmithReplaceHandler);
         BabelTimeSocket.getInstance().removeCallback("re.jewelry.refreshInfo",this.refreshInfoHandler);
         BabelTimeSocket.getInstance().removeCallback("re.jewelry.sealTransfer",this.treasureSealTransferHandler);
      }
   }
}

