package blacksmith.ui
{
   import blacksmith.mc.HeroPanelMC;
   import flash.events.DataEvent;
   import flash.events.MouseEvent;
   import game.data.group.HeroDetailData;
   import mmo.ext.filter.FilterLib;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.slot.PersonSlot;
   import util.Globalization;
   
   public class HeroListPanel extends UISprite
   {
      public static const SELECTHERO:String = "selectHero";
      
      private var _top:Number = 5;
      
      private var _buttom:Number = 5;
      
      private var _left:Number = 5;
      
      private var _right:Number = 5;
      
      private var _distanceH:Number = 5;
      
      private var _distanceV:Number = 5;
      
      private var _data:Array;
      
      private var selectedPos:int;
      
      private const ROWNUM:int = 2;
      
      private const LINENUM:int = 4;
      
      private var persons:Array;
      
      private var heroMC:HeroPanelMC;
      
      public function HeroListPanel(param1:int = 298, param2:int = 260)
      {
         var _loc3_:PersonSlot = null;
         super();
         this.heroMC = new HeroPanelMC(param1,param2);
         addChild(this.heroMC);
         this.heroMC.title.text = Globalization.getString("blackSmith.20");
         this.heroMC.heroList.lineMaxChildrenNumber = 2;
         this.heroMC.heroList.rowMaxChildrenNumber = 4;
         this.heroMC.heroList.lineSpace = this._distanceH;
         this.heroMC.heroList.rowSpace = this._distanceV;
         this.persons = [];
         var _loc4_:uint = 0;
         while(_loc4_ < 2 * 4)
         {
            _loc3_ = new PersonSlot();
            _loc3_.setSize(50,50);
            this.heroMC.heroList.addChild(_loc3_);
            this.persons.push(_loc3_);
            _loc4_++;
         }
         this.heroMC.btn_page.addEventListener("click",this.freshPageData);
      }
      
      public function clickRoleHero() : void
      {
         PersonSlot(this.persons[0]).dispatchEvent(new MouseEvent("click"));
      }
      
      public function setHerosInfo(param1:Array) : void
      {
         this._data = param1;
         this.heroMC.btn_page.init(1,Math.ceil(param1.length / (2 * 4)));
         this.freshPageData(null);
      }
      
      private function freshPageData(param1:MouseEvent) : void
      {
         var _loc5_:int = 0;
         var _loc3_:PersonSlot = null;
         var _loc4_:uint = uint((this.heroMC.btn_page.currentPage - 1) * 2 * 4);
         var _loc2_:uint = this.heroMC.btn_page.currentPage * 2 * 4;
         while(_loc4_ < _loc2_)
         {
            _loc5_ = _loc4_ % (2 * 4);
            _loc3_ = this.persons[_loc5_];
            if(this._data[_loc4_])
            {
               if(_loc4_ == this.selectedPos)
               {
                  this.persons[_loc5_].filters = [FilterLib.glow_inner_0xFF9900];
               }
               else
               {
                  this.persons[_loc5_].filters = [];
               }
               _loc3_.visible = true;
               _loc3_.name = HeroDetailData(this._data[_loc4_]).hid.toString();
               _loc3_.setItem(this._data[_loc4_],false,false);
               _loc3_.addEventListener("click",this.onClickHero);
            }
            else
            {
               _loc3_.visible = false;
               this.persons[_loc5_].filters = [];
            }
            _loc4_++;
         }
      }
      
      private function onClickHero(param1:MouseEvent) : void
      {
         param1.stopImmediatePropagation();
         var _loc2_:int = this.selectedPos % (2 * 4);
         this.persons[_loc2_] && (this.persons[_loc2_].filters = []);
         this.selectedPos = (this.heroMC.btn_page.currentPage - 1) * (2 * 4) + this.persons.indexOf(param1.currentTarget);
         this.persons[this.selectedPos % (2 * 4)].filters = [FilterLib.glow_inner_0xFF9900];
         dispatchEvent(new DataEvent("selectHero",true,false,param1.currentTarget.name));
      }
      
      override public function dispose() : void
      {
         super.dispose();
         while(this.persons.length)
         {
            this.persons.pop();
         }
         this.persons = null;
      }
   }
}

