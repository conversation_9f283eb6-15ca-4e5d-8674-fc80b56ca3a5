package blacksmith.ui
{
   import blacksmith.manage.TreasureSmithManager;
   import blacksmith.xmlParser.EquipUpgradeManager;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.events.PageNavigatorEvent;
   import game.items.ItemManager;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.manager.XmlManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   import mmo.ui.control.tab.TabButton;
   import mmo.utils.Guid;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class PreviewEqupmentGold extends UISprite
   {
      public static const lineNum:int = 8;
      
      public static const rowNum:int = 1;
      
      private var scrollPane:ScrollPane;
      
      private var paneCont:Sprite;
      
      private var tabsBox:UIBox;
      
      private var btn_page:PageNavigator;
      
      private var gps:Array;
      
      private var setectType:String = "";
      
      public function PreviewEqupmentGold()
      {
         var _loc4_:UISkin = null;
         super();
         _loc4_ = UIManager.getUISkin("group_bg");
         _loc4_.setSize(152,380);
         _loc4_.x = 6;
         addChild(_loc4_);
         this.tabsBox = new UIBox();
         this.tabsBox.lineMaxChildrenNumber = 1;
         this.tabsBox.rowMaxChildrenNumber = 8;
         this.tabsBox.leftSpace = 14;
         this.tabsBox.lineSpace = 2;
         this.tabsBox.topSpace = 8;
         addChild(this.tabsBox);
         this.gps = EquipUpgradeManager.instance.getTreeGoldGps();
         var _loc3_:int = int(this.gps.length);
         _loc4_ = UIManager.getUISkin("intro_small_bg");
         _loc4_.setSize(555,380);
         _loc4_.x = 164;
         addChild(_loc4_);
         _loc4_ = UIManager.getUISkin("split_small_ver");
         _loc4_.height = 374;
         _loc4_.y = 2;
         _loc4_.x = 322;
         addChild(_loc4_);
         _loc4_ = UIManager.getUISkin("split_small_ver");
         _loc4_.height = 374;
         _loc4_.y = 2;
         _loc4_.x = 696;
         addChild(_loc4_);
         var _loc1_:Label = new Label(Globalization.getString("equipUpgrade.25"),TextFormatLib.format_0xffed89_14px,[FilterLib.glow_0x272727]);
         _loc1_.autoSize = "center";
         _loc1_.x = 168;
         _loc1_.y = 4;
         _loc1_.width = 154;
         addChild(_loc1_);
         _loc1_ = new Label(Globalization.getString("equipUpgrade.26"),TextFormatLib.format_0xffed89_14px,[FilterLib.glow_0x272727]);
         _loc1_.autoSize = "center";
         _loc1_.x = 324;
         _loc1_.y = 4;
         _loc1_.width = 366;
         addChild(_loc1_);
         this.scrollPane = new ScrollPane(548,344);
         this.scrollPane.x = 168;
         this.scrollPane.y = 30;
         this.scrollPane.alwaysHideScrollBar = false;
         addChild(this.scrollPane);
         this.paneCont = new Sprite();
         this.scrollPane.addToPane(this.paneCont);
         this.btn_page = new PageNavigator();
         this.btn_page.init(1,Math.ceil(_loc3_ / (1 * 8)));
         this.btn_page.x = 24;
         this.btn_page.y = 350;
         addChild(this.btn_page);
         this.btn_page.addEventListener("pageChange",this.changePageHandler);
         this.changePageHandler(new PageNavigatorEvent("pageChange",1));
         var _loc2_:DisplayObject = this.tabsBox.getChildByName(this.gps[0]);
         _loc2_.dispatchEvent(new MouseEvent("click"));
      }
      
      private function changePageHandler(param1:PageNavigatorEvent) : void
      {
         var _loc6_:String = null;
         var _loc4_:TabButton = null;
         var _loc5_:uint = uint((param1.currentPage - 1) * 1 * 8);
         var _loc2_:int = param1.currentPage * 1 * 8;
         var _loc3_:String = Guid.getUID();
         this.tabsBox.clearAllChild(this.tabsBox);
         while(_loc5_ < _loc2_)
         {
            if(!this.gps[_loc5_])
            {
               break;
            }
            _loc6_ = this.getGpData(this.gps[_loc5_]);
            _loc4_ = new TabButton(_loc6_,UIManager.getMultiUISkin("button_prize"),_loc3_);
            _loc4_.offsetY = 8;
            _loc4_.normalTextFormat = _loc4_.checkTextFormat = TextFormatLib.format_0xffed89_14px;
            _loc4_.name = this.gps[_loc5_];
            if(this.setectType == this.gps[_loc5_])
            {
               _loc4_.isCheck = true;
            }
            this.tabsBox.addChild(_loc4_);
            _loc4_.addEventListener("click",this.clickTabHandler);
            _loc5_++;
         }
      }
      
      private function clickTabHandler(param1:MouseEvent) : void
      {
         var _loc3_:* = null;
         var _loc4_:int = 0;
         var _loc5_:uint = 0;
         var _loc2_:* = param1;
         if(this.setectType == _loc2_.currentTarget.name)
         {
            return;
         }
         this.scrollPane.clearAllChild(this.paneCont);
         this.setectType = _loc2_.currentTarget.name;
         _loc3_ = XmlManager.getXml("equipment_up").children().(@type == setectType);
         _loc4_ = int(_loc3_.length());
         _loc5_ = 0;
         while(_loc5_ < _loc4_)
         {
            this.createLine(_loc3_[_loc5_],_loc5_);
            _loc5_++;
         }
      }
      
      public function getGpData(param1:String) : String
      {
         var _loc3_:Array = param1.split("|");
         var _loc2_:String = "";
         switch(int(_loc3_[1]) - 1)
         {
            case 0:
               _loc2_ = Globalization.getString("equipUpgrade.17");
               break;
            case 1:
               _loc2_ = Globalization.getString("equipUpgrade.18");
               break;
            case 2:
               _loc2_ = Globalization.getString("equipUpgrade.39");
         }
         return StringUtil.substitute(Globalization.getString("equipUpgrade.16"),_loc3_[0],_loc2_);
      }
      
      private function createLine(param1:XML, param2:int) : void
      {
         var _loc19_:Array = null;
         var _loc8_:Slot = null;
         var _loc23_:Icon = null;
         var _loc10_:String = null;
         var _loc25_:int = 0;
         var _loc11_:TextFormat = null;
         var _loc24_:Label = null;
         var _loc16_:Label = null;
         var _loc3_:Label = null;
         var _loc15_:Sprite = new Sprite();
         _loc15_.y = 115 * param2;
         var _loc4_:Slot = new Slot();
         _loc4_.setItem(this.getTemp(param1.@srcItem));
         var _loc14_:Array = String(param1.@needItems).split(",");
         var _loc9_:int = int(_loc14_.length);
         var _loc22_:Label = new Label("",TextFormatLib.format_0x00FF00_12px);
         _loc22_.x = 12;
         var _loc5_:int = int(param1.@srcItem);
         var _loc17_:int = MainData.getInstance().bagData.getNumItemByItemTemplate(_loc5_);
         _loc17_ = _loc17_ + TreasureSmithManager.getHerosEquipmentNumByTempId(_loc5_);
         _loc22_.htmlText = StringUtil.substitute(Globalization.getString("equipUpgrade.32"),_loc17_);
         _loc15_.addChild(_loc22_);
         var _loc12_:UISkin = UIManager.getUISkin("arrow_left2");
         _loc12_.x = 60;
         _loc15_.addChild(_loc12_);
         var _loc18_:Slot = new Slot();
         _loc18_.setItem(this.getTemp(param1.@aimItem));
         _loc4_.x = 12;
         _loc18_.x = 102;
         _loc15_.addChild(_loc4_);
         _loc15_.addChild(_loc18_);
         var _loc26_:Label = new Label("",TextFormatLib.format_0x00FF00_12px);
         _loc26_.x = 98;
         var _loc13_:int = int(param1.@aimItem);
         var _loc6_:int = MainData.getInstance().bagData.getNumItemByItemTemplate(_loc13_);
         _loc6_ = _loc6_ + TreasureSmithManager.getHerosEquipmentNumByTempId(_loc13_);
         _loc26_.htmlText = StringUtil.substitute(Globalization.getString("equipUpgrade.32"),_loc6_);
         _loc15_.addChild(_loc26_);
         if(_loc9_ > 5)
         {
            _loc4_.y = _loc18_.y = 76;
            _loc26_.y = 125;
            _loc22_.y = 125;
            _loc12_.y = 90;
         }
         else
         {
            _loc4_.y = _loc18_.y = 16;
            _loc22_.y = 65;
            _loc26_.y = 65;
            _loc12_.y = 30;
         }
         var _loc21_:UISkin = UIManager.getUISkin("text_bg_8");
         _loc21_.setSize(362,28);
         _loc21_.y = 60;
         _loc21_.x = 160;
         _loc15_.addChild(_loc21_);
         var _loc7_:Label = new Label(Globalization.getString("equipUpgrade.31"),TextFormatLib.format_0x00FF00_12px);
         _loc7_.x = 160;
         _loc7_.y = 65;
         _loc15_.addChild(_loc7_);
         var _loc20_:uint = 0;
         while(_loc20_ < _loc9_)
         {
            if(_loc20_ > 4)
            {
               _loc19_ = String(_loc14_[_loc20_]).split("|");
               _loc8_ = new Slot();
               _loc8_.setItem(this.getTemp(_loc19_[0],_loc19_[1]));
               _loc8_.x = 197;
               _loc8_.y = 99;
               _loc15_.addChild(_loc8_);
               _loc23_ = new Icon();
               _loc10_ = ItemManager.getInstance().getItemTemplate(_loc19_[0]).smallSmallitem;
               _loc23_.setData(UrlManager.getItemImgUrl(_loc10_));
               _loc23_.x = 197;
               _loc23_.y = 152;
               _loc15_.addChild(_loc23_);
               _loc25_ = MainData.getInstance().bagData.userBag.numItems(_loc19_[0]);
               _loc11_ = _loc25_ >= _loc19_[1] ? TextFormatLib.format_0x00FF00_12px : TextFormatLib.format_0xFF0000_12px;
               _loc24_ = new Label(_loc25_.toString(),_loc11_);
               _loc24_.x = _loc23_.x + 25;
               _loc24_.y = 155;
               _loc15_.addChild(_loc24_);
            }
            else
            {
               _loc19_ = String(_loc14_[_loc20_]).split("|");
               _loc8_ = new Slot();
               _loc8_.setItem(this.getTemp(_loc19_[0],_loc19_[1]));
               _loc8_.x = 197 + 68 * _loc20_;
               _loc8_.y = 9;
               _loc15_.addChild(_loc8_);
               _loc23_ = new Icon();
               _loc10_ = ItemManager.getInstance().getItemTemplate(_loc19_[0]).smallSmallitem;
               _loc23_.setData(UrlManager.getItemImgUrl(_loc10_));
               _loc23_.x = 197 + 68 * _loc20_;
               _loc23_.y = 62;
               _loc15_.addChild(_loc23_);
               _loc25_ = MainData.getInstance().bagData.userBag.numItems(_loc19_[0]);
               _loc11_ = _loc25_ >= _loc19_[1] ? TextFormatLib.format_0x00FF00_12px : TextFormatLib.format_0xFF0000_12px;
               _loc24_ = new Label(_loc25_.toString(),_loc11_);
               _loc24_.x = _loc23_.x + 25;
               _loc24_.y = 65;
               _loc15_.addChild(_loc24_);
            }
            _loc20_++;
         }
         if(_loc9_ > 5)
         {
            _loc16_ = this.createKeyValueLabel(_loc15_,Globalization.getString("equipUpgrade.23"),198,180,70);
            _loc16_.text = param1.@costBelly;
            _loc3_ = this.createKeyValueLabel(_loc15_,Globalization.getString("equipUpgrade.24"),338,180,38);
            _loc3_.text = param1.@costSoul;
            _loc21_ = UIManager.getUISkin("purpleSoul");
            _loc21_.x = 440;
            _loc21_.y = 178;
            _loc15_.addChild(_loc21_);
            _loc21_ = UIManager.getUISkin("splite_small_hor");
            _loc21_.width = 600;
            _loc21_.y = 205;
            _loc15_.addChild(_loc21_);
         }
         else
         {
            _loc16_ = this.createKeyValueLabel(_loc15_,Globalization.getString("equipUpgrade.23"),198,90,70);
            _loc16_.text = param1.@costBelly;
            _loc3_ = this.createKeyValueLabel(_loc15_,Globalization.getString("equipUpgrade.24"),338,90,38);
            _loc3_.text = param1.@costSoul;
            _loc21_ = UIManager.getUISkin("purpleSoul");
            _loc21_.x = 440;
            _loc21_.y = 88;
            _loc15_.addChild(_loc21_);
            _loc21_ = UIManager.getUISkin("splite_small_hor");
            _loc21_.width = 600;
            _loc21_.y = 115;
            _loc15_.addChild(_loc21_);
         }
         this.paneCont.addChild(_loc15_);
      }
      
      public function createKeyValueLabel(param1:Sprite, param2:String, param3:Number, param4:Number, param5:Number, param6:String = "train_bg") : Label
      {
         var _loc9_:UISkin = UIManager.getUISkin(param6);
         _loc9_.setSize(130,24);
         _loc9_.x = param3;
         _loc9_.y = param4;
         param1.addChild(_loc9_);
         var _loc8_:Label = new Label(param2,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc8_.x = param3 + 6;
         _loc8_.y = param4 + 2;
         param1.addChild(_loc8_);
         var _loc7_:Label = new Label("",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc7_.autoSize = "center";
         _loc7_.x = param3 + 62;
         _loc7_.y = param4 + 2;
         _loc7_.width = param5;
         param1.addChild(_loc7_);
         return _loc7_;
      }
      
      private function getTemp(param1:int, param2:int = 1) : SlotTemplete
      {
         var _loc3_:SlotTemplete = new SlotTemplete();
         _loc3_.tempID = param1;
         _loc3_.num = param2;
         return _loc3_;
      }
   }
}

