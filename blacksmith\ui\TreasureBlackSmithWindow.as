package blacksmith.ui
{
   import blacksmith.manage.TreasureSmithManager;
   import blacksmith.mediator.TreasureBlackSmithMediator;
   import blacksmith.ui.treasureRefresh.TreasureEquipList;
   import blacksmith.ui.treasureRefresh.TreasureHelpWin;
   import blacksmith.ui.treasureRefresh.TreasureRefreshPanel;
   import flash.events.DataEvent;
   import flash.events.MouseEvent;
   import game.items.ItemManager;
   import game.items.framework.interfaces.ITreasureInterface;
   import game.items.framework.items.TreasureItem;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotItem;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class TreasureBlackSmithWindow extends PopUpWindow
   {
      public var heroList:HeroListPanel;
      
      public var treasureEquipmentList:TreasureEquipList;
      
      public var unuseEquipment:UnusedEquipmentList;
      
      public var treasureRefreshPanel:TreasureRefreshPanel;
      
      private var _helpBtn:ImgButton;
      
      private var _selectSlot:Slot;
      
      public var isHeroTreasure:Boolean;
      
      public function TreasureBlackSmithWindow()
      {
         super(890,538);
         setTitleImageData(AssetManager.getImgData("title_treasureHouse"));
         this.isLive = false;
         TreasureSmithManager.setPropertyNameDic();
         this.heroList = new HeroListPanel(340,302);
         this.heroList.x = 7;
         pane.addChild(this.heroList);
         this.treasureEquipmentList = new TreasureEquipList(340);
         pane.addChild(this.treasureEquipmentList);
         this.treasureEquipmentList.x = 126;
         this.treasureEquipmentList.addEventListener("click",this.onEquipPanelClick);
         this.unuseEquipment = new UnusedEquipmentList(231,140,4,2,0,Globalization.getString("treasureSmith.41"));
         pane.addChild(this.unuseEquipment);
         this.unuseEquipment.x = 7;
         this.unuseEquipment.y = this.heroList.height + 6;
         this.unuseEquipment.addEventListener("selectEquip",this.onSelectBagTreasureHandler);
         this.treasureRefreshPanel = new TreasureRefreshPanel();
         this.treasureRefreshPanel.x = 245;
         pane.addChild(this.treasureRefreshPanel);
         this._helpBtn = new ImgButton(UIManager.getMultiUISkin("soul_Help_button"));
         this._helpBtn.x = 260;
         this._helpBtn.y = 400;
         pane.addChild(this._helpBtn);
         this._helpBtn.addEventListener("click",this.onHelpBtnClick);
         AppFacade.instance.registerMediator(new TreasureBlackSmithMediator(this));
      }
      
      private function onHelpBtnClick(param1:MouseEvent) : void
      {
         var _loc2_:TreasureHelpWin = new TreasureHelpWin();
         PopUpCenter.addPopUp("TreasureSmithHelpWin",_loc2_,true,true);
      }
      
      private function onEquipPanelClick(param1:MouseEvent) : void
      {
         var _loc2_:Slot = param1.target as Slot;
         if(_loc2_)
         {
            if(_loc2_.slotItem)
            {
               this._selectSlot && (this._selectSlot.select = false);
               this.treasureRefreshPanel.resetLayerStatus();
               this.unuseEquipment.clearSelect();
               _loc2_.select = true;
               this.treasureEquipmentList.selectItemId = SlotItem(_loc2_.slotItem).item.item_id;
               this.updateRefreshPanel(_loc2_);
               this._selectSlot = _loc2_;
               this.isHeroTreasure = true;
            }
            param1.stopImmediatePropagation();
         }
      }
      
      private function onSelectBagTreasureHandler(param1:DataEvent) : void
      {
         var _loc2_:TreasureItem = ItemManager.getInstance().getItemInstanceByID(int(param1.data)) as TreasureItem;
         if(_loc2_)
         {
            this._selectSlot && (this._selectSlot.select = false);
            if(_loc2_.template is ITreasureInterface)
            {
               this.treasureRefreshPanel.resetLayerStatus();
               this.treasureRefreshPanel.setRefreshData(_loc2_);
               this.isHeroTreasure = false;
            }
         }
      }
      
      private function updateRefreshPanel(param1:Slot) : void
      {
         var _loc3_:SlotItem = param1.slotItem as SlotItem;
         if(!_loc3_)
         {
            return;
         }
         var _loc2_:TreasureItem = _loc3_.item as TreasureItem;
         if(_loc2_.template is ITreasureInterface)
         {
            this.treasureRefreshPanel.setRefreshData(_loc2_);
         }
      }
      
      override public function get posHeight() : Number
      {
         return 518;
      }
      
      override public function dispose() : void
      {
         super.dispose();
         TreasureSmithManager.deletePropertyNameDic();
         AppFacade.instance.removeMediator("TreasureBlackSmithMediator");
      }
   }
}

