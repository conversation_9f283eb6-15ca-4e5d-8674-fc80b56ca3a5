package blacksmith.ui
{
   import flash.display.Sprite;
   import flash.events.DataEvent;
   import flash.events.MouseEvent;
   import game.items.framework.items.Item;
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotItem;
   import util.Globalization;
   
   public class UnusedEquipmentList extends Sprite
   {
      public static const SELECT_EQUIP:String = "selectEquip";
      
      private var _box:UISprite;
      
      private var _navipager:PageNavigator;
      
      private var pane:ScrollPane;
      
      private var nulLabel:Label;
      
      private var _itemID:int;
      
      private var _perLineNum:int;
      
      private var _space:int;
      
      public function UnusedEquipmentList(param1:Number = 231, param2:Number = 140, param3:int = 4, param4:int = 2, param5:int = 0, param6:String = "")
      {
         var _loc8_:Label = null;
         super();
         this._perLineNum = param3;
         this._space = param4;
         var _loc7_:UISkin = UIManager.getUISkin("pane_bg");
         _loc7_.setSize(param1,param2);
         addChild(_loc7_);
         if(param6 != "")
         {
            _loc8_ = new Label(param6,TextFormatLib.format_0xebce82_12px,[FilterLib.glow_0x272727]);
         }
         else
         {
            _loc8_ = new Label(Globalization.getString("blackSmith.24"),TextFormatLib.format_0xebce82_12px,[FilterLib.glow_0x272727]);
         }
         _loc8_.autoSize = "center";
         _loc8_.x = 12;
         _loc8_.y = 3;
         _loc8_.width = param1 - 24;
         addChild(_loc8_);
         this.pane = new ScrollPane(param1 - 13 - param5,param2 - 37);
         this.pane.x = 8 + param5;
         this.pane.y = 28;
         addChild(this.pane);
         this._box = new UISprite();
         this.pane.addToPane(this._box);
         this._perLineNum = param3;
      }
      
      public function setInfo(param1:Array, param2:Boolean = false) : void
      {
         var _loc5_:Slot = null;
         var _loc6_:SlotItem = null;
         this._box.dispose();
         var _loc3_:int = int(param1.length);
         if(_loc3_ == 0 && param2)
         {
            this.nulLabel ||= new Label(Globalization.getString("equipUpgrade.28"),TextFormatLib.format_0xFFFFFF_14px,[FilterLib.glow_0x272727]);
            this.nulLabel.autoSize = "center";
            this.nulLabel.y = 150;
            this.nulLabel.width = 182;
            this.nulLabel.parent || this.pane.addChild(this.nulLabel);
            return;
         }
         this.nulLabel && this.nulLabel.parent && this.nulLabel.parent.removeChild(this.nulLabel);
         var _loc4_:int = 0;
         while(_loc4_ < _loc3_)
         {
            _loc5_ = new Slot();
            if(param1[_loc4_])
            {
               _loc6_ = new SlotItem();
               _loc6_.item = param1[_loc4_];
               _loc5_.setItem(_loc6_,false,true,false,true);
               if(this._itemID == param1[_loc4_].item_id)
               {
                  _loc5_.select = true;
               }
               _loc5_.x = _loc4_ % this._perLineNum * (this._space + 50);
               _loc5_.y = int(_loc4_ / this._perLineNum) * (this._space + 50);
               _loc5_.name = Item(param1[_loc4_]).item_id.toString();
               this._box.addChild(_loc5_);
               _loc5_.addEventListener("click",this.clickHandler);
            }
            _loc4_++;
         }
         this.pane.updateUI();
      }
      
      private function clickHandler(param1:MouseEvent) : void
      {
         param1.stopImmediatePropagation();
         if(this._itemID)
         {
            Slot(this._box.getChildByName(this._itemID.toString())).select = false;
         }
         this._itemID = param1.currentTarget.name;
         Slot(param1.currentTarget).select = true;
         dispatchEvent(new DataEvent("selectEquip",false,false,this._itemID.toString()));
      }
      
      public function clearSelect() : void
      {
         if(this._itemID == 0)
         {
            return;
         }
         var _loc1_:Slot = this._box.getChildByName(this._itemID.toString()) as Slot;
         this._itemID && _loc1_ && (_loc1_.select = false);
         this._itemID = 0;
      }
   }
}

