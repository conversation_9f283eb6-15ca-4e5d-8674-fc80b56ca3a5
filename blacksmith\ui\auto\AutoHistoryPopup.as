package blacksmith.ui.auto
{
   import blacksmith.manage.TreasureSmithManager;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.StringToDate;
   
   public class AutoHistoryPopup extends PopUpWindow
   {
      public static const NAME:String = "AutoHistoryPopup";
      
      public static const WIDTH:int = 463;
      
      public static const HEIGHT:int = 376;
      
      private var _confrimBtn:Button;
      
      private var _tf:TextFormat;
      
      private var _greenTF:TextFormat;
      
      private var _scrollPane:ScrollPane;
      
      public function AutoHistoryPopup(param1:Array)
      {
         var _loc2_:Label = null;
         var _loc3_:int = 0;
         this._tf = new TextFormat("Verdana",10,16772489);
         this._greenTF = new TextFormat("Verdana",12,65280);
         super(463,376,UIManager.getUISkin("win_guide"));
         this.isLive = false;
         this.closeBtnVisible = false;
         this._tf.leading = 3;
         this._greenTF.leading = 3;
         var _loc6_:UISkin = UIManager.getUISkin("pane_bg_light");
         _loc6_.setSize(446,292);
         _loc6_.x = 8;
         _loc6_.y = 34;
         this.addChild(_loc6_);
         this._scrollPane = new ScrollPane(448,272);
         this._scrollPane.y = 42;
         this.addChild(this._scrollPane);
         var _loc4_:Sprite = new Sprite();
         _loc4_.x = -8;
         this._scrollPane.addToPane(_loc4_);
         var _loc5_:Label = new Label(Globalization.getString("treasureSmith.72"),TextFormatLib.format_0x00FF00_12px_verdana);
         _loc5_.width = 463;
         _loc5_.autoSize = "center";
         _loc4_.addChild(_loc5_);
         _loc3_ = 0;
         while(_loc3_ < param1.length)
         {
            _loc2_ = new Label("",this._tf);
            _loc2_.htmlText = this.getString(param1[_loc3_]);
            _loc2_.x = 30;
            _loc2_.y = _loc5_.y + _loc5_.height + 6 + _loc3_ * (_loc2_.height + 20);
            _loc2_.width = 400;
            _loc2_.mouseEnabled = false;
            _loc2_.wordWrap = true;
            _loc4_.addChild(_loc2_);
            _loc3_++;
         }
         this._confrimBtn = new Button(Globalization.queding,null,75,UIManager.getMultiUISkin("button_big"));
         this._confrimBtn.x = 463 - this._confrimBtn.width >> 1;
         this._confrimBtn.y = 376 - 45;
         this.addChild(this._confrimBtn);
         this._confrimBtn.addEventListener("click",this.onMouseClickHandler);
      }
      
      private function getString(param1:Object) : String
      {
         var _loc4_:String = null;
         var _loc2_:String = null;
         var _loc3_:* = param1;
         _loc4_ = Globalization.getString("treasureSmith.73");
         _loc2_ = XmlManager.itemsConf.children().item.(@id == _loc3_.item_template_id).attribute("name");
         return StringUtil.substitute(_loc4_,StringToDate.parseDateString1(_loc3_.created_time * 1000,Globalization.getString("treasureSmith.74")),_loc2_,_loc3_.layer,_loc3_.counter,TreasureSmithManager.getProperyNameById(_loc3_.affix),_loc3_.star);
      }
      
      private function onMouseClickHandler(param1:MouseEvent) : void
      {
         this.close();
      }
      
      override public function get posHeight() : Number
      {
         return 376;
      }
   }
}

