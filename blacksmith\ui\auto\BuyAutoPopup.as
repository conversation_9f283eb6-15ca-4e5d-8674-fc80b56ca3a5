package blacksmith.ui.auto
{
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ButtonModel;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.HTMLForTextField;
   
   public class BuyAutoPopup extends PopUpWindow
   {
      public static const NAME:String = "BuyAutoPopup";
      
      public static const WIDTH:int = 400;
      
      public static const HEIGHT:int = 252;
      
      private var _titleItem:Label;
      
      private var _buyNumLabel:Label;
      
      private var _clearText:Label;
      
      private var _addBeiNumBtn1:Button;
      
      private var _addBeiNumBtn2:Button;
      
      private var _addBeiNumBtn3:Button;
      
      private var _addBeiNumBtn4:Button;
      
      private var _costLabel:Label;
      
      private var _okBtn:Button;
      
      private var _cancelBtn:Button;
      
      private var _buyKongBeiNum:int;
      
      private var _cost:Array;
      
      private var _packages:Array;
      
      private var _cmd:String;
      
      public function BuyAutoPopup(param1:Array, param2:Array, param3:String = "CS_TREASURE_SMITH_BUY_AUTO")
      {
         super(400,252);
         this.isLive = false;
         this.title = Globalization.getString("treasureSmith.77");
         this._cost = param1;
         this._packages = param2;
         this._cmd = param3;
         var _loc7_:UISkin = UIManager.getUISkin("group_bg");
         _loc7_.x = 6;
         _loc7_.setSize(380,157);
         pane.addChild(_loc7_);
         var _loc4_:UISkin = UIManager.getUISkin("descBorder");
         _loc4_.setSize(360,142);
         _loc4_.x = 16;
         _loc4_.y = 8;
         pane.addChild(_loc4_);
         this._titleItem = new Label(StringUtil.substitute(Globalization.getString("treasureSmith.78"),this._cost[0],this._cost[1]),TextFormatLib.format_0xFFB932_14px);
         this._titleItem.x = 95;
         this._titleItem.y = 14;
         pane.addChild(this._titleItem);
         var _loc5_:UISkin = UIManager.getUISkin("pane_normal");
         _loc5_.setSize(120,40);
         _loc5_.x = 112;
         _loc5_.y = 45;
         pane.addChild(_loc5_);
         this._buyNumLabel = new Label("0",TextFormatLib.format_0xfffb00_18px);
         this._buyNumLabel.x = _loc5_.x + 3;
         this._buyNumLabel.y = _loc5_.y + 5;
         pane.addChild(this._buyNumLabel);
         this._clearText = new Label("",TextFormatLib.format_0x00FF00_14px);
         var _loc6_:String = Globalization.getString("buyKongdaobei1");
         _loc6_ = HTMLForTextField.getUnderlineTag(_loc6_);
         this._clearText.htmlText = _loc6_;
         this._clearText.x = _loc5_.x + _loc5_.width + 4;
         this._clearText.y = 53;
         this._clearText.buttonMode = true;
         pane.addChild(this._clearText);
         this._clearText.addEventListener("click",this.clearBuyNumHandler);
         this._addBeiNumBtn1 = new Button("",null,75);
         this._addBeiNumBtn1.x = 38;
         this._addBeiNumBtn1.y = 95;
         this._addBeiNumBtn1.textLabel.defaultTextFormat = TextFormatLib.format_Verdana_0xFFFFFF_12px;
         this._addBeiNumBtn1.text = StringUtil.substitute(Globalization.getString("buyKongdaobei2"),this._packages[0]);
         pane.addChild(this._addBeiNumBtn1);
         this._addBeiNumBtn2 = new Button("",null,75);
         this._addBeiNumBtn2.x = this._addBeiNumBtn1.x + this._addBeiNumBtn1.width + 3;
         this._addBeiNumBtn2.y = 95;
         this._addBeiNumBtn2.textLabel.defaultTextFormat = TextFormatLib.format_Verdana_0xFFFFFF_12px;
         this._addBeiNumBtn2.text = StringUtil.substitute(Globalization.getString("buyKongdaobei2"),this._packages[1]);
         pane.addChild(this._addBeiNumBtn2);
         this._addBeiNumBtn3 = new Button("",null,75);
         this._addBeiNumBtn3.x = this._addBeiNumBtn2.x + this._addBeiNumBtn2.width + 3;
         this._addBeiNumBtn3.y = 95;
         this._addBeiNumBtn3.textLabel.defaultTextFormat = TextFormatLib.format_Verdana_0xFFFFFF_12px;
         this._addBeiNumBtn3.text = StringUtil.substitute(Globalization.getString("buyKongdaobei2"),this._packages[2]);
         pane.addChild(this._addBeiNumBtn3);
         this._addBeiNumBtn4 = new Button("",null,75);
         this._addBeiNumBtn4.x = this._addBeiNumBtn3.x + this._addBeiNumBtn3.width + 3;
         this._addBeiNumBtn4.y = 95;
         this._addBeiNumBtn4.textLabel.defaultTextFormat = TextFormatLib.format_Verdana_0xFFFFFF_12px;
         this._addBeiNumBtn4.text = StringUtil.substitute(Globalization.getString("buyKongdaobei2"),this._packages[3]);
         pane.addChild(this._addBeiNumBtn4);
         this._costLabel = new Label(StringUtil.substitute(Globalization.getString("user.4"),0),TextFormatLib.format_0xFFF600_12px);
         this._costLabel.width = 372;
         this._costLabel.autoSize = "center";
         this._costLabel.y = 158;
         pane.addChild(this._costLabel);
         this._okBtn = new Button(Globalization.queding,null,78);
         this._okBtn.x = 108;
         this._okBtn.y = 178;
         pane.addChild(this._okBtn);
         this._cancelBtn = new Button(Globalization.quxiao,null,78);
         this._cancelBtn.x = 216;
         this._cancelBtn.y = 178;
         pane.addChild(this._cancelBtn);
         pane.addEventListener("click",this.onClickPanelHandler);
      }
      
      override public function get posHeight() : Number
      {
         return 252;
      }
      
      private function onClickPanelHandler(param1:MouseEvent) : void
      {
         var _loc4_:int = MainData.getInstance().userData.gold_num;
         var _loc2_:int = 0;
         var _loc3_:ButtonModel = param1.target as ButtonModel;
         if(_loc3_ == null)
         {
            return;
         }
         switch(_loc3_)
         {
            case this._addBeiNumBtn1:
               this._buyKongBeiNum += int(this._packages[0]);
               this.updateCost();
               break;
            case this._addBeiNumBtn2:
               this._buyKongBeiNum += int(this._packages[1]);
               this.updateCost();
               break;
            case this._addBeiNumBtn3:
               this._buyKongBeiNum += int(this._packages[2]);
               this.updateCost();
               break;
            case this._addBeiNumBtn4:
               this._buyKongBeiNum += int(this._packages[3]);
               this.updateCost();
               break;
            case this._okBtn:
               _loc2_ = int(this._buyKongBeiNum * this._cost[0]) / this._cost[1];
               if(_loc2_ > _loc4_)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("Gl.51"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               if(this._buyKongBeiNum <= 0)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("shop.5"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               this.sendNotification(this._cmd,{"quantity":this._buyKongBeiNum});
               this.resetStatus();
               break;
            case this._cancelBtn:
               this.close();
         }
      }
      
      private function sendNotification(param1:String, param2:Object) : void
      {
         AppFacade.instance.sendNotification(param1,param2);
      }
      
      private function clearBuyNumHandler(param1:MouseEvent) : void
      {
         this.resetStatus();
      }
      
      private function updateCost() : void
      {
         var _loc4_:Number = MainData.getInstance().userData.gold_num;
         var _loc3_:Array = XmlManager.getXml("card_config").<EMAIL>(",");
         var _loc1_:int = uint(_loc4_ / this._cost[0]) * this._cost[1];
         if(this._buyKongBeiNum > 0)
         {
            this._buyKongBeiNum = this._buyKongBeiNum <= _loc1_ ? this._buyKongBeiNum : _loc1_;
         }
         else
         {
            this._buyKongBeiNum = 0;
         }
         this._buyNumLabel.text = String(this._buyKongBeiNum);
         var _loc2_:int = int(this._buyKongBeiNum * this._cost[0]) / this._cost[1];
         this._costLabel.text = StringUtil.substitute(Globalization.getString("user.4"),_loc2_);
      }
      
      private function resetStatus() : void
      {
         this._buyKongBeiNum = 0;
         this._buyNumLabel.text = String(this._buyKongBeiNum);
         this._costLabel.text = StringUtil.substitute(Globalization.getString("user.4"),0);
      }
   }
}

