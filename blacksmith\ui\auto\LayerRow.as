package blacksmith.ui.auto
{
   import blacksmith.events.treasure.TreasureSmithAutoLogEvent;
   import blacksmith.manage.TreasureSmithManager;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.items.framework.items.Gem;
   import game.items.framework.items.Item;
   import game.items.framework.items.TreasureItem;
   import game.items.framework.templates.Template_Gem;
   import game.items.framework.templates.Template_Treasure;
   import game.manager.ParaEvent;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.xmlParsers.affix.IAffix;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.dropdown.DropdownList;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class LayerRow extends Sprite
   {
      private static var listOfDropDown:Array = [];
      
      private static var DEFAULT_AFFIX_TEXT:String = Globalization.getString("treasureSmith.63");
      
      private static var DEFAULT_STAR_TEXT:String = Globalization.getString("treasureSmith.64");
      
      private var layerPropBtn:Button;
      
      private var starBtn:Button;
      
      private var layerDropDown:DropdownList;
      
      private var starDropDown:DropdownList;
      
      private var layerIdx:int = 0;
      
      private var container:Sprite;
      
      private var _star:int = -1;
      
      private var _affixID:int = -1;
      
      private var _selectItem:Item = null;
      
      private var _counter:int = 0;
      
      public function LayerRow(param1:int, param2:Item, param3:Sprite)
      {
         super();
         this.container = param3;
         this.layerIdx = param1;
         var _loc4_:Label = new Label(StringUtil.substitute(Globalization.getString("treasureSmith.9"),param1 + 1),TextFormatLib.format_0xFFB932_12px);
         _loc4_.x = 0;
         _loc4_.y = 40;
         addChild(_loc4_);
         this.layerPropBtn = new Button(DEFAULT_AFFIX_TEXT,null,150,UIManager.getMultiUISkin("button2"));
         this.layerPropBtn.x = _loc4_.x + _loc4_.width + 5;
         this.layerPropBtn.y = _loc4_.y;
         this.addChild(this.layerPropBtn);
         this.layerPropBtn.addEventListener("click",this.onLayerPropBtnClick);
         this.layerPropBtn.setLabelOffsetY(-2);
         this.starBtn = new Button(DEFAULT_STAR_TEXT,null,100,UIManager.getMultiUISkin("button2"));
         this.starBtn.x = this.layerPropBtn.x + this.layerPropBtn.width + 5;
         this.starBtn.y = this.layerPropBtn.y;
         this.addChild(this.starBtn);
         this.starBtn.addEventListener("click",this.onStarBtnClick);
         this.starBtn.setLabelOffsetY(-2);
         this.layerDropDown = new DropdownList(this.getLayerModelList(param2));
         this.layerDropDown.addEventListener("DropDownListItem_SELECTED",this.onDropDownSelected);
         listOfDropDown.push(this.layerDropDown);
         this.starDropDown = new DropdownList(this.getStarModelList(param2));
         this.starDropDown.addEventListener("DropDownListItem_SELECTED",this.onStarDropDownSelected);
         listOfDropDown.push(this.starDropDown);
      }
      
      public static function cleanupDropDown() : void
      {
         var _loc1_:DropdownList = null;
         for each(_loc1_ in listOfDropDown)
         {
            if(_loc1_.parent)
            {
               _loc1_.parent.removeChild(_loc1_);
            }
         }
         listOfDropDown = [];
      }
      
      public function reset() : void
      {
         this.star = -1;
         this.affixID = -1;
         this._counter = 0;
      }
      
      private function onStarDropDownSelected(param1:ParaEvent) : void
      {
         this.container.removeChild(this.starDropDown);
         this.star = param1.para as int;
      }
      
      private function onDropDownSelected(param1:ParaEvent) : void
      {
         this.container.removeChild(this.layerDropDown);
         this.affixID = param1.para as int;
      }
      
      private function getLayerModelList(param1:Item) : Array
      {
         var _loc2_:int = 0;
         var _loc5_:Array = [{
            "id":-1,
            "name":DEFAULT_AFFIX_TEXT
         }];
         var _loc3_:Array = this.getViewProperties(param1);
         var _loc4_:Array = _loc3_[this.layerIdx].split("|");
         for each(_loc2_ in _loc4_)
         {
            _loc5_.push({
               "id":_loc2_,
               "name":TreasureSmithManager.getProperyNameById(_loc2_)
            });
         }
         return _loc5_;
      }
      
      private function onStarBtnClick(param1:MouseEvent) : void
      {
         var _loc2_:DropdownList = null;
         for each(_loc2_ in listOfDropDown)
         {
            if(_loc2_.parent)
            {
               this.container.removeChild(_loc2_);
            }
         }
         this.container.addChild(this.starDropDown);
         this.starDropDown.x = this.starBtn.x + 60;
         this.starDropDown.y = this.y + 44;
      }
      
      private function onLayerPropBtnClick(param1:MouseEvent) : void
      {
         var _loc2_:DropdownList = null;
         for each(_loc2_ in listOfDropDown)
         {
            if(_loc2_.parent)
            {
               this.container.removeChild(_loc2_);
            }
         }
         this.container.addChild(this.layerDropDown);
         this.layerDropDown.x = this.layerPropBtn.x + 60;
         this.layerDropDown.y = this.y + 44;
      }
      
      private function getStarModelList(param1:Item) : Array
      {
         var _loc4_:Array = [{
            "id":-1,
            "name":DEFAULT_STAR_TEXT
         }];
         var _loc2_:int = this.getMaxStar(param1);
         var _loc3_:int = 1;
         while(_loc3_ < _loc2_)
         {
            _loc4_.push({
               "id":_loc3_,
               "name":StringUtil.substitute(Globalization.getString("treasureSmith.65"),_loc3_)
            });
            _loc3_++;
         }
         return _loc4_;
      }
      
      public function checkStopCondition(param1:int) : void
      {
         var _loc2_:Boolean = this.internalCheckStopCondition(param1);
         this._counter++;
         if(_loc2_)
         {
            this.reset();
         }
      }
      
      public function internalCheckStopCondition(param1:int) : Boolean
      {
         var _loc5_:* = param1;
         var _loc3_:* = null;
         var _loc4_:IAffix = null;
         var _loc2_:int = 0;
         if(_loc5_ != 0)
         {
            _loc3_ = XmlManager.getXml("cachet").cachet.(@id == _loc5_);
            _loc4_ = this.getAffixByCachet(_loc3_);
            _loc2_ = int(_loc3_.@star_lv);
            if(_loc4_)
            {
               if(this._affixID == -1 && _loc2_ >= this._star)
               {
                  dispatchEvent(new TreasureSmithAutoLogEvent("TreasureSmithAutoWriteLogEvent",this._selectItem.item_id,this._selectItem.template.id,this.layerIndex,_loc2_,_loc4_.id,this._counter));
                  return true;
               }
               if(_loc4_.id == this._affixID && _loc2_ >= this._star)
               {
                  dispatchEvent(new TreasureSmithAutoLogEvent("TreasureSmithAutoWriteLogEvent",this._selectItem.item_id,this._selectItem.template.id,this.layerIndex,_loc2_,_loc4_.id,this._counter));
                  return true;
               }
            }
         }
         return false;
      }
      
      private function getItemName() : String
      {
         return this._selectItem.template.name;
      }
      
      public function set selectItem(param1:Item) : void
      {
         this._selectItem = param1;
      }
      
      public function set star(param1:int) : void
      {
         this._star = param1;
         this.starBtn.text = this._star == -1 ? DEFAULT_STAR_TEXT : StringUtil.substitute(Globalization.getString("treasureSmith.65"),this._star);
      }
      
      public function set affixID(param1:int) : void
      {
         this._affixID = param1;
         this.layerPropBtn.text = this._affixID == -1 ? DEFAULT_AFFIX_TEXT : TreasureSmithManager.getProperyNameById(this._affixID);
      }
      
      public function get star() : int
      {
         return this._star;
      }
      
      public function get affixID() : int
      {
         return this._affixID;
      }
      
      public function get layerIndex() : int
      {
         return this.layerIdx + 1;
      }
      
      public function get isSelected() : Boolean
      {
         return this._star != -1 || this._affixID != -1;
      }
      
      private function getAffixByCachet(param1:XMLList) : IAffix
      {
         if(this._selectItem is TreasureItem)
         {
            return TreasureItem(this._selectItem).getSealPropertyByXml(param1);
         }
         if(this._selectItem is Gem)
         {
            return Gem(this._selectItem).getSealPropertyByXml(param1);
         }
         return null;
      }
      
      private function getViewProperties(param1:Item) : Array
      {
         if(param1 is TreasureItem)
         {
            return Template_Treasure(param1.template).viewProperties;
         }
         if(param1 is Gem)
         {
            return Template_Gem(param1.template).viewProperties;
         }
         return [];
      }
      
      private function getMaxStar(param1:Item) : int
      {
         var _loc2_:Array = [];
         if(param1 is TreasureItem)
         {
            _loc2_ = Template_Treasure(param1.template).layerStarMax;
         }
         else if(param1 is Gem)
         {
            _loc2_ = Template_Gem(param1.template).layerStarMax;
         }
         return parseInt(_loc2_[this.layerIdx]) + 1;
      }
   }
}

