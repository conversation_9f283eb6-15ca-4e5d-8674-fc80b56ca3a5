package blacksmith.ui.auto
{
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.items.framework.items.Gem;
   import game.items.framework.items.Item;
   import game.items.framework.items.TreasureItem;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.label.Label;
   import util.Globalization;
   
   public class TreasureAutoSettingDlg extends Sprite
   {
      private static const WIDTH:int = 400;
      
      private static const HEIGHT:int = 200;
      
      protected var closeBtn:ImgButton;
      
      private var panelBG:UISkin;
      
      private var rows:Array = [];
      
      private var startBtn:Button;
      
      private var _refreshFunc:Function = null;
      
      private var _selectItem:Item = null;
      
      private var _isRunning:Boolean = false;
      
      private var _messageLabel:Label = null;
      
      public function TreasureAutoSettingDlg()
      {
         super();
         this.panelBG = UIManager.getUISkin("win_bg");
         this.panelBG.setSize(400,200);
         this.addChild(this.panelBG);
         var _loc1_:Label = new Label(Globalization.getString("activity.11"),TextFormatLib.format_0xebce82_12px);
         _loc1_.width = 400;
         _loc1_.autoSize = "center";
         _loc1_.y = 3;
         this.addChild(_loc1_);
         this.closeBtn = new ImgButton(UIManager.getMultiUISkin("btn_close2"));
         this.addChild(this.closeBtn);
         this.closeBtn.addEventListener("click",this.onExitBtnClick);
         this.closeBtn.x = this.panelBG.width - this.closeBtn.width - 3;
         this.startBtn = new Button(Globalization.getString("treasureSmith.67"),null,70);
         this.startBtn.x = 400 / 2 - this.startBtn.width / 2;
         this.startBtn.y = 200;
         this.addChild(this.startBtn);
         this.startBtn.addEventListener("click",this.onStartBtnClick);
         this.addEventListener("removedFromStage",this.onExit);
      }
      
      public function show(param1:Item) : void
      {
         var _loc2_:LayerRow = null;
         if(param1 == null)
         {
            return;
         }
         this.visible = true;
         if(this.isRunning)
         {
            return;
         }
         this._selectItem = param1;
         this.cleanup();
         var _loc4_:int = this.getOpenSealNum(param1);
         var _loc3_:int = 0;
         while(_loc3_ < _loc4_)
         {
            if(this.hasProperty(param1,_loc3_))
            {
               _loc2_ = new LayerRow(_loc3_,param1,this);
               _loc2_.x = 55;
               _loc2_.y = _loc3_ * 25;
               _loc2_.selectItem = param1;
               addChild(_loc2_);
               this.rows.push(_loc2_);
            }
            _loc3_++;
         }
         this.startBtn.y = _loc4_ * 25 + 45;
         this.panelBG.setSize(400,_loc4_ * 25 + 85);
      }
      
      private function hasProperty(param1:Item, param2:int) : Boolean
      {
         var _loc3_:Object = this.getSeal(param1);
         if(param2 == 0)
         {
            return true;
         }
         if(_loc3_ && _loc3_.hasOwnProperty(param2 + 1))
         {
            return true;
         }
         if(_loc3_ && _loc3_.hasOwnProperty(param2))
         {
            return true;
         }
         return false;
      }
      
      private function onExitBtnClick(param1:MouseEvent) : void
      {
         this.visible = false;
         if(!this.isRunning)
         {
            this.cleanup();
         }
      }
      
      public function stop(param1:Boolean = false) : void
      {
         var _loc2_:LayerRow = null;
         if(param1)
         {
            for each(_loc2_ in this.rows)
            {
               _loc2_.reset();
            }
         }
         this.isRunning = false;
         AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
            "text":Globalization.getString("treasureSmith.70"),
            "textFormat":TextFormatLib.format_0xFF0000_14px
         });
      }
      
      private function cleanup() : void
      {
         var _loc1_:LayerRow = null;
         for each(_loc1_ in this.rows)
         {
            if(_loc1_.parent)
            {
               _loc1_.parent.removeChild(_loc1_);
            }
         }
         LayerRow.cleanupDropDown();
         this.rows = [];
         this.isRunning = false;
      }
      
      private function onExit(param1:Event) : void
      {
         this.cleanup();
      }
      
      private function onStartBtnClick(param1:MouseEvent) : void
      {
         if(this.isRunning)
         {
            this.stop();
            return;
         }
         this.visible = false;
         this.isRunning = true;
         this._refreshFunc();
      }
      
      public function get isRunning() : Boolean
      {
         return this._isRunning;
      }
      
      public function set isRunning(param1:Boolean) : void
      {
         this._isRunning = param1;
         if(param1)
         {
            this.startBtn.text = Globalization.getString("treasureSmith.68");
         }
         else
         {
            this.startBtn.text = Globalization.getString("treasureSmith.67");
         }
         if(this._messageLabel)
         {
            this._messageLabel.visible = param1;
         }
      }
      
      public function get selectedLayers() : Array
      {
         var _loc1_:LayerRow = null;
         var _loc2_:Array = [];
         for each(_loc1_ in this.rows)
         {
            if(_loc1_.isSelected)
            {
               _loc2_.push(_loc1_.layerIndex);
            }
         }
         return _loc2_;
      }
      
      public function set refreshFunc(param1:Function) : void
      {
         this._refreshFunc = param1;
      }
      
      public function checkStopCondition(param1:Array) : void
      {
         if(!this.isRunning)
         {
            return;
         }
         var _loc3_:Object = this.getSealGeneralFixed();
         var _loc2_:int = 0;
         for each(_loc2_ in param1)
         {
            if(_loc3_ && _loc3_.hasOwnProperty(_loc2_))
            {
               LayerRow(this.rows[_loc2_ - 1]).checkStopCondition(_loc3_[_loc2_]);
            }
         }
         if(this.selectedLayers.length == 0)
         {
            this.isRunning = false;
         }
         else
         {
            this._refreshFunc();
         }
      }
      
      private function getSealGeneralFixed() : Object
      {
         if(this._selectItem is TreasureItem)
         {
            return TreasureItem(this._selectItem).seal_general_fixed;
         }
         if(this._selectItem is Gem)
         {
            return Gem(this._selectItem).seal_general_fixed;
         }
         return null;
      }
      
      private function getSeal(param1:Item) : Object
      {
         if(param1 is TreasureItem)
         {
            return TreasureItem(param1).seal;
         }
         if(param1 is Gem)
         {
            return Gem(param1).seal;
         }
         return null;
      }
      
      public function set messageLabel(param1:Label) : void
      {
         this._messageLabel = param1;
      }
      
      private function getOpenSealNum(param1:Item) : int
      {
         if(param1 is TreasureItem)
         {
            return TreasureItem(param1).openSealNum;
         }
         if(param1 is Gem)
         {
            return Gem(param1).openSealNum;
         }
         return 0;
      }
   }
}

