package blacksmith.ui.treasureRefresh
{
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import game.data.group.HeroData;
   import game.data.group.TreasureSuitProperty;
   import game.items.framework.items.Item;
   import game.items.framework.items.TreasureItem;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.infoMC.IconInfoMC;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotItem;
   import mmo.ui.control.slot.TreasureSlot;
   import util.Globalization;
   
   public class TreasureEquipList extends Sprite
   {
      public static const SELECT_EQUIP:String = "selectEquip";
      
      private var _treasureSlot1:TreasureSlot;
      
      private var _treasureSlot2:TreasureSlot;
      
      private var _treasureSlot3:TreasureSlot;
      
      private var _treasureSlot4:TreasureSlot;
      
      private var _treasureSlot5:TreasureSlot;
      
      private var _treasureSlot6:TreasureSlot;
      
      private var _selectItemId:int;
      
      private var _heroData:HeroData;
      
      public function TreasureEquipList(param1:int)
      {
         super();
         var _loc3_:UISkin = UIManager.getUISkin("pane_bg");
         _loc3_.setSize(114,param1);
         addChild(_loc3_);
         var _loc2_:Label = new Label(Globalization.getString("treasureSmith.24"),TextFormatLib.format_0xebce82_12px);
         _loc2_.autoSize = "center";
         _loc2_.x = 12;
         _loc2_.y = 3;
         _loc2_.width = 89;
         addChild(_loc2_);
         this._treasureSlot1 = new TreasureSlot(1);
         this._treasureSlot1.x = 7;
         this._treasureSlot1.y = 31;
         addChild(this._treasureSlot1);
         this._treasureSlot1.mouseChildren = false;
         this._treasureSlot1.addEventListener("mouseOver",this.onTreasureMouseOver);
         this._treasureSlot1.addEventListener("mouseOut",this.onTreasureMouseOut);
         this._treasureSlot2 = new TreasureSlot(2);
         this._treasureSlot2.x = 59;
         this._treasureSlot2.y = 31;
         addChild(this._treasureSlot2);
         this._treasureSlot2.mouseChildren = false;
         this._treasureSlot2.addEventListener("mouseOver",this.onTreasureMouseOver);
         this._treasureSlot2.addEventListener("mouseOut",this.onTreasureMouseOut);
         this._treasureSlot3 = new TreasureSlot(3);
         this._treasureSlot3.x = 7;
         this._treasureSlot3.y = 84;
         addChild(this._treasureSlot3);
         this._treasureSlot3.mouseChildren = false;
         this._treasureSlot3.addEventListener("mouseOver",this.onTreasureMouseOver);
         this._treasureSlot3.addEventListener("mouseOut",this.onTreasureMouseOut);
         this._treasureSlot4 = new TreasureSlot(4);
         this._treasureSlot4.x = 59;
         this._treasureSlot4.y = 84;
         addChild(this._treasureSlot4);
         this._treasureSlot4.mouseChildren = false;
         this._treasureSlot4.addEventListener("mouseOver",this.onTreasureMouseOver);
         this._treasureSlot4.addEventListener("mouseOut",this.onTreasureMouseOut);
         this._treasureSlot5 = new TreasureSlot(5);
         this._treasureSlot5.x = 7;
         this._treasureSlot5.y = 136;
         addChild(this._treasureSlot5);
         this._treasureSlot5.mouseChildren = false;
         this._treasureSlot5.addEventListener("mouseOver",this.onTreasureMouseOver);
         this._treasureSlot5.addEventListener("mouseOut",this.onTreasureMouseOut);
         this._treasureSlot6 = new TreasureSlot(6);
         this._treasureSlot6.x = 59;
         this._treasureSlot6.y = 136;
         addChild(this._treasureSlot6);
         this._treasureSlot6.mouseChildren = false;
         this._treasureSlot6.addEventListener("mouseOver",this.onTreasureMouseOver);
         this._treasureSlot6.addEventListener("mouseOut",this.onTreasureMouseOut);
      }
      
      private function onTreasureMouseOver(param1:MouseEvent) : void
      {
         var _loc4_:Slot = param1.currentTarget as Slot;
         if(!_loc4_.slotItem)
         {
            return;
         }
         var _loc2_:TreasureItem = (_loc4_.slotItem as SlotItem).item as TreasureItem;
         var _loc3_:Point = new Point(param1.currentTarget.x,param1.currentTarget.y);
         TreasureSuitProperty.showTreasureSuit(_loc2_,this._heroData.hid,_loc3_,_loc4_);
      }
      
      private function onTreasureMouseOut(param1:MouseEvent) : void
      {
         IconInfoMC.hide();
      }
      
      private function showTreasure(param1:Slot, param2:Item, param3:Boolean) : void
      {
         var _loc4_:SlotItem = null;
         param1.clearInfo();
         if(param2 == null)
         {
            return;
         }
         _loc4_ = new SlotItem();
         _loc4_.item = param2;
         if(this._selectItemId == param2.item_id)
         {
            param1.select = true;
         }
         param1.setItem(_loc4_,false,param3,false);
         param1.isShowGlowEffect = false;
      }
      
      private function clearSelect() : void
      {
         if(this._selectItemId == 0)
         {
            return;
         }
         this._selectItemId = 0;
         this._treasureSlot1.select = false;
         this._treasureSlot2.select = false;
         this._treasureSlot3.select = false;
         this._treasureSlot4.select = false;
         this._treasureSlot5.select = false;
         this._treasureSlot6.select = false;
      }
      
      public function setTreasureEquipmentsInfo(param1:HeroData) : void
      {
         this.clearSelect();
         this._heroData = param1;
         this.showTreasure(this._treasureSlot1,param1.getTreasureAt(1),true);
         this.showTreasure(this._treasureSlot2,param1.getTreasureAt(2),true);
         this.showTreasure(this._treasureSlot3,param1.getTreasureAt(3),true);
         this.showTreasure(this._treasureSlot4,param1.getTreasureAt(4),true);
         this.showTreasure(this._treasureSlot5,param1.getTreasureAt(5),true);
         this.showTreasure(this._treasureSlot6,param1.getTreasureAt(6),true);
      }
      
      public function clickDefaultTreasure() : void
      {
         if(this._treasureSlot1.slotItem)
         {
            this._treasureSlot1.dispatchEvent(new MouseEvent("click"));
         }
      }
      
      public function set selectItemId(param1:int) : void
      {
         this._selectItemId = param1;
      }
   }
}

