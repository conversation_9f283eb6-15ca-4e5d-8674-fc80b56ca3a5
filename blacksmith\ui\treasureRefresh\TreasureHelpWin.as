package blacksmith.ui.treasureRefresh
{
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class TreasureHelpWin extends PopUpWindow
   {
      public static const NAME:String = "TreasureSmithHelpWin";
      
      private var _submitBtn:Button;
      
      private var _tf:TextFormat = new TextFormat("Verdana",12,16772489);
      
      public function TreasureHelpWin()
      {
         super(463,416,UIManager.getUISkin("win_guide"));
         this.isLive = false;
         this.closeBtnVisible = false;
         this._tf.leading = 4;
         var _loc8_:UISkin = UIManager.getUISkin("pane_bg_light");
         _loc8_.width = 446;
         _loc8_.x = 8;
         _loc8_.y = 34;
         this.addChild(_loc8_);
         var _loc7_:Label = new Label(Globalization.getString("treasureSmith.1"),this._tf);
         _loc7_.x = 25;
         _loc7_.y = 50;
         _loc7_.wordWrap = true;
         _loc7_.width = 420;
         _loc7_.mouseEnabled = false;
         this.addChild(_loc7_);
         var _loc5_:Label = new Label(Globalization.getString("treasureSmith.2"),this._tf);
         _loc5_.x = 25;
         _loc5_.y = _loc7_.y + _loc7_.height;
         _loc5_.wordWrap = true;
         _loc5_.width = 420;
         _loc5_.mouseEnabled = false;
         this.addChild(_loc5_);
         var _loc6_:Label = new Label(Globalization.getString("treasureSmith.3"),this._tf);
         _loc6_.x = 25;
         _loc6_.y = _loc5_.y + _loc5_.height;
         _loc6_.wordWrap = true;
         _loc6_.width = 420;
         _loc6_.mouseEnabled = false;
         this.addChild(_loc6_);
         var _loc1_:Label = new Label(Globalization.getString("treasureSmith.4"),this._tf);
         _loc1_.x = 25;
         _loc1_.y = _loc6_.y + _loc6_.height;
         _loc1_.wordWrap = true;
         _loc1_.width = 420;
         _loc1_.mouseEnabled = false;
         this.addChild(_loc1_);
         var _loc2_:Label = new Label(Globalization.getString("treasureSmith.5"),this._tf);
         _loc2_.x = 25;
         _loc2_.y = _loc1_.y + _loc1_.height;
         _loc2_.wordWrap = true;
         _loc2_.width = 420;
         _loc2_.mouseEnabled = false;
         this.addChild(_loc2_);
         var _loc4_:Label = new Label(Globalization.getString("treasureSmith.6"),this._tf);
         _loc4_.x = 25;
         _loc4_.y = _loc2_.y + _loc2_.height;
         _loc4_.wordWrap = true;
         _loc4_.width = 420;
         _loc4_.mouseEnabled = false;
         this.addChild(_loc4_);
         var _loc3_:Label = new Label(Globalization.getString("treasureSmith.7"),this._tf);
         _loc3_.x = 25;
         _loc3_.y = _loc4_.y + _loc4_.height;
         _loc3_.wordWrap = true;
         _loc3_.width = 420;
         _loc3_.mouseEnabled = false;
         this.addChild(_loc3_);
         _loc8_.height = _loc3_.y + _loc3_.height + 4;
         this._submitBtn = new Button(Globalization.queding,null,75,UIManager.getMultiUISkin("button_big"));
         this._submitBtn.x = 195;
         this._submitBtn.y = _loc8_.height + _loc8_.y;
         this.addChild(this._submitBtn);
         this._submitBtn.addEventListener("click",this.onMouseClickHandler);
         this.setSize(463,this._submitBtn.y + this._submitBtn.height + 12);
      }
      
      private function onMouseClickHandler(param1:MouseEvent) : void
      {
         this.close();
      }
   }
}

