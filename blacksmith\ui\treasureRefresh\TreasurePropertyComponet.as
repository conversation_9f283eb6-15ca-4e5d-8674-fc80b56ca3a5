package blacksmith.ui.treasureRefresh
{
   import blacksmith.mc.TreasurePropertyMC;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.data.group.HeroDataUtil;
   import game.items.framework.items.TreasureItem;
   import game.items.framework.templates.Template_Treasure;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import game.xmlParsers.affix.IAffix;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.CheckBox;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotItem;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class TreasurePropertyComponet extends Sprite
   {
      public static const MAX_PROPERTY_NUM:int = 12;
      
      public static const BG_WIDTH:int = 250;
      
      public static const BG_WIDTH2:int = 295;
      
      public static const BG_HEIGHT:int = 258;
      
      private var _smithTreasureSlot:Slot;
      
      private var _treasureName:Label;
      
      private var _propertyLabel:Label;
      
      private var _allSelectBox:CheckBox;
      
      private var _allReplaceBtn:Button;
      
      private var _propertyMcArr:Array;
      
      private var _selectLayerArr:Array;
      
      public var changePropertyFun:Function;
      
      public var replaceFun:Function;
      
      private var _page:int = 1;
      
      private var _currMaxLayer:int = 8;
      
      private var _layerContiner:Sprite = new Sprite();
      
      private var _loc4_:TreasurePropertyMC = null;
      
      public function TreasurePropertyComponet(param1:Boolean = true)
      {
         var _loc4_:Label = null;
         var _loc5_:UISkin = null;
         _loc4_ = null;
         this._propertyMcArr = [];
         this._selectLayerArr = [];
         super();
         var _loc2_:UISkin = UIManager.getUISkin("board_bg");
         _loc2_.setSize(295,258);
         this.addChild(_loc2_);
         this._smithTreasureSlot = new Slot(UIManager.getUISkin("treasure1Slot"));
         this._smithTreasureSlot.x = 70;
         this._smithTreasureSlot.y = 8;
         this.addChild(this._smithTreasureSlot);
         this._treasureName = new Label("",TextFormatLib.format_0xffb932_12px);
         this._treasureName.x = 124;
         this._treasureName.y = 22;
         this.addChild(this._treasureName);
         if(param1)
         {
            _loc5_ = UIManager.getUISkin("level_bg2");
            _loc5_.setSize(132,18);
            _loc5_.x = 6;
            _loc5_.y = 65;
            this.addChild(_loc5_);
            _loc4_ = new Label(Globalization.getString("treasureSmith.25"),TextFormatLib.format_0xFFB932_12px);
            _loc4_.x = 11;
            _loc4_.y = 64;
            this.addChild(_loc4_);
            this._propertyLabel = new Label("",TextFormatLib.format_0xFFF5CE_12px_leading2);
            this._propertyLabel.x = 106;
            this._propertyLabel.y = 64;
            this.addChild(this._propertyLabel);
            this._allSelectBox = new CheckBox(Globalization.getString("treasureSmith.26"),TextFormatLib.format_0xFFB932_12px);
            this._allSelectBox.x = 230;
            this._allSelectBox.y = 65;
            this.addChild(this._allSelectBox);
            this._allSelectBox.addEventListener("click",this.onSelectAllHandler);
         }
         else
         {
            this._allReplaceBtn = new Button(Globalization.getString("treasureSmith.27"),null,60,UIManager.getMultiUISkin("btn_topMenu"));
            this._allReplaceBtn.x = 222;
            this._allReplaceBtn.y = 65;
            this.addChild(this._allReplaceBtn);
            this._allReplaceBtn.visible = false;
            this._allReplaceBtn.addEventListener("click",this.onReplaceAllHandler);
         }
         var _loc3_:int = 0;
         var _loc6_:int = 0;
         while(_loc3_ < 12)
         {
            _loc4_ = new TreasurePropertyMC(_loc3_ + 1,param1);
            if(_loc3_ % 8 == 0 && _loc3_ > 0)
            {
               _loc6_ = 0;
            }
            _loc4_.x = 7;
            _loc4_.y = 90 + _loc6_ * 20;
            this._propertyMcArr.push(_loc4_);
            _loc4_.propertyCheckBox && _loc4_.propertyCheckBox.addEventListener("click",this.onChangeLayer);
            _loc4_.replaceBtn && _loc4_.replaceBtn.addEventListener("click",this.onReplaceHandler);
            _loc3_++;
            _loc6_++;
         }
      }
      
      private function onReplaceAllHandler(param1:MouseEvent) : void
      {
         var _loc6_:Boolean = false;
         var _loc4_:TreasureItem = TreasureItem(SlotItem(this._smithTreasureSlot.slotItem).item);
         var _loc5_:Object = _loc4_.seal;
         var _loc2_:Array = _loc4_.needReplaceLayers.slice().sort();
         var _loc3_:int = 1;
         while(_loc3_ < _loc2_[_loc2_.length - 1])
         {
            if(!(_loc5_ && _loc5_.hasOwnProperty(_loc3_)))
            {
               _loc6_ = false;
               break;
            }
            _loc6_ = true;
            _loc3_++;
         }
         if(_loc2_.length == 1 && _loc2_[0] == 1)
         {
            _loc6_ = true;
         }
         if(_loc6_)
         {
            this.replaceFun && this.replaceFun(0);
            return;
         }
         AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
            "text":StringUtil.substitute(Globalization.getString("finestrength.20"),_loc2_[_loc2_.length - 1] - 1),
            "textFormat":TextFormatLib.format_0xFF0000_14px
         });
      }
      
      private function onReplaceHandler(param1:MouseEvent) : void
      {
         var _loc3_:TreasurePropertyMC = param1.target.parent as TreasurePropertyMC;
         var _loc2_:Object = TreasureItem(SlotItem(this._smithTreasureSlot.slotItem).item).seal;
         if(_loc3_.propertyLayer == 1)
         {
            this.replaceFun && this.replaceFun((param1.target.parent as TreasurePropertyMC).propertyLayer);
         }
         else
         {
            if(!(_loc2_ && _loc2_.hasOwnProperty(_loc3_.propertyLayer - 1)))
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute("属性替换失败，需要前{0}层都存在洗练属性之后才能替换。",_loc3_.propertyLayer - 1),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
               return;
            }
            this.replaceFun && this.replaceFun((param1.target.parent as TreasurePropertyMC).propertyLayer);
         }
      }
      
      private function onChangeLayer(param1:MouseEvent) : void
      {
         this.changePropertyFun && this.changePropertyFun();
      }
      
      private function onSelectAllHandler(param1:MouseEvent) : void
      {
         var _loc3_:TreasurePropertyMC = null;
         var _loc2_:int = 0;
         while(_loc2_ < 12)
         {
            _loc3_ = this._propertyMcArr[_loc2_];
            if(_loc3_.propertyCheckBox && _loc3_.isCanSelect)
            {
               _loc3_.propertyCheckBox.isCheck = this._allSelectBox.isCheck;
            }
            _loc2_++;
         }
         this.changePropertyFun && this.changePropertyFun();
      }
      
      private function removeAllPropertyMc() : void
      {
         var _loc1_:TreasurePropertyMC = null;
         for each(_loc1_ in this._propertyMcArr)
         {
            _loc1_.parent && _loc1_.parent.removeChild(_loc1_);
            if(_loc1_.propertyCheckBox)
            {
               _loc1_.propertyCheckBox.isCheck = false;
            }
            _loc1_.isCanSelect = false;
         }
      }
      
      private function showPropertyMc(param1:TreasureItem, param2:Boolean, param3:int = 1) : void
      {
         var _loc17_:TreasurePropertyMC = null;
         var _loc21_:int = 0;
         var _loc19_:int = 0;
         var _loc4_:String = null;
         var _loc5_:int = 0;
         var _loc11_:int = 0;
         var _loc9_:* = null;
         var _loc12_:IAffix = null;
         var _loc20_:int = 0;
         var _loc13_:String = null;
         var _loc18_:* = param1;
         var _loc8_:* = param2;
         var _loc14_:Template_Treasure = _loc18_.template as Template_Treasure;
         var _loc7_:int = _loc14_.max_sealLayer;
         var _loc16_:int = _loc18_.openSealNum;
         var _loc10_:Object = _loc18_.seal;
         var _loc6_:Object = _loc18_.seal_general_fixed;
         var _loc15_:int = 0;
         _currMaxLayer = _loc7_;
         if(param3 == 1)
         {
            _loc15_ = 0;
         }
         else if(param3 == 2)
         {
            _loc15_ = 8;
         }
         else if(param3 == 3)
         {
            _loc15_ = 16;
         }
         if(_layerContiner.parent == null)
         {
            this.addChild(_layerContiner);
         }
         while(_loc15_ < 12)
         {
            _loc21_ = 0;
            _loc17_ = this._propertyMcArr[_loc15_];
            if(param3 == 1)
            {
               if(_loc15_ < 8)
               {
                  _layerContiner.addChild(_loc17_);
                  if(_loc15_ < _loc16_)
                  {
                     _loc19_ = int(_loc14_.layerStarMax[_loc15_]);
                     if(_loc8_ && _loc6_ && _loc6_.hasOwnProperty(_loc15_ + 1))
                     {
                        _loc21_ = int(_loc6_[_loc15_ + 1]);
                        if(_loc17_.replaceBtn)
                        {
                           _loc17_.replaceBtn.visible = true;
                        }
                     }
                     else if(_loc10_ && _loc10_.hasOwnProperty(_loc15_ + 1))
                     {
                        _loc21_ = int(_loc10_[_loc15_ + 1]);
                        if(_loc17_.replaceBtn)
                        {
                           _loc17_.replaceBtn.visible = false;
                        }
                     }
                     if(_loc21_ != 0)
                     {
                        _loc4_ = "";
                        _loc9_ = XmlManager.getXml("cachet").cachet.(@id == _loc21_);
                        _loc12_ = _loc18_.getSealPropertyByXml(_loc9_);
                        if(_loc12_)
                        {
                           _loc11_ = int(_loc9_.@bindHero);
                           if(_loc11_ != 0)
                           {
                              _loc4_ += HeroDataUtil.indexHeroNameBytid(_loc11_);
                           }
                           _loc4_ = _loc12_.print();
                        }
                        else
                        {
                           _loc4_ = StringUtil.substitute(Globalization.getString("infoMc.65"),Math.floor(int(_loc9_.@noun_addRate) / 100));
                        }
                        _loc5_ = int(_loc9_.@star_lv);
                        _loc17_.showProperty(0,_loc4_,_loc5_,_loc19_,true);
                     }
                     else if(_loc17_.propertyLayer == 1)
                     {
                        _loc17_.showProperty(1,"",0,_loc19_,true);
                     }
                     else if(_loc10_ && _loc10_.hasOwnProperty(_loc17_.propertyLayer - 1))
                     {
                        _loc17_.showProperty(1,"",0,_loc19_,true);
                     }
                     else
                     {
                        _loc17_.showProperty(1,"",0,_loc19_,false);
                     }
                  }
                  else
                  {
                     _loc20_ = int(_loc14_.openSealNeedReinforce_lv[_loc15_]);
                     _loc13_ = StringUtil.substitute(Globalization.getString("treasureSmith.57"),_loc20_);
                     _loc17_.showProperty(2,_loc13_);
                  }
               }
               else
               {
                  _loc17_.parent && _loc17_.parent.removeChild(_loc17_);
               }
            }
            else if(param3 == 2)
            {
               if(_loc15_ < 16)
               {
                  _layerContiner.addChild(_loc17_);
                  if(_loc15_ < _loc16_)
                  {
                     _loc19_ = int(_loc14_.layerStarMax[_loc15_]);
                     if(_loc8_ && _loc6_ && _loc6_.hasOwnProperty(_loc15_ + 1))
                     {
                        _loc21_ = int(_loc6_[_loc15_ + 1]);
                        if(_loc17_.replaceBtn)
                        {
                           _loc17_.replaceBtn.visible = true;
                        }
                     }
                     else if(_loc10_ && _loc10_.hasOwnProperty(_loc15_ + 1))
                     {
                        _loc21_ = int(_loc10_[_loc15_ + 1]);
                        if(_loc17_.replaceBtn)
                        {
                           _loc17_.replaceBtn.visible = false;
                        }
                     }
                     if(_loc21_ != 0)
                     {
                        _loc4_ = "";
                        _loc9_ = XmlManager.getXml("cachet").cachet.(@id == _loc21_);
                        _loc12_ = _loc18_.getSealPropertyByXml(_loc9_);
                        if(_loc12_)
                        {
                           _loc11_ = int(_loc9_.@bindHero);
                           if(_loc11_ != 0)
                           {
                              _loc4_ += HeroDataUtil.indexHeroNameBytid(_loc11_);
                           }
                           _loc4_ = _loc12_.print();
                        }
                        else
                        {
                           _loc4_ = StringUtil.substitute(Globalization.getString("infoMc.65"),Math.floor(int(_loc9_.@noun_addRate) / 100));
                        }
                        _loc5_ = int(_loc9_.@star_lv);
                        _loc17_.showProperty(0,_loc4_,_loc5_,_loc19_,true);
                     }
                     else if(_loc17_.propertyLayer == 1)
                     {
                        _loc17_.showProperty(1,"",0,_loc19_,true);
                     }
                     else if(_loc10_ && _loc10_.hasOwnProperty(_loc17_.propertyLayer - 1))
                     {
                        _loc17_.showProperty(1,"",0,_loc19_,true);
                     }
                     else
                     {
                        _loc17_.showProperty(1,"",0,_loc19_,false);
                     }
                  }
                  else
                  {
                     _loc20_ = int(_loc14_.openSealNeedReinforce_lv[_loc15_]);
                     _loc13_ = StringUtil.substitute(Globalization.getString("treasureSmith.57"),_loc20_);
                     _loc17_.showProperty(2,_loc13_);
                  }
               }
               else
               {
                  _loc17_.parent && _loc17_.parent.removeChild(_loc17_);
               }
            }
            else if(param3 == 3)
            {
               if(_loc15_ < 24)
               {
                  _layerContiner.addChild(_loc17_);
                  if(_loc15_ < _loc16_)
                  {
                     _loc19_ = int(_loc14_.layerStarMax[_loc15_]);
                     if(_loc8_ && _loc6_ && _loc6_.hasOwnProperty(_loc15_ + 1))
                     {
                        _loc21_ = int(_loc6_[_loc15_ + 1]);
                        if(_loc17_.replaceBtn)
                        {
                           _loc17_.replaceBtn.visible = true;
                        }
                     }
                     else if(_loc10_ && _loc10_.hasOwnProperty(_loc15_ + 1))
                     {
                        _loc21_ = int(_loc10_[_loc15_ + 1]);
                        if(_loc17_.replaceBtn)
                        {
                           _loc17_.replaceBtn.visible = false;
                        }
                     }
                     if(_loc21_ != 0)
                     {
                        _loc4_ = "";
                        _loc9_ = XmlManager.getXml("cachet").cachet.(@id == _loc21_);
                        _loc12_ = _loc18_.getSealPropertyByXml(_loc9_);
                        if(_loc12_)
                        {
                           _loc11_ = int(_loc9_.@bindHero);
                           if(_loc11_ != 0)
                           {
                              _loc4_ += HeroDataUtil.indexHeroNameBytid(_loc11_);
                           }
                           _loc4_ = _loc12_.print();
                        }
                        else
                        {
                           _loc4_ = StringUtil.substitute(Globalization.getString("infoMc.65"),Math.floor(int(_loc9_.@noun_addRate) / 100));
                        }
                        _loc5_ = int(_loc9_.@star_lv);
                        _loc17_.showProperty(0,_loc4_,_loc5_,_loc19_,true);
                     }
                     else if(_loc17_.propertyLayer == 1)
                     {
                        _loc17_.showProperty(1,"",0,_loc19_,true);
                     }
                     else if(_loc10_ && _loc10_.hasOwnProperty(_loc17_.propertyLayer - 1))
                     {
                        _loc17_.showProperty(1,"",0,_loc19_,true);
                     }
                     else
                     {
                        _loc17_.showProperty(1,"",0,_loc19_,false);
                     }
                  }
                  else
                  {
                     _loc20_ = int(_loc14_.openSealNeedReinforce_lv[_loc15_]);
                     _loc13_ = StringUtil.substitute(Globalization.getString("treasureSmith.57"),_loc20_);
                     _loc17_.showProperty(2,_loc13_);
                  }
               }
               else
               {
                  _loc17_.parent && _loc17_.parent.removeChild(_loc17_);
               }
            }
            _loc15_++;
         }
      }
      
      private function updateLayerProperty(param1:TreasureItem, param2:int, param3:Boolean = true) : void
      {
         var _loc4_:String = null;
         var _loc5_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:* = null;
         var _loc8_:IAffix = null;
         var _loc14_:* = param1;
         var _loc9_:* = param2;
         var _loc12_:* = param3;
         var _loc11_:Template_Treasure = _loc14_.template as Template_Treasure;
         var _loc10_:Object = _loc12_ ? _loc14_.seal : _loc14_.seal_general_fixed;
         var _loc16_:int = int(_loc10_[_loc9_]);
         var _loc15_:int = int(_loc11_.layerStarMax[_loc9_ - 1]);
         var _loc13_:TreasurePropertyMC = this._propertyMcArr[_loc9_ - 1];
         if(_loc16_ != 0)
         {
            _loc4_ = "";
            _loc6_ = XmlManager.getXml("cachet").cachet.(@id == _loc16_);
            _loc8_ = _loc14_.getSealPropertyByXml(_loc6_);
            if(_loc8_)
            {
               _loc7_ = int(_loc6_.@bindHero);
               if(_loc7_ != 0)
               {
                  _loc4_ += HeroDataUtil.indexHeroNameBytid(_loc7_);
               }
               _loc4_ = _loc8_.print();
            }
            else
            {
               _loc4_ = StringUtil.substitute(Globalization.getString("infoMc.65"),Math.floor(int(_loc6_.@noun_addRate) / 100));
            }
            _loc5_ = int(_loc6_.@star_lv);
            _loc13_.showProperty(0,_loc4_,_loc5_,_loc15_,true);
         }
      }
      
      public function clearData() : void
      {
         _layerContiner.removeChildren(0,_layerContiner.numChildren - 1);
      }
      
      public function setPropertyData(param1:TreasureItem, param2:int, param3:int = 1) : void
      {
         var _loc8_:TreasurePropertyMC = null;
         var _loc9_:int = 0;
         if(param1 == null)
         {
            return;
         }
         var _loc4_:Template_Treasure = param1.template as Template_Treasure;
         var _loc5_:SlotItem = new SlotItem();
         _loc5_.item = param1;
         this._smithTreasureSlot.setItem(_loc5_);
         this._treasureName.htmlText = StringUtil.substitute("{0} <font color=\'#00ff00\'>+{1}</font>",param1.name,param1.reinforce_level);
         var _loc7_:int = _loc4_.max_sealLayer;
         if(this._propertyLabel)
         {
            this._propertyLabel.text = StringUtil.substitute("{0}/{1}",param1.openSealNum,_loc7_);
         }
         var _loc6_:int = 0;
         if(param2 == 1)
         {
            this.showPropertyMc(param1,false,param3);
         }
         else if(param2 == 2)
         {
            _loc9_ = param1.needRepalceNum;
            this._allReplaceBtn.visible = _loc9_ <= 0 ? false : true;
            this.showPropertyMc(param1,true,param3);
         }
      }
      
      public function isProterty(param1:TreasureItem, param2:Array) : Boolean
      {
         var _loc6_:Object = null;
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc13_:int = 0;
         var _loc12_:int = 0;
         var _loc4_:* = null;
         var _loc10_:TreasurePropertyMC = null;
         var _loc11_:* = param1;
         var _loc9_:* = param2;
         var _loc8_:Boolean = false;
         var _loc7_:Template_Treasure = _loc11_.template as Template_Treasure;
         _loc6_ = _loc11_.seal_general_fixed;
         for each(_loc5_ in _loc9_)
         {
            _loc6_ = _loc11_.seal_general_fixed;
            _loc13_ = int(_loc6_[_loc5_]);
            _loc12_ = int(_loc7_.layerStarMax[_loc5_ - 1]);
            _loc4_ = XmlManager.getXml("cachet").cachet.(@id == _loc13_);
            _loc3_ = int(_loc4_.@star_lv);
            if(_loc3_ >= _loc12_)
            {
               _loc10_ = this._propertyMcArr[_loc5_ - 1];
               if(_loc6_ && _loc6_.hasOwnProperty(_loc5_))
               {
                  if(_loc10_.replaceBtn.visible)
                  {
                     _loc8_ = true;
                     break;
                  }
               }
            }
         }
         return _loc8_;
      }
      
      public function updateLayerBySmith(param1:TreasureItem, param2:Array) : void
      {
         var _loc4_:int = 0;
         var _loc5_:TreasurePropertyMC = null;
         if(param1 == null)
         {
            return;
         }
         if(param2 == null)
         {
            return;
         }
         this._allReplaceBtn.visible = true;
         var _loc3_:Object = param1.seal_general_fixed;
         for each(_loc4_ in param2)
         {
            _loc5_ = this._propertyMcArr[_loc4_ - 1];
            if(_loc3_ && _loc3_.hasOwnProperty(_loc4_))
            {
               if(_loc5_.replaceBtn)
               {
                  _loc5_.replaceBtn.visible = true;
               }
               this.updateLayerProperty(param1,_loc4_,false);
            }
         }
      }
      
      public function updateLayerByReplace(param1:TreasureItem, param2:Array, param3:int) : void
      {
         var _loc12_:int = 0;
         var _loc4_:TreasurePropertyMC = null;
         var _loc7_:Boolean = false;
         var _loc11_:Object = null;
         var _loc10_:Object = null;
         var _loc9_:Template_Treasure = null;
         var _loc6_:int = 0;
         var _loc8_:TreasurePropertyMC = null;
         if(param1 == null)
         {
            return;
         }
         if(this._allReplaceBtn)
         {
            this._allReplaceBtn.visible = param1.needRepalceNum > 0 ? true : false;
         }
         var _loc5_:int = 0;
         for each(_loc12_ in param2)
         {
            _loc5_ = _loc12_ > _loc5_ ? _loc12_ : _loc5_;
            _loc4_ = this._propertyMcArr[_loc12_ - 1];
            if(_loc4_.replaceBtn)
            {
               _loc4_.replaceBtn.visible = false;
            }
            else
            {
               this.updateLayerProperty(param1,_loc12_);
            }
         }
         if(_loc5_ < param1.openSealNum)
         {
            _loc7_ = true;
            _loc11_ = param1.seal;
            _loc10_ = param1.seal_general_fixed;
            if(param3 == 1)
            {
               if(!_loc11_.hasOwnProperty(_loc5_ + 1))
               {
                  _loc7_ = false;
               }
            }
            else if(param3 == 2)
            {
               if(!_loc11_.hasOwnProperty(_loc5_ + 1) && !_loc10_.hasOwnProperty(_loc5_ + 1))
               {
                  _loc7_ = false;
               }
            }
            if(!_loc7_)
            {
               _loc9_ = param1.template as Template_Treasure;
               _loc6_ = int(_loc9_.layerStarMax[_loc5_]);
               _loc8_ = this._propertyMcArr[_loc5_];
               _loc8_.showProperty(1,"",0,_loc6_,true);
            }
         }
      }
      
      public function clearPropertyInfo() : void
      {
         this._smithTreasureSlot.clearInfo();
         this._treasureName.text = "";
         if(this._propertyLabel)
         {
            this._propertyLabel.text = "";
         }
         if(this._allSelectBox)
         {
            this._allSelectBox.isCheck = false;
         }
         if(this._allReplaceBtn)
         {
            this._allReplaceBtn.visible = false;
         }
         this.removeAllPropertyMc();
      }
      
      public function get selectLayerArr() : Array
      {
         var _loc2_:TreasurePropertyMC = null;
         this._selectLayerArr.length = 0;
         var _loc1_:int = 0;
         while(_loc1_ < 12)
         {
            _loc2_ = this._propertyMcArr[_loc1_];
            if(_loc2_.propertyCheckBox.isCheck)
            {
               this._selectLayerArr.push(_loc2_.propertyLayer);
            }
            _loc1_++;
         }
         return this._selectLayerArr;
      }
      
      public function resetLayerSelect() : void
      {
         var _loc1_:TreasurePropertyMC = null;
         if(this._allSelectBox)
         {
            this._allSelectBox.isCheck = false;
         }
         for each(_loc1_ in this._propertyMcArr)
         {
            if(_loc1_.propertyCheckBox)
            {
               _loc1_.propertyCheckBox.isCheck = false;
               _loc1_.isCanSelect = false;
            }
         }
      }
   }
}

