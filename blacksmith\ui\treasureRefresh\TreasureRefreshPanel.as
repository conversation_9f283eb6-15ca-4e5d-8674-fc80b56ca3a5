package blacksmith.ui.treasureRefresh
{
   import blacksmith.events.treasure.TreasureReplaceEvent;
   import blacksmith.events.treasure.TreasureSmithEvent;
   import blacksmith.manage.TreasureSmithManager;
   import blacksmith.mc.ConsumeStoneMC;
   import blacksmith.ui.auto.AutoHistoryPopup;
   import blacksmith.ui.auto.BuyAutoPopup;
   import blacksmith.ui.auto.TreasureAutoSettingDlg;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.items.framework.items.TreasureItem;
   import game.items.framework.templates.Template_Treasure;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.modules.PackPassWordLock.view.PackPassWordLockDataManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.tab.TabPane;
   import mmo.ui.event.TabEvent;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class TreasureRefreshPanel extends Sprite
   {
      private static const WIDTH:int = 632;
      
      private static const HEIGHT:int = 325;
      
      private var _refreshTypeTab:TabPane;
      
      private var _originalPropertyComponet:TreasurePropertyComponet;
      
      private var _refreshPropertyComponet:TreasurePropertyComponet;
      
      private var _energySprite:Sprite;
      
      private var _energyBg:UISkin;
      
      private var _energyImg:UISkin;
      
      private var _energyLabel:Label;
      
      private var _energyValue:Label;
      
      private var _refreshCostContainer:Sprite;
      
      private var _costTitle:Label;
      
      private var _costGoldImg:UISkin;
      
      private var _costEnergyImg:UISkin;
      
      private var _costBellyImg:UISkin;
      
      private var _honorRemainImg:UISkin;
      
      private var _honorRemainLabel:Label;
      
      private var _costValueLabel1:Label;
      
      private var _costValueLabel2:Label;
      
      private var _doingAutoRefreshLabel:Label;
      
      private var _refreshCostContainer2:Sprite;
      
      private var _itemRefreshBG:Sprite;
      
      public var refreshBtn:Button;
      
      public var historyBtn:Button;
      
      private var _consumeMcArr:Array;
      
      private var _viewPropertyBtn:Button;
      
      private var _autoRefreshBtn:Button;
      
      private var _buyAutoBtn:Button;
      
      private var _selectItem:TreasureItem;
      
      private var _autoSettingDlg:TreasureAutoSettingDlg;
      
      private var _autoCost:Array;
      
      private var _autoPackages:Array;
      
      private var _upPage:Button;
      
      private var _downPage:Button;
      
      private var _page:int = 1;
      
      public function TreasureRefreshPanel()
      {
         var _loc10_:UISkin = null;
         var _loc9_:UISkin = null;
         var _loc7_:ConsumeStoneMC = null;
         this._consumeMcArr = [];
         super();
         var _loc8_:UISkin = UIManager.getUISkin("pane_bg");
         _loc8_.setSize(632,325);
         this.addChild(_loc8_);
         var _loc1_:Label = new Label(Globalization.getString("treasureSmith.29"),TextFormatLib.format_0xebce82_12px);
         _loc1_.width = 632;
         _loc1_.autoSize = "center";
         _loc1_.y = 3;
         this.addChild(_loc1_);
         var _loc3_:Array = [Globalization.getString("treasureSmith.30"),Globalization.getString("treasureSmith.31"),Globalization.getString("treasureSmith.32")];
         this._refreshTypeTab = new TabPane(_loc3_,0,72,377,100,UIManager.getMultiUISkin("button2"),0);
         this._refreshTypeTab.Gap = 70;
         this._refreshTypeTab.x = 632 - this._refreshTypeTab.width >> 1;
         this._refreshTypeTab.y = 55;
         this.addChild(this._refreshTypeTab);
         this._refreshTypeTab.selectedIndex = 1;
         this._refreshTypeTab.addEventListener(TabEvent.Tab_IndexChange,this.onTabChange);
         this._originalPropertyComponet = new TreasurePropertyComponet();
         this._originalPropertyComponet.x = 10;
         this._originalPropertyComponet.y = 60;
         this.addChild(this._originalPropertyComponet);
         this._originalPropertyComponet.changePropertyFun = this.selectPropertyHandler;
         var _loc6_:UISkin = UIManager.getUISkin("arrow");
         _loc6_.x = 305;
         _loc6_.y = 165;
         this.addChild(_loc6_);
         this._refreshPropertyComponet = new TreasurePropertyComponet(false);
         this._refreshPropertyComponet.x = 330;
         this._refreshPropertyComponet.y = 60;
         this.addChild(this._refreshPropertyComponet);
         this._refreshPropertyComponet.replaceFun = this.replaceHandler;
         var _loc5_:Icon = new Icon();
         _loc5_.x = 4;
         _loc5_.y = 325 + 2;
         _loc5_.setData(UrlManager.getTreasureSmithIMG("stoveSmith"));
         this.addChild(_loc5_);
         this._energySprite = new Sprite();
         this._energySprite.y = 325 + 10;
         this.addChild(this._energySprite);
         this._energySprite.visible = false;
         this._energyBg = UIManager.getUISkin("level_bg");
         this._energyBg.setSize(140,20);
         this._energySprite.addChild(this._energyBg);
         this._energySprite.x = 632 - this._energyBg.width >> 1;
         this._energyImg = UIManager.getUISkin("energy");
         this._energyImg.x = 3;
         this._energyImg.y = 1;
         this._energySprite.addChild(this._energyImg);
         this._energyLabel = new Label(Globalization.getString("treasureSmith.33"),TextFormatLib.format_0xffb932_12px);
         this._energyLabel.x = 18;
         this._energySprite.addChild(this._energyLabel);
         this._energyValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center);
         this._energyValue.x = 60;
         this._energyValue.width = 80;
         this._energyValue.autoSize = "center";
         this._energySprite.addChild(this._energyValue);
         this._refreshCostContainer = new Sprite();
         this._refreshCostContainer.y = 325 + 35;
         this.addChild(this._refreshCostContainer);
         var _loc4_:int = 0;
         while(_loc4_ < 3)
         {
            _loc10_ = UIManager.getUISkin("text_bg_2");
            _loc10_.setSize(200,24);
            _loc10_.y = _loc4_ * 27;
            this._refreshCostContainer.addChild(_loc10_);
            _loc4_++;
         }
         this._refreshCostContainer.x = 216;
         this._costTitle = new Label(Globalization.getString("treasureSmith.36"),TextFormatLib.format_0xffb932_12px);
         this._costTitle.width = 200;
         this._costTitle.autoSize = "center";
         this._costTitle.y = 2;
         this._refreshCostContainer.addChild(this._costTitle);
         this._costGoldImg = UIManager.getUISkin("gold");
         this._costGoldImg.x = 30;
         this._costGoldImg.y = 32;
         this._refreshCostContainer.addChild(this._costGoldImg);
         this._costEnergyImg = UIManager.getUISkin("energy");
         this._costEnergyImg.x = 30;
         this._costEnergyImg.y = 32;
         this._refreshCostContainer.addChild(this._costEnergyImg);
         this._costEnergyImg.visible = false;
         this._costBellyImg = UIManager.getUISkin("belly");
         this._costBellyImg.x = 30;
         this._costBellyImg.y = 58;
         this._refreshCostContainer.addChild(this._costBellyImg);
         this._costValueLabel1 = new Label("",TextFormatLib.format_0xffb932_12px);
         this._costValueLabel1.htmlText = StringUtil.substitute(Globalization.getString("treasureSmith.37"),0);
         this._costValueLabel1.x = 64;
         this._costValueLabel1.y = 30;
         this._refreshCostContainer.addChild(this._costValueLabel1);
         this._costValueLabel2 = new Label("",TextFormatLib.format_0xffb932_12px);
         this._costValueLabel2.htmlText = StringUtil.substitute(Globalization.getString("treasureSmith.38"),0);
         this._costValueLabel2.x = 64;
         this._costValueLabel2.y = 56;
         this._refreshCostContainer.addChild(this._costValueLabel2);
         this._doingAutoRefreshLabel = new Label(Globalization.getString("treasureSmith.69"),TextFormatLib.format_0xFF0000_14px);
         this._doingAutoRefreshLabel.x = -60;
         this._doingAutoRefreshLabel.y = -260;
         this._doingAutoRefreshLabel.visible = false;
         this._refreshCostContainer.addChild(this._doingAutoRefreshLabel);
         this._refreshCostContainer2 = new Sprite();
         this._refreshCostContainer2.y = 325 + 10;
         this.addChild(this._refreshCostContainer2);
         this._refreshCostContainer2.visible = false;
         this._itemRefreshBG = new Sprite();
         this._refreshCostContainer2.addChild(this._itemRefreshBG);
         this._itemRefreshBG.visible = false;
         _loc4_ = 0;
         while(_loc4_ < 5)
         {
            _loc9_ = UIManager.getUISkin("text_bg_2");
            _loc9_.setSize(270,22);
            _loc9_.y = _loc4_ * 24;
            this._refreshCostContainer2.addChild(_loc9_);
            if(_loc4_ > 0)
            {
               this._itemRefreshBG.addChild(_loc9_);
            }
            _loc4_++;
         }
         this._refreshCostContainer2.x = 181;
         var _loc2_:Label = new Label(Globalization.getString("treasureSmith.34"),TextFormatLib.format_0xffb932_12px);
         _loc2_.width = 270;
         _loc2_.autoSize = "center";
         this._refreshCostContainer2.addChild(_loc2_);
         _loc4_ = 0;
         while(_loc4_ < 12)
         {
            _loc7_ = new ConsumeStoneMC();
            _loc7_.x = 10 + _loc4_ % 2 * 132;
            _loc7_.y = 25 + int(_loc4_ / 2) * 24;
            this._refreshCostContainer2.addChild(_loc7_);
            this._consumeMcArr.push(_loc7_);
            _loc4_++;
         }
         this.refreshBtn = new Button(Globalization.getString("treasureSmith.35"),null,70);
         this.refreshBtn.x = 275;
         this.addChild(this.refreshBtn);
         this.refreshBtn.addEventListener("click",this.onRefreshHandler);
         this._viewPropertyBtn = new Button(Globalization.getString("treasureSmith.58"),null,80);
         this._viewPropertyBtn.x = this.refreshBtn.x + this.refreshBtn.width + 10;
         this.addChild(this._viewPropertyBtn);
         this._viewPropertyBtn.addEventListener("click",this.viewPropertyHandler);
         this._autoRefreshBtn = new Button(Globalization.getString("treasureSmith.66"),null,80);
         this._autoRefreshBtn.x = this.refreshBtn.x - this._autoRefreshBtn.width - 10;
         this._autoRefreshBtn.y = 446;
         this.addChild(this._autoRefreshBtn);
         this._autoRefreshBtn.addEventListener("click",this.autoRefreshHandler);
         this._buyAutoBtn = new Button(Globalization.getString("treasureSmith.76"),null,80);
         this._buyAutoBtn.x = this._autoRefreshBtn.x - this._autoRefreshBtn.width - 10;
         this._buyAutoBtn.y = 446;
         this.addChild(this._buyAutoBtn);
         this._buyAutoBtn.addEventListener("click",this.buyAutoHandler);
         this._honorRemainImg = UIManager.getUISkin("text_bg_2");
         this._honorRemainImg.setSize(100,24);
         this._honorRemainImg.x = this._buyAutoBtn.x;
         this._honorRemainImg.y = this._buyAutoBtn.y - 30;
         this.addChild(this._honorRemainImg);
         this._honorRemainLabel = new Label("",TextFormatLib.format_0xffb932_12px);
         this._honorRemainLabel.htmlText = "";
         this._honorRemainLabel.x = this._honorRemainImg.x + 10;
         this._honorRemainLabel.y = this._honorRemainImg.y;
         this.addChild(this._honorRemainLabel);
         this._autoSettingDlg = new TreasureAutoSettingDlg();
         this._autoSettingDlg.refreshFunc = this.onAutoRefreshCallback;
         this._autoSettingDlg.visible = false;
         this._autoSettingDlg.messageLabel = this._doingAutoRefreshLabel;
         this.addChild(this._autoSettingDlg);
         this.historyBtn = new Button(Globalization.getString("treasureSmith.71"),null,70);
         this.historyBtn.x = this._viewPropertyBtn.x + this._viewPropertyBtn.width + 10;
         this.historyBtn.y = 446;
         this.addChild(this.historyBtn);
         this.historyBtn.addEventListener("click",this.onHistoryClick);
         this._upPage = new Button("下一页",null,60,UIManager.getMultiUISkin("btn_topMenu"));
         this._upPage.x = 8;
         this._upPage.y = 32;
         this.addChild(this._upPage);
         this._upPage.addEventListener("click",this.onUpPage);
         this._downPage = new Button("上一页",null,60,UIManager.getMultiUISkin("btn_topMenu"));
         this._downPage.x = 70;
         this._downPage.y = 32;
         this.addChild(this._downPage);
         this._downPage.addEventListener("click",this.onDownPage);
      }
      
      private function onUpPage(param1:MouseEvent) : void
      {
         var _loc4_:Template_Treasure = _selectItem.template as Template_Treasure;
         var _loc2_:int = _loc4_.max_sealLayer;
         var _loc3_:int = Math.ceil(_loc2_ / 8);
         this._page++;
         if(this._page > _loc3_)
         {
            this._page = _loc3_;
         }
         _originalPropertyComponet.clearData();
         _refreshPropertyComponet.clearData();
         _originalPropertyComponet.setPropertyData(_selectItem,1,_page);
         _refreshPropertyComponet.setPropertyData(_selectItem,1,_page);
         this.updateSmithCost(_selectItem,this._refreshTypeTab.selectedIndex);
      }
      
      private function onDownPage(param1:MouseEvent) : void
      {
         this._page--;
         if(this._page < 1)
         {
            this._page = 1;
         }
         var _loc2_:int = 0;
         var _loc3_:ConsumeStoneMC = null;
         this._consumeMcArr = [];
         _originalPropertyComponet.clearData();
         _originalPropertyComponet.setPropertyData(_selectItem,1,_page);
         _refreshPropertyComponet.setPropertyData(_selectItem,1,_page);
         this.updateSmithCost(_selectItem,this._refreshTypeTab.selectedIndex);
      }
      
      private function viewPropertyHandler(param1:MouseEvent) : void
      {
         if(this._selectItem == null)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.59"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         var _loc2_:TreasureViewPropertyWin = new TreasureViewPropertyWin();
         _loc2_.setSmithProperty(this._selectItem);
         PopUpCenter.addPopUp("blacksmith.ui.treasureRefresh.TreasureViewPropertyPanel",_loc2_,true,true);
      }
      
      private function onTabChange(param1:Event) : void
      {
         this.updateSmithCost(this._selectItem,this._refreshTypeTab.selectedIndex);
      }
      
      private function selectPropertyHandler() : void
      {
         this.updateSmithCost(this._selectItem,this._refreshTypeTab.selectedIndex);
      }
      
      private function replaceHandler(param1:int) : void
      {
         if(this._selectItem == null)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.21"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         this.dispatchEvent(new TreasureReplaceEvent("TreasureSmithReplace",this._selectItem.item_id,param1));
      }
      
      private function onRefreshHandler(param1:MouseEvent) : void
      {
         if(this._selectItem == null)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.14"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(PackPassWordLockDataManager.getInstance().popUpLockWindow())
         {
            return;
         }
         if(this.selectLayerArr != null && this.selectLayerArr.length != 0)
         {
            if(this._refreshPropertyComponet.isProterty(this._selectItem,this.selectLayerArr))
            {
               PopUpCenter.confirmWin(Globalization.getString("treasureSmith.62"),this.okFun,null,0,true);
               return;
            }
         }
         this.dispatchEvent(new TreasureSmithEvent("TreasureSmithEvent",this._selectItem.item_id,this._refreshTypeTab.selectedIndex,this.selectLayerArr));
      }
      
      public function okFun() : void
      {
         this.dispatchEvent(new TreasureSmithEvent("TreasureSmithEvent",this._selectItem.item_id,this._refreshTypeTab.selectedIndex,this.selectLayerArr));
      }
      
      private function updateSmithCost(param1:TreasureItem, param2:int) : void
      {
         var _loc7_:ConsumeStoneMC = null;
         var _loc8_:Array = null;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         if(param2 == 0)
         {
            this._energySprite.visible = false;
            this._refreshCostContainer.visible = true;
            this._refreshCostContainer2.visible = false;
            this._costGoldImg.visible = true;
            this._costEnergyImg.visible = false;
            this.refreshBtn.y = 446;
            this._viewPropertyBtn.y = 446;
            this._costTitle.text = Globalization.getString("treasureSmith.36");
            if(param1 != null)
            {
               _loc6_ = int(TreasureSmithManager.getSmithCostByGold(param1,this.selectLayerArr,0)[0]);
               _loc5_ = int(TreasureSmithManager.getSmithCostByGold(param1,this.selectLayerArr,0)[1]);
            }
            this._costValueLabel1.htmlText = StringUtil.substitute(Globalization.getString("treasureSmith.37"),_loc6_);
            this._costValueLabel2.htmlText = StringUtil.substitute(Globalization.getString("treasureSmith.38"),_loc5_);
         }
         else if(param2 == 1)
         {
            this._energySprite.visible = true;
            this._refreshCostContainer.visible = true;
            this._refreshCostContainer2.visible = false;
            this._costGoldImg.visible = false;
            this._costEnergyImg.visible = true;
            this.refreshBtn.y = 446;
            this._viewPropertyBtn.y = 446;
            this._costTitle.text = Globalization.getString("treasureSmith.39");
            if(param1 != null)
            {
               _loc6_ = int(TreasureSmithManager.getSmithCostByGold(param1,this.selectLayerArr,1)[0]);
               _loc5_ = int(TreasureSmithManager.getSmithCostByGold(param1,this.selectLayerArr,1)[1]);
            }
            this._costValueLabel1.htmlText = StringUtil.substitute(Globalization.getString("treasureSmith.40"),_loc6_);
            this._costValueLabel2.htmlText = StringUtil.substitute(Globalization.getString("treasureSmith.38"),_loc5_);
         }
         else if(param2 == 2)
         {
            this._energySprite.visible = false;
            this._refreshCostContainer.visible = false;
            this._refreshCostContainer2.visible = true;
            this.refreshBtn.y = 446;
            this._viewPropertyBtn.y = 446;
            if(param1 != null)
            {
               this._itemRefreshBG.visible = true;
               _loc8_ = TreasureSmithManager.getSmithCostByItem(param1,this.selectLayerArr);
               _loc3_ = 0;
               while(_loc3_ < 12)
               {
                  _loc4_ = 0;
                  _loc7_ = this._consumeMcArr[_loc3_];
                  if(_loc3_ < this.selectLayerArr.length)
                  {
                     _loc7_.visible = true;
                     _loc4_ = MainData.getInstance().bagData.userBag.numItems(_loc8_[_loc3_].itemId);
                     _loc7_.setConsumeData(_loc8_[_loc3_].itemId,_loc4_,_loc8_[_loc3_].num);
                  }
                  else
                  {
                     _loc7_.visible = false;
                  }
                  _loc3_++;
               }
            }
            else
            {
               this._itemRefreshBG.visible = false;
            }
         }
      }
      
      public function setRefreshData(param1:TreasureItem) : void
      {
         if(param1 == null)
         {
            return;
         }
         if(this._autoSettingDlg.isRunning)
         {
            this._autoSettingDlg.stop(true);
         }
         this._selectItem = param1;
         this._originalPropertyComponet.setPropertyData(param1,1);
         this._refreshPropertyComponet.setPropertyData(param1,2);
         this.updateSmithCost(param1,this._refreshTypeTab.selectedIndex);
      }
      
      public function updateRefreshData(param1:Array) : void
      {
         this._autoSettingDlg.checkStopCondition(param1);
         this._refreshPropertyComponet.updateLayerBySmith(this._selectItem,param1);
         if(this._refreshTypeTab.selectedIndex == 2)
         {
            this.updateSmithCost(this._selectItem,this._refreshTypeTab.selectedIndex);
         }
      }
      
      public function updateReplaceData(param1:Array) : void
      {
         this._originalPropertyComponet.updateLayerByReplace(this._selectItem,param1,1);
         this._refreshPropertyComponet.updateLayerByReplace(this._selectItem,param1,2);
      }
      
      public function clearRefreshData() : void
      {
         var _loc2_:ConsumeStoneMC = null;
         this._selectItem = null;
         this._originalPropertyComponet.clearPropertyInfo();
         this._refreshPropertyComponet.clearPropertyInfo();
         this.updateSmithCost(this._selectItem,this._refreshTypeTab.selectedIndex);
         var _loc1_:int = 0;
         while(_loc1_ < 12)
         {
            _loc2_ = this._consumeMcArr[_loc1_];
            _loc2_.visible = false;
            _loc1_++;
         }
      }
      
      public function resetLayerStatus() : void
      {
         this._originalPropertyComponet.resetLayerSelect();
      }
      
      public function updateEnergyNum(param1:int) : void
      {
         this._energyValue.text = String(param1);
      }
      
      public function updateHonorNum(param1:int) : void
      {
         this._honorRemainLabel.htmlText = StringUtil.substitute(Globalization.getString("treasureSmith.75"),param1);
      }
      
      public function get selectLayerArr() : Array
      {
         return this._originalPropertyComponet.selectLayerArr;
      }
      
      private function autoRefreshHandler(param1:MouseEvent) : void
      {
         this._autoSettingDlg.show(this._selectItem);
         this._autoSettingDlg.x = 30;
         this._autoSettingDlg.y = 30;
      }
      
      private function onAutoRefreshCallback() : void
      {
         if(this._selectItem == null)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.14"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            return;
         }
         if(PackPassWordLockDataManager.getInstance().popUpLockWindow())
         {
            return;
         }
         var _loc1_:Array = this._autoSettingDlg.selectedLayers;
         if(_loc1_.length === 0)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("treasureSmith.80"),
               "textFormat":TextFormatLib.format_0xFF0000_14px
            });
            this.stopAutoRefresh();
            return;
         }
         this.dispatchEvent(new TreasureSmithEvent("AutoTreasureSmithEvent",this._selectItem.item_id,this._refreshTypeTab.selectedIndex,_loc1_));
      }
      
      private function onHistoryClick(param1:MouseEvent) : void
      {
         this.dispatchEvent(new Event("AutoHistoryTreasureSmithEvent"));
      }
      
      public function showAutoLog(param1:Array) : void
      {
         var _loc2_:AutoHistoryPopup = new AutoHistoryPopup(param1);
         PopUpCenter.addPopUp("AutoHistoryPopup",_loc2_,true,true);
      }
      
      private function buyAutoHandler(param1:MouseEvent) : void
      {
         var _loc2_:BuyAutoPopup = new BuyAutoPopup(this._autoCost,this._autoPackages,"CS_TREASURE_SMITH_BUY_AUTO");
         PopUpCenter.addPopUp("BuyAutoPopup",_loc2_,true,true);
      }
      
      public function updateAutoBuyConfig(param1:Array, param2:Array) : void
      {
         this._autoCost = param1;
         this._autoPackages = param2;
      }
      
      public function stopAutoRefresh(param1:Boolean = false) : void
      {
         this._autoSettingDlg.stop(param1);
      }
   }
}

