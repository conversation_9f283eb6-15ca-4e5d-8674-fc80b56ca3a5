package blacksmith.ui.treasureRefresh
{
   import blacksmith.mc.ViewPropertyMc;
   import flash.display.Sprite;
   import game.items.framework.items.TreasureItem;
   import game.items.framework.templates.Template_Treasure;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotItem;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class TreasureViewPropertyWin extends PopUpWindow
   {
      public static const NAME:String = "blacksmith.ui.treasureRefresh.TreasureViewPropertyPanel";
      
      private static const WIDTH:int = 650;
      
      private static const HEIGHT:int = 456;
      
      private var _treasureSlot:Slot;
      
      private var _treasureName:Label;
      
      private var _properyScroll:ScrollPane;
      
      private var _propertyLayer:Sprite;
      
      public function TreasureViewPropertyWin()
      {
         super(650,456);
         this.isLive = false;
         this.title = Globalization.getString("treasureSmith.61");
         this.initUI();
      }
      
      public function initUI() : void
      {
         var _loc2_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc2_.setSize(628,54);
         _loc2_.x = 5;
         pane.addChild(_loc2_);
         this._treasureSlot = new Slot(UIManager.getUISkin("treasure1Slot"));
         this._treasureSlot.x = 235;
         this._treasureSlot.y = 3;
         pane.addChild(this._treasureSlot);
         this._treasureName = new Label("",TextFormatLib.format_0xffb932_12px);
         this._treasureName.x = 296;
         this._treasureName.y = 18;
         pane.addChild(this._treasureName);
         var _loc1_:UISkin = UIManager.getUISkin("group_bg");
         _loc1_.x = 5;
         _loc1_.y = 60;
         _loc1_.setSize(630,340);
         pane.addChild(_loc1_);
         this._properyScroll = new ScrollPane(610,320);
         this._properyScroll.x = 14;
         this._properyScroll.y = 65;
         pane.addChild(this._properyScroll);
         this._propertyLayer = new Sprite();
         this._properyScroll.addToPane(this._propertyLayer);
      }
      
      public function setSmithProperty(param1:TreasureItem) : void
      {
         var _loc9_:ViewPropertyMc = null;
         var _loc7_:Array = null;
         if(param1 == null)
         {
            return;
         }
         var _loc8_:Template_Treasure = param1.template as Template_Treasure;
         var _loc2_:SlotItem = new SlotItem();
         _loc2_.item = param1;
         this._treasureSlot.setItem(_loc2_);
         this._treasureName.htmlText = StringUtil.substitute("{0} <font color=\'#00ff00\'>+{1}</font>",param1.name,param1.reinforce_level);
         var _loc3_:int = _loc8_.max_sealLayer;
         var _loc6_:Array = _loc8_.viewProperties;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         while(_loc4_ < _loc3_)
         {
            _loc9_ = new ViewPropertyMc();
            _loc7_ = _loc6_[_loc4_].split("|");
            _loc9_.setPropetyData(_loc4_ + 1,_loc7_);
            _loc9_.y = _loc5_;
            _loc5_ = _loc9_.y + _loc9_.height + 4;
            this._propertyLayer.addChild(_loc9_);
            _loc4_++;
         }
      }
      
      override public function get posHeight() : Number
      {
         return 456;
      }
   }
}

