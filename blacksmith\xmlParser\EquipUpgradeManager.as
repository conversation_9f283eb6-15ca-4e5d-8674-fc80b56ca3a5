package blacksmith.xmlParser
{
   import game.manager.XmlManager;
   
   public class EquipUpgradeManager
   {
      private static var _instance:EquipUpgradeManager;
      
      public function EquipUpgradeManager()
      {
         super();
      }
      
      public static function get instance() : EquipUpgradeManager
      {
         _instance ||= new EquipUpgradeManager();
         return _instance;
      }
      
      public function getUpNeedBelly(param1:int) : int
      {
         var _loc3_:* = param1;
         var _loc2_:XML = XmlManager.getXml("equipment_up");
         return int(_loc2_.children().(@id == _loc3_).@costBelly);
      }
      
      public function getUpNeedExperience(param1:int) : int
      {
         var _loc3_:* = param1;
         var _loc2_:XML = XmlManager.getXml("equipment_up");
         return int(_loc2_.children().(@id == _loc3_).@costExperience);
      }
      
      public function getUpNeedSoul(param1:int) : int
      {
         var _loc3_:* = param1;
         var _loc2_:XML = XmlManager.getXml("equipment_up");
         return int(_loc2_.children().(@id == _loc3_).@costSoul);
      }
      
      public function getAimItem(param1:int) : int
      {
         var _loc3_:* = param1;
         var _loc2_:XML = XmlManager.getXml("equipment_up");
         return int(_loc2_.children().(@id == _loc3_).@aimItem);
      }
      
      public function getNeedItems(param1:int) : Array
      {
         var _loc3_:XML = null;
         var _loc2_:Array = null;
         var _loc4_:* = param1;
         _loc3_ = XmlManager.getXml("equipment_up");
         return String(_loc3_.children().(@id == _loc4_).@needItems).split(",");
      }
      
      public function getTreeGps() : Array
      {
         var _loc5_:String = null;
         var _loc4_:Array = [];
         var _loc2_:XML = XmlManager.getXml("equipment_up");
         var _loc3_:int = int(_loc2_.children().length());
         var _loc1_:uint = 0;
         while(_loc1_ < _loc3_)
         {
            _loc5_ = _loc2_.children()[_loc1_].@type;
            if(_loc4_.indexOf(_loc5_) == -1)
            {
               _loc4_.push(_loc5_);
            }
            _loc1_++;
         }
         return _loc4_;
      }
      
      public function getTreeGoldGps() : Array
      {
         var _loc5_:String = null;
         var _loc4_:Array = [];
         var _loc2_:XML = XmlManager.getXml("equipment_up");
         var _loc3_:int = int(_loc2_.children().length());
         var _loc1_:uint = 0;
         while(_loc1_ < _loc3_)
         {
            _loc5_ = _loc2_.children()[_loc1_].@type;
            if(_loc5_.split("|")[0] >= 120)
            {
               if(_loc4_.indexOf(_loc5_) == -1)
               {
                  _loc4_.push(_loc5_);
               }
            }
            _loc1_++;
         }
         return _loc4_;
      }
   }
}

