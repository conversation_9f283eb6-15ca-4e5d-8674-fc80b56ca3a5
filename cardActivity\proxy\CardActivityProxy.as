package cardActivity.proxy
{
   import game.data.MainData;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   
   public class CardActivityProxy extends Proxy
   {
      public static const NAME:String = "CardActivityProxy";
      
      public function CardActivityProxy(param1:String = null, param2:Object = null)
      {
         super("CardActivityProxy",param2);
      }
      
      public function addCardHand(param1:int, param2:int, param3:int) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.festival.buyCard",this.onAddHandler);
         BabelTimeSocket.getInstance().sendMessage("festival.buyCard",new SocketCallback("re.festival.buyCard",[param1,param2,param3]),param1);
      }
      
      private function onAddHandler(param1:SocketDataEvent) : void
      {
         var _loc2_:Object = null;
         var _loc3_:* = undefined;
         var _loc4_:Item = null;
         BabelTimeSocket.getInstance().removeCallback("re.festival.buyCard",this.onAddHandler);
         var _loc7_:int = int((param1.callbackParames as Array)[0]);
         var _loc5_:int = int((param1.callbackParames as Array)[1]);
         var _loc6_:int = int((param1.callbackParames as Array)[2]);
         if(MainData.getInstance().bagData.userBag.getLastGridsNum() < 1)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("cardActivityAct.19"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
            return;
         }
         if(param1.data != "err")
         {
            _loc2_ = param1.data;
            for(_loc3_ in _loc2_)
            {
               _loc4_ = ItemFactory.creatItem(_loc2_[_loc3_]);
               MainData.getInstance().bagData.setGridItem(_loc3_,_loc4_);
            }
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - _loc6_;
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("cardActivityAct.13"),
               "textFormat":TextFormatLib.format_verdana_0x00FF00_14px
            });
            sendNotification("CS_ADD_CARD_SUC",_loc5_);
            this.getAddNum();
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("cardActivityAct.14"),
               "textFormat":TextFormatLib.format_0xFFFFFF_12px
            });
         }
      }
      
      public function exchangeCardAllHandler(param1:int, param2:String, param3:int) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.festival.sellCardsAll",this.onExchangeAllHandler);
         BabelTimeSocket.getInstance().sendMessage("festival.sellCardsAll",new SocketCallback("re.festival.sellCardsAll",[param1,param2,param3]),param1);
      }
      
      private function onExchangeAllHandler(param1:SocketDataEvent) : void
      {
         var _loc5_:Object = null;
         var _loc2_:* = undefined;
         var _loc3_:Item = null;
         BabelTimeSocket.getInstance().removeCallback("re.festival.sellCardsAll",this.onExchangeAllHandler);
         var _loc6_:int = int((param1.callbackParames as Array)[0]);
         var _loc4_:String = (param1.callbackParames as Array)[1];
         if(MainData.getInstance().bagData.userBag.getLastGridsNum() < 1)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("cardActivityAct.20"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
            return;
         }
         if(param1.data != "err")
         {
            param1.data.belly && (MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num + int(param1.data.belly));
            param1.data.experience && (MainData.getInstance().userData.experience_num = MainData.getInstance().userData.experience_num + int(param1.data.experience));
            param1.data.gold && (MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num + int(param1.data.gold));
            param1.data.execution && (MainData.getInstance().userData.cur_execution = MainData.getInstance().userData.cur_execution + int(param1.data.execution));
            param1.data.prestige && (MainData.getInstance().userData.prestige_num = MainData.getInstance().userData.prestige_num + int(param1.data.prestige));
            _loc5_ = param1.data.grid;
            for(_loc2_ in _loc5_)
            {
               _loc3_ = ItemFactory.creatItem(_loc5_[_loc2_]);
               MainData.getInstance().bagData.setGridItem(_loc2_,_loc3_);
            }
            sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("cardActivityAct.15"),_loc4_),
               "textFormat":TextFormatLib.format_verdana_0x00FF00_14px
            });
            sendNotification("CS_ADD_CARD_SUC",_loc6_);
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("cardActivityAct.8"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
         }
      }
      
      public function exchangeCardHandler(param1:int, param2:String) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.festival.sellCards",this.onExchangeHandler);
         BabelTimeSocket.getInstance().sendMessage("festival.sellCards",new SocketCallback("re.festival.sellCards",[param1,param2]),param1);
      }
      
      private function onExchangeHandler(param1:SocketDataEvent) : void
      {
         var _loc5_:Object = null;
         var _loc2_:* = undefined;
         var _loc3_:Item = null;
         BabelTimeSocket.getInstance().removeCallback("re.festival.sellCards",this.onExchangeHandler);
         var _loc6_:int = int((param1.callbackParames as Array)[0]);
         var _loc4_:String = (param1.callbackParames as Array)[1];
         if(MainData.getInstance().bagData.userBag.getLastGridsNum() < 1)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("cardActivityAct.20"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
            return;
         }
         if(param1.data != "err")
         {
            param1.data.belly && (MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num + int(param1.data.belly));
            param1.data.experience && (MainData.getInstance().userData.experience_num = MainData.getInstance().userData.experience_num + int(param1.data.experience));
            param1.data.gold && (MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num + int(param1.data.gold));
            param1.data.execution && (MainData.getInstance().userData.cur_execution = MainData.getInstance().userData.cur_execution + int(param1.data.execution));
            param1.data.prestige && (MainData.getInstance().userData.prestige_num = MainData.getInstance().userData.prestige_num + int(param1.data.prestige));
            _loc5_ = param1.data.grid;
            for(_loc2_ in _loc5_)
            {
               _loc3_ = ItemFactory.creatItem(_loc5_[_loc2_]);
               MainData.getInstance().bagData.setGridItem(_loc2_,_loc3_);
            }
            sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("cardActivityAct.15"),_loc4_),
               "textFormat":TextFormatLib.format_verdana_0x00FF00_14px
            });
            sendNotification("CS_ADD_CARD_SUC",_loc6_);
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("cardActivityAct.8"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
         }
      }
      
      public function getAddNum() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.festival.getAreadyBuyInfo",this.onAddNumUpdata);
         BabelTimeSocket.getInstance().sendMessage("festival.getAreadyBuyInfo",new SocketCallback("re.festival.getAreadyBuyInfo"));
      }
      
      private function onAddNumUpdata(param1:SocketDataEvent) : void
      {
         var _loc2_:Object = null;
         BabelTimeSocket.getInstance().removeCallback("re.festival.getAreadyBuyInfo",this.onAddNumUpdata);
         if(param1.data != "err")
         {
            if(param1.data != null)
            {
               _loc2_ = param1.data;
               sendNotification("CS_ADD_NUM_UPDATA",_loc2_);
            }
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("cardActivityAct.16"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
         }
      }
   }
}

