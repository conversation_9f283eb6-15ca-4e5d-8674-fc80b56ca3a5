package card.mediators
{
   import card.view.BankWindow;
   import card.view.CardWindow;
   import card.view.GoldCompeteWindow;
   import card.view.WagerWindow;
   import card.view.ui.TujianWindow;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.data.battle.BattleModuleParams;
   import game.manager.XmlManager;
   import game.modules.activity.view.win.help.CardShopIntroWin;
   import game.modules.card.commands.CardCommand;
   import game.modules.card.manager.CardManager;
   import game.modules.card.proxys.CardProxy;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import game.net.BabelTimeSocket;
   import game.xmlParsers.activity.Activity;
   import game.xmlParsers.activity.ActivityManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   import util.openModule;
   
   public class CardMediator extends PirateMediator
   {
      public static const NAME:String = "CardMediator";
      
      private var _viewComponent:CardWindow;
      
      private var timer:Timer;
      
      private var countdown:int;
      
      private var goldCompeteWindow:GoldCompeteWindow;
      
      private var wagerWindow:WagerWindow;
      
      private var tujianWindow:TujianWindow;
      
      public function CardMediator(param1:CardWindow = null)
      {
         this._viewComponent = param1;
         super("CardMediator",this._viewComponent);
         this._viewComponent.exitBtn.addEventListener("click",this.onExitBtnClickHandler);
         this._viewComponent.savingBtn.addEventListener("click",this.onSavingBtnClickHandler);
         this._viewComponent.takeBtn.addEventListener("click",this.onTakeBtnClickHandler);
         this._viewComponent.buyBtn.addEventListener("click",this.onBuyBtnClickHandler);
         this._viewComponent.genzhuBtn.addEventListener("click",this.onGenzhuClickHandler);
         this._viewComponent.jiazhuBtn.addEventListener("click",this.onJiazhuClickHandler);
         this._viewComponent.rangpaiBtn.addEventListener("click",this.onRangpaiClickHandler);
         this._viewComponent.kaipaiBtn.addEventListener("click",this.onRangpaiClickHandler);
         this._viewComponent.quanxiaBtn.addEventListener("click",this.onQuanxiaClickHandler);
         this._viewComponent.fangqiBtn.addEventListener("click",this.onFangqiClickHandler);
         this._viewComponent.matchingBtn.addEventListener("click",this.onMatchingClickHandler);
         this._viewComponent.myPrepareBtn.addEventListener("click",this.onPerpareClickHandler);
         this._viewComponent.cancelMatchBtn.addEventListener("click",this.onCancelMatchClickHandler);
         this._viewComponent.helpBtn.addEventListener("click",this.onHelpClickHandler);
         this._viewComponent.tujian.addEventListener("click",this.onTujianClickHandler);
         this.timer = new Timer(1000);
         this.timer.addEventListener("timer",this.onTimerHandler);
         this._viewComponent.showHandler = this.showHandler;
         this._viewComponent.closeHandler = this.closeHandler;
      }
      
      private function onTujianClickHandler(param1:MouseEvent) : void
      {
         this.tujianWindow = new TujianWindow();
         PopUpCenter.addPopUp("TujianWindow",this.tujianWindow,true,true);
      }
      
      private function onHelpClickHandler(param1:MouseEvent) : void
      {
         var _loc2_:CardShopIntroWin = new CardShopIntroWin();
         PopUpCenter.addPopUp("CardShopIntroWin",_loc2_,true,true);
      }
      
      private function onCancelMatchClickHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("CS_CARD_CANCELMATCH");
      }
      
      private function onPerpareClickHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("CARD_PERPARE_USER");
      }
      
      private function onTimerHandler(param1:TimerEvent) : void
      {
         this.countdown--;
         if(this.countdown <= 0)
         {
            this.timer.reset();
            AppFacade.instance.sendNotification("SC_CARD_MATCHING_OK");
         }
      }
      
      private function onBuyBtnClickHandler(param1:MouseEvent) : void
      {
         var _loc2_:ModuleParams = null;
         if(MainData.getInstance().userData.vip >= XmlManager.cardXML.card_config.@buyVipLevel)
         {
            _loc2_ = new ModuleParams("KongBellyShopModule",ModuleParams.act_Open,1,true,true);
            AppFacade.instance.sendNotification("HANDLE_MODULE",_loc2_);
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("activity.80"),XmlManager.cardXML.card_config.@buyVipLevel),
               "textFormat":TextFormatLib.format_0xFF0000_14px,
               "runTime":2
            });
         }
      }
      
      private function onGenzhuClickHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("CARD_WAGER",[1]);
      }
      
      private function onJiazhuClickHandler(param1:MouseEvent) : void
      {
         this.wagerWindow = new WagerWindow();
         PopUpCenter.addPopUp("WagerWindow",this.wagerWindow,true,true);
      }
      
      private function onRangpaiClickHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("CARD_WAGER",[3]);
      }
      
      private function onQuanxiaClickHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("CARD_WAGER",[4]);
      }
      
      private function onFangqiClickHandler(param1:MouseEvent) : void
      {
         var event:MouseEvent = param1;
         PopUpCenter.confirmWin(Globalization.getString("card.71"),function():void
         {
            AppFacade.instance.sendNotification("CARD_WAGER",[5]);
         },null,0,true);
      }
      
      private function onMatchingClickHandler(param1:MouseEvent) : void
      {
         var _loc2_:Activity = ActivityManager.getActivityByID(10);
         if(_loc2_.checkCardIsCanMatch())
         {
            AppFacade.instance.sendNotification("CARD_MATCHING");
         }
         else
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{"text":Globalization.getString("card.6")});
         }
      }
      
      private function showHandler(param1:Object) : void
      {
         AppFacade.instance.sendNotification("PLAY_BGM",{"name":50});
         AppFacade.instance.sendNotification("CARD_GETPROCESSINFO");
         CardManager.getInstance().myself.bindSetter("bankChip",this._viewComponent.userInfoPanel.updateUserInfo);
         CardManager.getInstance().myself.bindSetter("bankGold",this._viewComponent.userInfoPanel.updateUserInfo);
      }
      
      private function onExitBtnClickHandler(param1:MouseEvent) : void
      {
         this.closeHandler();
      }
      
      private function closeHandler() : void
      {
         AppFacade.instance.sendNotification("EXIT_CARD_HORN");
         GameScene.closeScene(30);
         BabelTimeSocket.getInstance("SOCKET_GENERAL_CARD").close();
         var _loc1_:ModuleParams = new ModuleParams("CardHeroInfo");
         _loc1_.action = ModuleParams.act_Close;
         AppFacade.instance.sendNotification("HANDLE_MODULE",_loc1_);
         _loc1_ = new ModuleParams("CardFormation");
         _loc1_.action = ModuleParams.act_Close;
         AppFacade.instance.sendNotification("HANDLE_MODULE",_loc1_);
         CardManager.getInstance().myself.unBindSetter("bankChip",this._viewComponent.userInfoPanel.updateUserInfo);
         CardManager.getInstance().myself.unBindSetter("bankGold",this._viewComponent.userInfoPanel.updateUserInfo);
      }
      
      private function onSavingBtnClickHandler(param1:MouseEvent) : void
      {
         var _loc2_:BankWindow = new BankWindow(true);
         PopUpCenter.addPopUp("BankWindow",_loc2_,true,true);
      }
      
      private function onTakeBtnClickHandler(param1:MouseEvent) : void
      {
         var _loc2_:BankWindow = new BankWindow(false);
         PopUpCenter.addPopUp("BankWindow",_loc2_,true,true);
      }
      
      override public function onRegister() : void
      {
         if(!facade.hasProxy("CardProxy"))
         {
            facade.registerProxy(new CardProxy());
         }
         var _loc1_:CardProxy = facade.retrieveProxy("CardProxy") as CardProxy;
         facade.registerCommand("CARD_PERPARE_USER",CardCommand);
         facade.registerCommand("CARD_MATCHING",CardCommand);
         facade.registerCommand("CARD_WAGER",CardCommand);
         facade.registerCommand("CARD_FREEZECDEND",CardCommand);
         facade.registerCommand("CARD_GETPROCESSINFO",CardCommand);
         facade.registerCommand("CARD_COMPETEPRICE",CardCommand);
         facade.registerCommand("CARD_COMPETEOK",CardCommand);
         facade.registerCommand("CARD_BILL2POKER",CardCommand);
         facade.registerCommand("CARD_BILL2GAME",CardCommand);
         facade.registerCommand("CS_CARD_CANCELMATCH",CardCommand);
         facade.registerCommand("CARD_GETTODAYFREEREWARD",CardCommand);
      }
      
      override public function onRemove() : void
      {
         facade.removeCommand("CARD_PERPARE_USER");
         facade.removeCommand("CARD_MATCHING");
         facade.removeCommand("CARD_WAGER");
         facade.removeCommand("CARD_FREEZECDEND");
         facade.removeCommand("CARD_GETPROCESSINFO");
         facade.removeCommand("CARD_COMPETEPRICE");
         facade.removeCommand("CARD_COMPETEOK");
         facade.removeCommand("CARD_BILL2POKER");
         facade.removeCommand("CARD_BILL2GAME");
         facade.removeCommand("CS_CARD_CANCELMATCH");
         facade.removeCommand("CARD_GETTODAYFREEREWARD");
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["CARD_SETPERPARE","SC_CARD_MATCHING",undefined,"SC_BUY_KONGDAOBEI","SC_CARD_MATCHING_OK","CARD_CANCELMATCH","CARD_SETCARD","CARD_USERLOGOFF","CARD_BETOPEN","CARD_UPDATE_COMPETEPRICE","CARD_UPDATE_INFO","CARD_UPDATE_BATTLE","CARD_BETOPERATION","CS_CARD_BATTLEFINISH","BATTLE_PLAY_END_INFO","CARD_FORMATION","STAGE_RESIZE"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var data:Object = null;
         var module:ModuleParams = null;
         var module2:ModuleParams = null;
         var bm:BattleModuleParams = null;
         var cardproxy:CardProxy = null;
         var notification:INotification = param1;
         data = notification.getBody();
         switch(notification.getName())
         {
            case "CARD_SETPERPARE":
               if(CardManager.getInstance().myself.currentState == 3)
               {
                  this._viewComponent.myPrepareBtn.visible = false;
                  this._viewComponent.myPrepareSK.visible = true;
               }
               if(CardManager.getInstance().opponent.currentState == 3)
               {
                  this._viewComponent.noPrepareSK.visible = false;
                  this._viewComponent.prepareSK.visible = true;
               }
               break;
            case "SC_CARD_MATCHING":
               this._viewComponent.mathingMC.gotoAndPlay(1);
               this._viewComponent.mathingMC.visible = true;
               this._viewComponent.matchingBtn.visible = false;
               this._viewComponent.opponentSK.visible = false;
               this._viewComponent.takeBtn.enabled = false;
               this._viewComponent.buyBtn.enabled = false;
               this._viewComponent.savingBtn.enabled = false;
               this._viewComponent.exitBtn.enabled = false;
               this._viewComponent.maskSP.visible = true;
               break;
            case "SC_CARD_MATCHING_OK":
               this._viewComponent.maskSP.visible = false;
               this._viewComponent.setOpponent();
               break;
            case "CARD_CANCELMATCH":
               this._viewComponent.mathingMC.stop();
               this._viewComponent.mathingMC.visible = false;
               this._viewComponent.matchingBtn.visible = true;
               this._viewComponent.maskSP.visible = false;
               this._viewComponent.opponentSK.visible = true;
               this._viewComponent.takeBtn.enabled = true;
               this._viewComponent.buyBtn.enabled = true;
               this._viewComponent.savingBtn.enabled = true;
               this._viewComponent.exitBtn.enabled = true;
               break;
            case "CARD_SETCARD":
               this._viewComponent.exitBtn.enabled = false;
               this._viewComponent.myBodySK.visible = false;
               this._viewComponent.vsSK.visible = false;
               this._viewComponent.myPrepareBtn.visible = false;
               this._viewComponent.noPrepareSK.visible = false;
               this._viewComponent.myPrepareSK.visible = false;
               this._viewComponent.prepareSK.visible = false;
               this._viewComponent.opponentSK.visible = false;
               this._viewComponent.opponentNameTF.visible = false;
               this._viewComponent.opponentBGSK.visible = false;
               this._viewComponent.mathingMC.visible = false;
               this._viewComponent.setEffectAndPlay("startStep",function():void
               {
                  _viewComponent.setCardInfo();
               });
               break;
            case "CARD_USERLOGOFF":
               this._viewComponent.stopEffect();
               module = new ModuleParams("CardHeroInfo");
               module.action = ModuleParams.act_Close;
               AppFacade.instance.sendNotification("HANDLE_MODULE",module);
               module = new ModuleParams("CardFormation");
               module.action = ModuleParams.act_Close;
               AppFacade.instance.sendNotification("HANDLE_MODULE",module);
               PopUpCenter.removePopUp("GoldCompeteWindow");
               this._viewComponent.mathingMC.stop();
               this._viewComponent.mathingMC.visible = false;
               this._viewComponent.userLogOff();
               PopUpCenter.removePopUp("WagerWindow");
               break;
            case "CARD_BETOPEN":
               PopUpCenter.closeAllWindow();
               this._viewComponent.setCountDown(-1);
               this._viewComponent.setWaitCountDown(-1);
               this._viewComponent.jiazhuBtn.visible = false;
               this._viewComponent.genzhuBtn.visible = false;
               this._viewComponent.rangpaiBtn.visible = false;
               this._viewComponent.kaipaiBtn.visible = false;
               this._viewComponent.quanxiaBtn.visible = false;
               this._viewComponent.fangqiBtn.visible = false;
               this._viewComponent.anteBtnBG.visible = false;
               this.goldCompeteWindow = new GoldCompeteWindow();
               this._viewComponent.setEffectAndPlay("competeStep",function():void
               {
                  PopUpCenter.addPopUp("GoldCompeteWindow",goldCompeteWindow,true);
               });
               PopUpCenter.removePopUp("WagerWindow");
               break;
            case "CARD_UPDATE_COMPETEPRICE":
               this.goldCompeteWindow.setDate([CardManager.getInstance().opponent.compete,CardManager.getInstance().myself.compete,Number(data)]);
               break;
            case "CARD_UPDATE_INFO":
               this._viewComponent.battleInfo.addInfoText(Number(data[0]),data[1]);
               break;
            case "CARD_UPDATE_BATTLE":
               this._viewComponent.battleInfo.addBattleText(Number(data[0]),data[1],data[2],data[3]);
               break;
            case "CARD_BETOPERATION":
               PopUpCenter.removePopUp("WagerWindow");
               this._viewComponent.betOperation(int(data[0]),int(data[1]),int(data[2]),Number(data[3]));
               break;
            case "SC_BUY_KONGDAOBEI":
               CardManager.getInstance().myself.bankChip = CardManager.getInstance().myself.bankChip + data[0];
               CardManager.getInstance().myself.bankGold = CardManager.getInstance().myself.bankGold - data[1];
               break;
            case "CS_CARD_BATTLEFINISH":
               PopUpCenter.closeAllWindow();
               PopUpCenter.removePopUp("WagerWindow");
               module2 = new ModuleParams("CardHeroInfo");
               module2.action = ModuleParams.act_Close;
               AppFacade.instance.sendNotification("HANDLE_MODULE",module2);
               module2 = new ModuleParams("CardFormation");
               module2.action = ModuleParams.act_Close;
               AppFacade.instance.sendNotification("HANDLE_MODULE",module2);
               PopUpCenter.removePopUp("GoldCompeteWindow");
               this._viewComponent.mathingMC.stop();
               this._viewComponent.mathingMC.visible = false;
               this._viewComponent.userLogOff();
               break;
            case "BATTLE_PLAY_END_INFO":
               AppFacade.instance.sendNotification("PLAY_BGM",{"name":50});
               bm = data as BattleModuleParams;
               cardproxy = facade.retrieveProxy("CardProxy") as CardProxy;
               if(cardproxy.brid == bm.brid)
               {
                  this._viewComponent.userLogOff();
                  cardproxy.brid = "";
                  PopUpCenter.confirmWin2(cardproxy.getBattleFinishString(),function():void
                  {
                     AppFacade.instance.sendNotification("CARD_MATCHING");
                  },null,Globalization.getString("Gl.180"),Globalization.getString("Gl.181"),true);
               }
               break;
            case "CARD_FORMATION":
               PopUpCenter.closeAllWindow();
               PopUpCenter.removePopUp("GoldCompeteWindow");
               this._viewComponent.setEffectAndPlay("formationStep",function():void
               {
                  openModule("CardFormation",true,{
                     "heroes":CardManager.getInstance().getHtidByCid(CardManager.getInstance().commonCard),
                     "formationHeroes":CardManager.getInstance().getHtidByCid(CardManager.getInstance().myself.cardList),
                     "prepareTime":data[0],
                     "first":data[1]
                  });
               });
               break;
            case "STAGE_RESIZE":
               this._viewComponent.resize();
               break;
            default:
               throw new Error("don\'t found [" + notification.getName() + "] function");
         }
      }
   }
}

