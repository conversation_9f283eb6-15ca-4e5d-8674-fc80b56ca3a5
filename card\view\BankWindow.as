package card.view
{
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.modules.card.manager.CardManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.TextButton;
   import mmo.ui.control.input.TextInput;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class BankWindow extends PopUpWindow
   {
      public static const NAME:String = "BankWindow";
      
      private var _isSaving:Boolean;
      
      public var goldTxt:TextInput;
      
      public var chipTxt:TextInput;
      
      public var addGoldBtn1:Button;
      
      public var addGoldBtn2:Button;
      
      public var addGoldBtn3:Button;
      
      public var addGoldBtn4:Button;
      
      public var addChipBtn1:Button;
      
      public var addChipBtn2:Button;
      
      public var addChipBtn3:Button;
      
      public var addChipBtn4:Button;
      
      public var goldEmptyBtn:TextButton;
      
      public var chipEmptyBtn:TextButton;
      
      public var submitBtn:Button;
      
      public var cancelBtn:Button;
      
      private var cardGoldTF:Label;
      
      private var cardGoldTF2:Label;
      
      private var cardChipTF:Label;
      
      private var cardChipTF2:Label;
      
      public function BankWindow(param1:Boolean)
      {
         super(460,345);
         this._isSaving = param1;
         this.isLive = false;
         if(this._isSaving)
         {
            this.setTitleImageData(UIManager.getUISkin("cardTitle_1").bitmapData);
         }
         else
         {
            this.setTitleImageData(UIManager.getUISkin("cardTitle_2").bitmapData);
         }
         var _loc13_:UISkin = UIManager.getUISkin("group_bg");
         _loc13_.x = 4;
         _loc13_.y = 2;
         _loc13_.width = 440;
         _loc13_.height = 250;
         pane.addChild(_loc13_);
         var _loc11_:TextFormat = TextFormatLib.format_0xfff5ce_12px;
         _loc11_.leading = 5;
         var _loc12_:Label = new Label(Globalization.getString("card.57"),_loc11_,[FilterLib.glow_0x272727],false);
         _loc12_.x = 20;
         _loc12_.y = 9;
         _loc12_.width = 365;
         _loc12_.height = 45;
         _loc12_.wordWrap = true;
         pane.addChild(_loc12_);
         _loc11_.leading = 0;
         var _loc16_:UISkin = UIManager.getUISkin("black_bg3");
         _loc16_.x = 12;
         _loc16_.y = 51;
         _loc16_.width = 208;
         _loc16_.height = 188;
         pane.addChild(_loc16_);
         var _loc17_:UISkin = UIManager.getUISkin("black_bg3");
         _loc17_.x = 227;
         _loc17_.y = 51;
         _loc17_.width = 208;
         _loc17_.height = 188;
         pane.addChild(_loc17_);
         var _loc20_:Label = new Label(Globalization.getString("card.58"),TextFormatLib.format_0xffb932_12px_center,[FilterLib.glow_0x272727]);
         _loc20_.x = 20;
         _loc20_.y = 119;
         pane.addChild(_loc20_);
         var _loc18_:Label = new Label(Globalization.getString("card.59"),TextFormatLib.format_0xffb932_12px_center,[FilterLib.glow_0x272727]);
         _loc18_.x = 230;
         _loc18_.y = 119;
         pane.addChild(_loc18_);
         if(this._isSaving)
         {
            _loc20_.text = Globalization.getString("card.60");
            _loc18_.text = Globalization.getString("card.61");
         }
         else
         {
            _loc20_.text = Globalization.getString("card.62");
            _loc18_.text = Globalization.getString("card.63");
         }
         var _loc19_:UISkin = UIManager.getUISkin("gold_card");
         _loc19_.x = 25;
         _loc19_.y = 150;
         pane.addChild(_loc19_);
         var _loc3_:UISkin = UIManager.getUISkin("gold_conch");
         _loc3_.x = 252;
         _loc3_.y = 150;
         pane.addChild(_loc3_);
         this.goldTxt = new TextInput("0",100,TextFormatLib.format_0xfffb00_18px,32);
         this.goldTxt.x = 60;
         this.goldTxt.y = 145;
         this.goldTxt.restrict = "0-9";
         this.goldTxt.addEventListener("change",this.onGoldChangeHandler);
         pane.addChild(this.goldTxt);
         this.addGoldBtn1 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipblue2"));
         this.addGoldBtn1.x = 15;
         this.addGoldBtn1.y = 180;
         this.addGoldBtn1.text = this.formatTxt(CardManager.getInstance().getBankGold()[0]);
         this.addGoldBtn1.name = CardManager.getInstance().getBankGold()[0];
         pane.addChild(this.addGoldBtn1);
         this.addGoldBtn2 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipyellow2"));
         this.addGoldBtn2.x = 65;
         this.addGoldBtn2.y = 180;
         this.addGoldBtn2.text = this.formatTxt(CardManager.getInstance().getBankGold()[1]);
         this.addGoldBtn2.name = CardManager.getInstance().getBankGold()[1];
         pane.addChild(this.addGoldBtn2);
         this.addGoldBtn3 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipred2"));
         this.addGoldBtn3.x = 115;
         this.addGoldBtn3.y = 180;
         this.addGoldBtn3.text = this.formatTxt(CardManager.getInstance().getBankGold()[2]);
         this.addGoldBtn3.name = CardManager.getInstance().getBankGold()[2];
         pane.addChild(this.addGoldBtn3);
         this.addGoldBtn4 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChippurple2"));
         this.addGoldBtn4.x = 165;
         this.addGoldBtn4.y = 180;
         this.addGoldBtn4.text = this.formatTxt(CardManager.getInstance().getBankGold()[3]);
         this.addGoldBtn4.name = CardManager.getInstance().getBankGold()[3];
         pane.addChild(this.addGoldBtn4);
         this.chipTxt = new TextInput("0",100,TextFormatLib.format_0x00ffed_18px,32);
         this.chipTxt.x = 280;
         this.chipTxt.y = 145;
         this.chipTxt.addEventListener("change",this.onChipChangeHandler);
         this.chipTxt.restrict = "0-9";
         pane.addChild(this.chipTxt);
         this.addChipBtn1 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipblue2"));
         this.addChipBtn1.x = 230;
         this.addChipBtn1.y = 180;
         this.addChipBtn1.text = this.formatTxt(CardManager.getInstance().getBankChip()[0]);
         this.addChipBtn1.name = CardManager.getInstance().getBankChip()[0];
         pane.addChild(this.addChipBtn1);
         this.addChipBtn2 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipyellow2"));
         this.addChipBtn2.x = 280;
         this.addChipBtn2.y = 180;
         this.addChipBtn2.text = this.formatTxt(CardManager.getInstance().getBankChip()[1]);
         this.addChipBtn2.name = CardManager.getInstance().getBankChip()[1];
         pane.addChild(this.addChipBtn2);
         this.addChipBtn3 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipred2"));
         this.addChipBtn3.x = 330;
         this.addChipBtn3.y = 180;
         this.addChipBtn3.text = this.formatTxt(CardManager.getInstance().getBankChip()[2]);
         this.addChipBtn3.name = CardManager.getInstance().getBankChip()[2];
         pane.addChild(this.addChipBtn3);
         this.addChipBtn4 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChippurple2"));
         this.addChipBtn4.x = 380;
         this.addChipBtn4.y = 180;
         this.addChipBtn4.text = this.formatTxt(CardManager.getInstance().getBankChip()[3]);
         this.addChipBtn4.name = CardManager.getInstance().getBankChip()[3];
         pane.addChild(this.addChipBtn4);
         var _loc5_:TextFormat = TextFormatLib.format_0x00FF00_14px;
         _loc5_.underline = true;
         this.goldEmptyBtn = new TextButton(Globalization.getString("buyKongdaobei1"),50,_loc5_,[FilterLib.glow_0x272727],65280,********,95489);
         this.goldEmptyBtn.x = 168;
         this.goldEmptyBtn.y = 150;
         pane.addChild(this.goldEmptyBtn);
         this.chipEmptyBtn = new TextButton(Globalization.getString("buyKongdaobei1"),50,_loc5_,[FilterLib.glow_0x272727],65280,********,95489);
         this.chipEmptyBtn.x = 388;
         this.chipEmptyBtn.y = 150;
         pane.addChild(this.chipEmptyBtn);
         _loc5_.underline = false;
         this.submitBtn = new Button(Globalization.getString("Gl.161"),TextFormatLib.format_0xFFED89_12px,85);
         this.submitBtn.x = 120;
         this.submitBtn.y = 265;
         pane.addChild(this.submitBtn);
         this.cancelBtn = new Button(Globalization.getString("Globalization.38"),TextFormatLib.format_0xFFED89_12px,85);
         this.cancelBtn.x = 250;
         this.cancelBtn.y = 265;
         pane.addChild(this.cancelBtn);
         var _loc4_:UISkin = UIManager.getUISkin("black_bg3");
         _loc4_.x = 28;
         _loc4_.y = 57;
         _loc4_.width = 174;
         _loc4_.height = 26;
         pane.addChild(_loc4_);
         var _loc7_:UISkin = UIManager.getUISkin("black_bg3");
         _loc7_.x = 28;
         _loc7_.y = 89;
         _loc7_.width = 174;
         _loc7_.height = 26;
         pane.addChild(_loc7_);
         var _loc2_:UISkin = UIManager.getUISkin("black_bg3");
         _loc2_.x = 244;
         _loc2_.y = 58;
         _loc2_.width = 174;
         _loc2_.height = 26;
         pane.addChild(_loc2_);
         var _loc14_:UISkin = UIManager.getUISkin("black_bg3");
         _loc14_.x = 244;
         _loc14_.y = 89;
         _loc14_.width = 174;
         _loc14_.height = 26;
         pane.addChild(_loc14_);
         var _loc15_:UISkin = UIManager.getUISkin("gold");
         _loc15_.x = 42;
         _loc15_.y = 98;
         this.addChild(_loc15_);
         var _loc10_:UISkin = UIManager.getUISkin("gold");
         _loc10_.x = 42;
         _loc10_.y = 130;
         this.addChild(_loc10_);
         var _loc6_:UISkin = UIManager.getUISkin("kongdaobei");
         _loc6_.x = 257;
         _loc6_.y = 101;
         this.addChild(_loc6_);
         var _loc22_:UISkin = UIManager.getUISkin("kongdaobei");
         _loc22_.x = 257;
         _loc22_.y = 129;
         this.addChild(_loc22_);
         var _loc8_:Label = new Label(Globalization.getString("card.64"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc8_.x = 60;
         _loc8_.y = 128;
         this.addChild(_loc8_);
         var _loc21_:Label = new Label(Globalization.getString("card.39"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc21_.x = 60;
         _loc21_.y = 96;
         this.addChild(_loc21_);
         this.cardGoldTF = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.cardGoldTF.x = 134;
         this.cardGoldTF.y = 126;
         this.addChild(this.cardGoldTF);
         this.cardGoldTF2 = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.cardGoldTF2.x = 97;
         this.cardGoldTF2.y = 94;
         this.addChild(this.cardGoldTF2);
         var _loc23_:Label = new Label(Globalization.getString("card.65"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc23_.x = 276;
         _loc23_.y = 128;
         this.addChild(_loc23_);
         var _loc9_:Label = new Label(Globalization.getString("card.40"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc9_.x = 276;
         _loc9_.y = 99;
         this.addChild(_loc9_);
         this.cardChipTF = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.cardChipTF.x = 324;
         this.cardChipTF.y = 98;
         this.addChild(this.cardChipTF);
         this.cardChipTF2 = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.cardChipTF2.x = 358;
         this.cardChipTF2.y = 126;
         this.addChild(this.cardChipTF2);
         this.addEventListener("click",this.onMouseClickHandler);
         this.showHander = this.showHandler;
      }
      
      public function showHandler(param1:Object) : void
      {
         this.cardGoldTF.text = "" + MainData.getInstance().userData.gold_num;
         this.cardChipTF2.text = "" + CardManager.getInstance().unbindChip;
         this.cardGoldTF2.text = "" + CardManager.getInstance().myself.bankGold;
         this.cardChipTF.text = "" + CardManager.getInstance().myself.bankChip;
      }
      
      private function onMouseClickHandler(param1:MouseEvent) : void
      {
         if(param1.target == this.goldEmptyBtn)
         {
            this.goldTxt.text = "0";
         }
         else if(param1.target == this.chipEmptyBtn)
         {
            this.chipTxt.text = "0";
         }
         else if(param1.target == this.addGoldBtn1)
         {
            this.goldTxt.text = "" + (int(this.goldTxt.text) + int(this.addGoldBtn1.name));
            this.setInputGold();
         }
         else if(param1.target == this.addGoldBtn2)
         {
            this.goldTxt.text = "" + (int(this.goldTxt.text) + int(this.addGoldBtn2.name));
            this.setInputGold();
         }
         else if(param1.target == this.addGoldBtn3)
         {
            this.goldTxt.text = "" + (int(this.goldTxt.text) + int(this.addGoldBtn3.name));
            this.setInputGold();
         }
         else if(param1.target == this.addGoldBtn4)
         {
            this.goldTxt.text = "" + (int(this.goldTxt.text) + int(this.addGoldBtn4.name));
            this.setInputGold();
         }
         else if(param1.target == this.addChipBtn1)
         {
            this.chipTxt.text = "" + (int(this.chipTxt.text) + int(this.addChipBtn1.name));
            this.setInputChip();
         }
         else if(param1.target == this.addChipBtn2)
         {
            this.chipTxt.text = "" + (int(this.chipTxt.text) + int(this.addChipBtn2.name));
            this.setInputChip();
         }
         else if(param1.target == this.addChipBtn3)
         {
            this.chipTxt.text = "" + (int(this.chipTxt.text) + int(this.addChipBtn3.name));
            this.setInputChip();
         }
         else if(param1.target == this.addChipBtn4)
         {
            this.chipTxt.text = "" + (int(this.chipTxt.text) + int(this.addChipBtn4.name));
            this.setInputChip();
         }
         else if(param1.target == this.submitBtn)
         {
            if(int(this.goldTxt.text) < CardManager.getInstance().minUnitGold() && int(this.chipTxt.text) < CardManager.getInstance().minUnitChip())
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.66"),CardManager.getInstance().minUnitGold(),CardManager.getInstance().minUnitChip()),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "runTime":2
               });
               return;
            }
            if(this._isSaving)
            {
               AppFacade.instance.sendNotification("CARD_BILL2POKER",[int(this.goldTxt.text),int(this.chipTxt.text)]);
            }
            else
            {
               if(int(this.goldTxt.text) > CardManager.getInstance().max_withdraw_gold - CardManager.getInstance().withdraw_gold || int(this.chipTxt.text) > CardManager.getInstance().max_withdraw_island_val - CardManager.getInstance().withdraw_island_val)
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":StringUtil.substitute(Globalization.getString("card.80"),CardManager.getInstance().max_withdraw_gold - CardManager.getInstance().withdraw_gold,CardManager.getInstance().max_withdraw_island_val - CardManager.getInstance().withdraw_island_val),
                     "textFormat":TextFormatLib.format_0xFF0000_12px,
                     "runTime":2
                  });
                  return;
               }
               AppFacade.instance.sendNotification("CARD_BILL2GAME",[int(this.goldTxt.text),int(this.chipTxt.text)]);
            }
            this.close();
         }
         else if(param1.target == this.cancelBtn)
         {
            this.close();
         }
      }
      
      private function setInputGold() : void
      {
         if(this._isSaving && MainData.getInstance().userData.gold_num < int(this.goldTxt.text))
         {
            this.goldTxt.text = "" + MainData.getInstance().userData.gold_num;
         }
         else if(!this._isSaving && CardManager.getInstance().myself.bankGold < int(this.goldTxt.text))
         {
            this.goldTxt.text = "" + CardManager.getInstance().myself.bankGold;
         }
      }
      
      private function setInputChip() : void
      {
         if(this._isSaving && CardManager.getInstance().unbindChip < int(this.chipTxt.text))
         {
            this.chipTxt.text = "" + CardManager.getInstance().unbindChip;
         }
         else if(!this._isSaving && CardManager.getInstance().myself.bankChip < int(this.chipTxt.text))
         {
            this.chipTxt.text = "" + CardManager.getInstance().myself.bankChip;
         }
      }
      
      private function onGoldChangeHandler(param1:Event) : void
      {
         TextInput(param1.currentTarget).text = "" + int(TextInput(param1.currentTarget).text);
         this.setInputGold();
      }
      
      private function onChipChangeHandler(param1:Event) : void
      {
         TextInput(param1.currentTarget).text = "" + int(TextInput(param1.currentTarget).text);
         this.setInputChip();
      }
      
      private function formatTxt(param1:int) : String
      {
         var _loc2_:String = "";
         if(param1 >= 1000 && param1 < 10000)
         {
            _loc2_ = "+" + int(param1 / 1000) + Globalization.getString("card.34");
         }
         else if(param1 >= 10000)
         {
            _loc2_ = "+" + int(param1 / 10000) + Globalization.getString("card.35");
         }
         else
         {
            _loc2_ = "+" + param1;
         }
         return _loc2_;
      }
   }
}

