package card.view
{
   import com.hurlant.crypto.Crypto;
   import flash.events.TextEvent;
   import flash.system.System;
   import game.Environment;
   import game.data.battle.BattleStringRequetDataOfCard;
   import game.manager.UIManager;
   import game.modules.card.manager.CardManager;
   import game.modules.card.proxys.CardProxy;
   import game.mvc.AppFacade;
   import mmo.Core;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.input.TextArea;
   import mmo.ui.control.tab.TabPane;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.time.TimeManager;
   
   public class BattleInfo extends UISprite
   {
      public var infoArea:TextArea;
      
      public var battleArea:TextArea;
      
      private var infoTxtList:Array = [];
      
      private var battleTxtList:Array = [];
      
      public function BattleInfo()
      {
         super();
         var _loc3_:UISkin = UIManager.getUISkin("black_bg5");
         _loc3_.width = 220;
         _loc3_.height = 135;
         this.addChild(_loc3_);
         var _loc2_:UISkin = UIManager.getUISkin("tab_bg");
         _loc2_.x = 0;
         _loc2_.y = 10;
         _loc2_.width = 210;
         this.addChild(_loc2_);
         var _loc1_:TabPane = new TabPane([Globalization.getString("copy.72"),Globalization.getString("teamwar.5")],0,65);
         _loc1_.x = 10;
         _loc1_.y = 27;
         this.addChild(_loc1_);
         this.infoArea = new TextArea("",212,98,TextFormatLib.format_0xFFED89_12px);
         this.infoArea.y = 5;
         this.infoArea.x = -4;
         this.infoArea.htmlText = "";
         this.infoArea.enabled = false;
         this.infoArea.textFilters = [FilterLib.glow_0x272727];
         this.infoArea.addEventListener("link",this.onLinkHandler);
         this.infoArea.visibleBG = false;
         _loc1_.addToTab(this.infoArea,0);
         this.battleArea = new TextArea("",212,98,TextFormatLib.format_0xFFED89_12px);
         this.battleArea.y = 5;
         this.battleArea.x = -4;
         this.battleArea.htmlText = "";
         this.battleArea.enabled = false;
         this.battleArea.textFilters = [FilterLib.glow_0x272727];
         this.battleArea.addEventListener("link",this.onLinkHandler);
         this.battleArea.visibleBG = false;
         _loc1_.addToTab(this.battleArea,1);
      }
      
      public function addInfoText(param1:Number, param2:String) : void
      {
         this.infoTxtList.push([param1,param2]);
         var _loc4_:int = int(this.infoTxtList.length);
         var _loc5_:String = "";
         var _loc3_:int = _loc4_ - 1;
         while(_loc3_ >= 0)
         {
            _loc5_ += this.getTimeString(this.infoTxtList[_loc3_][0]) + this.infoTxtList[_loc3_][1] + "\n";
            _loc3_--;
         }
         this.infoArea.htmlText = _loc5_;
      }
      
      public function addBattleText(param1:Number, param2:String, param3:int, param4:Boolean) : void
      {
         this.battleTxtList.push([param1,param2,param3,param4]);
         var _loc5_:String = "";
         if(param4)
         {
            _loc5_ += "<font color=\'#00ff00\'>" + Globalization.getString("card.50") + "</font>";
         }
         else
         {
            _loc5_ += "<font color=\'#ff0000\'>" + Globalization.getString("card.51") + "</font>";
         }
         _loc5_ += "  <font color=\'#ffb932\'><a href=\'event:battleReplay." + param3 + "\'>" + Globalization.getString("copy.71") + "</a></font>";
         _loc5_ = _loc5_ + ("  <font color=\'#ffb932\'><a href=\'event:copy." + param3 + "\'>" + Globalization.getString("arena.27") + "</a></font>");
         this.addInfoText(param1,param2 + _loc5_);
         var _loc6_:int = int(this.battleTxtList.length);
         _loc5_ = "";
         var _loc7_:int = _loc6_ - 1;
         while(_loc7_ >= 0)
         {
            _loc5_ += this.getTimeString(this.battleTxtList[_loc7_][0]) + this.battleTxtList[_loc7_][1];
            if(this.battleTxtList[_loc7_][3])
            {
               _loc5_ += "<font color=\'#00ff00\'>" + Globalization.getString("card.50") + "</font>";
            }
            else
            {
               _loc5_ += "<font color=\'#ff0000\'>" + Globalization.getString("card.51") + "</font>";
            }
            _loc5_ += "  <font color=\'#ffb932\'><a href=\'event:battleReplay." + this.battleTxtList[_loc7_][2] + "\'>" + Globalization.getString("copy.71") + "</a></font>";
            _loc5_ = _loc5_ + ("  <font color=\'#ffb932\'><a href=\'event:copy." + this.battleTxtList[_loc7_][2] + "\'>" + Globalization.getString("arena.27") + "</a></font>\n");
            _loc7_--;
         }
         this.battleArea.htmlText = _loc5_;
      }
      
      private function onLinkHandler(param1:TextEvent) : void
      {
         var _loc6_:BattleStringRequetDataOfCard = null;
         var _loc4_:CardProxy = null;
         var _loc5_:Object = null;
         var _loc2_:String = null;
         var _loc3_:String = null;
         if(param1.text.indexOf("battleReplay") != -1)
         {
            if(CardManager.getInstance().myself.currentState == 2 || CardManager.getInstance().myself.currentState == 3)
            {
               return;
            }
            _loc6_ = new BattleStringRequetDataOfCard();
            _loc6_.id = param1.text.substring(13);
            _loc6_.serverid = Core.cardServer;
            _loc6_.showJumpBattleButton = true;
            _loc4_ = AppFacade.instance.retrieveProxy("CardProxy") as CardProxy;
            _loc6_.selfLeaderName = _loc4_.getTeamNames(int(_loc6_.id))[0];
            _loc6_.armyLeaderName = _loc4_.getTeamNames(int(_loc6_.id))[1];
            AppFacade.instance.sendNotification("CS_PLAY_CARD_BATTLE",_loc6_);
         }
         else if(param1.text.indexOf("copy") != -1)
         {
            _loc5_ = Environment.loadingParams;
            if(_loc5_ && _loc5_.hasOwnProperty("recordUrl"))
            {
               _loc2_ = _loc5_.recordUrl;
            }
            else
            {
               _loc2_ = "http://www.zuiyouxi.com/game/replay";
            }
            _loc3_ = _loc2_ + "?serverid=" + Core.cardServer + "&bid=" + Crypto.encryptNumber(int(param1.text.substring(5)));
            System.setClipboard(_loc3_);
         }
      }
      
      public function getTimeString(param1:Number) : String
      {
         var _loc4_:Number = TimeManager.getInstance().getTime() / 1000;
         var _loc2_:Number = _loc4_ - param1;
         var _loc3_:String = "<font color=\'#00ff00\'>";
         if(_loc2_ >> 0 == 0)
         {
            _loc2_ = 1;
         }
         if(_loc2_ < 60)
         {
            _loc3_ += StringUtil.substitute(Globalization.getString("card.56"),int(_loc2_));
         }
         else if(_loc2_ >= 60 && _loc2_ < 3600)
         {
            _loc3_ += StringUtil.substitute(Globalization.getString("card.55"),int(_loc2_ / 60));
         }
         else if(_loc2_ >= 3600 && _loc2_ < 86400)
         {
            _loc3_ += StringUtil.substitute(Globalization.getString("card.54"),int(_loc2_ / 3600));
         }
         else if(_loc2_ >= 86400 && _loc2_ < 2592000)
         {
            _loc3_ += StringUtil.substitute(Globalization.getString("card.53"),int(_loc2_ / 86400));
         }
         else
         {
            _loc3_ += Globalization.getString("card.52");
         }
         return _loc3_ + "</font>";
      }
   }
}

