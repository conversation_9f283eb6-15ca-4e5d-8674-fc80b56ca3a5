package card.view
{
   import card.mediators.CardMediator;
   import card.view.ui.Card;
   import card.view.ui.FrostPanel;
   import com.greensock.TweenMax;
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import game.modules.card.manager.CardManager;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleParams;
   import game.mvc.module.ModulePart;
   import mmo.Core;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.utils.CountTimer;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.openModule;
   
   public class CardWindow extends ModulePart
   {
      public static const NAME:String = "CardWindow";
      
      public var showHandler:Function;
      
      public var closeHandler:Function;
      
      private var countTimer:CountTimer;
      
      public var exitBtn:Button;
      
      public var savingBtn:Button;
      
      public var takeBtn:Button;
      
      public var buyBtn:Button;
      
      private var _bgimage:Bitmap;
      
      public var residue:ResiduePanel;
      
      public var opponent:PlayerHead;
      
      public var myself:PlayerHead;
      
      public var battleInfo:BattleInfo;
      
      public var userInfoPanel:UserInfoPanel;
      
      public var genzhuBtn:Button;
      
      public var jiazhuBtn:Button;
      
      public var rangpaiBtn:Button;
      
      public var kaipaiBtn:Button;
      
      public var quanxiaBtn:Button;
      
      public var fangqiBtn:Button;
      
      public var matchingBtn:Button;
      
      public var opponentSK:UISkin;
      
      public var opponentBGSK:UISkin;
      
      public var opponentNameTF:Label;
      
      public var mathingMC:MovieClip;
      
      public var countDownTxt:Label;
      
      public var noPrepareSK:UISkin;
      
      public var prepareSK:UISkin;
      
      public var myPrepareBtn:Button;
      
      public var myPrepareSK:UISkin;
      
      public var chipInfo1:ChipInfo;
      
      public var chipInfo2:MyChipInfo;
      
      public var cardList:Array;
      
      public var myCard2:Card;
      
      public var myCard1:Card;
      
      private var countDownSK:UISkin;
      
      private var cdSK:UISkin;
      
      private var frostPanel:FrostPanel;
      
      public var cardManager:CardManager = CardManager.getInstance();
      
      private var timer:Timer;
      
      private var countDown:int;
      
      private var waitTimer:Timer;
      
      private var waitCountDwon:int;
      
      private var cardBG1:UISkin;
      
      private var cardBG2:UISkin;
      
      public var anteBtnBG:UISkin;
      
      private var maxAdd:int = CardManager.getInstance().getRaisetimes();
      
      private var opAdd:int = 0;
      
      private var myAdd:int = 0;
      
      private var movieMC:UISprite;
      
      public var myBodySK:UISkin;
      
      public var vsSK:UISkin;
      
      public var maskSP:Sprite;
      
      public var cancelMatchBtn:Button;
      
      private var tipsTF:Label;
      
      private var waitSK:UISkin;
      
      public var helpBtn:Button;
      
      public var prepareInfo:UISkin;
      
      private var logoSK:UISkin;
      
      private var cardlistSK:UISkin;
      
      private var oldwager:int;
      
      public var tujian:Button;
      
      private var effectMC:MovieClip;
      
      private var selectCard:Card;
      
      private var sendOppent:Boolean = false;
      
      private var sendMyself:Boolean = false;
      
      private var cardCount:int = 0;
      
      private var isUserLogOff:Boolean = false;
      
      private var myRang:Boolean;
      
      private var opponentRang:Boolean;
      
      public function CardWindow()
      {
         super();
         this.isLive = false;
         this._bgimage = AssetManager.getImg("BGImage");
         addChild(this._bgimage);
         this.cardlistSK = UIManager.getUISkin("cardList");
         this.cardlistSK.x = Core.stgW - 224;
         this.cardlistSK.y = 250;
         this.addChild(this.cardlistSK);
         this.logoSK = UIManager.getUISkin("card_logo");
         this.logoSK.x = Core.stgW - 193;
         this.logoSK.y = 133;
         this.addChild(this.logoSK);
         this.movieMC = new UISprite();
         this.addChild(this.movieMC);
         this.myBodySK = UIManager.getUISkin("defaultBody_card");
         this.myBodySK.bitmapData = UIManager.getUISkin("ServiceChallengeWorshipChampion" + CardManager.getInstance().myself.bodyID).bitmapData;
         this.myBodySK.x = 160;
         this.myBodySK.y = 170;
         if(CardManager.getInstance().myself.bodyID == 1)
         {
            this.myBodySK.x += 0;
         }
         else if(CardManager.getInstance().myself.bodyID == 2)
         {
            this.myBodySK.x += 36;
         }
         else if(CardManager.getInstance().myself.bodyID == 3)
         {
            this.myBodySK.x += 96;
         }
         else if(CardManager.getInstance().myself.bodyID == 4)
         {
            this.myBodySK.x += 30;
         }
         else if(CardManager.getInstance().myself.bodyID == 5)
         {
            this.myBodySK.x += 30;
         }
         else if(CardManager.getInstance().myself.bodyID == 6)
         {
            this.myBodySK.x -= 30;
         }
         this.addChild(this.myBodySK);
         this.vsSK = UIManager.getUISkin("card_VS");
         this.vsSK.x = 490;
         this.vsSK.y = 220;
         this.addChild(this.vsSK);
         this.exitBtn = new Button("",null,120,UIManager.getMultiUISkin("btn_return"));
         this.exitBtn.x = Core.stgW - 65;
         this.exitBtn.y = 15;
         this.addChild(this.exitBtn);
         this.savingBtn = new Button("",null,120,UIManager.getMultiUISkin("cunruBtn"));
         this.savingBtn.x = Core.stgW - 200;
         this.savingBtn.y = 15;
         this.addChild(this.savingBtn);
         this.takeBtn = new Button("",null,120,UIManager.getMultiUISkin("quchuBtn"));
         this.takeBtn.x = Core.stgW - 265;
         this.takeBtn.y = 17;
         this.addChild(this.takeBtn);
         this.buyBtn = new Button("",null,120,UIManager.getMultiUISkin("goumaiBtn"));
         this.buyBtn.x = Core.stgW - 345;
         this.buyBtn.y = 20;
         this.addChild(this.buyBtn);
         this.residue = new ResiduePanel();
         this.residue.x = Core.stgW - 317;
         this.residue.y = Core.stgH - 65;
         this.residue.setData();
         this.addChild(this.residue);
         this.opponent = new PlayerHead(false);
         this.opponent.x = Core.stgW / 2 + 80;
         this.opponent.y = 15;
         this.opponent.resetData();
         this.addChild(this.opponent);
         this.myself = new PlayerHead(true);
         this.myself.x = Core.stgW / 2 - 330;
         this.myself.y = Core.stgH - 100;
         this.myself.setData(this.cardManager.myself);
         this.addChild(this.myself);
         this.battleInfo = new BattleInfo();
         this.battleInfo.x = 0;
         this.battleInfo.y = 115;
         this.addChild(this.battleInfo);
         this.userInfoPanel = new UserInfoPanel();
         this.userInfoPanel.x = 0;
         this.userInfoPanel.y = 0;
         this.addChild(this.userInfoPanel);
         this.chipInfo1 = new ChipInfo();
         this.chipInfo1.x = Core.stgW / 2 + 60;
         this.chipInfo1.y = 155;
         this.chipInfo1.visible = false;
         this.addChild(this.chipInfo1);
         this.chipInfo2 = new MyChipInfo();
         this.chipInfo2.x = 295;
         this.chipInfo2.y = Core.stgH - 215;
         this.chipInfo2.visible = false;
         this.addChild(this.chipInfo2);
         this.anteBtnBG = UIManager.getUISkin("text_bg_17");
         this.anteBtnBG.x = Core.stgW / 2 - 165;
         this.anteBtnBG.y = Core.stgH / 2 + 88;
         this.anteBtnBG.height = 40;
         this.anteBtnBG.visible = false;
         this.addChild(this.anteBtnBG);
         this.genzhuBtn = new Button("",null,75,UIManager.getMultiUISkin("genzhu"));
         this.genzhuBtn.x = Core.stgW / 2 - 130;
         this.genzhuBtn.y = Core.stgH / 2 + 97;
         this.genzhuBtn.visible = false;
         this.addChild(this.genzhuBtn);
         this.jiazhuBtn = new Button("",null,75,UIManager.getMultiUISkin("jiazhu"));
         this.jiazhuBtn.x = this.genzhuBtn.x + this.genzhuBtn.width + 32;
         this.jiazhuBtn.y = Core.stgH / 2 + 97;
         this.jiazhuBtn.visible = false;
         this.addChild(this.jiazhuBtn);
         this.rangpaiBtn = new Button("",null,75,UIManager.getMultiUISkin("rangpai"));
         this.rangpaiBtn.x = this.jiazhuBtn.x + this.jiazhuBtn.width + 32;
         this.rangpaiBtn.y = Core.stgH / 2 + 97;
         this.rangpaiBtn.visible = false;
         this.addChild(this.rangpaiBtn);
         this.kaipaiBtn = new Button("",null,75,UIManager.getMultiUISkin("kaipaiBtn"));
         this.kaipaiBtn.x = this.jiazhuBtn.x + this.jiazhuBtn.width + 32;
         this.kaipaiBtn.y = Core.stgH / 2 + 97;
         this.kaipaiBtn.visible = false;
         this.addChild(this.kaipaiBtn);
         this.quanxiaBtn = new Button("",null,75,UIManager.getMultiUISkin("quanxia"));
         this.quanxiaBtn.x = this.rangpaiBtn.x + this.rangpaiBtn.width + 32;
         this.quanxiaBtn.y = Core.stgH / 2 + 97;
         this.quanxiaBtn.visible = false;
         this.addChild(this.quanxiaBtn);
         this.fangqiBtn = new Button("",null,75,UIManager.getMultiUISkin("fangqi"));
         this.fangqiBtn.x = this.quanxiaBtn.x + this.quanxiaBtn.width + 32;
         this.fangqiBtn.y = Core.stgH / 2 + 97;
         this.fangqiBtn.visible = false;
         this.addChild(this.fangqiBtn);
         this.matchingBtn = new Button("",null,100,UIManager.getMultiUISkin("matchingBtn"));
         this.matchingBtn.x = 494;
         this.matchingBtn.y = 476;
         this.addChild(this.matchingBtn);
         this.opponentSK = UIManager.getUISkin("defaultBody_card");
         this.opponentSK.x = 672;
         this.opponentSK.y = 183;
         this.addChild(this.opponentSK);
         this.opponentBGSK = UIManager.getUISkin("text_bg");
         this.opponentBGSK.x = 445;
         this.opponentBGSK.y = 448;
         this.opponentBGSK.width = 210;
         this.opponentBGSK.height = 25;
         this.opponentBGSK.visible = false;
         this.addChild(this.opponentBGSK);
         this.opponentNameTF = new Label("",TextFormatLib.format_0xFFED89_12px_center,[FilterLib.glow_0x272727],false);
         this.opponentNameTF.width = 120;
         this.opponentNameTF.height = 22;
         this.opponentNameTF.x = 490;
         this.opponentNameTF.y = 450;
         this.opponentNameTF.visible = false;
         this.addChild(this.opponentNameTF);
         this.mathingMC = AssetManager.getMc("MathingMC");
         this.mathingMC.x = 814;
         this.mathingMC.y = 330;
         this.mathingMC.stop();
         this.mathingMC.visible = false;
         this.addChild(this.mathingMC);
         this.countDownSK = UIManager.getUISkin("daojishi");
         this.countDownSK.x = Core.stgW - 370;
         this.countDownSK.y = Core.stgH - 20;
         this.addChild(this.countDownSK);
         this.cdSK = UIManager.getUISkin("prepareBG");
         this.cdSK.x = Core.stgW - 380;
         this.cdSK.y = Core.stgH - 94;
         this.addChild(this.cdSK);
         this.countDownTxt = new Label("0",TextFormatLib.format_0xFFFFFF_39px_b,[FilterLib.glow_0x642400]);
         this.countDownTxt.x = this.cdSK.x + 4;
         this.countDownTxt.y = this.cdSK.y + 5;
         this.addChild(this.countDownTxt);
         this.cardBG1 = UIManager.getUISkin("card_bg_2");
         this.cardBG1.x = Core.stgW / 2 - 120;
         this.cardBG1.y = 30;
         this.cardBG1.visible = false;
         this.addChild(this.cardBG1);
         this.cardBG2 = UIManager.getUISkin("card_bg_2");
         this.cardBG2.x = Core.stgW / 2 + 40;
         this.cardBG2.y = 30;
         this.cardBG2.visible = false;
         this.addChild(this.cardBG2);
         this.noPrepareSK = UIManager.getUISkin("noPrepareBtn");
         this.noPrepareSK.x = Core.stgW / 2 - 110;
         this.noPrepareSK.y = 45;
         this.noPrepareSK.visible = false;
         this.addChild(this.noPrepareSK);
         this.prepareSK = UIManager.getUISkin("prepareBtn");
         this.prepareSK.x = Core.stgW / 2 - 110;
         this.prepareSK.y = 45;
         this.prepareSK.visible = false;
         this.addChild(this.prepareSK);
         this.myPrepareBtn = new Button("",null,120,UIManager.getMultiUISkin("prepareCountDown"));
         this.myPrepareBtn.x = Core.stgW / 2 - 110;
         this.myPrepareBtn.y = Core.stgH - 90;
         this.myPrepareBtn.visible = false;
         this.addChild(this.myPrepareBtn);
         this.myPrepareSK = UIManager.getUISkin("prepareBtn");
         this.myPrepareSK.x = Core.stgW / 2 - 110;
         this.myPrepareSK.y = Core.stgH - 90;
         this.myPrepareSK.visible = false;
         this.addChild(this.myPrepareSK);
         this.frostPanel = new FrostPanel();
         this.frostPanel.x = 0;
         this.frostPanel.y = 255;
         this.addChild(this.frostPanel);
         this.timer = new Timer(1000);
         this.timer.addEventListener("timer",this.onTimerHandler);
         this.waitTimer = new Timer(1000);
         this.waitTimer.addEventListener("timer",this.onWaitTimerHandler);
         this.maskSP = new Sprite();
         this.maskSP.graphics.beginFill(16711680,0);
         this.maskSP.graphics.drawRect(0,0,Core.stgW,Core.stgH);
         this.maskSP.graphics.endFill();
         this.maskSP.visible = false;
         this.addChild(this.maskSP);
         this.cancelMatchBtn = new Button("",null,120,UIManager.getMultiUISkin("cancelMatchBtn"));
         this.cancelMatchBtn.x = Core.stgW / 2 - 110;
         this.cancelMatchBtn.y = Core.stgH - 90;
         this.maskSP.addChild(this.cancelMatchBtn);
         this.prepareInfo = UIManager.getUISkin("prepareInfo");
         this.prepareInfo.x = Core.stgW / 2 - 140;
         this.prepareInfo.y = Core.stgH - 170;
         this.maskSP.addChild(this.prepareInfo);
         this.tipsTF = new Label("",TextFormatLib.format_0xFFF000_26px,[FilterLib.glow_0x272727]);
         this.tipsTF.x = Core.stgW / 2 + 6;
         this.tipsTF.y = Core.stgH / 2 + 97;
         this.tipsTF.visible = false;
         this.addChild(this.tipsTF);
         this.waitSK = UIManager.getUISkin("wait_card");
         this.waitSK.x = Core.stgW / 2 - 120;
         this.waitSK.y = Core.stgH / 2 + 107;
         this.waitSK.visible = false;
         this.addChild(this.waitSK);
         this.helpBtn = new Button("",null,120,UIManager.getMultiUISkin("soul_Help_button"));
         this.helpBtn.x = Core.stgW - 130;
         this.helpBtn.y = 17;
         this.addChild(this.helpBtn);
         this.tujian = new Button("",null,120,UIManager.getMultiUISkin("card_tujian"));
         this.tujian.x = Core.stgW - 178;
         this.tujian.y = 423;
         this.addChild(this.tujian);
         this.setCountDown(-1);
         this.setWaitCountDown(-1);
         this.frostPanel.setData();
         AppFacade.instance.registerMediator(new CardMediator(this));
      }
      
      public function stopEffect() : void
      {
         if(this.effectMC != null)
         {
            this.effectMC.stop();
            this.effectMC.addFrameScript(this.effectMC.totalFrames - 1,null);
            this.effectMC.parent && this.effectMC.parent.removeChild(this.effectMC);
            this.effectMC = null;
         }
      }
      
      public function setEffectAndPlay(param1:String, param2:Function) : void
      {
         var effectName:String = param1;
         var callbackFun:Function = param2;
         if(this.effectMC != null)
         {
            this.effectMC.stop();
            this.effectMC.addFrameScript(this.effectMC.totalFrames - 1,null);
            this.effectMC.parent && this.effectMC.parent.removeChild(this.effectMC);
            this.effectMC = null;
         }
         this.effectMC = AssetManager.getMc(effectName);
         this.effectMC.x = Core.stgW / 2;
         this.effectMC.y = Core.stgH / 2;
         this.effectMC.addFrameScript(this.effectMC.totalFrames - 1,(function():*
         {
            var onEffectComplete:Function;
            return onEffectComplete = function():void
            {
               callbackFun();
               effectMC.stop();
               effectMC.addFrameScript(effectMC.totalFrames - 1,null);
               effectMC.parent && effectMC.parent.removeChild(effectMC);
            };
         })());
         addChild(this.effectMC);
         this.effectMC.play();
      }
      
      private function onTimerHandler(param1:TimerEvent) : void
      {
         this.countDown--;
         if(this.countDown < 0)
         {
            this.timer.reset();
         }
         this.setCountDown(this.countDown);
      }
      
      private function onWaitTimerHandler(param1:TimerEvent) : void
      {
         this.waitCountDwon--;
         if(this.waitCountDwon < 0)
         {
            this.waitTimer.reset();
         }
         this.setWaitCountDown(this.waitCountDwon);
      }
      
      override public function show(param1:Object) : void
      {
         super.show(param1);
         this.showHandler && this.showHandler(param1);
      }
      
      override public function close() : void
      {
         if(this._bgimage.bitmapData)
         {
            this._bgimage.bitmapData.dispose();
            this._bgimage.bitmapData = null;
         }
         this._bgimage = null;
         this.closeHandler && this.closeHandler();
         super.close();
      }
      
      override public function dispose() : void
      {
         AppFacade.instance.removeMediator("CardMediator");
         super.dispose();
      }
      
      private function onSelectHandler(param1:MouseEvent) : void
      {
         if(this.selectCard != param1.currentTarget as Card)
         {
            if(this.selectCard != null)
            {
               this.selectCard.select = false;
            }
            this.selectCard = param1.currentTarget as Card;
            this.selectCard.select = true;
            openModule("CardHeroInfo",false,this.selectCard.htid,false);
         }
      }
      
      public function setCountDown(param1:int) : void
      {
         this.countDown = param1;
         if(param1 < 0)
         {
            this.countDownSK.visible = false;
            this.cdSK.visible = false;
            this.countDownTxt.visible = false;
            this.timer.reset();
         }
         else
         {
            this.timer.start();
            this.countDownTxt.visible = true;
            this.cdSK.visible = true;
            this.countDownSK.visible = true;
            if(param1 < 10)
            {
               this.countDownTxt.text = "0" + param1;
            }
            else
            {
               this.countDownTxt.text = "" + param1;
            }
         }
      }
      
      public function setWaitCountDown(param1:int) : void
      {
         this.waitCountDwon = param1;
         if(param1 < 0)
         {
            this.tipsTF.visible = false;
            this.waitSK.visible = false;
            this.waitTimer.reset();
         }
         else
         {
            this.waitTimer.start();
            this.tipsTF.visible = true;
            this.waitSK.visible = true;
            this.tipsTF.text = "" + param1;
         }
      }
      
      public function setOpponent() : void
      {
         this.takeBtn.enabled = false;
         this.buyBtn.enabled = false;
         this.savingBtn.enabled = false;
         this.opponent.setData(CardManager.getInstance().opponent);
         this.opponent.visible = true;
         this.myself.setData(CardManager.getInstance().myself);
         this.myself.visible = true;
         this.opponentNameTF.visible = true;
         this.opponentBGSK.visible = true;
         this.opponentNameTF.text = CardManager.getInstance().opponent.name;
         this.mathingMC.gotoAndStop("body" + CardManager.getInstance().opponent.bodyID);
         this.myPrepareBtn.visible = true;
         this.noPrepareSK.visible = true;
         this.myPrepareSK.visible = false;
         this.prepareSK.visible = false;
         this.setCountDown(this.cardManager.getPreparetime());
      }
      
      public function setCardInfo() : void
      {
         var _loc2_:Card = null;
         this.isUserLogOff = false;
         CardManager.getInstance().played++;
         this.residue.setData();
         this.cardManager.opponent.wagertimes = 0;
         this.cardManager.opponent.wager = this.cardManager.getAnte();
         this.cardManager.myself.wagertimes = 0;
         this.cardManager.myself.wager = this.cardManager.getAnte();
         this.cardManager.myself.bankChip -= this.cardManager.getAnte();
         this.opAdd = 0;
         this.myAdd = 0;
         this.wagerHandler();
         this.sendOppent = false;
         this.sendMyself = false;
         this.cardCount = 0;
         this.cardList = [];
         var _loc4_:int = 0;
         while(_loc4_ < 5)
         {
            _loc2_ = new Card();
            _loc2_.addEventListener("click",this.onSelectHandler);
            _loc2_.x = 0 - _loc2_.width / 2;
            _loc2_.visible = true;
            this.cardList.push(_loc2_);
            _loc4_++;
         }
         this.myCard1 = new Card();
         this.myCard1.addEventListener("click",this.onSelectHandler);
         this.myCard1.setY(this.myCard1.y);
         this.myCard2 = new Card();
         this.myCard2.addEventListener("click",this.onSelectHandler);
         this.myCard2.setY(this.myCard2.y);
         this.myself.setData(CardManager.getInstance().myself);
         this.opponent.setData(CardManager.getInstance().opponent);
         this.myCard1.setData(this.cardManager.myself.cardList[0]);
         this.myCard2.setData(this.cardManager.myself.cardList[1]);
         var _loc3_:int = int(this.cardList.length);
         var _loc1_:int = 0;
         while(_loc1_ < _loc3_)
         {
            (this.cardList[_loc1_] as Card).select = false;
            (this.cardList[_loc1_] as Card).setData(this.cardManager.commonCard[_loc1_]);
            _loc1_++;
         }
         this.myCard1.select = false;
         this.myCard2.select = false;
         if(this.cardManager.myself.isBanker)
         {
            this.sendOpponentCard();
            this.setCountDown(-1);
            this.setWaitCountDown(this.cardManager.getChiptime());
         }
         else
         {
            this.sendMyselfCard();
            this.setCountDown(this.cardManager.getChiptime());
            this.setWaitCountDown(-1);
         }
      }
      
      private function sendOpponentCard() : void
      {
         var dealMC1:MovieClip;
         var dealMC2:MovieClip = null;
         var send:Function = null;
         send = function():void
         {
            if(isUserLogOff)
            {
               return;
            }
            sendOppent = true;
            dealMC2.play();
            TweenMax.to(dealMC2,0.6,{
               "x":cardBG2.x - dealMC2.width / 2,
               "y":cardBG2.y + dealMC2.height / 2,
               "onComplete":sendOtherCards
            });
         };
         if(this.isUserLogOff)
         {
            return;
         }
         dealMC1 = AssetManager.getMc("dealMC");
         dealMC1.x = Core.stgW - 220 + dealMC1.width / 2;
         dealMC1.y = 250 + dealMC1.height / 2;
         dealMC1.stop();
         this.movieMC.addChild(dealMC1);
         dealMC2 = AssetManager.getMc("dealMC");
         dealMC2.x = Core.stgW - 220 + dealMC2.width / 2;
         dealMC2.y = 250 + dealMC2.height / 2;
         dealMC2.stop();
         this.movieMC.addChild(dealMC2);
         dealMC1.play();
         TweenMax.to(dealMC1,0.6,{
            "x":this.cardBG1.x - dealMC1.width / 2,
            "y":this.cardBG1.y + dealMC1.height / 2,
            "onComplete":send
         });
      }
      
      private function sendMyselfCard() : void
      {
         var dealMC3:MovieClip = null;
         var dealMC4:MovieClip = null;
         var send:Function = null;
         var movieComplete:Function = null;
         send = function():void
         {
            if(isUserLogOff)
            {
               return;
            }
            var _loc1_:MovieClip = AssetManager.getMc("turningMC");
            _loc1_.x = dealMC3.x;
            _loc1_.y = dealMC3.y;
            myCard1.visible = true;
            _loc1_.box.addChild(myCard1);
            movieMC.addChild(_loc1_);
            dealMC3.stop();
            dealMC3.parent && dealMC3.parent.removeChild(dealMC3);
            sendMyself = true;
            dealMC4.play();
            TweenMax.to(dealMC4,0.6,{
               "x":Core.stgW / 2 + 175 - dealMC4.width / 2,
               "y":Core.stgH - 180 + dealMC4.height / 2,
               "onComplete":movieComplete
            });
         };
         movieComplete = function():void
         {
            if(isUserLogOff)
            {
               return;
            }
            sendOtherCards();
            var _loc1_:MovieClip = AssetManager.getMc("turningMC");
            _loc1_.x = dealMC4.x;
            _loc1_.y = dealMC4.y;
            myCard2.visible = true;
            _loc1_.box.addChild(myCard2);
            movieMC.addChild(_loc1_);
            dealMC4.stop();
            dealMC4.parent && dealMC4.parent.removeChild(dealMC4);
         };
         if(this.isUserLogOff)
         {
            return;
         }
         this.myCard1.setY(this.myCard1.y);
         this.myCard2.setY(this.myCard2.y);
         dealMC3 = AssetManager.getMc("dealMC");
         dealMC3.x = Core.stgW - 220 + dealMC3.width / 2;
         dealMC3.y = 250 + dealMC3.height / 2;
         dealMC3.stop();
         this.movieMC.addChild(dealMC3);
         dealMC4 = AssetManager.getMc("dealMC");
         dealMC4.x = Core.stgW - 220 + dealMC4.width / 2;
         dealMC4.y = 250 + dealMC4.height / 2;
         dealMC4.stop();
         this.movieMC.addChild(dealMC4);
         dealMC3.play();
         TweenMax.to(dealMC3,0.6,{
            "x":Core.stgW / 2 + 10 - dealMC3.width / 2,
            "y":Core.stgH - 180 + dealMC3.height / 2,
            "onComplete":send
         });
         this.myCard1.x = 0 - this.myCard1.width / 2;
         this.myCard2.x = 0 - this.myCard2.width / 2;
      }
      
      private function sendOtherCards() : void
      {
         var effComplete:Function;
         var len:int = 0;
         var dealMC0:MovieClip = null;
         if(this.isUserLogOff)
         {
            return;
         }
         if(this.sendMyself && this.sendOppent)
         {
            effComplete = function(param1:MovieClip, param2:Card):void
            {
               if(isUserLogOff)
               {
                  return;
               }
               sendOtherCards();
               var _loc3_:MovieClip = AssetManager.getMc("turningMC");
               _loc3_.x = param1.x;
               _loc3_.y = param1.y;
               param2.visible = true;
               _loc3_.box.addChild(param2);
               movieMC.addChild(_loc3_);
               param1.parent && param1.parent.removeChild(param1);
            };
            len = int(this.cardList.length);
            if(this.cardCount < len)
            {
               dealMC0 = AssetManager.getMc("dealMC");
               dealMC0.x = Core.stgW - 220 + dealMC0.width / 2;
               dealMC0.y = 250 + dealMC0.height / 2;
               dealMC0.stop();
               this.movieMC.addChild(dealMC0);
               dealMC0.play();
               TweenMax.to(dealMC0,0.6,{
                  "x":Core.stgW / 2 - 525 + this.cardCount * 163 + (this.cardList[this.cardCount] as Card).width / 2,
                  "y":Core.stgH / 2,
                  "onComplete":effComplete,
                  "onCompleteParams":[dealMC0,this.cardList[this.cardCount] as Card]
               });
               (this.cardList[this.cardCount] as Card).setY((this.cardList[this.cardCount] as Card).y);
               this.cardCount++;
            }
            else
            {
               if(this.isUserLogOff)
               {
                  return;
               }
               this.chipInfo1.visible = true;
               this.chipInfo2.visible = true;
               if(this.cardManager.myself.isBanker == true)
               {
                  this.jiazhuBtn.visible = false;
                  this.genzhuBtn.visible = false;
                  this.rangpaiBtn.visible = false;
                  this.kaipaiBtn.visible = false;
                  this.quanxiaBtn.visible = false;
                  this.fangqiBtn.visible = false;
                  this.anteBtnBG.visible = false;
               }
               else
               {
                  this.setEffectAndPlay("myselfStep",function():void
                  {
                     showAnteBtn(true);
                     kaipaiBtn.visible = false;
                     genzhuBtn.enabled = false;
                     jiazhuBtn.enabled = true;
                     rangpaiBtn.enabled = true;
                     quanxiaBtn.enabled = true;
                  });
               }
            }
            return;
         }
         if(this.sendOppent)
         {
            this.sendMyselfCard();
         }
         else if(this.sendMyself)
         {
            this.sendOpponentCard();
         }
      }
      
      public function userLogOff() : void
      {
         CardManager.getInstance().myself.currentState = 4;
         this.isUserLogOff = true;
         AppFacade.instance.sendNotification("HANDLE_MODULE",new ModuleParams("CardHeroInfo",ModuleParams.act_Close));
         this.matchingBtn.visible = true;
         this.cardBG1.visible = false;
         this.cardBG2.visible = false;
         this.myPrepareBtn.visible = false;
         this.noPrepareSK.visible = false;
         this.myPrepareSK.visible = false;
         this.prepareSK.visible = false;
         this.opponentNameTF.visible = false;
         this.opponentBGSK.visible = false;
         this.opponentSK.visible = true;
         this.takeBtn.enabled = true;
         this.buyBtn.enabled = true;
         this.savingBtn.enabled = true;
         this.exitBtn.enabled = true;
         this.showAnteBtn(false);
         this.chipInfo1.visible = false;
         this.chipInfo2.visible = false;
         this.setCountDown(-1);
         this.setWaitCountDown(-1);
         this.vsSK.visible = true;
         this.myBodySK.visible = true;
         this.opponent.resetData();
         this.myself.resetData(true);
         this.movieMC.clearAllChild(this.movieMC);
         this.myRang = false;
         this.opponentRang = false;
         this.myAdd = 0;
         this.opAdd = 0;
      }
      
      private function showAnteBtn(param1:Boolean) : void
      {
         this.jiazhuBtn.visible = param1;
         this.genzhuBtn.visible = param1;
         this.rangpaiBtn.visible = param1;
         this.quanxiaBtn.visible = param1;
         this.fangqiBtn.visible = param1;
         this.anteBtnBG.visible = param1;
         this.kaipaiBtn.visible = param1;
      }
      
      public function wagerHandler() : void
      {
         this.chipInfo1.setData(this.cardManager.opponent);
         this.chipInfo2.setData(this.cardManager.myself);
      }
      
      public function betOperation(param1:int, param2:int, param3:int, param4:Number) : void
      {
         var type:int = param1;
         var count:int = param2;
         var island_val:int = param3;
         var uuid:Number = param4;
         if(uuid == CardManager.getInstance().myself.uuid)
         {
            this.updateMine(type,count,island_val,uuid);
            this.setEffectAndPlay("opponentStep",function():void
            {
            });
            return;
         }
         this.cardManager.opponent.wagertimes = count;
         this.oldwager = this.cardManager.opponent.wager;
         this.cardManager.opponent.wager = island_val;
         this.setEffectAndPlay("myselfStep",function():void
         {
            setWaitCountDown(-1);
            setCountDown(cardManager.getChiptime());
            jiazhuBtn.visible = true;
            genzhuBtn.visible = true;
            rangpaiBtn.visible = true;
            quanxiaBtn.visible = true;
            fangqiBtn.visible = true;
            anteBtnBG.visible = true;
            updateOpponent(type,count,island_val,uuid);
         });
      }
      
      private function updateMine(param1:int, param2:int, param3:int, param4:Number) : void
      {
         this.setCountDown(-1);
         this.setWaitCountDown(this.cardManager.getChiptime());
         this.jiazhuBtn.visible = false;
         this.genzhuBtn.visible = false;
         this.rangpaiBtn.visible = false;
         this.quanxiaBtn.visible = false;
         this.fangqiBtn.visible = false;
         this.anteBtnBG.visible = false;
         this.kaipaiBtn.visible = false;
         var _loc5_:String = Globalization.getString("card.13");
         switch(param1 - 1)
         {
            case 0:
               this.cardManager.myself.bankChip -= param3 - this.cardManager.myself.wager;
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.16"),_loc5_,"<font color=\'#FFF000\'>" + (param3 - this.cardManager.myself.wager) + "</font>"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
               break;
            case 1:
               this.cardManager.myself.bankChip -= param3 - this.cardManager.myself.wager;
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.15"),_loc5_,"<font color=\'#FFF000\'>" + (param3 - this.cardManager.myself.wager) + "</font>"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
               break;
            case 2:
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.17"),_loc5_,this.cardManager.opponent.name),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
               break;
            case 3:
               this.cardManager.myself.bankChip -= param3 - this.cardManager.myself.wager;
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.27"),_loc5_,"<font color=\'#FFF000\'>" + param3 + "</font>"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
               break;
            case 4:
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("card.69"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
         }
         this.cardManager.myself.wagertimes = param2;
         this.cardManager.myself.wager = param3;
         if(param1 == 3)
         {
            this.myRang = true;
         }
         if(param1 == 2)
         {
            this.myAdd += 1;
         }
         this.wagerHandler();
         this.tipsTF.visible = true;
      }
      
      private function updateOpponent(param1:int, param2:int, param3:int, param4:Number) : void
      {
         this.jiazhuBtn.enabled = false;
         this.genzhuBtn.enabled = false;
         this.rangpaiBtn.enabled = false;
         this.kaipaiBtn.enabled = false;
         this.quanxiaBtn.enabled = false;
         this.fangqiBtn.enabled = true;
         var _loc5_:String = this.cardManager.opponent.name;
         switch(param1 - 1)
         {
            case 0:
               if(this.myAdd < this.maxAdd)
               {
                  this.jiazhuBtn.enabled = true;
                  if(this.cardManager.myself.bankChip < this.cardManager.getMinraise())
                  {
                     this.jiazhuBtn.enabled = false;
                  }
                  this.quanxiaBtn.enabled = true;
               }
               if(this.opponentRang)
               {
                  this.kaipaiBtn.enabled = true;
                  this.kaipaiBtn.visible = true;
                  this.rangpaiBtn.visible = false;
                  this.rangpaiBtn.enabled = false;
               }
               else
               {
                  this.rangpaiBtn.visible = true;
                  this.rangpaiBtn.enabled = true;
                  this.kaipaiBtn.enabled = false;
                  this.kaipaiBtn.visible = false;
               }
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.16"),_loc5_,"<font color=\'#FFF000\'>" + (param3 - this.oldwager) + "</font>"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
               break;
            case 1:
               if(param3 - this.oldwager > this.cardManager.myself.bankChip)
               {
                  this.quanxiaBtn.enabled = true;
               }
               else
               {
                  this.genzhuBtn.enabled = true;
               }
               if(this.cardManager.myself.wagertimes < CardManager.getInstance().getRaisetimes() && !this.myRang)
               {
                  this.jiazhuBtn.enabled = true;
               }
               else
               {
                  this.jiazhuBtn.enabled = false;
               }
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.15"),_loc5_,"<font color=\'#FFF000\'>" + (param3 - this.oldwager) + "</font>"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
               break;
            case 2:
               this.kaipaiBtn.enabled = true;
               this.kaipaiBtn.visible = true;
               this.rangpaiBtn.visible = false;
               if(this.myAdd < this.maxAdd)
               {
                  this.jiazhuBtn.enabled = true;
                  if(this.cardManager.myself.bankChip < this.cardManager.getMinraise())
                  {
                     this.jiazhuBtn.enabled = false;
                  }
                  this.quanxiaBtn.enabled = true;
               }
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.17"),_loc5_,Globalization.getString("card.13")),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
               break;
            case 3:
               this.quanxiaBtn.enabled = true;
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.27"),_loc5_,"<font color=\'#FFF000\'>" + param3 + "</font>"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
               break;
            case 4:
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.27"),_loc5_,Globalization.getString("card.13")),
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":2
               });
         }
         if(param1 == 3)
         {
            this.opponentRang = true;
         }
         if(param1 == 2)
         {
            this.opAdd += 1;
         }
         this.wagerHandler();
         this.tipsTF.visible = false;
      }
      
      public function resize() : void
      {
         this.anteBtnBG.x = Core.stgW / 2 - 165;
         this.anteBtnBG.y = Core.stgH / 2 + 88;
         this.genzhuBtn.x = Core.stgW / 2 - 130;
         this.genzhuBtn.y = Core.stgH / 2 + 97;
         this.jiazhuBtn.x = this.genzhuBtn.x + this.genzhuBtn.width + 32;
         this.jiazhuBtn.y = Core.stgH / 2 + 97;
         this.rangpaiBtn.x = this.jiazhuBtn.x + this.jiazhuBtn.width + 32;
         this.rangpaiBtn.y = Core.stgH / 2 + 97;
         this.kaipaiBtn.x = this.jiazhuBtn.x + this.jiazhuBtn.width + 32;
         this.kaipaiBtn.y = Core.stgH / 2 + 97;
         this.quanxiaBtn.x = this.rangpaiBtn.x + this.rangpaiBtn.width + 32;
         this.quanxiaBtn.y = Core.stgH / 2 + 97;
         this.fangqiBtn.x = this.quanxiaBtn.x + this.quanxiaBtn.width + 32;
         this.fangqiBtn.y = Core.stgH / 2 + 97;
         this.chipInfo1.x = Core.stgW / 2 + 60;
         this.chipInfo1.y = 155;
         this.chipInfo2.x = 295;
         this.chipInfo2.y = Core.stgH - 215;
         this.exitBtn.x = Core.stgW - 65;
         this.exitBtn.y = 15;
         this.savingBtn.x = Core.stgW - 200;
         this.savingBtn.y = 16;
         this.takeBtn.x = Core.stgW - 265;
         this.takeBtn.y = 17;
         this.buyBtn.x = Core.stgW - 345;
         this.buyBtn.y = 20;
         this.residue.x = Core.stgW - 317;
         this.residue.y = Core.stgH - 65;
         this.opponent.x = Core.stgW / 2 + 80;
         this.opponent.y = 15;
         this.myself.x = Core.stgW / 2 - 330;
         this.myself.y = Core.stgH - 100;
         this.helpBtn.x = Core.stgW - 130;
         this.helpBtn.y = 17;
         this.cardlistSK.x = Core.stgW - 224;
         this.cardlistSK.y = 250;
         this.logoSK.x = Core.stgW - 193;
         this.logoSK.y = 133;
         this.tipsTF.x = Core.stgW / 2 + 6;
         this.tipsTF.y = Core.stgH / 2 + 97;
         this.waitSK.x = Core.stgW / 2 - 120;
         this.waitSK.y = Core.stgH / 2 + 107;
         this.tujian.x = Core.stgW - 178;
         this.tujian.y = 423;
      }
   }
}

