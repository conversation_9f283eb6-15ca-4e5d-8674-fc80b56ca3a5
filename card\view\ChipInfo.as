package card.view
{
   import game.manager.UIManager;
   import game.modules.card.manager.CardManager;
   import game.modules.card.manager.Gamester;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import util.Globalization;
   
   public class ChipInfo extends UISprite
   {
      public var surplusWagerTxt:Label;
      
      public var wagerTxt:Label;
      
      public function ChipInfo()
      {
         super();
         var _loc4_:UISkin = UIManager.getUISkin("black_bg3");
         _loc4_.width = 155;
         _loc4_.height = 65;
         this.addChild(_loc4_);
         var _loc3_:UISkin = UIManager.getUISkin("line2");
         _loc3_.width = 145;
         _loc3_.x = 5;
         _loc3_.y = 34;
         this.addChild(_loc3_);
         var _loc1_:Label = new Label(Globalization.getString("card.43"),TextFormatLib.format_0xFFB932_14px,[FilterLib.glow_0x272727]);
         _loc1_.x = 10;
         _loc1_.y = 10;
         this.addChild(_loc1_);
         this.surplusWagerTxt = new Label("",TextFormatLib.format_0xFFF600_14px,[FilterLib.glow_0x272727]);
         this.surplusWagerTxt.x = 90;
         this.surplusWagerTxt.y = 9;
         this.surplusWagerTxt.text = "" + CardManager.getInstance().getWagertimes();
         this.addChild(this.surplusWagerTxt);
         var _loc2_:Label = new Label(Globalization.getString("card.42"),TextFormatLib.format_0xFFB932_14px,[FilterLib.glow_0x272727]);
         _loc2_.x = 10;
         _loc2_.y = 40;
         this.addChild(_loc2_);
         this.wagerTxt = new Label("",TextFormatLib.format_0xE0FFFE_18px,[FilterLib.glow_0x00A8FF]);
         this.wagerTxt.x = 90;
         this.wagerTxt.y = 36;
         this.wagerTxt.text = "0";
         this.addChild(this.wagerTxt);
      }
      
      public function setData(param1:Gamester) : void
      {
         this.surplusWagerTxt.text = "" + (CardManager.getInstance().getRaisetimes() - param1.wagertimes);
         this.wagerTxt.text = "" + param1.wager;
      }
   }
}

