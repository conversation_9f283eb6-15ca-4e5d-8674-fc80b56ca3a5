package card.view
{
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.text.TextFormat;
   import flash.utils.Timer;
   import game.manager.UIManager;
   import game.modules.card.manager.CardManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.TextButton;
   import mmo.ui.control.input.TextInput;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class GoldCompeteWindow extends PopUpWindow
   {
      public static const NAME:String = "GoldCompeteWindow";
      
      public var subminBtn:Button;
      
      public var cancelBtn:Button;
      
      public var duifangTxt:Label;
      
      public var benfangTxt:Label;
      
      public var fangzhuSK1:UISkin;
      
      public var fangzhuSK2:UISkin;
      
      public var textInput:TextInput;
      
      public var addGoldBtn1:Button;
      
      public var addGoldBtn2:Button;
      
      public var addGoldBtn3:Button;
      
      public var addGoldBtn4:Button;
      
      private var numBG:UISkin;
      
      private var timer:Timer;
      
      private var timeCount:int;
      
      private var bgSP:UISprite;
      
      private var goldEmptyBtn:TextButton;
      
      private var zuizhong1:UISkin;
      
      private var zuizhong2:UISkin;
      
      public function GoldCompeteWindow()
      {
         super(320,315,UIManager.getUISkin("win_guide"),false);
         this.isLive = false;
         this.setTitleImageData(UIManager.getUISkin("title_GoldCompete").bitmapData);
         this.canEscClose = false;
         var _loc10_:UISkin = UIManager.getUISkin("group_bg");
         _loc10_.x = 5;
         _loc10_.y = 0;
         _loc10_.width = 300;
         _loc10_.height = 240;
         pane.addChild(_loc10_);
         var _loc9_:TextFormat = TextFormatLib.format_0xFFED89_12px;
         _loc9_.leading = 4;
         var _loc7_:Label = new Label(Globalization.getString("card.44"),_loc9_,[FilterLib.glow_0x272727],false);
         _loc7_.width = 280;
         _loc7_.height = 45;
         _loc7_.x = 15;
         _loc7_.y = 7;
         _loc7_.wordWrap = true;
         pane.addChild(_loc7_);
         _loc9_.leading = 0;
         var _loc8_:UISkin = UIManager.getUISkin("black_bg3");
         _loc8_.x = 12;
         _loc8_.y = 55;
         _loc8_.width = 283;
         _loc8_.height = 100;
         pane.addChild(_loc8_);
         var _loc1_:UISkin = UIManager.getUISkin("duif");
         _loc1_.x = 25;
         _loc1_.y = 80;
         pane.addChild(_loc1_);
         var _loc3_:UISkin = UIManager.getUISkin("line2");
         _loc3_.x = 15;
         _loc3_.y = 108;
         _loc3_.width = 210;
         pane.addChild(_loc3_);
         var _loc6_:UISkin = UIManager.getUISkin("benfang");
         _loc6_.x = 25;
         _loc6_.y = 120;
         pane.addChild(_loc6_);
         this.duifangTxt = new Label("0",TextFormatLib.format_0x00FF00_12px,[FilterLib.glow_0x272727]);
         this.duifangTxt.x = 96;
         this.duifangTxt.y = 82;
         pane.addChild(this.duifangTxt);
         this.benfangTxt = new Label("0",TextFormatLib.format_0xFF0000_12px,[FilterLib.glow_0x272727]);
         this.benfangTxt.x = 96;
         this.benfangTxt.y = 122;
         pane.addChild(this.benfangTxt);
         this.fangzhuSK1 = UIManager.getUISkin("fangzhu");
         this.fangzhuSK1.x = 150;
         this.fangzhuSK1.y = 77;
         pane.addChild(this.fangzhuSK1);
         this.fangzhuSK2 = UIManager.getUISkin("fangzhu");
         this.fangzhuSK2.x = 150;
         this.fangzhuSK2.y = 117;
         pane.addChild(this.fangzhuSK2);
         var _loc5_:UISkin = UIManager.getUISkin("daojishi");
         _loc5_.x = 218;
         _loc5_.y = 125;
         pane.addChild(_loc5_);
         this.textInput = new TextInput("0",60,TextFormatLib.format_0xFFED89_12px);
         this.textInput.x = 18;
         this.textInput.y = 205;
         this.textInput.width = 50;
         this.textInput.restrict = "0-9";
         this.textInput.addEventListener("change",this.onTextChangeHandler);
         pane.addChild(this.textInput);
         var _loc4_:Label = new Label(Globalization.getString("card.45"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc4_.x = 15;
         _loc4_.y = 170;
         pane.addChild(_loc4_);
         this.addGoldBtn1 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipblue"));
         this.addGoldBtn1.x = 115;
         this.addGoldBtn1.y = 185;
         this.addGoldBtn1.text = this.formatTxt(CardManager.getInstance().getGold()[0]);
         this.addGoldBtn1.name = CardManager.getInstance().getGold()[0];
         pane.addChild(this.addGoldBtn1);
         this.addGoldBtn2 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipyellow"));
         this.addGoldBtn2.x = 160;
         this.addGoldBtn2.y = 185;
         this.addGoldBtn2.text = this.formatTxt(CardManager.getInstance().getGold()[1]);
         this.addGoldBtn2.name = CardManager.getInstance().getGold()[1];
         pane.addChild(this.addGoldBtn2);
         this.addGoldBtn3 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipred"));
         this.addGoldBtn3.x = 205;
         this.addGoldBtn3.y = 185;
         this.addGoldBtn3.text = this.formatTxt(CardManager.getInstance().getGold()[2]);
         this.addGoldBtn3.name = CardManager.getInstance().getGold()[2];
         pane.addChild(this.addGoldBtn3);
         this.addGoldBtn4 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChippurple"));
         this.addGoldBtn4.x = 250;
         this.addGoldBtn4.y = 185;
         this.addGoldBtn4.text = this.formatTxt(CardManager.getInstance().getGold()[3]);
         this.addGoldBtn4.name = CardManager.getInstance().getGold()[3];
         pane.addChild(this.addGoldBtn4);
         this.subminBtn = new Button(Globalization.getString("card.46"),null,60,UIManager.getMultiUISkin("btn_topMenu"));
         this.subminBtn.setTextOffset(0,-2);
         this.subminBtn.x = 68;
         this.subminBtn.y = 250;
         pane.addChild(this.subminBtn);
         this.cancelBtn = new Button(Globalization.getString("card.47"),null,60,UIManager.getMultiUISkin("btn_topMenu"));
         this.cancelBtn.setTextOffset(0,-2);
         this.cancelBtn.x = 178;
         this.cancelBtn.y = 250;
         pane.addChild(this.cancelBtn);
         this.numBG = UIManager.getUISkin("numBG");
         this.numBG.x = 208;
         this.numBG.y = 85;
         pane.addChild(this.numBG);
         this.bgSP = new UISprite();
         this.bgSP.x = 208;
         this.bgSP.y = 85;
         pane.addChild(this.bgSP);
         var _loc2_:TextFormat = TextFormatLib.format_0x00FF00_12px;
         _loc2_.underline = true;
         this.goldEmptyBtn = new TextButton(Globalization.getString("buyKongdaobei1"),0,_loc2_,[FilterLib.glow_0x272727],65280,11337526,95489);
         this.goldEmptyBtn.x = 75;
         this.goldEmptyBtn.y = 205;
         pane.addChild(this.goldEmptyBtn);
         _loc2_.underline = false;
         this.zuizhong1 = UIManager.getUISkin("card_zuizhongchujia");
         this.zuizhong1.x = 12;
         this.zuizhong1.y = 54;
         this.zuizhong1.visible = false;
         pane.addChild(this.zuizhong1);
         this.zuizhong2 = UIManager.getUISkin("card_dengdai");
         this.zuizhong2.x = 51;
         this.zuizhong2.y = 143;
         this.zuizhong2.visible = false;
         pane.addChild(this.zuizhong2);
         this.timer = new Timer(1000);
         this.timer.addEventListener("timer",this.onTimerHandler);
         this.addEventListener("click",this.onMouseClickHandler);
         this.setDate([0,0,CardManager.getInstance().getGoldtime()]);
      }
      
      private function onTimerHandler(param1:TimerEvent) : void
      {
         this.timeCount--;
         if(this.timeCount <= 0)
         {
            this.timer.reset();
            return;
         }
         this.setCountDown(this.timeCount);
      }
      
      private function onTextChangeHandler(param1:Event) : void
      {
         TextInput(param1.currentTarget).text = "" + int(TextInput(param1.currentTarget).text);
         this.setInputGold();
      }
      
      private function setInputGold() : void
      {
         if(CardManager.getInstance().myself.bankGold < int(this.textInput.text))
         {
            this.textInput.text = "" + CardManager.getInstance().myself.bankGold;
         }
      }
      
      private function formatTxt(param1:int) : String
      {
         var _loc2_:String = "";
         if(param1 >= 1000 && param1 < 10000)
         {
            _loc2_ = "+" + int(param1 / 1000) + Globalization.getString("card.34");
         }
         else if(param1 >= 10000)
         {
            _loc2_ = "+" + int(param1 / 10000) + Globalization.getString("card.35");
         }
         else
         {
            _loc2_ = "+" + param1;
         }
         return _loc2_;
      }
      
      public function setDate(param1:Array) : void
      {
         this.timeCount = int(param1[2]);
         if(this.timeCount > 0)
         {
            this.setCountDown(this.timeCount);
            this.timer.start();
         }
         else
         {
            this.timer.reset();
         }
         var _loc2_:String = "";
         if(int(param1[0]) > int(param1[1]))
         {
            _loc2_ = "<font color=\'#ff0000\'>{0}</font>";
         }
         else if(int(param1[0]) < int(param1[1]))
         {
            _loc2_ = "<font color=\'#00ff00\'>{0}</font>";
         }
         else if(int(param1[0]) == int(param1[1]))
         {
            if(CardManager.getInstance().myself.isBanker)
            {
               _loc2_ = "<font color=\'#00ff00\'>{0}</font>";
            }
            else
            {
               _loc2_ = "<font color=\'#ff0000\'>{0}</font>";
            }
         }
         if(int(param1[0] != -1))
         {
            this.duifangTxt.htmlText = StringUtil.substitute(_loc2_,param1[0]);
         }
         else
         {
            this.zuizhong1.visible = true;
         }
         if(int(param1[1]) != -1)
         {
            this.benfangTxt.text = param1[1];
         }
         else
         {
            this.zuizhong2.visible = true;
         }
         if(CardManager.getInstance().myself.isBanker)
         {
            this.fangzhuSK1.visible = false;
            this.fangzhuSK2.visible = true;
         }
         else
         {
            this.fangzhuSK1.visible = true;
            this.fangzhuSK2.visible = false;
         }
      }
      
      private function onMouseClickHandler(param1:MouseEvent) : void
      {
         var event:MouseEvent = param1;
         if(event.target == this.goldEmptyBtn)
         {
            this.textInput.text = "0";
         }
         else if(event.target == this.addGoldBtn1)
         {
            this.textInput.text = "" + (int(this.textInput.text) + int(this.addGoldBtn1.name));
            this.setInputGold();
         }
         else if(event.target == this.addGoldBtn2)
         {
            this.textInput.text = "" + (int(this.textInput.text) + int(this.addGoldBtn2.name));
            this.setInputGold();
         }
         else if(event.target == this.addGoldBtn3)
         {
            this.textInput.text = "" + (int(this.textInput.text) + int(this.addGoldBtn3.name));
            this.setInputGold();
         }
         else if(event.target == this.addGoldBtn4)
         {
            this.textInput.text = "" + (int(this.textInput.text) + int(this.addGoldBtn4.name));
            this.setInputGold();
         }
         else if(event.target == this.subminBtn)
         {
            if(int(this.textInput.text) > 0)
            {
               AppFacade.instance.sendNotification("CARD_COMPETEPRICE",int(this.textInput.text));
               this.textInput.text = "0";
            }
            else
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":StringUtil.substitute(Globalization.getString("card.48"),1),
                  "textFormat":TextFormatLib.format_0xFF0000_14px,
                  "runTime":2
               });
            }
         }
         else if(event.target == this.cancelBtn)
         {
            PopUpCenter.confirmWin(StringUtil.substitute(Globalization.getString("card.49"),CardManager.getInstance().myself.compete),function():void
            {
               if(!zuizhong1.visible)
               {
                  zuizhong2.visible = true;
               }
               cancelBtn.enabled = false;
               subminBtn.enabled = false;
               AppFacade.instance.sendNotification("CARD_COMPETEOK");
            },null,0,true);
         }
      }
      
      private function setCountDown(param1:int) : void
      {
         var _loc8_:String = null;
         var _loc2_:UISkin = null;
         var _loc4_:String = null;
         clearAllChild(this.bgSP);
         if(param1 < 0)
         {
            return;
         }
         if(param1 < 10)
         {
            _loc8_ = "00" + param1;
         }
         else if(param1 < 100)
         {
            _loc8_ = "0" + param1;
         }
         else
         {
            _loc8_ = "" + param1;
         }
         var _loc6_:int = 4;
         var _loc7_:int = 26;
         var _loc3_:int = _loc8_.length;
         var _loc5_:int = 0;
         while(_loc5_ < _loc3_)
         {
            _loc4_ = _loc8_.substring(_loc5_,_loc5_ + 1);
            _loc2_ = UIManager.getUISkin("e" + _loc4_);
            _loc2_.x = _loc6_ + _loc7_ * _loc5_;
            _loc2_.y = 6;
            this.bgSP.addChild(_loc2_);
            _loc5_ += 1;
         }
      }
   }
}

