package card.view
{
   import flash.utils.Timer;
   import game.data.user.RolesManager;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.modules.card.manager.Gamester;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import util.Globalization;
   
   public class PlayerHead extends UISprite
   {
      private var headIcon:Icon;
      
      private var bankerSK:UISkin;
      
      private var nameTF:Label;
      
      private var _isMySelf:Boolean;
      
      private var timer:Timer;
      
      private var countdown:int;
      
      private var bg3:UISkin;
      
      private const MAXCD:int = 10;
      
      public function PlayerHead(param1:Boolean)
      {
         super();
         var _loc4_:UISkin = UIManager.getUISkin("card_head2");
         _loc4_.x = 59;
         _loc4_.y = 0;
         this.addChild(_loc4_);
         var _loc2_:UISkin = UIManager.getUISkin("card_head1");
         _loc2_.x = 42;
         _loc2_.y = -6;
         this.addChild(_loc2_);
         var _loc3_:UISkin = UIManager.getUISkin("card_name");
         _loc3_.x = 42;
         _loc3_.y = 69;
         this.addChild(_loc3_);
         this.bg3 = UIManager.getUISkin("card_head3");
         this.bg3.x = 36;
         this.bg3.y = -12;
         this.addChild(this.bg3);
         this._isMySelf = param1;
         this.headIcon = new Icon();
         this.headIcon.x = 64;
         this.headIcon.y = 10;
         this.addChild(this.headIcon);
         this.bankerSK = UIManager.getUISkin("fangzhu");
         this.bankerSK.y = 45;
         this.addChild(this.bankerSK);
         this.nameTF = new Label("",TextFormatLib.format_0xffb932_12px_center,[FilterLib.glow_0x272727],false);
         this.nameTF.height = 20;
         this.nameTF.width = 90;
         this.nameTF.x = 51;
         this.nameTF.y = 70;
         this.addChild(this.nameTF);
         if(this._isMySelf)
         {
            this.bg3.visible = true;
         }
         else
         {
            this.bg3.visible = false;
         }
      }
      
      public function resetData(param1:Boolean = false) : void
      {
         this.bankerSK.visible = false;
         if(!param1)
         {
            this.nameTF.text = Globalization.getString("card.41");
            this.headIcon.setData(UIManager.getUISkin("card_head"));
            this.headIcon.x = 68;
            this.headIcon.y = 2;
         }
      }
      
      public function setData(param1:Gamester) : void
      {
         if(param1.isBanker)
         {
            this.bankerSK.visible = true;
         }
         else
         {
            this.bankerSK.visible = false;
         }
         this.nameTF.text = param1.name;
         this.headIcon.setData(UrlManager.getUserHeadUrl(RolesManager.instance.getUserHeroImgByHtid(param1.headID)));
         switch(param1.headID - 11001)
         {
            case 0:
               this.headIcon.x = 45;
               this.headIcon.y = 0;
               break;
            case 1:
               this.headIcon.x = 61;
               this.headIcon.y = -7;
               break;
            case 2:
               this.headIcon.x = 62;
               this.headIcon.y = -5;
               break;
            case 3:
               this.headIcon.x = 62;
               this.headIcon.y = -2;
               break;
            case 4:
               this.headIcon.x = 61;
               this.headIcon.y = -3;
               break;
            case 5:
               this.headIcon.x = 59;
               this.headIcon.y = -3;
         }
      }
   }
}

