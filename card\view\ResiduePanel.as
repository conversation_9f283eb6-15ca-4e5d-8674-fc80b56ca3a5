package card.view
{
   import game.manager.UIManager;
   import game.modules.card.manager.CardManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   
   public class ResiduePanel extends UISprite
   {
      private var text:Label;
      
      public function ResiduePanel()
      {
         super();
         var _loc2_:UISkin = UIManager.getUISkin("text_bg_17");
         _loc2_.width = 270;
         _loc2_.height = 50;
         _loc2_.y = 5;
         this.addChild(_loc2_);
         var _loc1_:UISkin = UIManager.getUISkin("residue");
         _loc1_.x = 68;
         _loc1_.y = 18;
         this.addChild(_loc1_);
         this.text = new Label("0",TextFormatLib.format_0x00FF00_20px);
         this.text.x = 252;
         this.text.y = 16;
         this.addChild(this.text);
      }
      
      public function setData() : void
      {
         var _loc1_:int = CardManager.getInstance().getMaxPlay() - CardManager.getInstance().played;
         if(CardManager.getInstance().getMaxPlay() == 0)
         {
            this.visible = false;
         }
         else
         {
            this.visible = true;
         }
         if(_loc1_ < 0)
         {
            _loc1_ = 0;
         }
         this.text.text = "" + _loc1_;
      }
   }
}

