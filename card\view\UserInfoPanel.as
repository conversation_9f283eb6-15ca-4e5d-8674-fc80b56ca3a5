package card.view
{
   import game.manager.UIManager;
   import game.modules.card.manager.CardManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import util.Globalization;
   
   public class UserInfoPanel extends UISprite
   {
      private var cardGoldTF:Label;
      
      private var cardChipTF:Label;
      
      public function UserInfoPanel()
      {
         super();
         var _loc8_:UISkin = UIManager.getUISkin("pane_bg");
         _loc8_.x = 3;
         _loc8_.y = 2;
         _loc8_.width = 200;
         _loc8_.height = 100;
         this.addChild(_loc8_);
         var _loc7_:UISkin = UIManager.getUISkin("text_bg_11");
         _loc7_.x = 26;
         _loc7_.y = 35;
         _loc7_.width = 150;
         _loc7_.height = 22;
         this.addChild(_loc7_);
         var _loc5_:UISkin = UIManager.getUISkin("text_bg_11");
         _loc5_.x = 26;
         _loc5_.y = 62;
         _loc5_.width = 150;
         _loc5_.height = 22;
         this.addChild(_loc5_);
         var _loc6_:Label = new Label(Globalization.getString("card.38"),TextFormatLib.format_0xFFB932_14px,[FilterLib.glow_0x272727]);
         _loc6_.x = 80;
         _loc6_.y = 3;
         this.addChild(_loc6_);
         var _loc1_:UISkin = UIManager.getUISkin("gold");
         _loc1_.x = 32;
         _loc1_.y = 38;
         this.addChild(_loc1_);
         var _loc2_:UISkin = UIManager.getUISkin("kongdaobei");
         _loc2_.x = 32;
         _loc2_.y = 65;
         this.addChild(_loc2_);
         var _loc4_:Label = new Label(Globalization.getString("card.39"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc4_.x = 54;
         _loc4_.y = 37;
         this.addChild(_loc4_);
         this.cardGoldTF = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.cardGoldTF.x = 100;
         this.cardGoldTF.y = 35;
         this.addChild(this.cardGoldTF);
         var _loc3_:Label = new Label(Globalization.getString("card.40"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc3_.x = 52;
         _loc3_.y = 63;
         this.addChild(_loc3_);
         this.cardChipTF = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.cardChipTF.x = 100;
         this.cardChipTF.y = 61;
         this.addChild(this.cardChipTF);
      }
      
      public function updateUserInfo(param1:Object) : void
      {
         this.cardGoldTF.text = "" + CardManager.getInstance().myself.bankGold;
         this.cardChipTF.text = "" + CardManager.getInstance().myself.bankChip;
      }
   }
}

