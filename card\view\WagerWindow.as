package card.view
{
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.manager.UIManager;
   import game.modules.card.manager.CardManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.input.TextInput;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class WagerWindow extends PopUpWindow
   {
      public static const NAME:String = "WagerWindow";
      
      private var textInput:TextInput;
      
      private var resetBtn:Button;
      
      private var submitBtn:Button;
      
      private var addChipBtn1:Button;
      
      private var addChipBtn2:Button;
      
      private var addChipBtn3:Button;
      
      private var addChipBtn4:Button;
      
      public function WagerWindow()
      {
         super(255,165);
         this.isLive = false;
         this.title = Globalization.getString("card.37");
         var _loc1_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc1_.x = 0;
         _loc1_.y = 0;
         _loc1_.width = 240;
         _loc1_.height = 50;
         pane.addChild(_loc1_);
         this.textInput = new TextInput("0",80,TextFormatLib.format_0xFFFFFF_12px);
         this.textInput.x = 10;
         this.textInput.y = 18;
         this.textInput.text = "" + Math.abs(CardManager.getInstance().opponent.wager - CardManager.getInstance().myself.wager);
         this.textInput.restrict = "0-9";
         this.textInput.addEventListener("change",this.onTxtChangeHandler);
         pane.addChild(this.textInput);
         this.resetBtn = new Button(Globalization.getString("buyKongdaobei1"),null,60,UIManager.getMultiUISkin("btn_topMenu"));
         this.resetBtn.x = 105;
         this.resetBtn.y = 18;
         this.resetBtn.setTextOffset(0,-2);
         pane.addChild(this.resetBtn);
         this.submitBtn = new Button(Globalization.getString("matchRule.1"),null,60,UIManager.getMultiUISkin("btn_topMenu"));
         this.submitBtn.x = 170;
         this.submitBtn.y = 18;
         this.submitBtn.setTextOffset(0,-2);
         pane.addChild(this.submitBtn);
         this.addChipBtn1 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipblue2"));
         this.addChipBtn1.x = 10;
         this.addChipBtn1.y = 55;
         this.addChipBtn1.text = this.formatTxt(CardManager.getInstance().getRaisevalue()[0]);
         this.addChipBtn1.name = "" + CardManager.getInstance().getRaisevalue()[0];
         pane.addChild(this.addChipBtn1);
         this.addChipBtn2 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipyellow2"));
         this.addChipBtn2.x = 70;
         this.addChipBtn2.y = 55;
         this.addChipBtn2.text = this.formatTxt(CardManager.getInstance().getRaisevalue()[1]);
         this.addChipBtn2.name = "" + CardManager.getInstance().getRaisevalue()[1];
         pane.addChild(this.addChipBtn2);
         this.addChipBtn3 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipred2"));
         this.addChipBtn3.x = 130;
         this.addChipBtn3.y = 55;
         this.addChipBtn3.text = this.formatTxt(CardManager.getInstance().getRaisevalue()[2]);
         this.addChipBtn3.name = "" + CardManager.getInstance().getRaisevalue()[2];
         pane.addChild(this.addChipBtn3);
         this.addChipBtn4 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChippurple2"));
         this.addChipBtn4.x = 190;
         this.addChipBtn4.y = 55;
         this.addChipBtn4.text = this.formatTxt(CardManager.getInstance().getRaisevalue()[3]);
         this.addChipBtn4.name = "" + CardManager.getInstance().getRaisevalue()[3];
         pane.addChild(this.addChipBtn4);
         this.addEventListener("click",this.onMouseClickHandler);
      }
      
      private function onMouseClickHandler(param1:MouseEvent) : void
      {
         if(param1.target == this.resetBtn)
         {
            this.textInput.text = "0";
         }
         else if(param1.target == this.submitBtn)
         {
            if(int(this.textInput.text) == 0)
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("card.36"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px,
                  "runTime":2
               });
            }
            else
            {
               if(int(this.textInput.text) < Math.min(CardManager.getInstance().getMinraise() + (CardManager.getInstance().opponent.wager - CardManager.getInstance().myself.wager),CardManager.getInstance().getWager() - CardManager.getInstance().myself.wager))
               {
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":StringUtil.substitute(Globalization.getString("card.79"),Math.min(CardManager.getInstance().getMinraise() + (CardManager.getInstance().opponent.wager - CardManager.getInstance().myself.wager),CardManager.getInstance().getWager() - CardManager.getInstance().myself.wager)),
                     "textFormat":TextFormatLib.format_0xFF0000_14px,
                     "runTime":2
                  });
                  return;
               }
               AppFacade.instance.sendNotification("CARD_WAGER",[2,int(this.textInput.text)]);
               this.close();
            }
         }
         else if(param1.target == this.addChipBtn1)
         {
            this.textInput.text = "" + (int(this.textInput.text) + int(this.addChipBtn1.name));
            this.setInputChip();
         }
         else if(param1.target == this.addChipBtn2)
         {
            this.textInput.text = "" + (int(this.textInput.text) + int(this.addChipBtn2.name));
            this.setInputChip();
         }
         else if(param1.target == this.addChipBtn3)
         {
            this.textInput.text = "" + (int(this.textInput.text) + int(this.addChipBtn3.name));
            this.setInputChip();
         }
         else if(param1.target == this.addChipBtn4)
         {
            this.textInput.text = "" + (int(this.textInput.text) + int(this.addChipBtn4.name));
            this.setInputChip();
         }
      }
      
      private function setInputChip() : void
      {
         if(CardManager.getInstance().myself.bankChip < int(this.textInput.text))
         {
            this.textInput.text = "" + CardManager.getInstance().myself.bankChip;
         }
         if(int(this.textInput.text) > Math.min(CardManager.getInstance().getMaxraise() + (CardManager.getInstance().opponent.wager - CardManager.getInstance().myself.wager),CardManager.getInstance().getWager() - CardManager.getInstance().myself.wager))
         {
            this.textInput.text = "" + Math.min(CardManager.getInstance().getMaxraise() + (CardManager.getInstance().opponent.wager - CardManager.getInstance().myself.wager),CardManager.getInstance().getWager() - CardManager.getInstance().myself.wager);
         }
      }
      
      private function onTxtChangeHandler(param1:Event) : void
      {
         TextInput(param1.currentTarget).text = "" + int(TextInput(param1.currentTarget).text);
         this.setInputChip();
      }
      
      private function formatTxt(param1:int) : String
      {
         var _loc2_:String = "";
         if(param1 >= 1000 && param1 < 10000)
         {
            _loc2_ = "+" + int(param1 / 1000) + Globalization.getString("card.34");
         }
         else if(param1 >= 10000)
         {
            _loc2_ = "+" + int(param1 / 10000) + Globalization.getString("card.35");
         }
         else
         {
            _loc2_ = "+" + param1;
         }
         return _loc2_;
      }
   }
}

