package card.view.ui
{
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.items.ItemQualityInfo;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.manager.XmlManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.utils.AutoLoadBmpForSwf;
   import util.Globalization;
   import util.createLabel;
   
   public class Card extends UISprite
   {
      private var strengthTF:Label;
      
      private var agileTF:Label;
      
      private var intelligenceTF:Label;
      
      private var nameTF:Label;
      
      private var selectSK:UISkin;
      
      private var overSK:UISkin;
      
      private var theY:int;
      
      private var isSelect:Boolean;
      
      public var htid:int;
      
      private var headImg:AutoLoadBmpForSwf;
      
      private var bgSP:Sprite;
      
      private var bgSK:UISkin;
      
      public function Card()
      {
         var _loc1_:Label = null;
         super();
         addChild(new Bitmap(new BitmapData(132,167,true,0)));
         this.buttonMode = true;
         this.bgSP = new Sprite();
         this.addChild(this.bgSP);
         this.bgSK = UIManager.getUISkin("card_big_bg");
         this.bgSK.scaleX = 0.8;
         this.bgSK.scaleY = 0.8;
         this.bgSK.smoothing = true;
         this.bgSK.cacheAsBitmap = true;
         this.bgSP.addChild(this.bgSK);
         this.headImg = new AutoLoadBmpForSwf();
         this.bgSP.addChild(this.headImg);
         this.overSK = UIManager.getUISkin("card_over");
         this.overSK.visible = false;
         this.overSK.x = -2;
         this.overSK.y = -1;
         this.bgSP.addChild(this.overSK);
         this.selectSK = UIManager.getUISkin("card_selected");
         this.selectSK.visible = false;
         this.selectSK.x = -6;
         this.selectSK.y = -5;
         this.bgSP.addChild(this.selectSK);
         this.nameTF = new Label("",TextFormatLib.format_0xFFED89_12px_center,[FilterLib.glow_0x272727],false);
         this.nameTF.width = 100;
         this.nameTF.height = 20;
         this.nameTF.x = 18;
         this.nameTF.y = 123;
         this.bgSP.addChild(this.nameTF);
         _loc1_ = createLabel(Globalization.getString("cardFormation.6"),4,141,0,this.bgSP,"left",TextFormatLib.format_0xff4c3e_12px,[FilterLib.glow_0x000000]);
         _loc1_ = createLabel(Globalization.getString("cardFormation.7"),84,141,0,this.bgSP,"left",TextFormatLib.format_0x67ff32_12px,[FilterLib.glow_0x000000]);
         _loc1_ = createLabel(Globalization.getString("cardFormation.8"),44,141,0,this.bgSP,"left",TextFormatLib.format_0x32c8ff_12px,[FilterLib.glow_0x000000]);
         this.strengthTF = new Label("",TextFormatLib.format_0xFFB932_10px_s,[FilterLib.glow_0x272727]);
         this.strengthTF.x = 25;
         this.strengthTF.y = 142;
         this.bgSP.addChild(this.strengthTF);
         this.agileTF = new Label("",TextFormatLib.format_0xFFB932_10px_s,[FilterLib.glow_0x272727]);
         this.agileTF.x = 65;
         this.agileTF.y = 142;
         this.bgSP.addChild(this.agileTF);
         this.intelligenceTF = new Label("",TextFormatLib.format_0xFFB932_10px_s,[FilterLib.glow_0x272727]);
         this.intelligenceTF.x = 105;
         this.intelligenceTF.y = 142;
         this.bgSP.addChild(this.intelligenceTF);
         this.bgSP.mouseChildren = false;
         this.bgSP.mouseEnabled = false;
         this.addEventListener("mouseOver",this.onMouseOverHandler);
         this.addEventListener("mouseOut",this.onMouseOutHandler);
      }
      
      private function onMouseOverHandler(param1:MouseEvent) : void
      {
         if(!this.isSelect)
         {
            this.overSK.visible = true;
            this.bgSP.y = this.theY - 3;
         }
      }
      
      private function onMouseOutHandler(param1:MouseEvent) : void
      {
         this.overSK.visible = false;
         if(!this.isSelect)
         {
            this.bgSP.y = this.theY;
         }
      }
      
      public function setData(param1:int) : void
      {
         var heroXML:XML = null;
         var textformat:TextFormat = null;
         var cID:int = param1;
         heroXML = XmlManager.cardHeroXML.card_hero.(@cardid == cID)[0];
         this.htid = int(heroXML.@htid);
         this.nameTF.text = String(heroXML.@name);
         textformat = new TextFormat("Verdana",12,16772489,null,null,null,null,null,"center");
         textformat.color = ItemQualityInfo.getQualityColor(int(heroXML.@nameColor));
         this.nameTF.defaultTextFormat = textformat;
         this.nameTF.setTextFormat(textformat);
         this.headImg.setUrl(UrlManager.getCardheroUrl(heroXML.@cardphotoid),heroXML.@cardphotoid,function():void
         {
            headImg.smoothing = true;
            bgSK.visible = false;
            bgSK.dispose();
         });
         this.headImg.scaleX = 0.8;
         this.headImg.scaleY = 0.8;
         this.strengthTF.text = String(int(heroXML.@strength) / 100);
         this.agileTF.text = String(int(heroXML.@agile) / 100);
         this.intelligenceTF.text = String(int(heroXML.@intelligence) / 100);
      }
      
      public function set select(param1:Boolean) : void
      {
         this.isSelect = param1;
         this.selectSK.visible = param1;
         if(!param1)
         {
            this.bgSP.y = this.theY;
         }
         else
         {
            this.bgSP.y = this.theY - 5;
         }
      }
      
      public function setY(param1:int) : void
      {
         this.theY = param1;
      }
   }
}

