package card.view.ui
{
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.items.ItemQualityInfo;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.manager.XmlManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.utils.AutoLoadBmpForSwf;
   import util.Globalization;
   
   public class CardBook extends UISprite
   {
      private var strengthTF:Label;
      
      private var agileTF:Label;
      
      private var intelligenceTF:Label;
      
      private var nameTF:Label;
      
      public var htid:int;
      
      private var headImg:AutoLoadBmpForSwf;
      
      private var bgSK:UISkin;
      
      private var overSK:UISkin;
      
      private var selectSK:UISkin;
      
      private var isSelect:Boolean;
      
      public function CardBook()
      {
         super();
         this.bgSK = UIManager.getUISkin("card_big_bg");
         this.bgSK.smoothing = true;
         this.bgSK.cacheAsBitmap = true;
         this.bgSK.scaleX = 0.7;
         this.bgSK.scaleY = 0.7;
         addChild(this.bgSK);
         this.headImg = new AutoLoadBmpForSwf();
         addChild(this.headImg);
         var _loc3_:Label = new Label(Globalization.getString("cardFormation.6"),TextFormatLib.format_0xFF4C3E_11px,[FilterLib.glow_0x000000]);
         _loc3_.x = 4;
         _loc3_.y = 122;
         addChild(_loc3_);
         var _loc2_:Label = new Label(Globalization.getString("cardFormation.8"),TextFormatLib.format_0x32c8ff_11px,[FilterLib.glow_0x000000]);
         _loc2_.x = 38;
         _loc2_.y = 122;
         addChild(_loc2_);
         var _loc1_:Label = new Label(Globalization.getString("cardFormation.7"),TextFormatLib.format_0x67ff32_11px,[FilterLib.glow_0x000000]);
         _loc1_.x = 74;
         _loc1_.y = 122;
         addChild(_loc1_);
         this.nameTF = new Label("",TextFormatLib.format_0xFFED89_12px_center,[FilterLib.glow_0x272727],false);
         this.nameTF.width = 100;
         this.nameTF.height = 20;
         this.nameTF.x = 11;
         this.nameTF.y = 108;
         addChild(this.nameTF);
         this.strengthTF = new Label("",TextFormatLib.format_0xFFB932_10px_s,[FilterLib.glow_0x272727]);
         this.strengthTF.x = 20;
         this.strengthTF.y = 124;
         addChild(this.strengthTF);
         this.agileTF = new Label("",TextFormatLib.format_0xFFB932_10px_s,[FilterLib.glow_0x272727]);
         this.agileTF.x = 55;
         this.agileTF.y = 124;
         addChild(this.agileTF);
         this.intelligenceTF = new Label("",TextFormatLib.format_0xFFB932_10px_s,[FilterLib.glow_0x272727]);
         this.intelligenceTF.x = 90;
         this.intelligenceTF.y = 124;
         addChild(this.intelligenceTF);
         this.overSK = UIManager.getUISkin("card_over70");
         this.overSK.visible = false;
         this.overSK.x = -2;
         this.overSK.y = -2;
         addChild(this.overSK);
         this.selectSK = UIManager.getUISkin("card_selected70");
         this.selectSK.visible = false;
         this.selectSK.x = -5;
         this.selectSK.y = -5;
         addChild(this.selectSK);
         this.mouseChildren = false;
         this.buttonMode = true;
         this.addEventListener("mouseOver",this.onMouseOverHandler);
         this.addEventListener("mouseOut",this.onMouseOutHandler);
      }
      
      private function onMouseOverHandler(param1:MouseEvent) : void
      {
         if(!this.isSelect)
         {
            this.overSK.visible = true;
         }
      }
      
      private function onMouseOutHandler(param1:MouseEvent) : void
      {
         this.overSK.visible = false;
      }
      
      public function set select(param1:Boolean) : void
      {
         this.isSelect = param1;
         this.selectSK.visible = param1;
      }
      
      public function setData(param1:int) : void
      {
         var heroXML:XML = null;
         var textformat:TextFormat = null;
         var cID:int = param1;
         heroXML = XmlManager.cardHeroXML.card_hero.(@cardid == cID)[0];
         this.htid = int(heroXML.@htid);
         this.nameTF.text = String(heroXML.@name);
         textformat = new TextFormat("Verdana",12,16772489,null,null,null,null,null,"center");
         textformat.color = ItemQualityInfo.getQualityColor(int(heroXML.@nameColor));
         this.nameTF.defaultTextFormat = textformat;
         this.nameTF.setTextFormat(textformat);
         this.headImg.setUrl(UrlManager.getCardheroUrl(heroXML.@cardphotoid),heroXML.@cardphotoid,function():void
         {
            headImg.smoothing = true;
         });
         this.headImg.scaleX = 0.7;
         this.headImg.scaleY = 0.7;
         this.strengthTF.text = String(int(heroXML.@strength) / 100);
         this.agileTF.text = String(int(heroXML.@agile) / 100);
         this.intelligenceTF.text = String(int(heroXML.@intelligence) / 100);
      }
   }
}

