package card.view.ui
{
   import game.data.group.HeroDataUtil;
   import game.data.skill.SkillInfoADT;
   import game.items.ItemQualityInfo;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.modules.user.view.HeroInfoBoard.heroInfoPanel.learnSkill.skillTooltip;
   import game.modules.user.view.HeroInfoBoard.heroInfoPanel.propertyInfo.TipAbleLabel;
   import game.modules.user.view.HeroInfoBoard.heroInfoPanel.roleHeroTransfor.NumberHelper;
   import game.modules.user.view.ToolTipArea;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.customTooltip.CustomToolTipManager;
   import mmo.ui.control.infoMC.IconInfoMC;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   import mmo.utils.AutoLoadBmpForSwf;
   import util.Globalization;
   import util.createLabel;
   
   public class CardHeroInfo extends UISprite
   {
      private var icon:AutoLoadBmpForSwf;
      
      private var txt_name:Label;
      
      private var txt_str:Label;
      
      private var txt_int:Label;
      
      private var txt_agi:Label;
      
      private var attackGiftValue:TipAbleLabel;
      
      private var killGiftValue:TipAbleLabel;
      
      private var magicAttackGiftValue:TipAbleLabel;
      
      private var phyDenfenseGiftValue:TipAbleLabel;
      
      private var killDefGiftValue:TipAbleLabel;
      
      private var magicDefGiftValue:TipAbleLabel;
      
      private var txt_normalSkill:Label;
      
      private var killSkillTipArea:ToolTipArea;
      
      private var txt_killSkill:Label;
      
      private var normalSkillTipArea:ToolTipArea;
      
      private var devilFruit:UIBox;
      
      private const BASE:int = 10000;
      
      private const FIX:int = 3;
      
      public function CardHeroInfo()
      {
         var _loc2_:UISkin = null;
         var _loc1_:Label = null;
         super();
         _loc2_ = UIManager.getUISkin("card_big_bg");
         _loc2_.x = 50;
         _loc2_.y = 11;
         addChild(_loc2_);
         this.icon = new AutoLoadBmpForSwf();
         this.icon.x = 50;
         this.icon.y = 11;
         addChild(this.icon);
         this.txt_name = createLabel("",56,168,160,this,"center");
         _loc1_ = createLabel(Globalization.getString("cardFormation.6"),58,190,0,this,"left",TextFormatLib.format_0xff4c3e_12px,[FilterLib.glow_0x000000]);
         _loc1_ = createLabel(Globalization.getString("cardFormation.8"),108,190,0,this,"left",TextFormatLib.format_0x32c8ff_12px,[FilterLib.glow_0x000000]);
         _loc1_ = createLabel(Globalization.getString("cardFormation.7"),158,190,0,this,"left",TextFormatLib.format_0x67ff32_12px,[FilterLib.glow_0x000000]);
         this.txt_str = createLabel("",78,190,0,this);
         this.txt_int = createLabel("",128,190,0,this);
         this.txt_agi = createLabel("",178,190,0,this);
         _loc2_ = UIManager.getUISkin("train_bg");
         _loc2_.setSize(242,77);
         _loc2_.x = 10;
         _loc2_.y = 222;
         addChild(_loc2_);
         _loc1_ = createLabel(Globalization.getString("heroProperty.1"),15,230,0,this,"left",TextFormatLib.format_0xffed89_14px);
         _loc1_ = createLabel(Globalization.getString("infoMc.44"),15,250,0,this);
         _loc1_ = createLabel(Globalization.getString("infoMc.45"),90,250,0,this);
         _loc1_ = createLabel(Globalization.getString("infoMc.46"),165,250,0,this);
         _loc1_ = createLabel(Globalization.getString("infoMc.47"),15,274,0,this);
         _loc1_ = createLabel(Globalization.getString("infoMc.48"),90,274,0,this);
         _loc1_ = createLabel(Globalization.getString("infoMc.49"),165,274,0,this);
         this.attackGiftValue = new TipAbleLabel("",TextFormatLib.format_Verdana_0xFFFFFF_12px);
         this.attackGiftValue.x = 50;
         this.attackGiftValue.y = 250;
         addChild(this.attackGiftValue);
         this.killGiftValue = new TipAbleLabel("",TextFormatLib.format_Verdana_0xFFFFFF_12px);
         this.killGiftValue.x = 125;
         this.killGiftValue.y = 250;
         addChild(this.killGiftValue);
         this.magicAttackGiftValue = new TipAbleLabel("killDefGiftLabel",TextFormatLib.format_Verdana_0xFFFFFF_12px);
         this.magicAttackGiftValue.x = 200;
         this.magicAttackGiftValue.y = 250;
         addChild(this.magicAttackGiftValue);
         this.phyDenfenseGiftValue = new TipAbleLabel("",TextFormatLib.format_Verdana_0xFFFFFF_12px);
         this.phyDenfenseGiftValue.x = 50;
         this.phyDenfenseGiftValue.y = 274;
         addChild(this.phyDenfenseGiftValue);
         this.killDefGiftValue = new TipAbleLabel("",TextFormatLib.format_Verdana_0xFFFFFF_12px);
         this.killDefGiftValue.x = 125;
         this.killDefGiftValue.y = 274;
         addChild(this.killDefGiftValue);
         this.magicDefGiftValue = new TipAbleLabel("magicDefGiftValue",TextFormatLib.format_Verdana_0xFFFFFF_12px);
         this.magicDefGiftValue.x = 200;
         this.magicDefGiftValue.y = 274;
         addChild(this.magicDefGiftValue);
         _loc2_ = UIManager.getUISkin("train_bg");
         _loc2_.setSize(242,100);
         _loc2_.x = 10;
         _loc2_.y = 302;
         addChild(_loc2_);
         _loc1_ = createLabel(Globalization.getString("infoMc.34"),15,307,0,this);
         _loc1_ = createLabel(Globalization.getString("infoMc.35"),15,327,0,this);
         this.txt_normalSkill = createLabel("",75,307,0,this);
         this.normalSkillTipArea = new ToolTipArea(70,24);
         this.normalSkillTipArea.tooltipClass = skillTooltip;
         this.normalSkillTipArea.x = 75;
         this.normalSkillTipArea.y = 307;
         addChild(this.normalSkillTipArea);
         this.txt_killSkill = createLabel("",75,327,0,this);
         this.killSkillTipArea = new ToolTipArea(70,24);
         this.killSkillTipArea.tooltipClass = skillTooltip;
         this.killSkillTipArea.x = 75;
         this.killSkillTipArea.y = 327;
         addChild(this.killSkillTipArea);
         this.devilFruit = new UIBox();
         this.devilFruit.lineMaxChildrenNumber = 3;
         this.devilFruit.rowSpace = 5;
         addChild(this.devilFruit);
         this.devilFruit.x = 30;
         this.devilFruit.y = 347;
      }
      
      public function setData(param1:int) : void
      {
         var color:String;
         var valueOfAtt:Number;
         var valueOfKill:Number;
         var valueOfMgi:Number;
         var valueOfAttDef:Number;
         var valueOfKillDef:Number;
         var valueOfMgiDef:Number;
         var stringOfAtt:String;
         var stringOfKill:String;
         var stringOfMgi:String;
         var stringOfAttDef:String;
         var stringOfKillDef:String;
         var stringOfMgiDef:String;
         var nomalSkill:SkillInfoADT;
         var killSkill:SkillInfoADT;
         var fruits:Array;
         var len:int;
         var i:int;
         var slot:Slot = null;
         var st:SlotTemplete = null;
         var htid:int = param1;
         var heroXML:XML = HeroDataUtil.getElementPropertyById(htid);
         this.txt_str.text = NumberHelper.getNumberString(int(heroXML.@strength) / 100);
         this.txt_agi.text = NumberHelper.getNumberString(int(heroXML.@intelligence) / 100);
         this.txt_int.text = NumberHelper.getNumberString(int(heroXML.@agile) / 100);
         color = MessageReceive.parseColor(ItemQualityInfo.getQualityColor(heroXML.@nameColor));
         this.txt_name.htmlText = "<font color=\'" + color + "\'>" + String(heroXML.@name) + "</font>";
         this.icon.setUrl(UrlManager.getCardheroUrl(String(heroXML.@cardphotoid)),String(heroXML.@cardphotoid),function():void
         {
            icon.smoothing = true;
         });
         valueOfAtt = int(heroXML.@phyFDmgRatio) / 10000;
         valueOfKill = int(heroXML.@killFDmgRatio) / 10000;
         valueOfMgi = int(heroXML.@mgcFDmgRatio) / 10000;
         valueOfAttDef = int(heroXML.@phyFEptRatio) / 10000;
         valueOfKillDef = int(heroXML.@killFEptRatio) / 10000;
         valueOfMgiDef = int(heroXML.@mgcFEptRatio) / 10000;
         stringOfAtt = HeroDataUtil.getHeroAttGiftLevelString(valueOfAtt);
         stringOfKill = HeroDataUtil.getHeroAttGiftLevelString(valueOfKill);
         stringOfMgi = HeroDataUtil.getHeroAttGiftLevelString(valueOfMgi);
         stringOfAttDef = HeroDataUtil.getHeroDefGiftLevelString(valueOfAttDef);
         stringOfKillDef = HeroDataUtil.getHeroDefGiftLevelString(valueOfKillDef);
         stringOfMgiDef = HeroDataUtil.getHeroDefGiftLevelString(valueOfMgiDef);
         this.attackGiftValue.text = stringOfAtt;
         this.attackGiftValue.value = NumberHelper.getNumberString(valueOfAtt,3);
         this.killGiftValue.text = stringOfKill;
         this.killGiftValue.value = NumberHelper.getNumberString(valueOfKill,3);
         this.magicAttackGiftValue.text = stringOfMgi;
         this.magicAttackGiftValue.value = NumberHelper.getNumberString(valueOfMgi,3);
         this.phyDenfenseGiftValue.text = stringOfAttDef;
         this.phyDenfenseGiftValue.value = NumberHelper.getNumberString(valueOfAttDef,3);
         this.killDefGiftValue.text = stringOfKillDef;
         this.killDefGiftValue.value = NumberHelper.getNumberString(valueOfKillDef,3);
         this.magicDefGiftValue.text = stringOfMgiDef;
         this.magicDefGiftValue.value = NumberHelper.getNumberString(valueOfMgiDef,3);
         nomalSkill = HeroDataUtil.indexHeroNormalSkillInfoADT(htid);
         killSkill = HeroDataUtil.indexHeroKillSkillInfoADT(htid);
         this.txt_normalSkill.text = nomalSkill.name;
         this.normalSkillTipArea.setSize(this.txt_normalSkill.textWidth + 10,24);
         this.normalSkillTipArea.showString = nomalSkill;
         this.txt_killSkill.text = killSkill.name;
         this.killSkillTipArea.setSize(this.txt_killSkill.textWidth + 10,24);
         this.killSkillTipArea.showString = killSkill;
         fruits = HeroDataUtil.indexHeroDevilApple(htid);
         len = int(fruits.length);
         this.devilFruit.clearAllChild(this.devilFruit);
         i = 0;
         while(i < len)
         {
            slot = new Slot();
            st = new SlotTemplete();
            st.tempID = fruits[i];
            slot.setItem(st,false,false,false,false);
            this.devilFruit.addChild(slot);
            i++;
         }
      }
      
      override public function dispose() : void
      {
         super.dispose();
      }
      
      public function close() : void
      {
         CustomToolTipManager.instance.releaseTipData();
         IconInfoMC.hide();
      }
   }
}

