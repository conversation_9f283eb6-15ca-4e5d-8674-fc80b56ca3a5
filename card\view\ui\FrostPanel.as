package card.view.ui
{
   import flash.events.TimerEvent;
   import flash.text.TextFormat;
   import flash.utils.Timer;
   import game.manager.UIManager;
   import game.modules.card.manager.CardManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class FrostPanel extends UISprite
   {
      public var frostGold:int;
      
      public var frostChip:int;
      
      public var frostTime:Number;
      
      private var infoTF:Label;
      
      private var frostTF:Label;
      
      private var _frostTime:Number;
      
      private var frostTimeTF:Label;
      
      private var timer:Timer;
      
      public function FrostPanel()
      {
         super();
         var _loc3_:UISkin = UIManager.getUISkin("bg_v3");
         _loc3_.width = 220;
         this.addChild(_loc3_);
         var _loc2_:TextFormat = TextFormatLib.format_0xFFED89_12px;
         _loc2_.leading = 4;
         this.infoTF = new Label("",_loc2_,[FilterLib.glow_0x272727],false);
         this.infoTF.width = 210;
         this.infoTF.height = 40;
         this.infoTF.x = 3;
         this.infoTF.y = 7;
         this.infoTF.wordWrap = true;
         this.infoTF.htmlText = Globalization.getString("card.67");
         this.addChild(this.infoTF);
         _loc2_.leading = 0;
         var _loc1_:UISkin = UIManager.getUISkin("line2");
         _loc1_.x = 12;
         _loc1_.y = 47;
         _loc1_.width = 190;
         this.addChild(_loc1_);
         this.frostTF = new Label(Globalization.getString("card.68"),TextFormatLib.format_0xFFED89_12px,[FilterLib.glow_0x272727]);
         this.frostTF.x = 30;
         this.frostTF.y = 50;
         this.addChild(this.frostTF);
         this.frostTimeTF = new Label("00:00:00",TextFormatLib.format_0x00FF00_12px,[FilterLib.glow_0x272727]);
         this.frostTimeTF.x = 88;
         this.frostTimeTF.y = 50;
         this.addChild(this.frostTimeTF);
         this.timer = new Timer(1000);
         this.timer.addEventListener("timer",this.onTimerHandler);
      }
      
      public function setData() : void
      {
         this._frostTime = CardManager.getInstance().frostTime;
         if(this._frostTime > 0)
         {
            this.timer.start();
            this.visible = true;
         }
         else
         {
            this.timer.reset();
            this.visible = false;
         }
         this.infoTF.htmlText = StringUtil.substitute(Globalization.getString("card.67"),CardManager.getInstance().frostGold,CardManager.getInstance().frostChip);
         this.frostTimeTF.text = "" + this.formatTime(int(this._frostTime / 60 / 60)) + ":" + this.formatTime(int(this._frostTime / 60 % 60)) + ":" + this.formatTime(this._frostTime % 60);
      }
      
      private function onTimerHandler(param1:TimerEvent) : void
      {
         this._frostTime--;
         this.frostTimeTF.text = "" + this.formatTime(int(this._frostTime / 60 / 60)) + ":" + this.formatTime(int(this._frostTime / 60 % 60)) + ":" + this.formatTime(this._frostTime % 60);
         if(this._frostTime < 0)
         {
            this.timer.reset();
            CardManager.getInstance().myself.bankGold = CardManager.getInstance().myself.bankGold + CardManager.getInstance().frostGold;
            CardManager.getInstance().myself.bankChip = CardManager.getInstance().myself.bankChip + CardManager.getInstance().frostChip;
            CardManager.getInstance().frostGold = 0;
            CardManager.getInstance().frostChip = 0;
            this.visible = false;
            AppFacade.instance.sendNotification("CARD_FREEZECDEND");
         }
      }
      
      private function formatTime(param1:int) : String
      {
         if(param1 < 10)
         {
            return "0" + param1;
         }
         return "" + param1;
      }
   }
}

