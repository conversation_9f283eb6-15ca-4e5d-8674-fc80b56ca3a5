package card.view.ui
{
   import flash.events.MouseEvent;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.WindowModel;
   
   public class TujianWindow extends WindowModel
   {
      public static const NAME:String = "TujianWindow";
      
      private var zuoBtn:Button;
      
      private var youBtn:Button;
      
      private var zuizuoBtn:Button;
      
      private var zuiyouBtn:Button;
      
      private var textBg:UISkin;
      
      private var pageText:Label;
      
      private var cardHeroInfo:CardHeroInfo;
      
      private var closeBtn:Button;
      
      private var cardXMLList:Array;
      
      private var selectCard:CardBook;
      
      private var cardList:Array;
      
      private var currentPage:int;
      
      private var pageMax:int = 6;
      
      public function TujianWindow()
      {
         var _loc2_:UISkin = null;
         var _loc4_:UISkin = null;
         var _loc1_:UISkin = null;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         var _loc5_:CardBook = null;
         super();
         this.isLive = false;
         _loc2_ = UIManager.getUISkin("card_book");
         this.addChild(_loc2_);
         _loc4_ = UIManager.getUISkin("card_di");
         _loc4_.x = 96;
         _loc4_.y = 104;
         this.addChild(_loc4_);
         _loc1_ = UIManager.getUISkin("card_di");
         _loc1_.x = 96;
         _loc1_.y = 292;
         this.addChild(_loc1_);
         this.cardHeroInfo = new CardHeroInfo();
         this.cardHeroInfo.x = 534;
         this.cardHeroInfo.y = 88;
         this.addChild(this.cardHeroInfo);
         this.zuoBtn = new Button("",null,120,UIManager.getMultiUISkin("xiangzuo_btn"));
         this.zuoBtn.x = 197;
         this.zuoBtn.y = 484;
         this.zuoBtn.addEventListener("click",this.onZuoClickHandler);
         this.addChild(this.zuoBtn);
         this.youBtn = new Button("",null,120,UIManager.getMultiUISkin("xiangyou_btn"));
         this.youBtn.x = 306;
         this.youBtn.y = 484;
         this.youBtn.addEventListener("click",this.onYouClickHandler);
         this.addChild(this.youBtn);
         this.zuizuoBtn = new Button("",null,120,UIManager.getMultiUISkin("zuizuo_btn"));
         this.zuizuoBtn.x = 154;
         this.zuizuoBtn.y = 484;
         this.zuizuoBtn.addEventListener("click",this.onZuiZuoClickHandler);
         this.addChild(this.zuizuoBtn);
         this.zuiyouBtn = new Button("",null,120,UIManager.getMultiUISkin("zuiyou_btn"));
         this.zuiyouBtn.x = 338;
         this.zuiyouBtn.y = 484;
         this.zuiyouBtn.addEventListener("click",this.onZuiYouClickHandler);
         this.addChild(this.zuiyouBtn);
         this.textBg = UIManager.getUISkin("card_kuang");
         this.textBg.x = 236;
         this.textBg.y = 490;
         this.addChild(this.textBg);
         this.pageText = new Label("",TextFormatLib.format_0xffc62b_14px_center,[FilterLib.glow_0x272727],false);
         this.pageText.x = 235;
         this.pageText.y = 491;
         this.pageText.width = 60;
         this.pageText.height = 24;
         this.addChild(this.pageText);
         this.closeBtn = new Button("",null,120,UIManager.getMultiUISkin("card_guanbi_btn"));
         this.closeBtn.x = 866;
         this.closeBtn.y = 85;
         this.closeBtn.addEventListener("click",this.onCloseClickHandler);
         this.addChild(this.closeBtn);
         this.cardList = [];
         _loc7_ = 0;
         while(_loc7_ < 6)
         {
            _loc5_ = new CardBook();
            if(_loc7_ >= 3)
            {
               _loc5_.x = 108 + (_loc7_ - 3) * 117;
               _loc5_.y = 307;
            }
            else
            {
               _loc5_.x = 108 + _loc7_ * 117;
               _loc5_.y = 121;
            }
            this.addChild(_loc5_);
            _loc5_.addEventListener("click",this.onCardClickHandler);
            this.cardList.push(_loc5_);
            _loc7_++;
         }
         var _loc3_:* = XmlManager.cardHeroXML.card_hero.(@sign == "1");
         this.cardXMLList = [];
         _loc6_ = 0;
         while(_loc6_ < _loc3_.length())
         {
            this.cardXMLList.push(_loc3_[_loc6_]);
            _loc6_++;
         }
         this.currentPage = 0;
         this.getDataByPage();
      }
      
      private function onCardClickHandler(param1:MouseEvent) : void
      {
         if(this.selectCard != null)
         {
            this.selectCard.select = false;
         }
         (param1.target as CardBook).select = true;
         this.selectCard = param1.target as CardBook;
         this.cardHeroInfo.setData(this.selectCard.htid);
      }
      
      private function setData(param1:Array) : void
      {
         var _loc2_:CardBook = null;
         var _loc3_:int = 0;
         while(_loc3_ < 6)
         {
            _loc2_ = this.cardList[_loc3_];
            if(param1[_loc3_] == null || param1[_loc3_] == undefined)
            {
               _loc2_.visible = false;
            }
            else
            {
               _loc2_.visible = true;
               _loc2_.setData(param1[_loc3_].@cardid);
            }
            if(_loc3_ == 0)
            {
               _loc2_.dispatchEvent(new MouseEvent("click"));
            }
            _loc3_++;
         }
      }
      
      private function onZuoClickHandler(param1:MouseEvent) : void
      {
         this.currentPage--;
         this.getDataByPage();
      }
      
      private function onYouClickHandler(param1:MouseEvent) : void
      {
         this.currentPage++;
         this.getDataByPage();
      }
      
      private function onZuiZuoClickHandler(param1:MouseEvent) : void
      {
         this.currentPage = 0;
         this.getDataByPage();
      }
      
      private function onZuiYouClickHandler(param1:MouseEvent) : void
      {
         var _loc2_:int = int(this.cardXMLList.length);
         this.currentPage = Math.ceil(_loc2_ / this.pageMax);
         this.getDataByPage();
      }
      
      private function getDataByPage() : void
      {
         var _loc2_:int = int(this.cardXMLList.length);
         if(this.currentPage <= 0)
         {
            this.currentPage = 0;
         }
         if(this.currentPage >= Math.ceil(_loc2_ / this.pageMax) - 1)
         {
            this.currentPage = Math.ceil(_loc2_ / this.pageMax) - 1;
         }
         this.pageText.text = this.currentPage + 1 + "/" + Math.ceil(_loc2_ / this.pageMax);
         var _loc1_:Array = this.cardXMLList.slice(this.currentPage * this.pageMax,(this.currentPage + 1) * this.pageMax);
         this.setData(_loc1_);
      }
      
      private function onCloseClickHandler(param1:MouseEvent) : void
      {
         this.close();
      }
   }
}

