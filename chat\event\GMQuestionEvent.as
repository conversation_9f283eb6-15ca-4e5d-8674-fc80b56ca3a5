package chat.event
{
   import flash.events.Event;
   
   public class GMQuestionEvent extends Event
   {
      public static const SEND_QUESTION:String = "sendQuestion";
      
      private var _questionType:String;
      
      private var _content:String;
      
      public function GMQuestionEvent(param1:String, param2:Boolean = false, param3:<PERSON>olean = false)
      {
         super(param1,param2,param3);
      }
      
      public function get questionType() : String
      {
         return this._questionType;
      }
      
      public function set questionType(param1:String) : void
      {
         this._questionType = param1;
      }
      
      public function get content() : String
      {
         return this._content;
      }
      
      public function set content(param1:String) : void
      {
         this._content = param1;
      }
      
      override public function clone() : Event
      {
         var _loc1_:GMQuestionEvent = new GMQuestionEvent(type,bubbles,cancelable);
         _loc1_.questionType = this.questionType;
         _loc1_.content = this.content;
         return _loc1_;
      }
      
      override public function toString() : String
      {
         return formatToString("GMQuestionEvent","type","bubbles","cancelable");
      }
   }
}

