package chat.event
{
   import chat.mvc.proxy.MessageSend;
   import flash.events.Event;
   
   public class SendMessageEvent extends Event
   {
      public static const SENDMSG:String = "sendMsg";
      
      public static const TOWN_TEAM_SENDMSG:String = "TOWN_TEAM_SENDMSG";
      
      private var _sendMsg:MessageSend;
      
      public function SendMessageEvent(param1:String, param2:MessageSend, param3:Boolean = false, param4:<PERSON>olean = false)
      {
         super(param1,param3,param4);
         this._sendMsg = param2;
      }
      
      public function get sendMsg() : MessageSend
      {
         return this._sendMsg;
      }
      
      override public function clone() : Event
      {
         return new SendMessageEvent(type,this._sendMsg,bubbles,"cancel");
      }
      
      override public function toString() : String
      {
         return formatToString("sendMsg","type","bubbles","cancelable");
      }
   }
}

