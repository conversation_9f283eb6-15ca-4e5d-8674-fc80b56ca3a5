package chat.event
{
   import chat.mvc.proxy.MessageSend;
   import flash.events.Event;
   
   public class WorldBoatMessageEvent extends Event
   {
      public static const SENDMSG:String = "sendMsg";
      
      public static const WORLD_BOAT_SENDMSG:String = "WORLD_BOAT_SENDMSG";
      
      private var _sendMsg:MessageSend;
      
      public function WorldBoatMessageEvent(param1:String, param2:MessageSend, param3:<PERSON><PERSON><PERSON> = false, param4:<PERSON><PERSON>an = false)
      {
         super(param1,param3,param4);
         this._sendMsg = param2;
      }
      
      public function get sendMsg() : MessageSend
      {
         return this._sendMsg;
      }
      
      override public function clone() : Event
      {
         return new WorldBoatMessageEvent(type,this._sendMsg,bubbles,"cancel");
      }
      
      override public function toString() : String
      {
         return formatToString("sendMsg","type","bubbles","cancelable");
      }
   }
}

