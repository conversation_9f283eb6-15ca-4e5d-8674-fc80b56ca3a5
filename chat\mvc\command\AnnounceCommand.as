package chat.mvc.command
{
   import chat.mvc.proxy.NetConnectProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class AnnounceCommand extends SimpleCommand
   {
      public function AnnounceCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc2_:NetConnectProxy = facade.retrieveProxy("chat.mvc.proxy.GMListProxy") as NetConnectProxy;
         _loc2_.getAnnounceInfos();
      }
   }
}

