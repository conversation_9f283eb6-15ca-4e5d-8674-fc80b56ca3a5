package chat.mvc.command
{
   import chat.mvc.proxy.MessageSend;
   import chat.mvc.proxy.MsgSendCollProxy;
   import flash.geom.Point;
   import flash.utils.Dictionary;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.user.UserData;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.modules.chat.msgInfo.getChannelIDByName;
   import game.modules.chat.proxy.MsgReceiveCollProxy;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   import util.Globalization;
   import util.time.TimeManager;
   
   public class ChatCommand extends SimpleCommand
   {
      private var timerMap:Dictionary;
      
      private var currentChannel:String;
      
      private var msgSend:MessageSend;
      
      public function ChatCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc2_:String = "";
         this.msgSend = param1.getBody() as MessageSend;
         this.currentChannel = this.msgSend.sendChannel;
         if(this.msgSend.messageText == "")
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("chat.1"),
               "textFormat":TextFormatLib.format_0xFF0000_12px,
               "point":new Point(50,480)
            });
            return;
         }
         switch(this.currentChannel)
         {
            case "channelCurrent":
               switch(GameScene.getCurrentScene() - 1)
               {
                  case 0:
                  case 7:
                  case 8:
                  case 9:
                  case 10:
                  case 11:
                  case 12:
                     _loc2_ = "chat.sendTown";
                     break;
                  case 1:
                     _loc2_ = "chat.sendCopy";
                     break;
                  case 3:
                     _loc2_ = "chat.sendResource";
                     break;
                  case 5:
                     _loc2_ = "chat.sendHarbor";
               }
               break;
            case "channelGroup":
               _loc2_ = "chat.sendGroup";
               break;
            case "channelGuild":
               _loc2_ = "chat.sendGuild";
               break;
            case "channelPrivately":
               _loc2_ = "chat.sendPersonal";
               break;
            case "channelWorld":
               _loc2_ = "chat.sendWorld";
         }
         BabelTimeSocket.getInstance().regCallback("re.chat.sendMsg",this.sendMsgHandler);
         if(this.currentChannel == "channelPrivately")
         {
            BabelTimeSocket.getInstance().sendMessage(_loc2_,new SocketCallback("re.chat.sendMsg"),this.msgSend.target,this.msgSend.messageText,this.msgSend.ignore);
         }
         else
         {
            BabelTimeSocket.getInstance().sendMessage(_loc2_,new SocketCallback("re.chat.sendMsg"),this.msgSend.messageText,this.msgSend.ignore);
         }
      }
      
      private function sendMsgHandler(param1:SocketDataEvent) : void
      {
         var _loc4_:Object = null;
         var _loc5_:Array = null;
         var _loc2_:Array = null;
         var _loc3_:MessageReceive = null;
         BabelTimeSocket.getInstance().removeCallback("re.chat.sendMsg",this.sendMsgHandler);
         if(param1.data == "noPermissions")
         {
            return;
         }
         if(this.currentChannel != "channelPrivately")
         {
            if(param1.data)
            {
               _loc5_ = this.msgSend.messageText.match(/<a href.*?<\/a>/);
               if(!(_loc5_ && _loc5_.length))
               {
                  MsgSendCollProxy(facade.retrieveProxy("chat.mvc.proxy.MsgSendCollProxy")).addMessage(this.msgSend);
               }
            }
            return;
         }
         var _loc6_:int;
         switch(_loc6_ = int(param1.data.error_code))
         {
            case 10000:
               this.msgSend.messageText = param1.data.message;
               this.msgSend.utid = param1.data.utid;
               _loc2_ = this.msgSend.messageText.match(/<a href.*?<\/a>/);
               if(!(_loc2_ && _loc2_.length))
               {
                  MsgSendCollProxy(facade.retrieveProxy("chat.mvc.proxy.MsgSendCollProxy")).addMessage(this.msgSend);
               }
               _loc3_ = new MessageReceive(this.createMessage(this.msgSend));
               MsgReceiveCollProxy(facade.retrieveProxy("chat.mvc.proxy.MsgReceiveCollProxy")).setReceiveMessage(_loc3_);
               break;
            case 10001:
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.2"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":new Point(50,480)
               });
               break;
            case 10002:
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.17"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":new Point(50,480)
               });
               break;
            case 10003:
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.17"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":new Point(50,480)
               });
               break;
            case 10100:
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.12"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":new Point(50,480)
               });
         }
      }
      
      private function createMessage(param1:MessageSend) : Object
      {
         MsgSendCollProxy(facade.retrieveProxy("chat.mvc.proxy.MsgSendCollProxy")).addMessage(param1);
         var _loc3_:Object = {};
         var _loc2_:UserData = MainData.getInstance().userData;
         _loc3_.message_text = param1.messageText;
         _loc3_.channel = getChannelIDByName(param1.sendChannel);
         _loc3_.sender_uid = _loc2_.uid;
         _loc3_.sender_uname = _loc2_.uname;
         _loc3_.sender_utid = _loc2_.utid;
         _loc3_.send_time = TimeManager.getInstance().getTime();
         _loc3_.target = param1.target;
         _loc3_.receive_utid = param1.utid;
         _loc3_.showFace = 1;
         return _loc3_;
      }
   }
}

