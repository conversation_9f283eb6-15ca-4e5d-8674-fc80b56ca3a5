package chat.mvc.command
{
   import chat.mvc.proxy.NetConnectProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class GMCommand extends SimpleCommand
   {
      public function GMCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc2_:NetConnectProxy = facade.retrieveProxy("chat.mvc.proxy.GMListProxy") as NetConnectProxy;
         switch(param1.getName())
         {
            case "CS_GM_GETQUESTION":
               _loc2_.getGMInfos();
               break;
            case "CS_GM_SENDQUESTION":
               _loc2_.sendQuestion(param1.getBody());
         }
      }
   }
}

