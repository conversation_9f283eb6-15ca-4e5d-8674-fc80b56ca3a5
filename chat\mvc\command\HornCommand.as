package chat.mvc.command
{
   import chat.mvc.proxy.ItemInfoProxy;
   import chat.mvc.proxy.MessageSend;
   import flash.geom.Point;
   import game.data.MainData;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   import game.manager.XmlManager;
   import game.modules.card.manager.CardManager;
   import game.mvc.AppFacade;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   import util.Globalization;
   
   public class HornCommand extends SimpleCommand
   {
      private var msgSend:MessageSend;
      
      private var channel:String;
      
      public function HornCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc9_:String = null;
         var _loc7_:String = null;
         var _loc8_:int = 0;
         var _loc2_:ItemInfoProxy = null;
         var _loc3_:Object = null;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         switch(param1.getName())
         {
            case "CS_HORN_SENDMSG":
               _loc9_ = "";
               this.msgSend = param1.getBody() as MessageSend;
               this.channel = this.msgSend.sendChannel;
               if(this.msgSend.messageText == "")
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("chat.1"),
                     "textFormat":TextFormatLib.format_0xFF0000_12px,
                     "point":new Point(50,480)
                  });
                  return;
               }
               if(this.channel == "speaker")
               {
                  _loc9_ = "chat.sendBroadCast";
               }
               if(!BabelTimeSocket.getInstance().hasEventListener("re.chat.sendHorn"))
               {
                  BabelTimeSocket.getInstance().regCallback("re.chat.sendHorn",this.sendHornHandler);
               }
               BabelTimeSocket.getInstance().sendMessage(_loc9_,new SocketCallback("re.chat.sendHorn"),this.msgSend.messageText,this.msgSend.hornType);
               break;
            case "CS_CARD_HORN":
               _loc9_ = "";
               this.msgSend = param1.getBody().msg as MessageSend;
               this.channel = this.msgSend.sendChannel;
               _loc7_ = MainData.getInstance().userData.uname;
               _loc8_ = int(param1.getBody().needGold);
               if(this.msgSend.messageText == "")
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("chat.1"),
                     "textFormat":TextFormatLib.format_0xFF0000_12px,
                     "point":new Point(50,480)
                  });
                  return;
               }
               if(this.channel == "cardHorn")
               {
                  _loc9_ = "chat.sendBroadCastInCardServer";
               }
               if(!BabelTimeSocket.getInstance("SOCKET_GENERAL_CARD").hasEventListener("re.chat.sendBroadCastInCardServer"))
               {
                  BabelTimeSocket.getInstance("SOCKET_GENERAL_CARD").regCallback("re.chat.sendBroadCastInCardServer",this.sendCardHornHandler);
               }
               BabelTimeSocket.getInstance("SOCKET_GENERAL_CARD").sendMessage(_loc9_,new SocketCallback("re.chat.sendBroadCastInCardServer",[_loc8_]),_loc7_,this.msgSend.messageText);
               break;
            case "CS_GRAP_REDPAPER":
               if(!AppFacade.instance.hasProxy("chat.mvc.proxy.ItemInfoProxy"))
               {
                  AppFacade.instance.registerProxy(new ItemInfoProxy());
               }
               _loc2_ = facade.retrieveProxy("chat.mvc.proxy.ItemInfoProxy") as ItemInfoProxy;
               _loc3_ = param1.getBody();
               _loc6_ = int(_loc3_.redId);
               _loc5_ = int(_loc3_.redLocation);
               _loc4_ = int(_loc3_.redType);
               _loc2_.grapRedPaperInfo(_loc4_,_loc5_,_loc6_);
         }
      }
      
      private function sendHornHandler(param1:SocketDataEvent) : void
      {
         var _loc6_:int = 0;
         var _loc4_:Object = null;
         var _loc5_:* = undefined;
         var _loc2_:Object = null;
         var _loc3_:Item = null;
         BabelTimeSocket.getInstance().removeCallback("re.chat.sendHorn",this.sendHornHandler);
         if(this.msgSend.hornType == 1)
         {
            _loc6_ = int(XmlManager.speaker.Speaker.@costGold);
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - _loc6_;
         }
         else if(param1.data)
         {
            _loc4_ = param1.data;
            for(_loc5_ in _loc4_)
            {
               _loc2_ = _loc4_[_loc5_];
               _loc3_ = ItemFactory.creatItem(_loc2_);
               if(MainData.getInstance().bagData.available)
               {
                  MainData.getInstance().bagData.setGridItem(_loc5_,_loc3_);
               }
            }
         }
         sendNotification("horn_send_success");
      }
      
      private function sendCardHornHandler(param1:SocketDataEvent) : void
      {
         var _loc2_:int = int(param1.callbackParames[0]);
         BabelTimeSocket.getInstance("SOCKET_GENERAL_CARD").removeCallback("re.chat.sendBroadCastInCardServer",this.sendCardHornHandler);
         CardManager.getInstance().myself.bankGold = CardManager.getInstance().myself.bankGold - _loc2_;
         if(param1.data == "noGold")
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("horn.17"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
            return;
         }
         sendNotification("horn_send_success");
      }
   }
}

