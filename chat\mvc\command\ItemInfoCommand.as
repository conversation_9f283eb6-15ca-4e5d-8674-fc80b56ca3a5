package chat.mvc.command
{
   import chat.mvc.proxy.ItemInfoProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class ItemInfoCommand extends SimpleCommand
   {
      public function ItemInfoCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc2_:ItemInfoProxy = facade.retrieveProxy("chat.mvc.proxy.ItemInfoProxy") as ItemInfoProxy;
         _loc2_.getItemInfo(int(param1.getBody()));
      }
   }
}

