package chat.mvc.command
{
   import chat.mvc.proxy.MessageSend;
   import chat.mvc.proxy.TownTeamChatProxy;
   import flash.geom.Point;
   import flash.utils.Dictionary;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   import util.Globalization;
   
   public class TownTeamChatCommand extends SimpleCommand
   {
      private var timerMap:Dictionary;
      
      private var msgSend:MessageSend;
      
      public function TownTeamChatCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc3_:TownTeamChatProxy = AppFacade.instance.retrieveProxy("chat.mvc.proxy.TownTeamChatProxy") as TownTeamChatProxy;
         var _loc2_:String = "";
         this.msgSend = param1.getBody() as MessageSend;
         if(this.msgSend.messageText == "")
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("chat.1"),
               "textFormat":TextFormatLib.format_0xFF0000_12px,
               "point":new Point(50,480)
            });
            return;
         }
         _loc3_.send(this.msgSend.messageText);
      }
   }
}

