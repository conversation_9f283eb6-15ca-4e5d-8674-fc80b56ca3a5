package chat.mvc.mediator
{
   import chat.mvc.view.SystemBoardCastPane;
   import game.modules.chat.msgInfo.MessageReceive;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.mediator.Mediator;
   
   public class BoardCastMediator extends Mediator
   {
      public static const NAME:String = "chat.mvc.mediator.BoardCastMediator";
      
      public function BoardCastMediator(param1:Object = null)
      {
         super("chat.mvc.mediator.BoardCastMediator",param1);
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["CHAT_SYSTEM_BOARDCAST","SC_ANNOUNCE_GETINFOS","horn_on_off"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc3_:MessageReceive = null;
         var _loc2_:Boolean = false;
         switch(param1.getName())
         {
            case "CHAT_SYSTEM_BOARDCAST":
               _loc3_ = param1.getBody() as MessageReceive;
               SystemBoardCastPane(viewComponent).addMsg(_loc3_);
               break;
            case "SC_ANNOUNCE_GETINFOS":
               SystemBoardCastPane(viewComponent).showAnnounceInfo(param1.getBody() as Array);
               break;
            case "horn_on_off":
               _loc2_ = param1.getBody() as Boolean;
               if(viewComponent)
               {
                  SystemBoardCastPane(viewComponent).visible = !_loc2_;
               }
         }
      }
   }
}

