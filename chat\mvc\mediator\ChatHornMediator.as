package chat.mvc.mediator
{
   import chat.event.SendMessageEvent;
   import chat.mvc.view.HornInputPannel;
   import chat.mvc.view.HornOutputPannel;
   import flash.events.Event;
   import game.core.scene.GameScene;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.mvc.module.ModuleParams;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.mediator.Mediator;
   
   public class ChatHornMediator extends Mediator
   {
      public static const NAME:String = "chat.mvc.mediator.ChatHornMediator";
      
      private var _input:HornInputPannel;
      
      private var _output:HornOutputPannel;
      
      private var _msg:MessageReceive;
      
      public var changeValue:Boolean = true;
      
      public function ChatHornMediator(param1:Object = null)
      {
         super("chat.mvc.mediator.ChatHornMediator",param1);
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["chat_horn_boardcast","horn_send_success","ENTER_CARD_HORN","EXIT_CARD_HORN","CS_PIRATE_ARENA_STATE"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         switch(param1.getName())
         {
            case "chat_horn_boardcast":
               if(GameScene.getCurrentScene() == 46)
               {
                  return;
               }
               this._msg = param1.getBody() as MessageReceive;
               if(this.changeValue)
               {
                  if(this._output && this._output.parent)
                  {
                     this._output.addMessage(this._msg);
                  }
                  else
                  {
                     this.showOutput();
                  }
               }
               else
               {
                  if(this._msg.channel == "speaker")
                  {
                     return;
                  }
                  if(this._output && this._output.parent)
                  {
                     this._output.addMessage(this._msg);
                  }
                  else
                  {
                     this.showOutput();
                  }
               }
               break;
            case "horn_send_success":
               if(this._input)
               {
                  this._input.setSendStatu();
               }
               break;
            case "ENTER_CARD_HORN":
               this.changeValue = false;
               if(this._output)
               {
                  this._output.close();
               }
               break;
            case "EXIT_CARD_HORN":
               this.changeValue = true;
               if(this._input)
               {
                  this._input.close();
               }
               if(this._output)
               {
                  this._output.close();
               }
               break;
            case "CS_PIRATE_ARENA_STATE":
               if(param1.getBody())
               {
                  if(this._output)
                  {
                     this._output.visible = false;
                  }
               }
               else if(this._output)
               {
                  this._output.visible = true;
               }
         }
      }
      
      public function registerInput(param1:HornInputPannel) : void
      {
         this._input = param1;
         this._input.addEventListener("sendMsg",this.inputHandler);
         this._input.addEventListener("close_horn_input",this.closeInputHandler);
      }
      
      public function registerOutput(param1:HornOutputPannel) : void
      {
         this._output = param1;
         this._output.initFaceBox();
         this._output.addEventListener("close_horn_output",this.outputHandler);
         if(this._msg)
         {
            this._output.addMessage(this._msg);
         }
         this._msg = null;
      }
      
      private function showOutput() : void
      {
         var _loc1_:ModuleParams = new ModuleParams("BoardHornOutputModule");
         sendNotification("HANDLE_MODULE",_loc1_);
      }
      
      private function inputHandler(param1:SendMessageEvent) : void
      {
         var _loc2_:int = 0;
         if(this._input.judgeSever)
         {
            sendNotification("CS_HORN_SENDMSG",param1.sendMsg);
         }
         else
         {
            _loc2_ = this._input.needGold;
            sendNotification("CS_CARD_HORN",{
               "msg":param1.sendMsg,
               "needGold":_loc2_
            });
         }
      }
      
      private function closeInputHandler(param1:Event) : void
      {
         if(this._input)
         {
            this._input.removeEventListener("sendMsg",this.inputHandler);
            this._input.removeEventListener("close_horn_input",this.closeInputHandler);
            this._input = null;
         }
      }
      
      private function outputHandler(param1:Event) : void
      {
         var _loc2_:* = param1.type;
         if("close_horn_output" === _loc2_)
         {
            this._output.removeEventListener("close_horn_output",this.outputHandler);
            sendNotification("DISPOSE_MODULE","BoardHornOutputModule");
            if(this._output && this._output.parent)
            {
               this._output.parent.removeChild(this._output);
               this._output.dispose();
            }
            this._output = null;
            this.isHorn = false;
         }
      }
      
      public function set isHorn(param1:Boolean) : void
      {
         sendNotification("horn_on_off",param1);
      }
      
      public function get isHorn() : Boolean
      {
         if(this._output)
         {
            return true;
         }
         return false;
      }
   }
}

