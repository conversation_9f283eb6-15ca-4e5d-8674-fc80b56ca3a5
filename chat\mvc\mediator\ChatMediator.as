package chat.mvc.mediator
{
   import chat.event.SendMessageEvent;
   import chat.mvc.command.AnnounceCommand;
   import chat.mvc.command.ChatCommand;
   import chat.mvc.command.HornCommand;
   import chat.mvc.command.ItemInfoCommand;
   import chat.mvc.proxy.ItemInfoProxy;
   import chat.mvc.proxy.MessageSend;
   import chat.mvc.proxy.MsgSendCollProxy;
   import chat.mvc.proxy.NetConnectProxy;
   import chat.mvc.view.ChatPanel;
   import com.greensock.TweenMax;
   import flash.display.DisplayObject;
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.events.TimerEvent;
   import flash.geom.Point;
   import flash.utils.ByteArray;
   import flash.utils.Dictionary;
   import flash.utils.Timer;
   import flash.utils.setTimeout;
   import game.Environment;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.ModuleData;
   import game.data.PirateMediator;
   import game.data.group.HeroData;
   import game.items.ItemManager;
   import game.items.framework.interfaces.IBasicInterface;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   import game.items.framework.templates.Template_Equipment;
   import game.items.framework.templates.Template_Treasure;
   import game.manager.GlobalDataManager;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.modules.MenuInfo;
   import game.modules.card.manager.CardManager;
   import game.modules.chat.msgInfo.MessageCollection;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.modules.chat.proxy.MsgReceiveCollProxy;
   import game.modules.elves.manager.ElvesManager;
   import game.modules.task.model.TeamTools;
   import game.modules.user.UserBaseInfoGroup;
   import game.mvc.AppFacade;
   import game.mvc.module.ModuleManager;
   import game.mvc.module.ModuleParams;
   import mmo.Config;
   import mmo.Core;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.infoMC.IconInfoMC;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   import util.StringToDate;
   import util.openModule;
   import util.time.TimeManager;
   
   public class ChatMediator extends PirateMediator
   {
      public static const NAME:String = "chat.mvc.mediator.ChatMediator";
      
      private const SHOWITEM_TIME:int = 15;
      
      private var showItemTimer:Timer;
      
      private var startTime:Number;
      
      private var timerMap:Dictionary;
      
      private var freshContentTime:Timer;
      
      private var isShow:Boolean;
      
      private var startTimeMap:Dictionary;
      
      private var timeSpaceMap:Dictionary;
      
      private var iconMCPoint:Point;
      
      public function ChatMediator(param1:Object = null)
      {
         super("chat.mvc.mediator.ChatMediator",param1);
         this.isShow = false;
         ChatPanel(param1).showHandler = this.showHandler;
         ChatPanel(param1).closeHandler = this.closeHandler;
         ChatPanel(param1).addEventListener("sendMsg",this.sendMsgHandler);
         ChatPanel(param1).addEventListener("link",this.onLink);
         ChatPanel(param1).addEventListener("changeChannel",this.changeChannelHandler);
         ChatPanel(param1).addEventListener("openGM",this.openGMHandler);
         ChatPanel(param1).addEventListener("openHorn",this.openHornHandler);
         ChatPanel(param1).addEventListener("keyUp",this.keyUPHandler);
         ChatPanel(param1).addEventListener("keyDown",this.keyDownHandler);
         this.showItemTimer = new Timer(1000);
         this.showItemTimer.addEventListener("timer",this.timerChangeHandler);
         this.timerMap = new Dictionary();
         this.timerMap["channelGroup"] = new Timer(1000);
         this.timerMap["channelGuild"] = new Timer(1000);
         this.timerMap["channelPrivately"] = new Timer(1000);
         this.timerMap["channelWorld"] = new Timer(1000);
         this.timeSpaceMap = new Dictionary();
         this.timeSpaceMap["channelGroup"] = 3000;
         this.timeSpaceMap["channelGuild"] = 1000;
         this.timeSpaceMap["channelPrivately"] = 1000;
         this.timeSpaceMap["channelWorld"] = 3000;
         this.startTimeMap = new Dictionary();
         this.freshContentTime = new Timer(1000);
         this.freshContentTime.addEventListener("timer",this.freshChatContent);
         this.refreshMsgInfo("channelGeneral");
      }
      
      private function closeHandler() : void
      {
         this.freshContentTime.stop();
      }
      
      private function showHandler(param1:Object) : void
      {
         checkDataAvialable(this.checkOver);
      }
      
      private function checkOver() : void
      {
         this.freshChatContent(null);
         this.freshContentTime.reset();
         this.freshContentTime.start();
         if(Config.gm)
         {
            this.tweenToBig(ChatPanel(viewComponent)._chatMc.btn_gm);
         }
         if(!this.isShow)
         {
            sendNotification("CS_ANNOUNCE_GETINFOS");
            this.isShow = true;
         }
      }
      
      private function freshChatContent(param1:TimerEvent) : void
      {
         var _loc2_:String = ChatPanel(viewComponent).currentChannel;
         this.refreshMsgInfo(_loc2_,true);
      }
      
      private function keyDownHandler(param1:KeyboardEvent) : void
      {
         param1.stopImmediatePropagation();
      }
      
      private function keyUPHandler(param1:KeyboardEvent) : void
      {
         var _loc2_:String = null;
         param1.stopImmediatePropagation();
         switch(int(param1.keyCode) - 38)
         {
            case 0:
               _loc2_ = MsgSendCollProxy(facade.retrieveProxy("chat.mvc.proxy.MsgSendCollProxy")).getPreMessage();
               _loc2_ && ChatPanel(viewComponent).setChatInfo(_loc2_);
               break;
            case 2:
               _loc2_ = MsgSendCollProxy(facade.retrieveProxy("chat.mvc.proxy.MsgSendCollProxy")).getNextMessage();
               _loc2_ && ChatPanel(viewComponent).setChatInfo(_loc2_);
         }
      }
      
      private function timerChangeHandler(param1:TimerEvent) : void
      {
         var _loc2_:Number = TimeManager.getInstance().getTime();
         if((_loc2_ - this.startTime) / 1000 >= 15)
         {
            this.showItemTimer.stop();
            this.showItemTimer.reset();
            this.startTime = 0;
         }
      }
      
      private function openGMHandler(param1:Event) : void
      {
         Config.gm = false;
         sendNotification("HANDLE_MODULE",new ModuleParams("GMModule",ModuleParams.act_Open));
      }
      
      private function openHornHandler(param1:Event) : void
      {
         if(!this.canSayInWorldAndHorn())
         {
            return;
         }
         var _loc2_:ModuleParams = new ModuleParams("BoardHornInputModule");
         sendNotification("HANDLE_MODULE",_loc2_);
      }
      
      private function changeChannelHandler(param1:DataEvent) : void
      {
         this.freshContentTime.stop();
         this.refreshMsgInfo(param1.data);
         this.freshContentTime.reset();
         this.freshContentTime.start();
      }
      
      private function canSayInWorldAndHorn() : Boolean
      {
         if(!Environment.loadingParams.hasOwnProperty("openDateTime"))
         {
            return false;
         }
         var _loc4_:int = MainData.getInstance().groupData.roleModle.level;
         var _loc3_:Number = TimeManager.getInstance().getTime();
         var _loc1_:Array = Environment.loadingParams.openDateTime.toString().split("-");
         var _loc2_:Number = 5184000000 + new Date(_loc1_[0],_loc1_[1] - 1,_loc1_[2],_loc1_[3],_loc1_[4],_loc1_[5]).time;
         if(_loc3_ >= _loc2_)
         {
            if(_loc4_ >= 38)
            {
               return true;
            }
            sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("chat.45"),38),
               "textFormat":TextFormatLib.format_0xFF0000_12px,
               "point":new Point(50,480)
            });
            return false;
         }
         if(_loc4_ < 20)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("chat.45"),20),
               "textFormat":TextFormatLib.format_0xFF0000_12px,
               "point":new Point(50,480)
            });
            return false;
         }
         return true;
      }
      
      private function refreshMsgInfo(param1:String, param2:Boolean = false) : void
      {
         var _loc3_:MsgReceiveCollProxy = MsgReceiveCollProxy(facade.retrieveProxy("chat.mvc.proxy.MsgReceiveCollProxy"));
         var _loc4_:MessageCollection = _loc3_.getMsgListByChannel(param1);
         if(param2 && !_loc4_.hasNewMsg)
         {
            return;
         }
         _loc4_.hasNewMsg = false;
         ChatPanel(viewComponent).setChatContent(_loc4_.getMessageList());
      }
      
      private function onLink(param1:TextEvent) : void
      {
         var data:Array = null;
         var info:String = null;
         var pos:Point = null;
         var uid:int = 0;
         var mp:ModuleParams = null;
         var needTimer:int = 0;
         var mapId:int = 0;
         var infos:Array = null;
         var mps:ModuleParams = null;
         var itemInfo:String = null;
         var byteArr:ByteArray = null;
         var dt:Object = null;
         var item:Item = null;
         var curStr:String = null;
         var dayStarTime:String = null;
         var dayEndTime:String = null;
         var curStart:Number = NaN;
         var curEnd:Number = NaN;
         var nowTime:Number = NaN;
         var grapObj:Object = null;
         var tempID:int = 0;
         var temp:IBasicInterface = null;
         var xml:* = undefined;
         var i:int = 0;
         var n:String = null;
         var curid:int = 0;
         var level:int = 0;
         var strT:String = null;
         var event:TextEvent = param1;
         var type:String = event.text.split("_")[0];
         info = event.text.split("_")[1];
         var server:int = int(event.text.split("_")[2]);
         switch(type)
         {
            case "user":
               if(int(info) == MainData.getInstance().userData.uid)
               {
                  return;
               }
               pos = new Point(ChatPanel(viewComponent).stage.mouseX + 10,ChatPanel(viewComponent).stage.mouseY);
               MenuInfo.show(pos.x,pos.y,int(info),null,"",server);
               break;
            case "wupin":
               this.iconMCPoint = new Point(ChatPanel(viewComponent).width,ChatPanel(viewComponent).stage.mouseY + 5);
               if(int(info) == 0)
               {
                  tempID = int(event.text.split("_")[2]);
                  temp = ItemManager.getInstance().getItemTemplate(tempID.toString());
                  this.showItemInfo(null,temp);
                  return;
               }
               sendNotification("CS_ITEMINFO",int(info));
               break;
            case "hero":
               uid = int(event.text.split("_")[2]);
               sendNotification("CS_GETHEROINFO",[int(info),uid]);
               break;
            case "battle":
               if(CardManager.getInstance().myself.currentState != 3)
               {
                  if(CardManager.getInstance().myself.currentState != 2)
                  {
                     sendNotification("CS_PLAY_BATTLE_RECORD",event.text.replace(type + "_",""));
                  }
               }
               break;
            case "baseHero":
               mp = new ModuleParams("HeroInfoModule",ModuleParams.act_Open);
               mp.isCenter = true;
               mp.isModel = false;
               mp.data = int(info);
               sendNotification("HANDLE_MODULE",mp);
               break;
            case "team":
               needTimer = 0;
               mapId = GlobalDataManager.instance.heroInfo.mapid;
               for each(xml in XmlManager.abyss_room.abyss_room)
               {
                  if(int(xml.@townId) == mapId)
                  {
                     AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                        "text":Globalization.getString("townteamfight.33"),
                        "textFormat":TextFormatLib.format_0xFF0000_14px
                     });
                     return;
                  }
               }
               if(TeamTools.checkTeamModuleOpen())
               {
                  return;
               }
               infos = info.split("|");
               if(infos[2] == MainData.getInstance().userData.uid)
               {
                  return;
               }
               if(MainData.getInstance().userData.autoJoinTeamLock)
               {
                  return;
               }
               if(ModuleManager.instance.chkModuleIsOpen("TeamChallengeTeamOperationModule"))
               {
                  sendNotification("HANDLE_MODULE",new ModuleParams("TeamChallengeTeamOperationModule",ModuleParams.act_Close));
                  needTimer = 500;
               }
               if(ModuleManager.instance.chkModuleIsOpen("TeamModule"))
               {
                  sendNotification("HANDLE_MODULE",new ModuleParams("TeamModule",ModuleParams.act_Close));
               }
               if(ModuleManager.instance.chkModuleIsOpen("ActivityTeamChooserWindow"))
               {
                  sendNotification("HANDLE_MODULE",new ModuleParams("ActivityTeamChooserWindow",ModuleParams.act_Close));
               }
               if(ModuleManager.instance.chkModuleIsOpen("CopyLegionChooseModule"))
               {
                  sendNotification("HANDLE_MODULE",new ModuleParams("CopyLegionChooseModule",ModuleParams.act_Close));
               }
               MainData.getInstance().userData.autoJoinTeamLock = true;
               mps = new ModuleParams(infos[0],ModuleParams.act_Open);
               mps.isCenter = true;
               mps.isModel = false;
               mps.data = {
                  "autoJoinRoomId":int(infos[1]),
                  "autoJoinTeamId":int(infos[2])
               };
               if(needTimer == 0)
               {
                  sendNotification("HANDLE_MODULE",mps);
               }
               else
               {
                  setTimeout(function():void
                  {
                     sendNotification("HANDLE_MODULE",mps);
                  },needTimer);
               }
               break;
            case "wupinInfo":
               itemInfo = info;
               byteArr = new ByteArray();
               i = 0;
               while(i < itemInfo.length)
               {
                  n = itemInfo.charAt(i) + itemInfo.charAt(i + 1);
                  byteArr[i / 2] = parseInt(n,16);
                  i = i + 2;
               }
               byteArr.position = 0;
               dt = byteArr.readObject();
               item = ItemFactory.creatItem(dt,false,false);
               this.iconMCPoint = new Point(ChatPanel(viewComponent).width,ChatPanel(viewComponent).stage.mouseY + 5);
               this.showItemInfo(item,item.template);
               break;
            case "notification":
               info = event.text.replace(/notification\_/,"");
               sendNotification(info);
               break;
            case "module":
               info = event.text.replace(/module\_/,"");
               if(info == "BloodyBattlePrepareTeamModule")
               {
                  if(TeamTools.checkTeamModuleOpen())
                  {
                     return;
                  }
                  if(!MainData.getInstance().openSwitchData.isAlreadyOpen(50))
                  {
                     AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                        "text":Globalization.getString("BloodyBattle.37"),
                        "textFormat":TextFormatLib.format_0xFF0000_14px
                     });
                     return;
                  }
               }
               if(info == "AttackOnGeneralsTeamModule")
               {
                  if(TeamTools.checkTeamModuleOpen())
                  {
                     return;
                  }
                  if(!MainData.getInstance().openSwitchData.isAlreadyOpen(52))
                  {
                     AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                        "text":Globalization.getString("BloodyBattle.37"),
                        "textFormat":TextFormatLib.format_0xFF0000_14px
                     });
                     return;
                  }
                  GameScene.enterScene(38);
                  return;
               }
               if(info == "CrossServerTeamCompModule")
               {
                  if(TeamTools.checkTeamModuleOpen())
                  {
                     return;
                  }
                  if(MainData.getInstance().teamChallengeData.isJoin)
                  {
                     AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                        "text":Globalization.getString("crossServerTeam.9"),
                        "textFormat":TextFormatLib.format_0xFF0000_14px
                     });
                     return;
                  }
                  curid = MainData.getInstance().teamChallengeData.id;
                  level = int(XmlManager.teamConquest.teamConquest.(@id == curid)[0].@level);
                  if(MainData.getInstance().groupData.roleModle.level < level)
                  {
                     strT = StringUtil.substitute(Globalization.getString("crossServerTeam.23"),level);
                     AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                        "text":strT,
                        "textFormat":TextFormatLib.format_0xFF0000_14px
                     });
                     return;
                  }
               }
               openModule(info);
               break;
            case "moduleparameters":
               data = info.split("|");
               if(data[0] == "TeamChallengeTeamOperationModule")
               {
                  if(TeamTools.checkTeamModuleOpen())
                  {
                     return;
                  }
                  ModuleData.abyssCopysCS(data[1]);
               }
               break;
            case "redpaperlink":
               curStr = MainData.getInstance().userData.redPaperOpenEndTimeStr;
               dayStarTime = (curStr.split("|") as Array)[0];
               dayEndTime = (curStr.split("|") as Array)[1];
               curStart = StringToDate.praseTimeStrToNum(dayStarTime);
               curEnd = StringToDate.praseTimeStrToNum(dayEndTime);
               nowTime = TimeManager.getInstance().getTime();
               if(nowTime > curStart && nowTime < curEnd)
               {
                  data = event.text.split("_");
                  grapObj = {};
                  grapObj.redId = data[2];
                  grapObj.redLocation = data[3];
                  grapObj.redType = data[4];
                  sendNotification("CS_GRAP_REDPAPER",grapObj);
                  break;
               }
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("redPaper.32"),
                  "textFormat":TextFormatLib.red_12px
               });
               return;
         }
      }
      
      private function showItemInfo(param1:Item, param2:IBasicInterface) : void
      {
         if(param2 is Template_Treasure && Template_Treasure(param2).suit_id > 0)
         {
            IconInfoMC.setContrastObjectContent([{
               "txt":"",
               "temp":param2,
               "item":param1,
               "close":true
            },{
               "txt":"",
               "temp":param2,
               "item":param1,
               "append":true
            }]);
            IconInfoMC.showInfoMcs(this.iconMCPoint,20);
         }
         else if(param2 is Template_Equipment && Template_Equipment(param2).suit_id > 0)
         {
            IconInfoMC.setContrastObjectContent([{
               "txt":"",
               "temp":param2,
               "item":param1,
               "close":true
            },{
               "txt":"",
               "temp":param2,
               "item":param1,
               "append":true
            }]);
            IconInfoMC.showInfoMcs(this.iconMCPoint,20);
         }
         else
         {
            IconInfoMC.setContent(param2,param1,180,true);
            IconInfoMC.show(this.iconMCPoint,20);
         }
      }
      
      private function sendMsgHandler(param1:SendMessageEvent) : void
      {
         var id:int;
         var sendMsg:MessageSend = null;
         var getUid:Function = null;
         var event:SendMessageEvent = param1;
         getUid = function(param1:int):void
         {
            if(param1 == 0)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.7"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":new Point(50,480)
               });
               return;
            }
            if(param1 == MainData.getInstance().userData.uid)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.12"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":new Point(50,480)
               });
               return;
            }
            sendMsg.target = param1.toString();
            timerMap[sendMsg.sendChannel].reset();
            timerMap[sendMsg.sendChannel].start();
            startTimeMap[sendMsg.sendChannel] = TimeManager.getInstance().getTime();
            sendNotification("CS_CHAT_SENDMSG",sendMsg);
         };
         if(MainData.getInstance().userData.ban_chat_time * 1000 > TimeManager.getInstance().getTime())
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("chat.13"),
               "textFormat":TextFormatLib.format_0xFF0000_12px,
               "point":new Point(50,480)
            });
            return;
         }
         sendMsg = event.sendMsg;
         if(this.timerMap[sendMsg.sendChannel] && TimeManager.getInstance().getTime() - this.startTimeMap[sendMsg.sendChannel] < this.timeSpaceMap[sendMsg.sendChannel])
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("chat.4"),
               "textFormat":TextFormatLib.format_0xFF0000_12px,
               "point":new Point(50,480)
            });
            return;
         }
         if(sendMsg.sendChannel == "channelGuild")
         {
            if(MainData.getInstance().userData.guild_id == 0)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.5"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":new Point(50,480)
               });
               return;
            }
         }
         if(sendMsg.sendChannel == "channelGroup")
         {
            if(MainData.getInstance().userData.group_id == 0)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.11"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":new Point(50,480)
               });
               return;
            }
         }
         if(sendMsg.sendChannel == "channelWorld" && !this.canSayInWorldAndHorn())
         {
            return;
         }
         if(sendMsg.sendChannel == "channelPrivately" && MainData.getInstance().groupData.roleModle.level < 15)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":StringUtil.substitute(Globalization.getString("chat.44"),15),
               "textFormat":TextFormatLib.format_0xFF0000_12px,
               "point":new Point(50,480)
            });
            return;
         }
         if(!ElvesManager.getInstance().isOpenFace())
         {
            sendMsg.checkQiaobaMeg();
         }
         if(sendMsg.sendChannel != "channelPrivately")
         {
            this.timerMap[sendMsg.sendChannel] && this.timerMap[sendMsg.sendChannel].reset();
            this.timerMap[sendMsg.sendChannel] && this.timerMap[sendMsg.sendChannel].start();
            this.timerMap[sendMsg.sendChannel] && (this.startTimeMap[sendMsg.sendChannel] = TimeManager.getInstance().getTime());
            sendNotification("CS_CHAT_SENDMSG",sendMsg);
            return;
         }
         if(sendMsg.target == "")
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("chat.6"),
               "textFormat":TextFormatLib.format_0xFF0000_12px,
               "point":new Point(50,480)
            });
            return;
         }
         id = UserBaseInfoGroup.instance.getUIDByUName(sendMsg.target);
         if(id)
         {
            if(id == MainData.getInstance().userData.uid)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.12"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":new Point(50,480)
               });
               return;
            }
            sendMsg.target = id.toString();
            this.timerMap[sendMsg.sendChannel].reset();
            this.timerMap[sendMsg.sendChannel].start();
            this.startTimeMap[sendMsg.sendChannel] = TimeManager.getInstance().getTime();
            sendNotification("CS_CHAT_SENDMSG",sendMsg);
         }
         else
         {
            sendNotification("CS_USER_GETUSERID",{
               "name":sendMsg.target,
               "callBack":getUid
            });
         }
      }
      
      override public function onRegister() : void
      {
         facade.registerCommand("CS_HORN_SENDMSG",HornCommand);
         facade.registerCommand("CS_CARD_HORN",HornCommand);
         facade.registerCommand("CS_CHAT_SENDMSG",ChatCommand);
         facade.registerCommand("CS_ITEMINFO",ItemInfoCommand);
         facade.registerCommand("CS_GRAP_REDPAPER",HornCommand);
         facade.registerCommand("CS_ANNOUNCE_GETINFOS",AnnounceCommand);
         facade.registerProxy(new ItemInfoProxy());
         facade.registerProxy(new MsgSendCollProxy());
         facade.registerProxy(new NetConnectProxy());
      }
      
      override public function onRemove() : void
      {
         facade.removeCommand("CS_CHAT_SENDMSG");
         facade.removeCommand("CS_ITEMINFO");
         facade.removeProxy("chat.mvc.proxy.MsgSendCollProxy");
         facade.removeProxy("chat.mvc.proxy.ItemInfoProxy");
         facade.removeCommand("CS_ANNOUNCE_GETINFOS");
         facade.removeCommand("CS_GRAP_REDPAPER");
         facade.removeProxy("chat.mvc.proxy.GMListProxy");
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["CHAT_REFRESH_CHANNELNSG","CHAT_SHOWITEM","CHAT_SHOWHERO","CHAT_SHOWBATTLE","SC_ITEMINFO","CHAT_PRIVATECHAT","CHAT_SENDMSG","HIDE_CHAT_PANEL","STAGE_RESIZE","SC_GM_GETQUESTION","SHOW_CHAT_PANEL","ENTER_CARD_HORN","EXIT_CARD_HORN","CHAT_INPUT_SET_FOCUS","CS_PIRATE_ARENA_STATE","BLACK_SHOP_SHOW_MSG","TO_COUNT_TIMER_UNDERGROUND"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc11_:Object = null;
         var _loc13_:MessageSend = null;
         var _loc17_:String = null;
         var _loc18_:String = null;
         var _loc21_:MessageSend = null;
         var _loc19_:Item = null;
         var _loc20_:String = null;
         var _loc3_:MessageSend = null;
         var _loc5_:HeroData = null;
         var _loc4_:MessageSend = null;
         var _loc7_:Object = null;
         var _loc2_:Item = null;
         var _loc15_:int = 0;
         var _loc16_:String = null;
         var _loc10_:Object = null;
         var _loc6_:MsgReceiveCollProxy = null;
         var _loc23_:Object = null;
         var _loc8_:MsgReceiveCollProxy = null;
         var _loc22_:Number = NaN;
         var _loc24_:int = 0;
         var _loc9_:MessageReceive = null;
         var _loc12_:MessageReceive = null;
         var _loc14_:String = MainData.getInstance().userData.uname;
         switch(param1.getName())
         {
            case "SC_GM_GETQUESTION":
               if(Config.gm)
               {
                  this.tweenToBig(ChatPanel(viewComponent)._chatMc.btn_gm);
               }
               break;
            case "CHAT_SENDMSG":
               ChatPanel(viewComponent)._chatMc.btn_all.dispatchEvent(new MouseEvent("click",true,true));
               _loc11_ = param1.getBody();
               _loc13_ = new MessageSend();
               _loc13_.messageText = _loc11_.msg;
               _loc13_.sendChannel = _loc11_.channel;
               if(_loc11_.hasOwnProperty("ignore"))
               {
                  _loc13_.ignore = _loc11_.ignore;
               }
               _loc13_.target = "";
               this.sendMsgHandler(new SendMessageEvent("sendMsg",_loc13_));
               break;
            case "CHAT_REFRESH_CHANNELNSG":
               _loc17_ = String(param1.getBody());
               _loc18_ = ChatPanel(viewComponent).currentChannel;
               if(_loc17_ == "channelPrivately" && _loc18_ != "channelPrivately")
               {
                  this.tweenToBig(ChatPanel(viewComponent)._chatMc.btn_chat);
               }
               break;
            case "CHAT_SHOWITEM":
               if(this.showItemTimer.running)
               {
                  _loc22_ = TimeManager.getInstance().getTime();
                  _loc24_ = Math.ceil((15 * 1000 - (_loc22_ - this.startTime)) / 1000);
                  sendNotification("POP_TEXT_TIPS",{
                     "text":StringUtil.substitute(Globalization.getString("chat.3"),_loc24_),
                     "textFormat":TextFormatLib.format_0xFF0000_12px
                  });
                  return;
               }
               this.showItemTimer.start();
               this.startTime = TimeManager.getInstance().getTime();
               _loc21_ = new MessageSend();
               _loc19_ = param1.getBody() as Item;
               _loc20_ = MessageReceive.getItemColorName(_loc19_.template.id);
               _loc21_.messageText = StringUtil.substitute(Globalization.getString("chat.8"),"wupin",_loc19_.item_id,_loc20_);
               _loc21_.sendChannel = ChatPanel(viewComponent).currentSayChannel;
               _loc21_.target = ChatPanel(viewComponent)._chatMc.chatUser.text;
               _loc21_.ignore = true;
               this.sendMsgHandler(new SendMessageEvent("sendMsg",_loc21_));
               break;
            case "CHAT_SHOWHERO":
               _loc3_ = new MessageSend();
               _loc5_ = param1.getBody() as HeroData;
               _loc3_.messageText = StringUtil.substitute(Globalization.getString("chat.9"),"hero",_loc5_.hid + "_" + MainData.getInstance().userData.uid,"<font color=\'#00ff00\'>" + _loc5_.name + "</font>");
               _loc3_.sendChannel = ChatPanel(viewComponent).currentSayChannel;
               _loc3_.target = ChatPanel(viewComponent)._chatMc.chatUser.text;
               _loc3_.ignore = true;
               this.sendMsgHandler(new SendMessageEvent("sendMsg",_loc3_));
               break;
            case "CHAT_SHOWBATTLE":
               _loc4_ = new MessageSend();
               _loc7_ = param1.getBody();
               _loc4_.messageText = "<font color=\'#00ff00\'>" + StringUtil.substitute(Globalization.getString("chat.10"),"battle",_loc7_.id,_loc7_.src,_loc7_.target) + "</font>";
               _loc4_.sendChannel = ChatPanel(viewComponent).currentSayChannel;
               _loc4_.target = ChatPanel(viewComponent)._chatMc.chatUser.text;
               _loc4_.ignore = true;
               this.sendMsgHandler(new SendMessageEvent("sendMsg",_loc4_));
               break;
            case "SC_ITEMINFO":
               _loc2_ = param1.getBody() as Item;
               if(_loc2_ != null)
               {
                  this.showItemInfo(_loc2_,_loc2_.template);
               }
               break;
            case "CHAT_PRIVATECHAT":
               _loc7_ = param1.getBody();
               _loc15_ = int(_loc7_[0]);
               _loc16_ = _loc7_[1];
               if(!UserBaseInfoGroup.instance.getUNameByUID(_loc15_))
               {
                  UserBaseInfoGroup.instance.addUserBaseInfo(_loc15_,_loc16_);
               }
               ChatPanel(viewComponent).privateChat(_loc15_,_loc16_);
               break;
            case "HIDE_CHAT_PANEL":
               ChatPanel(viewComponent)._chatMc.btn_close.dispatchEvent(new MouseEvent("click"));
               break;
            case "SHOW_CHAT_PANEL":
               if(ChatPanel(viewComponent)._chatMc.btn_open.visible)
               {
                  ChatPanel(viewComponent)._chatMc.btn_open.dispatchEvent(new MouseEvent("click"));
               }
               break;
            case "STAGE_RESIZE":
               ChatPanel(viewComponent).y = Core.stgH;
               break;
            case "ENTER_CARD_HORN":
               ChatPanel(viewComponent)._chatMc.btn_horn.setToolTip(Globalization.getString("horn.18"),"rightMiddle");
               ChatPanel(viewComponent)._chatMc.btn_horn.setSkins(UIManager.getMultiUISkin("CardHornBtn"));
               break;
            case "EXIT_CARD_HORN":
               ChatPanel(viewComponent)._chatMc.btn_horn.setToolTip(Globalization.getString("horn.12"),"rightMiddle");
               ChatPanel(viewComponent)._chatMc.btn_horn.setSkins(UIManager.getMultiUISkin("HornEnterBtn"));
               break;
            case "CHAT_INPUT_SET_FOCUS":
               ChatPanel(viewComponent).setInputFocus();
               break;
            case "CS_PIRATE_ARENA_STATE":
               if(param1.getBody())
               {
                  ChatPanel(viewComponent)._chatMc.btn_horn.visible = false;
               }
               else
               {
                  ChatPanel(viewComponent)._chatMc.btn_horn.visible = true;
               }
               break;
            case "BLACK_SHOP_SHOW_MSG":
               _loc10_ = param1.getBody();
               _loc6_ = MsgReceiveCollProxy(facade.retrieveProxy("chat.mvc.proxy.MsgReceiveCollProxy"));
               if(_loc6_)
               {
                  _loc9_ = new MessageReceive(_loc10_);
                  _loc6_.setReceiveMessage(_loc9_);
               }
               break;
            case "TO_COUNT_TIMER_UNDERGROUND":
               _loc23_ = param1.getBody();
               _loc8_ = MsgReceiveCollProxy(facade.retrieveProxy("chat.mvc.proxy.MsgReceiveCollProxy"));
               if(_loc8_)
               {
                  _loc12_ = new MessageReceive(_loc23_);
                  _loc8_.setReceiveMessage(_loc12_);
               }
         }
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().userData];
      }
      
      private function tweenToBig(param1:DisplayObject) : void
      {
         TweenMax.to(param1,0.3,{
            "glowFilter":{
               "color":16776960,
               "inner":true,
               "alpha":1,
               "blurX":8,
               "blurY":8,
               "strength":3
            },
            "onComplete":this.tweenToSmall,
            "onCompleteParams":[param1]
         });
      }
      
      private function tweenToSmall(param1:DisplayObject) : void
      {
         TweenMax.to(param1,0.3,{
            "glowFilter":{
               "inner":true,
               "blurX":2,
               "blurY":2
            },
            "onComplete":this.tweenToBig,
            "onCompleteParams":[param1]
         });
      }
   }
}

