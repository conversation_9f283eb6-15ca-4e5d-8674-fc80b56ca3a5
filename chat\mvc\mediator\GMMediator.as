package chat.mvc.mediator
{
   import chat.event.GMQuestionEvent;
   import chat.mvc.command.GMCommand;
   import chat.mvc.proxy.NetConnectProxy;
   import chat.mvc.view.GMPanel;
   import flash.events.Event;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import game.data.PirateMediator;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.event.TabEvent;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   import util.time.TimeManager;
   
   public class GMMediator extends PirateMediator
   {
      public static const NAME:String = "chat.mvc.mediator.GMMediator";
      
      private var sendMsgTimer:Timer;
      
      private var startTime:Number;
      
      public function GMMediator(param1:Object = null)
      {
         super("chat.mvc.mediator.GMMediator",param1);
         GMPanel(param1).showHander = this.showHandler;
         GMPanel(param1).addEventListener("sendQuestion",this.sendQuestionHandler);
         GMPanel(param1).tabPane.addEventListener(TabEvent.Tab_IndexChange,this.changeTabHandler);
         this.sendMsgTimer = new Timer(1000);
         this.sendMsgTimer.addEventListener("timer",this.timerChangeHandler);
      }
      
      private function timerChangeHandler(param1:TimerEvent) : void
      {
         if(TimeManager.getInstance().getTime() - this.startTime >= 60000)
         {
            this.sendMsgTimer.stop();
            this.sendMsgTimer.reset();
         }
      }
      
      private function changeTabHandler(param1:Event) : void
      {
         var _loc2_:NetConnectProxy = null;
         if(GMPanel(viewComponent).tabPane.selectedIndex == 1)
         {
            _loc2_ = facade.retrieveProxy("chat.mvc.proxy.GMListProxy") as NetConnectProxy;
            GMPanel(viewComponent).showQuestions(_loc2_.gameList);
         }
      }
      
      private function showHandler(param1:Object) : void
      {
         sendNotification("CS_GM_GETQUESTION");
      }
      
      private function sendQuestionHandler(param1:GMQuestionEvent) : void
      {
         if(this.sendMsgTimer.running)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("chat.20"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
            return;
         }
         sendNotification("CS_GM_SENDQUESTION",{
            "classID":param1.questionType,
            "content":param1.content
         });
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_GM_SENDQUESTION"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc2_:* = param1.getName();
         if("SC_GM_SENDQUESTION" === _loc2_)
         {
            this.startTime = TimeManager.getInstance().getTime();
            this.sendMsgTimer.start();
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("chat.21"),
               "textFormat":TextFormatLib.format_0x00FF00_12px
            });
            GMPanel(viewComponent).clearMsg();
         }
      }
      
      override public function onRegister() : void
      {
         AppFacade.instance.registerMultiCommand(GMCommand,"CS_GM_GETQUESTION","CS_GM_SENDQUESTION");
      }
      
      override public function onRemove() : void
      {
         AppFacade.instance.removeMultiCommand("CS_GM_GETQUESTION","CS_GM_SENDQUESTION");
         this.sendMsgTimer.stop();
         this.sendMsgTimer.reset();
         this.sendMsgTimer.removeEventListener("timer",this.timerChangeHandler);
         this.sendMsgTimer = null;
      }
   }
}

