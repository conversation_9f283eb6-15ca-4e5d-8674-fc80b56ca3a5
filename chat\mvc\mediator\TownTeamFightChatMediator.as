package chat.mvc.mediator
{
   import chat.event.SendMessageEvent;
   import chat.mvc.view.TownTeamFightChatModule;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.modules.town.proxys.TownMapProxy;
   import game.mvc.AppFacade;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.mediator.Mediator;
   
   public class TownTeamFightChatMediator extends Mediator
   {
      public static const NAME:String = "chat.mvc.mediator.TownTeamFightChatMediator";
      
      private var _msg:MessageReceive;
      
      private var _vc:TownTeamFightChatModule;
      
      public function TownTeamFightChatMediator(param1:Object = null)
      {
         super("chat.mvc.mediator.TownTeamFightChatMediator",param1);
         this._vc = param1 as TownTeamFightChatModule;
         this._vc.showHander = this.showHander;
         this._vc.addEventListener("TOWN_TEAM_SENDMSG",this.inputHandler);
      }
      
      private function inputHandler(param1:SendMessageEvent) : void
      {
         sendNotification("CS_TOWN_TEAM_SENDMSG",param1.sendMsg);
      }
      
      private function showHander(param1:*) : void
      {
         this._vc.userList.clearAll();
         this._vc.userList.addItems(param1);
         this.addAllMsgs();
      }
      
      private function addAllMsgs() : void
      {
         var _loc7_:* = undefined;
         var _loc5_:String = null;
         var _loc6_:Date = null;
         var _loc1_:String = null;
         var _loc2_:String = null;
         var _loc4_:String = null;
         var _loc3_:String = null;
         var _loc8_:TownMapProxy = AppFacade.instance.retrieveProxy(TownMapProxy.NAME) as TownMapProxy;
         for each(_loc7_ in _loc8_.townTeamNewChats)
         {
            _loc5_ = _loc8_.townTeamUsersHash[_loc7_.uid].uname;
            _loc6_ = _loc7_.time;
            _loc1_ = _loc7_.content;
            _loc2_ = this.twoZeroNum(_loc6_.getHours());
            _loc4_ = this.twoZeroNum(_loc6_.getMinutes());
            _loc3_ = this.twoZeroNum(_loc6_.getSeconds());
            this._vc.addMsg(StringUtil.substitute("<font size=\'12\'><font color=\'#ffb932\'>{0}</font> <font color=\'#00ff00\'>{1}:{2}:{3}</font>\n<font color=\'#ffed89\'>{4}</font></font>",_loc5_,_loc2_,_loc4_,_loc3_,_loc1_));
         }
         _loc8_.townTeamNewChats.length = 0;
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_TOWN_TEAM_SENDMSG","TOWN_TEAM_FIGHT_CARD_TURN_CLOSE","CS_USER_ENTER_TOWN_UPDATE_USERS"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc2_:Array = null;
         var _loc3_:* = undefined;
         var _loc4_:TownMapProxy = this.facade.retrieveProxy(TownMapProxy.NAME) as TownMapProxy;
         switch(param1.getName())
         {
            case "SC_TOWN_TEAM_SENDMSG":
               this.addAllMsgs();
               break;
            case "TOWN_TEAM_FIGHT_CARD_TURN_CLOSE":
               this._vc.clearMsg();
               break;
            case "CS_USER_ENTER_TOWN_UPDATE_USERS":
               _loc2_ = [];
               for each(_loc3_ in _loc4_.townTeamUsersHash)
               {
                  _loc2_.push(_loc3_);
               }
               _loc2_.sortOn("index",16);
               this._vc.userList.clearAll();
               this._vc.userList.addItems(_loc2_);
         }
      }
      
      private function twoZeroNum(param1:int) : String
      {
         var _loc2_:String = "00" + param1;
         return _loc2_.substr(_loc2_.length - 2);
      }
   }
}

