package chat.mvc.mediator
{
   import chat.mvc.proxy.WorldBoatBoardProxy;
   import chat.mvc.view.WorldBoatBoardPane;
   import game.data.PirateMediator;
   import game.mvc.AppFacade;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   
   public class WorldBoatBoardMediator extends PirateMediator
   {
      public static const NAME:String = "chat.mvc.mediator.WorldBoatBoardMediator";
      
      public function WorldBoatBoardMediator(param1:Object = null)
      {
         super("chat.mvc.mediator.WorldBoatBoardMediator",param1);
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["CHAT_SYSTEM_BOARDCAST","horn_on_off","chat_horn_boardcast","SC_WORLD_BOAT_BOARD_MSG","CROSS_SERVER_SHIP_FIGHT_CLEARING","CROSS_SERVER_SHIP_FIGHT_QUIT"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc10_:WorldBoatBoardProxy = null;
         var _loc8_:Boolean = false;
         var _loc9_:* = undefined;
         var _loc2_:String = null;
         var _loc4_:String = null;
         var _loc7_:String = null;
         var _loc6_:String = null;
         var _loc5_:String = null;
         var _loc3_:String = null;
         switch(param1.getName())
         {
            case "SC_WORLD_BOAT_BOARD_MSG":
               _loc10_ = AppFacade.instance.retrieveProxy("chat.mvc.proxy.WorldBoatBoardProxy") as WorldBoatBoardProxy;
               if(_loc10_ == null)
               {
                  return;
               }
               for each(_loc9_ in _loc10_.worldBoatChats)
               {
                  _loc2_ = String(_loc9_.uid);
                  _loc4_ = _loc9_.content;
                  _loc7_ = "12";
                  _loc6_ = _loc4_.replace(_loc7_,"25");
                  _loc5_ = "FFFFFF";
                  _loc3_ = _loc6_.replace(_loc5_,"F6FF08");
                  WorldBoatBoardPane(viewComponent).addMsg(StringUtil.substitute("<font size=\'25\'><font color=\'#fc09e2\'>{0}：</font></font><font size=\'25\'><font color=\'#f6ff08\'>{1}</font></font>",_loc2_,_loc3_));
               }
               _loc10_.worldBoatChats.length = 0;
               break;
            case "horn_on_off":
               _loc8_ = param1.getBody() as Boolean;
               if(viewComponent)
               {
                  WorldBoatBoardPane(viewComponent).visible = !_loc8_;
               }
               break;
            case "CROSS_SERVER_SHIP_FIGHT_CLEARING":
               WorldBoatBoardPane(viewComponent).changeVisble(false);
               break;
            case "CROSS_SERVER_SHIP_FIGHT_QUIT":
               WorldBoatBoardPane(viewComponent).changeVisble(false);
         }
      }
   }
}

