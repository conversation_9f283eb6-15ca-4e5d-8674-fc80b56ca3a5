package chat.mvc.mediator
{
   import chat.event.WorldBoatMessageEvent;
   import chat.mvc.proxy.WorldBoatWarChatProxy;
   import chat.mvc.view.WorldBoatWarChatModule;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.mvc.AppFacade;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.mediator.Mediator;
   
   public class WorldBoatWarChatMediator extends Mediator
   {
      public static const NAME:String = "chat.mvc.mediator.WorldBoatWarChatMediator";
      
      private var _msg:MessageReceive;
      
      private var _vc:WorldBoatWarChatModule;
      
      public function WorldBoatWarChatMediator(param1:Object = null)
      {
         super("chat.mvc.mediator.WorldBoatWarChatMediator",param1);
         this._vc = param1 as WorldBoatWarChatModule;
         this._vc.showHander = this.showHander;
         this._vc.addEventListener("WORLD_BOAT_SENDMSG",this.inputHandler);
      }
      
      private function inputHandler(param1:WorldBoatMessageEvent) : void
      {
         sendNotification("CS_WORLD_BOAT_TALKMSG",param1.sendMsg);
      }
      
      private function showHander(param1:*) : void
      {
         this._vc.x = 750;
         this._vc.y = 330;
         sendNotification("CS_GET_WORLDBOAT_CHAT");
      }
      
      private function addPreMsgs() : void
      {
         var _loc3_:* = undefined;
         var _loc1_:String = null;
         var _loc2_:String = null;
         var _loc4_:WorldBoatWarChatProxy = AppFacade.instance.retrieveProxy("chat.mvc.proxy.WorldBoatWarChatProxy") as WorldBoatWarChatProxy;
         for each(_loc3_ in _loc4_.bigLaBaChats)
         {
            _loc1_ = String(_loc3_.uid);
            _loc2_ = _loc3_.content;
            this._vc.addMsg(StringUtil.substitute("<font size=\'12\'><font color=\'#ffb932\'>{0}：</font><font color=\'#ffed89\'>{1}</font></font>",_loc1_,_loc2_));
         }
         _loc4_.bigLaBaChats.length = 0;
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_WORLD_BOAT_SENDMSG","SC_GET_WORLDBOAT_CHAT","CROSS_SERVER_SHIP_FIGHT_CLEARING","WORLD_BOAT_CHAT_REFRESH_NUM"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc2_:Object = null;
         var _loc3_:int = 0;
         var _loc4_:WorldBoatWarChatProxy = this.facade.retrieveProxy("chat.mvc.proxy.WorldBoatWarChatProxy") as WorldBoatWarChatProxy;
         switch(param1.getName())
         {
            case "SC_WORLD_BOAT_SENDMSG":
               this.addPreMsgs();
               break;
            case "SC_GET_WORLDBOAT_CHAT":
               _loc2_ = param1.getBody();
               this._vc.freeNum = _loc2_.free_count;
               if(this._vc.freeNum > 0)
               {
                  this.judeB(false);
               }
               else
               {
                  this.judeB(true);
               }
               this._vc.updateNum();
               this.addPreMsgs();
               break;
            case "CROSS_SERVER_SHIP_FIGHT_CLEARING":
               this._vc.close();
               break;
            case "WORLD_BOAT_CHAT_REFRESH_NUM":
               _loc3_ = int(param1.getBody());
               this._vc.freeNum = _loc3_;
               if(this._vc.freeNum > 0)
               {
                  this.judeB(false);
               }
               else
               {
                  this.judeB(true);
               }
               this._vc.updateNum();
         }
      }
      
      private function judeB(param1:Boolean) : void
      {
         this._vc.goldCostLb.visible = param1;
         this._vc.goldSkin.visible = param1;
         this._vc.freeLb.visible = !param1;
         this._vc.goldSendNum.visible = param1;
      }
      
      private function twoZeroNum(param1:int) : String
      {
         var _loc2_:String = "00" + param1;
         return _loc2_.substr(_loc2_.length - 2);
      }
   }
}

