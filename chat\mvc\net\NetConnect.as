package chat.mvc.net
{
   import chat.mvc.proxy.NetConnectProxy;
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.net.URLVariables;
   import game.data.MainData;
   import game.mvc.AppFacade;
   import mmo.Config;
   import util.json.JSON;
   
   public class NetConnect
   {
      public static const QUESTION_URL:String = "http://www.zuiyouxi.com/question/do";
      
      public static const ANNOUNCE_URL:String = "http://www.zuiyouxi.com/notice/do";
      
      public static const DEBUG_QUESTION_URL:String = "http://192.168.1.203:10000/question/do";
      
      public static const DEBUG_ANNOUNCE_URL:String = "http://192.168.1.203:10000/notice/do";
      
      private var ldr:URLLoader;
      
      public function NetConnect()
      {
         super();
      }
      
      public function doAction(param1:Object, param2:String) : void
      {
         this.ldr = new URLLoader();
         this.ldr.addEventListener("complete",this.completeHandler);
         this.ldr.addEventListener("close",this.closeHandler);
         this.ldr.addEventListener("ioError",this.ioerrorHandler);
         var _loc3_:URLRequest = new URLRequest(param2);
         _loc3_.data = this.encodeData(param1);
         _loc3_.method = "POST";
         this.ldr.load(_loc3_);
      }
      
      private function ioerrorHandler(param1:IOErrorEvent) : void
      {
      }
      
      private function closeHandler(param1:Event) : void
      {
      }
      
      private function completeHandler(param1:Event) : void
      {
         var _loc2_:Object = util.json.JSON.decode(param1.target.data);
         if(_loc2_.ret == "ok")
         {
            switch(_loc2_.action)
            {
               case "question":
                  NetConnectProxy(AppFacade.instance.retrieveProxy("chat.mvc.proxy.GMListProxy")).addMsg(_loc2_.msg);
                  AppFacade.instance.sendNotification("SC_GM_SENDQUESTION");
                  break;
               case "answer":
                  AppFacade.instance.retrieveProxy("chat.mvc.proxy.GMListProxy").setData(_loc2_.msg);
                  break;
               case "get":
                  AppFacade.instance.sendNotification("SC_ANNOUNCE_GETINFOS",_loc2_.msg);
            }
         }
      }
      
      private function encodeData(param1:Object) : URLVariables
      {
         var _loc2_:* = null;
         var _loc3_:URLVariables = new URLVariables();
         for(_loc2_ in param1)
         {
            _loc3_[_loc2_] = param1[_loc2_];
         }
         _loc3_["serverID"] = Config.serverID;
         _loc3_["uid"] = MainData.getInstance().userData.uid;
         _loc3_["uname"] = MainData.getInstance().userData.uname;
         return _loc3_;
      }
   }
}

