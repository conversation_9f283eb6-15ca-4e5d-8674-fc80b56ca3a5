package chat.mvc.proxy
{
   import game.data.MainData;
   import game.items.framework.items.ItemFactory;
   import game.modules.activity.view.win.redPaperOtherInfo.RedPaperGrapInfo;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   
   public class ItemInfoProxy extends Proxy
   {
      public static const NAME:String = "chat.mvc.proxy.ItemInfoProxy";
      
      public function ItemInfoProxy()
      {
         super("chat.mvc.proxy.ItemInfoProxy");
      }
      
      public function getItemInfo(param1:int) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.itemInfo.getItemInfo",this.getItemInfoHandler);
         BabelTimeSocket.getInstance().sendMessage("itemInfo.getItemInfo",new SocketCallback("re.itemInfo.getItemInfo"),param1);
      }
      
      private function getItemInfoHandler(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.itemInfo.getItemInfo",this.getItemInfoHandler);
         if(param1.data)
         {
            sendNotification("SC_ITEMINFO",ItemFactory.creatItem(param1.data,false,false));
         }
      }
      
      public function grapRedPaperInfo(param1:int, param2:int, param3:int) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.team.excute.bonusGrap.grapBonus",this.getGrapRedPaperInfo);
         BabelTimeSocket.getInstance().sendMessage("team.excute.bonusGrap.grapBonus",new SocketCallback("re.team.excute.bonusGrap.grapBonus"),param1,param2,param3);
      }
      
      private function getGrapRedPaperInfo(param1:SocketDataEvent) : void
      {
         var _loc2_:RedPaperGrapInfo = null;
         BabelTimeSocket.getInstance().removeCallback("re.team.excute.bonusGrap.grapBonus",this.getGrapRedPaperInfo);
         if(param1.error == "ok")
         {
            if(param1.data == "notGraptime")
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("redPaper.32"),
                  "textFormat":TextFormatLib.red_12px
               });
               return;
            }
            if(PopUpCenter.containsWin("RedPaperGrapInfoWin"))
            {
               PopUpCenter.removePopUp("RedPaperGrapInfoWin");
            }
            _loc2_ = new RedPaperGrapInfo();
            _loc2_.setPaperWin(param1.data);
            PopUpCenter.addPopUp("RedPaperGrapInfoWin",_loc2_,true);
            if(param1.data.receive_gold == 0)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("redPaper.41"),
                  "textFormat":TextFormatLib.red_12px
               });
               return;
            }
            if(param1.data.isFirst == 0)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("redPaper.42"),
                  "textFormat":TextFormatLib.red_12px
               });
               return;
            }
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num + int(param1.data.receive_gold);
            sendNotification("CS_REDPAPER_SENDWIN_INFO");
         }
      }
   }
}

