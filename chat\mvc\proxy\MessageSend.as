package chat.mvc.proxy
{
   public class MessageSend
   {
      private var _messageText:String;
      
      private var _sendChannel:String;
      
      private var _hornType:int;
      
      private var _target:String;
      
      private var _utid:int;
      
      private var _ignore:Boolean;
      
      public function MessageSend()
      {
         super();
      }
      
      public function set messageText(param1:String) : void
      {
         this._messageText = param1;
      }
      
      public function set sendChannel(param1:String) : void
      {
         this._sendChannel = param1;
      }
      
      public function set hornType(param1:int) : void
      {
         this._hornType = param1;
      }
      
      public function set target(param1:String) : void
      {
         this._target = param1;
      }
      
      public function checkQiaobaMeg() : void
      {
         this._messageText = this._messageText.replace(/#1[0-9][0-9]/g,"");
      }
      
      public function get messageText() : String
      {
         return this._messageText;
      }
      
      public function get sendChannel() : String
      {
         return this._sendChannel;
      }
      
      public function get hornType() : int
      {
         return this._hornType;
      }
      
      public function get target() : String
      {
         return this._target;
      }
      
      public function get utid() : int
      {
         return this._utid;
      }
      
      public function set utid(param1:int) : void
      {
         this._utid = param1;
      }
      
      public function get ignore() : Boolean
      {
         return this._ignore;
      }
      
      public function set ignore(param1:Boolean) : void
      {
         this._ignore = param1;
      }
   }
}

