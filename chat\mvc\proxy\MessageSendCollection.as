package chat.mvc.proxy
{
   public class MessageSendCollection
   {
      private var sendMsgList:Array;
      
      private var _maxLength:uint;
      
      private var currentIndex:uint;
      
      public function MessageSendCollection(param1:uint = 20)
      {
         super();
         this._maxLength = param1;
         this.sendMsgList = [];
         this.currentIndex = 0;
      }
      
      public function addMessage(param1:MessageSend) : void
      {
         var _loc2_:String = null;
         var _loc3_:uint = 0;
         while(_loc3_ < this.sendMsgList.length)
         {
            _loc2_ = String(this.sendMsgList[_loc3_]);
            if(_loc2_ == param1.messageText)
            {
               return;
            }
            _loc3_++;
         }
         this.sendMsgList.push(param1.messageText);
         this.checkListLength();
         this.currentIndex = this.sendMsgList.length;
      }
      
      public function removeMessage(param1:String) : void
      {
         var _loc2_:uint = uint(this.sendMsgList.indexOf(param1));
         if(_loc2_ == -1)
         {
            return;
         }
         if(_loc2_ <= this.currentIndex)
         {
            this.currentIndex--;
         }
         this.sendMsgList.splice(this.sendMsgList.indexOf(param1),1);
      }
      
      public function removeMesssageByIndex(param1:uint) : void
      {
         if(param1 >= this.maxLength)
         {
            return;
         }
         if(param1 <= this.currentIndex)
         {
            this.currentIndex--;
         }
         this.sendMsgList.splice(param1,1);
      }
      
      public function getPreMessage() : String
      {
         if(this.currentIndex == 0)
         {
            if(this.sendMsgList.length >= 1)
            {
               this.currentIndex = this.sendMsgList.length - 1;
            }
         }
         else
         {
            this.currentIndex--;
         }
         return this.sendMsgList[this.currentIndex];
      }
      
      public function getNextMessage() : String
      {
         if(this.currentIndex == this.sendMsgList.length - 1)
         {
            this.currentIndex = 0;
         }
         else
         {
            this.currentIndex++;
         }
         return this.sendMsgList[this.currentIndex];
      }
      
      private function checkListLength() : void
      {
         while(this.sendMsgList.length > this.maxLength)
         {
            this.sendMsgList.shift();
         }
      }
      
      public function get maxLength() : uint
      {
         return this._maxLength;
      }
   }
}

