package chat.mvc.proxy
{
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class MsgSendCollProxy extends Proxy
   {
      public static const NAME:String = "chat.mvc.proxy.MsgSendCollProxy";
      
      private var msgSendList:MessageSendCollection;
      
      public function MsgSendCollProxy()
      {
         super("chat.mvc.proxy.MsgSendCollProxy");
         this.msgSendList = new MessageSendCollection();
      }
      
      public function addMessage(param1:MessageSend) : void
      {
         this.msgSendList.addMessage(param1);
      }
      
      public function removeMessage(param1:String) : void
      {
         this.msgSendList.removeMessage(param1);
      }
      
      public function removeMesssageByIndex(param1:uint) : void
      {
         this.msgSendList.removeMesssageByIndex(param1);
      }
      
      public function getPreMessage() : String
      {
         return this.msgSendList.getPreMessage();
      }
      
      public function getNextMessage() : String
      {
         return this.msgSendList.getNextMessage();
      }
      
      public function get maxLength() : uint
      {
         return this.msgSendList.maxLength;
      }
   }
}

