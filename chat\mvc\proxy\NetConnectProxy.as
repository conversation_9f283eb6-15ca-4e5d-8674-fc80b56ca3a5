package chat.mvc.proxy
{
   import chat.mvc.net.NetConnect;
   import game.Environment;
   import game.net.BabelTimeSocket;
   import game.net.SocketDataEvent;
   import mmo.Config;
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class NetConnectProxy extends Proxy
   {
      public static const NAME:String = "chat.mvc.proxy.GMListProxy";
      
      private var net:NetConnect;
      
      private var _gameList:Array;
      
      public function NetConnectProxy(param1:Object = null)
      {
         super("chat.mvc.proxy.GMListProxy",param1);
         this.net = new NetConnect();
         this._gameList = [];
         BabelTimeSocket.getInstance().regCallback("re.gm.newMsg",this.getNewMsgHandler);
         BabelTimeSocket.getInstance().regCallback("re.chat.getAnnounce",this.getAnnounceHandler);
      }
      
      private function getAnnounceHandler(param1:SocketDataEvent) : void
      {
         if(param1.data.length > 0)
         {
            if(Environment.loadingParams.hasOwnProperty("noticeUrl") && Environment.loadingParams.noticeUrl != "")
            {
               this.net.doAction({
                  "action":"get",
                  "bid":param1.data[0]
               },Environment.loadingParams.noticeUrl);
            }
            else
            {
               this.net.doAction({
                  "action":"get",
                  "bid":param1.data[0]
               },"http://www.zuiyouxi.com/notice/do");
            }
         }
         else
         {
            this.getAnnounceInfos();
         }
      }
      
      private function getNewMsgHandler(param1:SocketDataEvent) : void
      {
         Config.gm = true;
         sendNotification("SC_GM_GETQUESTION");
      }
      
      public function getAnnounceInfos() : void
      {
         if(Environment.loadingParams.hasOwnProperty("noticeUrl") && Environment.loadingParams.noticeUrl != "")
         {
            this.net.doAction({"action":"get"},Environment.loadingParams.noticeUrl);
         }
         else
         {
            this.net.doAction({"action":"get"},"http://www.zuiyouxi.com/notice/do");
         }
      }
      
      public function getGMInfos() : void
      {
         if(Environment.loadingParams.hasOwnProperty("questionUrl") && Environment.loadingParams.questionUrl != "")
         {
            this.net.doAction({"action":"answer"},Environment.loadingParams.questionUrl);
         }
         else
         {
            this.net.doAction({"action":"answer"},"http://www.zuiyouxi.com/question/do");
         }
      }
      
      public function sendQuestion(param1:Object) : void
      {
         param1.action = "question";
         if(Environment.loadingParams.hasOwnProperty("questionUrl") && Environment.loadingParams.questionUrl != "")
         {
            this.net.doAction(param1,Environment.loadingParams.questionUrl);
         }
         else
         {
            this.net.doAction(param1,"http://www.zuiyouxi.com/question/do");
         }
      }
      
      public function addMsg(param1:Object) : void
      {
         this.gameList.unshift(param1);
      }
      
      override public function setData(param1:Object) : void
      {
         this._gameList = param1 as Array;
      }
      
      public function get gameList() : Array
      {
         return this._gameList;
      }
   }
}

