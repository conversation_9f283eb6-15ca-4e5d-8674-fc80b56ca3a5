package chat.mvc.proxy
{
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class TownTeamChatProxy extends Proxy
   {
      public static const NAME:String = "chat.mvc.proxy.TownTeamChatProxy";
      
      public function TownTeamChatProxy(param1:String = null, param2:Object = null)
      {
         super(param1,param2);
      }
      
      public function send(param1:String) : void
      {
         BabelTimeSocket.getInstance().sendMessage("abysscopy.chat",new SocketCallback("RE_TOWN_TEAM_SENDMSG",[param1]),param1);
      }
   }
}

