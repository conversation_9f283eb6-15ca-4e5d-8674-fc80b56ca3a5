package chat.mvc.proxy
{
   import game.net.BabelTimeSocket;
   import game.net.SocketDataEvent;
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class WorldBoatBoardProxy extends Proxy
   {
      public static const NAME:String = "chat.mvc.proxy.WorldBoatBoardProxy";
      
      private const _worldBoatChats:Array = [];
      
      private var _socket:BabelTimeSocket;
      
      public function WorldBoatBoardProxy(param1:String = null, param2:Object = null)
      {
         super(param1,param2);
         this._socket = BabelTimeSocket.getInstance("SOCKET_GENERAL_BOAT_CHALLENGE");
         this._socket.regCallback("sc.worldboat.msg",this.scSend);
      }
      
      private function scSend(param1:SocketDataEvent) : void
      {
         this._worldBoatChats.push({
            "uid":param1.data.name,
            "content":param1.data.msg
         });
         sendNotification("SC_WORLD_BOAT_BOARD_MSG");
      }
      
      public function get worldBoatChats() : Array
      {
         return this._worldBoatChats;
      }
   }
}

