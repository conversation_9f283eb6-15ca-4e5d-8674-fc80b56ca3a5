package chat.mvc.proxy
{
   import game.data.ModuleData;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class WorldBoatWarChatProxy extends Proxy
   {
      public static const NAME:String = "chat.mvc.proxy.WorldBoatWarChatProxy";
      
      private const _bigLaBaChats:Array = [];
      
      private var _socket:BabelTimeSocket;
      
      public function WorldBoatWarChatProxy(param1:String = null, param2:Object = null)
      {
         super(param1,param2);
         this._socket = BabelTimeSocket.getInstance("SOCKET_GENERAL_BOAT_CHALLENGE");
         this._socket.regCallback("RE_WORLD_SENDMSG",this.onSend);
         this._socket.regCallback("sc.worldboat.msg",this.scSend);
         this._socket.regCallback("re.worldboat.broadcastMsg",this.sendReHandler);
      }
      
      public function send(param1:String) : void
      {
         this._socket.sendMessage("worldboat.broadcastMsg",new SocketCallback("re.worldboat.broadcastMsg",[param1]),param1);
      }
      
      private function sendReHandler(param1:SocketDataEvent) : void
      {
         if(param1.data != null)
         {
            ModuleData.crossServerShipFightGold = param1.data.gold;
            sendNotification("WORLD_BOAT_GOLD_CHANGE");
            sendNotification("WORLD_BOAT_CHAT_REFRESH_NUM",param1.data.freecount);
         }
      }
      
      private function scSend(param1:SocketDataEvent) : void
      {
         this._bigLaBaChats.push({
            "uid":param1.data.name,
            "content":param1.data.msg
         });
         sendNotification("SC_WORLD_BOAT_SENDMSG");
      }
      
      private function onSend(param1:SocketDataEvent) : void
      {
      }
      
      public function worldChatInfo() : void
      {
         this._socket.regCallback("re.worldboat.getMsgInfo",this.getWorldChatInfo);
         this._socket.sendMessage("worldboat.getMsgInfo",new SocketCallback("re.worldboat.getMsgInfo"));
      }
      
      private function getWorldChatInfo(param1:SocketDataEvent) : void
      {
         var _loc2_:* = undefined;
         this._socket.removeCallback("re.worldboat.getMsgInfo",this.getWorldChatInfo);
         if(param1.data != null)
         {
            for(_loc2_ in param1.data.msg)
            {
               this._bigLaBaChats.push({
                  "uid":param1.data.msg[_loc2_].name,
                  "content":param1.data.msg[_loc2_].msg
               });
            }
            this._bigLaBaChats.reverse();
            sendNotification("SC_GET_WORLDBOAT_CHAT",param1.data);
         }
      }
      
      public function get bigLaBaChats() : Array
      {
         return this._bigLaBaChats;
      }
   }
}

