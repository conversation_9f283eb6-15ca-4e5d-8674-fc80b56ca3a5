package chat.mvc.util
{
   import flash.geom.ColorTransform;
   import flash.text.TextField;
   
   public class SelectionColor
   {
      public function SelectionColor()
      {
         super();
      }
      
      public static function setFieldSelectionColor(param1:TextField, param2:uint) : void
      {
         param1.backgroundColor = invert(param1.backgroundColor);
         param1.borderColor = invert(param1.borderColor);
         param1.textColor = invert(param1.textColor);
         var _loc3_:ColorTransform = new ColorTransform();
         _loc3_.color = param2;
         _loc3_.redMultiplier = -1;
         _loc3_.greenMultiplier = -1;
         _loc3_.blueMultiplier = -1;
         param1.transform.colorTransform = _loc3_;
      }
      
      protected static function invert(param1:uint) : uint
      {
         var _loc2_:ColorTransform = new ColorTransform();
         _loc2_.color = param1;
         return invertColorTransform(_loc2_).color;
      }
      
      protected static function invertColorTransform(param1:ColorTransform) : ColorTransform
      {
         var colorTrans:ColorTransform = param1;
         with(colorTrans)
         {
            redMultiplier = -redMultiplier;
            greenMultiplier = -greenMultiplier;
            blueMultiplier = -blueMultiplier;
            redOffset = 255 - redOffset;
            greenOffset = 255 - greenOffset;
            blueOffset = 255 - blueOffset;
         }
         return colorTrans;
      }
   }
}

