package chat.mvc.view
{
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.event.ListEvent;
   
   public class ChannelListMenu extends UISprite
   {
      private var _labelField:String;
      
      private var _data:Array;
      
      private var _selected:Object;
      
      private var _bgSkin:UISkin;
      
      public function ChannelListMenu(param1:String = "label")
      {
         super();
         this._labelField = param1;
         this._bgSkin = UIManager.getUISkin("channelList_bg");
         addChild(this._bgSkin);
         addEventListener("addedToStage",this.addToStageHandler);
      }
      
      private function addToStageHandler(param1:Event) : void
      {
         removeEventListener("addedToStage",this.addToStageHandler);
         stage.addEventListener("click",this.clickStageHandler);
      }
      
      private function clickStageHandler(param1:MouseEvent) : void
      {
         stage && stage.removeEventListener("click",this.clickStageHandler);
         this.parent && this.parent.removeChild(this);
         this.dispose();
      }
      
      public function setData(param1:Array) : void
      {
         var _loc2_:Object = null;
         var _loc3_:Button = null;
         this._data = param1;
         var _loc4_:uint = 0;
         for each(_loc2_ in param1)
         {
            _loc3_ = new Button(_loc2_[this._labelField],TextFormatLib.format_0xFFED89_11px,35,UIManager.getMultiUISkin("chat_button"));
            _loc3_.name = _loc4_.toString();
            _loc3_.x = 3;
            _loc3_.y = _loc4_ * 21;
            addChild(_loc3_);
            _loc3_.addEventListener("click",this.clickHandler);
            _loc4_++;
         }
         this._bgSkin.setSize(39,_loc3_.y + 24);
      }
      
      private function clickHandler(param1:MouseEvent) : void
      {
         dispatchEvent(new ListEvent(ListEvent.clickItem,this._data[int(param1.currentTarget.name)]));
      }
      
      override public function dispose() : void
      {
         stage && stage.removeEventListener("click",this.clickStageHandler);
         super.dispose();
      }
   }
}

