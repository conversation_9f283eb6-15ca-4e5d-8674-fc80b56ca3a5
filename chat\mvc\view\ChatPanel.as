package chat.mvc.view
{
   import chat.event.SendMessageEvent;
   import chat.mvc.mediator.ChatMediator;
   import chat.mvc.proxy.MessageSend;
   import com.greensock.TweenMax;
   import flash.display.DisplayObjectContainer;
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import game.manager.XmlManager;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.modules.chat.text.TextLayoutManager;
   import game.mvc.AppFacade;
   import game.mvc.module.IModulePart;
   import mmo.ext.filter.FilterLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.tab.MouseTabButton;
   import mmo.ui.control.text.RichTextArea;
   import mmo.ui.event.ListEvent;
   import mmo.ui.event.ScrollBarEvent;
   import util.Globalization;
   
   public class ChatPanel extends UISprite implements IModulePart
   {
      public static const CHANGE_CHANNEL:String = "changeChannel";
      
      public static const OPEN_GM:String = "openGM";
      
      public static const OPEN_HORN:String = "openHorn";
      
      public static const SEND_MSG:String = "sendMsg";
      
      public var _chatMc:ChatPanelMC;
      
      private var chat_input:RichTextArea;
      
      private var chat_output:RichTextArea;
      
      private var scrollBar:ChatScrollBar_Y;
      
      private var _configXML:XML;
      
      private var _timer:Timer;
      
      private var _currentChannel:String;
      
      private var _currentSayChannel:String;
      
      private var _target:int;
      
      private var listMenu:ChannelListMenu;
      
      private var pane:FacePanel;
      
      private var link:TextEvent;
      
      private var _msgList:Array;
      
      public var showHandler:Function;
      
      public var closeHandler:Function;
      
      private var isMove:Boolean = true;
      
      public function ChatPanel()
      {
         super();
         this.initMC();
         this.creatTextArea();
         this._msgList = [];
         this._timer = new Timer(5000,1);
         this._timer.addEventListener("timerComplete",this.completeHandler);
         addEventListener("addedToStage",this.addToStageHandler);
         AppFacade.instance.registerMediator(new ChatMediator(this));
         this._chatMc.btn_all.dispatchEvent(new MouseEvent("click",true,true));
      }
      
      public function get currentSayChannel() : String
      {
         return this._currentSayChannel;
      }
      
      public function get currentChannel() : String
      {
         return this._currentChannel;
      }
      
      public function get target() : int
      {
         return this._target;
      }
      
      private function completeHandler(param1:TimerEvent) : void
      {
         this._timer.stop();
         this._timer.reset();
         if(this._chatMc.btn_open.visible)
         {
            this.chat_output.clear();
         }
      }
      
      private function addToStageHandler(param1:Event) : void
      {
         removeEventListener("addedToStage",this.addToStageHandler);
         this.y = stage.stageHeight;
      }
      
      private function initMC() : void
      {
         this._chatMc = new ChatPanelMC();
         addChild(this._chatMc);
         this._chatMc.chatUser.visible = false;
         this._currentSayChannel = "channelWorld";
         this._chatMc.btn_open.visible = false;
         this._chatMc.btn_close.visible = true;
         this._chatMc._BtnEip.buttonMode = true;
         this._chatMc.btn_gm.enabled = true;
         this._chatMc.setHornBtnPosition();
         this._chatMc.addEventListener("click",this.clickHandler);
      }
      
      private function swapComponent(param1:UISprite, param2:UISprite) : void
      {
         var _loc3_:DisplayObjectContainer = param1.parent;
         var _loc4_:int = _loc3_.getChildIndex(param1);
         param2.x = param1.x;
         param2.y = param1.y;
         _loc3_.addChildAt(param2,_loc4_);
         param1.parent && _loc3_.removeChild(param1);
         param2.name = param1.name;
      }
      
      private function clickHandler(param1:MouseEvent) : void
      {
         var _loc9_:MessageSend = null;
         var _loc7_:String = null;
         var _loc8_:Array = null;
         var _loc2_:int = 0;
         var _loc3_:RegExp = null;
         var _loc6_:String = null;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         param1.stopImmediatePropagation();
         switch(param1.target)
         {
            case this._chatMc.btn_all:
               this.resetChannelBtnState(MouseTabButton(param1.target),"isCheck",false,true);
               this._currentChannel = "channelGeneral";
               dispatchEvent(new DataEvent("changeChannel",false,false,this._currentChannel));
               break;
            case this._chatMc.btn_world:
               this.resetChannelBtnState(MouseTabButton(param1.target),"isCheck",false,true);
               this._currentSayChannel = this._currentChannel = "channelWorld";
               this._chatMc.btn_state.text = Globalization.getString("chat.25");
               dispatchEvent(new DataEvent("changeChannel",false,false,this._currentChannel));
               break;
            case this._chatMc.btn_group:
               this.resetChannelBtnState(MouseTabButton(param1.target),"isCheck",false,true);
               this._currentSayChannel = this._currentChannel = "channelGroup";
               this._chatMc.btn_state.text = Globalization.getString("chat.26");
               dispatchEvent(new DataEvent("changeChannel",false,false,this._currentChannel));
               break;
            case this._chatMc.btn_guild:
               this.resetChannelBtnState(MouseTabButton(param1.target),"isCheck",false,true);
               this._currentSayChannel = this._currentChannel = "channelGuild";
               this._chatMc.btn_state.text = Globalization.getString("chat.27");
               dispatchEvent(new DataEvent("changeChannel",false,false,this._currentChannel));
               break;
            case this._chatMc.btn_chat:
               if(TweenMax.isTweening(this._chatMc.btn_chat))
               {
                  TweenMax.killTweensOf(this._chatMc.btn_chat);
                  this._chatMc.btn_chat.filters = [];
               }
               this.resetChannelBtnState(MouseTabButton(param1.target),"isCheck",false,true);
               this._currentSayChannel = this._currentChannel = "channelPrivately";
               this._chatMc.btn_state.text = Globalization.getString("chat.28");
               dispatchEvent(new DataEvent("changeChannel",false,false,this._currentSayChannel));
               this.privateChat(this._target,this._chatMc.chatUser.text);
               break;
            case this._chatMc.btn_gm:
               if(TweenMax.isTweening(this._chatMc.btn_gm))
               {
                  TweenMax.killTweensOf(this._chatMc.btn_gm);
                  this._chatMc.btn_gm.filters = [];
               }
               this._chatMc.btn_gm.isCheck = false;
               dispatchEvent(new Event("openGM"));
               break;
            case this._chatMc.btn_close:
               this.resetChannelBtnState(null,"visible",false,null);
               this.chat_output.resizeTo(220,10);
               this._chatMc.bg.visible = false;
               this.chat_output.y = -20;
               this.chat_output.autoAdjust();
               this.resetScrollBar(10);
               this._chatMc.btn_open.visible = true;
               this._chatMc.btn_close.visible = false;
               this._chatMc.btn_close.isCheck = false;
               this._chatMc.setHornBtnPosition();
               this.scrollBar.visible = false;
               this.chat_output.clear();
               if(this._timer.running)
               {
                  this.setInfoContent(this._msgList[this._msgList.length - 1]);
               }
               break;
            case this._chatMc.btn_open:
               this.resetChannelBtnState(null,"visible",true,null);
               this.chat_output.resizeTo(220,200);
               this.chat_output.y = -250;
               this.chat_output.autoAdjust();
               this._chatMc.btn_open.visible = false;
               this._chatMc.btn_close.visible = true;
               this._chatMc.btn_open.isCheck = true;
               this._chatMc.setHornBtnPosition();
               this.scrollBar.visible = true;
               this.resetScrollBar(200);
               this.setChatContent(this._msgList,false);
               break;
            case this._chatMc.btn_send:
               if(this.chat_input.textField.getLineLength(0) > 60)
               {
                  PopUpCenter.alertWin(Globalization.getString("chat.29"));
                  return;
               }
               _loc9_ = new MessageSend();
               _loc7_ = this.chat_input.text;
               _loc7_ = _loc7_.replace(/</g,"*");
               _loc7_ = _loc7_.replace(/>/g,"*");
               _loc8_ = _loc7_.split("#");
               _loc2_ = int(_loc8_.length);
               _loc3_ = /^[0-1][0-3][0-9]/;
               _loc6_ = _loc8_[0];
               _loc5_ = 0;
               _loc4_ = 1;
               while(_loc4_ < _loc2_)
               {
                  if(_loc3_.test(_loc8_[_loc4_]))
                  {
                     if(_loc5_ >= 5)
                     {
                        _loc6_ += _loc8_[_loc4_].replace(_loc3_,"");
                     }
                     else
                     {
                        _loc6_ += "#" + _loc8_[_loc4_];
                     }
                     _loc5_++;
                  }
                  else
                  {
                     _loc6_ += "#" + _loc8_[_loc4_];
                  }
                  _loc4_++;
               }
               _loc9_.messageText = _loc6_;
               _loc9_.sendChannel = this._currentSayChannel;
               _loc9_.target = this._chatMc.chatUser.text;
               dispatchEvent(new SendMessageEvent("sendMsg",_loc9_));
               this.chat_input.clear();
               break;
            case this._chatMc.btn_speak:
               this.showBoardCastPanel();
               break;
            case this._chatMc.btn_state:
               if(this.listMenu && this.listMenu.parent)
               {
                  this.listMenu.dispose();
                  this.listMenu.parent.removeChild(this.listMenu);
                  this.listMenu = null;
               }
               else
               {
                  this.listMenu = new ChannelListMenu();
                  this.listMenu.addEventListener(ListEvent.clickItem,this.changeSelectItemHandler);
                  this.listMenu.setData(this.getChannelMenuData());
                  addChild(this.listMenu);
                  this.listMenu.y = this._chatMc.btn_state.y - this.listMenu.height - 5;
               }
               break;
            case this._chatMc._BtnEip:
               this.showFace();
               break;
            case this._chatMc.btn_horn:
               dispatchEvent(new DataEvent("openHorn"));
         }
         this.resetInputPosition();
      }
      
      private function resetInputPosition() : void
      {
         if(this._currentSayChannel != "channelPrivately")
         {
            this.chat_input.x = this._chatMc.chatUser.x;
            if(this.chat_input.width < 146)
            {
               this.chat_input.resizeTo(146,20);
               this.chat_input.autoAdjust();
            }
            this._chatMc.chatUser.visible = false;
         }
      }
      
      private function changeSelectItemHandler(param1:ListEvent) : void
      {
         this._currentSayChannel = param1.data.data;
         this.resetInputPosition();
         this._currentSayChannel == "channelPrivately" && this.privateChat(this._target,this._chatMc.chatUser.text);
         this._chatMc.btn_state.text = param1.data.label;
      }
      
      private function getChannelMenuData() : Array
      {
         return [{
            "label":Globalization.getString("chat.25"),
            "data":"channelWorld"
         },{
            "label":Globalization.getString("chat.26"),
            "data":"channelGroup"
         },{
            "label":Globalization.getString("chat.27"),
            "data":"channelGuild"
         },{
            "label":Globalization.getString("chat.28"),
            "data":"channelPrivately"
         }];
      }
      
      private function resetChannelBtnState(param1:MouseTabButton, param2:String, param3:*, param4:*) : void
      {
         this._chatMc.btn_all[param2] = param3;
         this._chatMc.btn_world[param2] = param3;
         this._chatMc.btn_group[param2] = param3;
         this._chatMc.btn_guild[param2] = param3;
         this._chatMc.btn_chat[param2] = param3;
         this._chatMc.btn_gm[param2] = param3;
         param1 && (param1[param2] = param4);
      }
      
      private function showFace() : void
      {
         this.pane ||= new FacePanel();
         if(this.pane.parent)
         {
            this.pane.dispose();
            removeChild(this.pane);
            this.pane = null;
            return;
         }
         addChild(this.pane);
         this.pane.x = this._chatMc._BtnEip.x - 170;
         this.pane.y = this._chatMc._BtnEip.y - 216;
         this.pane.addEventListener("faceSelect",this.faceSelectHandler);
         this.pane.addEventListener("clickOutSide",this.clickOutHandler);
      }
      
      private function clickOutHandler(param1:Event) : void
      {
         removeChild(this.pane);
      }
      
      private function faceSelectHandler(param1:DataEvent) : void
      {
         if(this.chat_input.text.match(/#0[0-3]{1}[0-9]{1}/g).length >= 5)
         {
            return;
         }
         if(this.chat_input.textField.getLineLength(0) >= 60)
         {
            return;
         }
         this.chat_input.appendRichText(param1.data);
         stage && (stage.focus = this.chat_input.textField);
         this.chat_input.textField.setSelection(this.chat_input.textField.length,this.chat_input.textField.length);
         this.chat_input.focusRect = false;
         this.clickOutHandler(null);
      }
      
      private function showBoardCastPanel() : void
      {
      }
      
      private function sendMessageHandler(param1:SendMessageEvent) : void
      {
         param1.currentTarget.removeEventListener("sendMsg",this.sendMessageHandler);
         dispatchEvent(param1);
      }
      
      private function creatTextArea() : void
      {
         this._configXML = new XML(XmlManager.getXml("face").child("face").copy());
         this._configXML = this._configXML.appendChild(XmlManager.getXml("face").mc.children().copy());
         this.chat_input = new RichTextArea(140,20);
         this.chat_input.configXML = this._configXML;
         this.chat_input.y = this._chatMc.chatUser.y + 1;
         this.chat_input.x = this._chatMc.chatUser.x;
         this.chat_input.textField.wordWrap = false;
         this.chat_input.textField.multiline = false;
         this.chat_input.direction = "bottom";
         this.chat_input.textField.type = "input";
         this.chat_input.textField.textColor = 16777215;
         this.chat_input.textField.maxChars = 60;
         this.chat_input.textField.useRichTextClipboard = false;
         this._chatMc.addChild(this.chat_input);
         this._chatMc.swapChildren(this.chat_input,this._chatMc._BtnEip);
         this.chat_output = new RichTextArea(220,200);
         this.chat_output.configXML = this._configXML;
         this.chat_output.x = 22;
         this.chat_output.y = -250;
         this.chat_output.textField.wordWrap = true;
         this.chat_output.textField.multiline = true;
         this.chat_output.textField.filters = [FilterLib.glow_0x272727];
         this._chatMc.addChild(this.chat_output);
         this._chatMc.swapChildren(this.chat_output,this._chatMc.btn_open);
         this.initScrollBar();
         this.chat_output.addEventListener("link",this.onLink);
         this.chat_output.addEventListener("click",this.outputLinkHandler);
         this.chat_output.addEventListener("rollOver",this.rollOverHandler);
         this.chat_output.addEventListener("rollOut",this.rollOutHandler);
         this.chat_input.addEventListener("keyDown",this.keyDownHandler);
      }
      
      public function setInputFocus() : void
      {
         stage && (stage.focus = this.chat_input.textField);
         this.chat_input.textField.setSelection(this.chat_input.textField.length,this.chat_input.textField.length);
         this.chat_input.focusRect = false;
      }
      
      private function rollOverHandler(param1:MouseEvent) : void
      {
         this._chatMc.btn_close.visible && (this._chatMc.bg.visible = true);
      }
      
      private function rollOutHandler(param1:MouseEvent) : void
      {
         this._chatMc.bg.visible = false;
      }
      
      private function keyDownHandler(param1:KeyboardEvent) : void
      {
         if(param1.keyCode == 13)
         {
            this._chatMc.btn_send.dispatchEvent(new MouseEvent("click"));
         }
      }
      
      private function outputLinkHandler(param1:MouseEvent) : void
      {
         this.link && dispatchEvent(this.link);
         this.link = null;
      }
      
      private function onLink(param1:TextEvent) : void
      {
         param1.stopImmediatePropagation();
         this.link = param1;
      }
      
      public function privateChat(param1:int = 0, param2:String = "") : void
      {
         this._currentSayChannel = "channelPrivately";
         this.chat_input.x = this._chatMc.chatUser.x + this._chatMc.chatUser.width;
         this.chat_input.resizeTo(94,20);
         this.chat_input.autoAdjust();
         this._chatMc.chatUser.visible = true;
         this._chatMc.chatUser.text = param2;
         this._target = param1;
         this._chatMc.btn_state.text = Globalization.getString("chat.28");
      }
      
      public function setChatInfo(param1:String) : void
      {
         this.chat_input.clear();
         this.chat_input.appendRichText(param1);
         this.chat_input.autoAdjust();
      }
      
      public function setChatContent(param1:Array, param2:Boolean = true) : void
      {
         var _loc9_:uint = 0;
         var _loc3_:MessageReceive = null;
         var _loc4_:String = null;
         var _loc7_:String = null;
         var _loc6_:String = null;
         var _loc5_:RegExp = null;
         this.isMove = this.chat_output.textField.scrollV == this.chat_output.textField.maxScrollV;
         var _loc8_:int = this.chat_output.textField.scrollV;
         this.chat_output.clear();
         this._msgList = param1;
         param2 && this._timer.reset();
         param2 && this._timer.start();
         if(this._chatMc.btn_open.visible)
         {
            this.setInfoContent(param1[param1.length - 1]);
         }
         else
         {
            _loc9_ = 0;
            while(_loc9_ < param1.length)
            {
               _loc3_ = param1[_loc9_];
               _loc4_ = TextLayoutManager.parseClipMessage(_loc3_);
               _loc7_ = "SIZE=\"32\"";
               _loc6_ = "SIZE=\"12\"";
               _loc5_ = new RegExp(_loc7_,"g");
               _loc4_ = _loc4_.replace(_loc5_,_loc6_);
               this.chat_output.appendRichText(_loc4_);
               _loc9_++;
            }
            if(this.isMove)
            {
               this.chat_output.textField.scrollV = this.chat_output.textField.maxScrollV;
            }
            else
            {
               this.chat_output.textField.scrollV = _loc8_;
            }
         }
      }
      
      private function setInfoContent(param1:MessageReceive) : void
      {
         if(!param1)
         {
            return;
         }
         var _loc5_:String = TextLayoutManager.parseClipMessage(param1);
         var _loc3_:String = "SIZE=\"32\"";
         var _loc4_:String = "SIZE=\"12\"";
         var _loc2_:RegExp = new RegExp(_loc3_,"g");
         _loc5_ = _loc5_.replace(_loc2_,_loc4_);
         this.chat_output.appendRichText(_loc5_);
         this.chat_output.y = -this.chat_output.textField.textHeight - 32;
         this.chat_output.resizeTo(220,this.chat_output.textField.textHeight + 10);
         this.chat_output.autoAdjust();
      }
      
      private function resetScrollBar(param1:int) : void
      {
         this.scrollBar.y = this.chat_output.y;
         this.scrollBar.setHeight(param1);
         this.scrollBar.resetScrollButtonSizeByScroll(this.chat_output.textField.maxScrollV,this.chat_output.textField.numLines);
         this.scrollBar.enabled = this.chat_output.textField.maxScrollV > 1;
         this.scrollBar.setProgress((this.chat_output.textField.scrollV - 1) / (this.chat_output.textField.maxScrollV - 1),false);
         this.chat_output.textField.scrollV = this.chat_output.textField.maxScrollV;
         this.isMove = true;
      }
      
      private function initScrollBar() : void
      {
         var _loc2_:Number = this._chatMc.bg.width;
         var _loc1_:Number = this._chatMc.bg.height;
         this.scrollBar = new ChatScrollBar_Y(this.chat_output.textField.height);
         this.scrollBar.x = 4;
         this.scrollBar.y = this.chat_output.y;
         addChild(this.scrollBar);
         this.scrollBar.enabled = this.chat_output.textField.maxScrollV > 1;
         this.scrollBar.addEventListener(ScrollBarEvent.Progress_Update,this.progressUpdata);
         this.scrollBar.addEventListener("downClick",this.downCkickHandler);
         this.scrollBar.addEventListener("upClick",this.upClickhandler);
         this.chat_output.textField.addEventListener("scroll",this.textScroll);
      }
      
      private function upClickhandler(param1:Event) : void
      {
         param1.stopImmediatePropagation();
         var _loc2_:int = this.chat_output.textField.scrollV;
         if(_loc2_ > 1)
         {
            this.chat_output.textField.scrollV = _loc2_ - 1;
         }
      }
      
      private function downCkickHandler(param1:Event) : void
      {
         param1.stopImmediatePropagation();
         var _loc3_:int = this.chat_output.textField.scrollV;
         var _loc2_:int = this.chat_output.textField.maxScrollV;
         if(_loc3_ < _loc2_)
         {
            this.chat_output.textField.scrollV = _loc3_ + 1;
         }
      }
      
      private function progressUpdata(param1:Event) : void
      {
         this.chat_output.textField.removeEventListener("scroll",this.textScroll);
         this.chat_output.textField.scrollV = this.chat_output.textField.maxScrollV * this.scrollBar.progress;
         this.isMove = this.scrollBar.progress == 1;
         this.chat_output.textField.addEventListener("scroll",this.textScroll);
      }
      
      override public function dispose() : void
      {
         this.chat_output.textField.removeEventListener("scroll",this.textScroll);
         this.pane && this.pane.dispose();
         this.pane = null;
         super.dispose();
      }
      
      private function textScroll(param1:Event) : void
      {
         this.scrollBar.enabled = this.chat_output.textField.maxScrollV > 1;
         if(!this.scrollBar.enabled)
         {
            return;
         }
         this.scrollBar.resetScrollButtonSizeByScroll(this.chat_output.textField.maxScrollV,this.chat_output.textField.numLines);
         if(this.isMove)
         {
            this.scrollBar.setProgress(1,false);
            if(this.chat_output.textField.scrollV != this.chat_output.textField.maxScrollV)
            {
               this.chat_output.textField.scrollV = this.chat_output.textField.maxScrollV;
            }
         }
         else
         {
            this.scrollBar.setProgress((this.chat_output.textField.scrollV - 1) / (this.chat_output.textField.maxScrollV - 1),false);
         }
      }
      
      public function show(param1:Object) : void
      {
         this.showHandler && this.showHandler(param1);
      }
      
      public function close() : void
      {
         this.closeHandler && this.closeHandler();
      }
   }
}

