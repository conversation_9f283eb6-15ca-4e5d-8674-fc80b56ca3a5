package chat.mvc.view
{
   import chat.mvc.util.SelectionColor;
   import flash.display.BitmapData;
   import flash.display.Shape;
   import flash.display.Sprite;
   import game.manager.UIManager;
   import mmo.ext.color.ColorLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.tab.MouseTabButton;
   import util.Globalization;
   
   public class ChatPanelMC extends Sprite
   {
      public var btn_chat:MouseTabButton;
      
      public var btn_close:MouseTabButton;
      
      public var btn_guild:MouseTabButton;
      
      public var btn_send:But<PERSON>;
      
      public var btn_world:MouseTabButton;
      
      public var btn_group:MouseTabButton;
      
      public var btn_state:Button;
      
      public var btn_all:MouseTabButton;
      
      public var chatUser:ChatTextInput;
      
      public var bg:UISkin;
      
      public var input_bg:UISkin;
      
      public var btn_gm:MouseTabButton;
      
      public var btn_speak:But<PERSON>;
      
      public var btn_open:MouseTabButton;
      
      public var _BtnEip:Button;
      
      public var btn_horn:ImgButton;
      
      public function ChatPanelMC()
      {
         super();
         this.btn_close = this.createMouseTabButton("",18,25,UIManager.getMultiUISkin("chat_btn_close"));
         this.btn_close.x = 2;
         this.btn_close.y = -47.15;
         addChildAt(this.btn_close,0);
         this.btn_open = this.createMouseTabButton("",18,25,UIManager.getMultiUISkin("btn_open"));
         this.btn_open.x = 2;
         this.btn_open.y = -48.15;
         addChildAt(this.btn_open,0);
         this.btn_all = this.createMouseTabButton(Globalization.getString("chat.30"));
         this.btn_all.x = 22;
         this.btn_all.y = -47.15;
         addChildAt(this.btn_all,0);
         this.btn_world = this.createMouseTabButton(Globalization.getString("chat.25"));
         this.btn_world.x = 58;
         this.btn_world.y = -47.15;
         addChildAt(this.btn_world,0);
         this.btn_group = this.createMouseTabButton(Globalization.getString("chat.26"));
         this.btn_group.x = 94;
         this.btn_group.y = -47.15;
         addChildAt(this.btn_group,0);
         this.btn_guild = this.createMouseTabButton(Globalization.getString("chat.27"));
         this.btn_guild.x = 130;
         this.btn_guild.y = -47.15;
         addChildAt(this.btn_guild,0);
         this.btn_chat = this.createMouseTabButton(Globalization.getString("chat.28"));
         this.btn_chat.x = 166;
         this.btn_chat.y = -47.15;
         addChildAt(this.btn_chat,0);
         this.btn_gm = this.createMouseTabButton("GM");
         this.btn_gm.x = 202;
         this.btn_gm.y = -47.15;
         addChildAt(this.btn_gm,0);
         var _loc1_:Shape = new Shape();
         _loc1_.graphics.beginBitmapFill(new BitmapData(1,1,true,855638016));
         _loc1_.graphics.drawRoundRect(0,0,190,230,10,10);
         _loc1_.graphics.endFill();
         this.bg = new UISkin(new BitmapData(_loc1_.width,_loc1_.height,true,0));
         this.bg.bitmapData.draw(_loc1_);
         this.bg.setSize(244,232);
         this.bg.y = -256;
         this.bg.visible = false;
         addChildAt(this.bg,0);
         this.input_bg = UIManager.getUISkin("chat_input_bg");
         this.input_bg.setSize(248,26);
         this.input_bg.y = -26;
         addChild(this.input_bg);
         this._BtnEip = new Button("",null,25,UIManager.getMultiUISkin("btn_face"));
         this._BtnEip.x = 190;
         this._BtnEip.y = -23;
         addChild(this._BtnEip);
         this.btn_send = new Button("",null,29,UIManager.getMultiUISkin("btn_send"));
         this.btn_send.x = 216;
         this.btn_send.y = -23;
         addChild(this.btn_send);
         this.btn_state = new Button(Globalization.getString("chat.25"),TextFormatLib.format_0xFFED89_11px,35,UIManager.getMultiUISkin("chat_button"));
         this.btn_state.y = -23;
         addChild(this.btn_state);
         this.btn_horn = new ImgButton(UIManager.getMultiUISkin("HornEnterBtn"));
         this.btn_horn.x = 0;
         this.btn_horn.setToolTip(Globalization.getString("horn.12"),"rightMiddle");
         addChild(this.btn_horn);
         this.setHornBtnPosition();
         this.chatUser = new ChatTextInput("",59,null,19);
         SelectionColor.setFieldSelectionColor(this.chatUser.textLabel,ColorLib.grey);
         this.chatUser.x = 35;
         this.chatUser.y = -22.2;
         addChild(this.chatUser);
      }
      
      private function createMouseTabButton(param1:String, param2:Number = 34, param3:Number = 20, param4:Vector.<UISkin> = null) : MouseTabButton
      {
         param4 = !!param4 ? param4 : UIManager.getMultiUISkin("chat_tab");
         var _loc5_:MouseTabButton = new MouseTabButton(param1,false,false,param4);
         _loc5_.offsetY = 2.5;
         _loc5_.setSize(param2,param3);
         _loc5_.setStateFormat(TextFormatLib.format_0xFFED89_12px,TextFormatLib.format_0xFFED89_12px);
         return _loc5_;
      }
      
      public function setHornBtnPosition() : void
      {
         if(this.btn_open.visible)
         {
            this.btn_horn.y = -82;
         }
         else
         {
            this.btn_horn.y = -284;
         }
      }
   }
}

