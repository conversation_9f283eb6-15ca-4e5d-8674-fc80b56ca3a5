package chat.mvc.view
{
   import flash.events.Event;
   import mmo.ui.control.scrollBar.ScrollBar_Y;
   
   public class ChatScrollBar_Y extends ScrollBar_Y
   {
      public var moveRate:Number;
      
      public function ChatScrollBar_Y(param1:int = 60)
      {
         super(param1);
      }
      
      override protected function downClick(param1:Event) : void
      {
         this.fixVal = 0;
         dispatchEvent(new Event("downClick"));
      }
      
      override protected function upClick(param1:Event) : void
      {
         this.fixVal = 0;
         dispatchEvent(new Event("upClick"));
      }
      
      override public function set enabled(param1:Boolean) : void
      {
         scrollBtn.visible = param1;
      }
   }
}

