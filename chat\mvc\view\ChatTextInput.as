package chat.mvc.view
{
   import flash.text.TextFormat;
   import mmo.ui.control.input.TextInput;
   import mmo.ui.control.label.Label;
   
   public class ChatTextInput extends TextInput
   {
      public function ChatTextInput(param1:String, param2:int, param3:TextFormat = null, param4:int = 20)
      {
         super(param1,param2,param3,param4);
      }
      
      public function get textLabel() : Label
      {
         return label;
      }
   }
}

