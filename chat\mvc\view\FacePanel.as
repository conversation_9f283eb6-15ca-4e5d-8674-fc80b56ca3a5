package chat.mvc.view
{
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.utils.ByteArray;
   import game.manager.UIManager;
   import game.manager.UrlManager;
   import game.manager.XmlManager;
   import game.modules.elves.manager.ElvesManager;
   import game.mvc.AppFacade;
   import mmo.Core;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.ClickOutEventUISprite;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.tab.TabPane;
   import mmo.ui.event.TabEvent;
   import util.Globalization;
   
   public class FacePanel extends ClickOutEventUISprite
   {
      public static const FACESELECT:String = "faceSelect";
      
      private var container:UIBox;
      
      private var tabBar:TabPane;
      
      private var _max:int;
      
      private var _start:int;
      
      public function FacePanel()
      {
         super();
         isLive = false;
         var _loc1_:UISkin = UIManager.getUISkin("pane_tips");
         _loc1_.setSize(230,216);
         addChild(_loc1_);
         _loc1_ = UIManager.getUISkin("tab_bg");
         _loc1_.setSize(210,26);
         _loc1_.y = 1;
         _loc1_.x = 3;
         addChild(_loc1_);
         this.tabBar = new TabPane([Globalization.getString("chat.47"),Globalization.getString("chat.48")],0,52);
         this.tabBar.y = 26;
         this.tabBar.x = 10;
         addChild(this.tabBar);
         this.container = new UIBox();
         this.container.lineMaxChildrenNumber = 6;
         this.container.rowMaxChildrenNumber = 5;
         this.container.lineSpace = 5;
         this.container.rowSpace = 5;
         addChild(this.container);
         this.container.x = 5;
         this.container.y = 30;
         this.tabBar.addEventListener(TabEvent.Tab_IndexChange,this.changeTabHandler);
         this.showFace();
      }
      
      private function changeTabHandler(param1:Event) : void
      {
         this.showFace(this.tabBar.selectedIndex * 30);
      }
      
      private function clickFaceHandler(param1:MouseEvent) : void
      {
         var _loc2_:Point = null;
         param1.stopImmediatePropagation();
         if(this.tabBar.selectedIndex > 0)
         {
            if(!ElvesManager.getInstance().isOpenFace())
            {
               _loc2_ = this.localToGlobal(new Point(50,70));
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.46"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "point":_loc2_
               });
               return;
            }
         }
         dispatchEvent(new DataEvent("faceSelect",false,false,String(param1.currentTarget.name)));
      }
      
      public function showFace(param1:int = 0, param2:int = 30) : void
      {
         this._max = param2 + param1;
         this._start = param1;
         var _loc4_:XML = new XML(XmlManager.getXml("face").face.copy());
         var _loc5_:Array = [];
         var _loc3_:int = this._start;
         while(_loc3_ < this._max)
         {
            if(!Core.dataLib.getByteArray(UrlManager.getFaceUrl(_loc4_.children()[_loc3_].@iconUrl)))
            {
               _loc5_.push(UrlManager.getFaceUrl(_loc4_.children()[_loc3_].@iconUrl));
            }
            _loc3_++;
         }
         if(_loc5_.length)
         {
            Core.dataLib.load(_loc5_,this.loadedHandler);
         }
         else
         {
            this.loadedHandler();
         }
      }
      
      public function delTab(param1:int) : void
      {
         this.tabBar.delTab(param1);
      }
      
      private function loadedHandler() : void
      {
         var _loc2_:GIFSp = null;
         var _loc3_:String = null;
         var _loc1_:ByteArray = null;
         this.container.clearAllChild(this.container);
         var _loc5_:XML = new XML(XmlManager.getXml("face").face.copy());
         var _loc4_:int = this._start;
         while(_loc4_ < this._max)
         {
            _loc2_ = new GIFSp();
            _loc3_ = UrlManager.getFaceUrl(_loc5_.children()[_loc4_].@iconUrl);
            _loc1_ = Core.dataLib.getByteArray(_loc3_);
            _loc1_.position = 0;
            _loc2_.readStream(_loc1_,_loc3_);
            _loc2_.mouseChildren = false;
            _loc2_.mouseEnabled = true;
            _loc2_.name = _loc5_.children()[_loc4_].@iconStr;
            _loc2_.addEventListener("click",this.clickFaceHandler);
            this.container.addChild(_loc2_);
            _loc4_++;
         }
      }
   }
}

import com.worlize.gif.GIFPlayer;
import com.worlize.gif.GifDataManager;
import flash.utils.ByteArray;
import mmo.ui.control.UISprite;

class GIFSp extends UISprite
{
   private var gif:GIFPlayer;
   
   public function GIFSp()
   {
      super();
   }
   
   override public function get width() : Number
   {
      return 32;
   }
   
   override public function get height() : Number
   {
      return 32;
   }
   
   public function readStream(param1:ByteArray, param2:String) : void
   {
      if(!GifDataManager.instance.hasGif(param2))
      {
         this.gif = new GIFPlayer();
         this.gif.loadBytes(param1,param2);
      }
      else
      {
         this.gif = GifDataManager.instance.getClone(param2);
      }
      addChild(this.gif);
   }
}
