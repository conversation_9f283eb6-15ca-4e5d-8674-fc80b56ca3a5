package chat.mvc.view
{
   import chat.event.GMQuestionEvent;
   import chat.mvc.mediator.GMMediator;
   import chat.mvc.view.mc.SendQuestionMC;
   import chat.mvc.view.mc.ShowQuestionMC;
   import flash.events.Event;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.text.TextFormat;
   import game.Environment;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.tab.TabPane;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class GMPanel extends PopUpWindow
   {
      public var tabPane:TabPane;
      
      private var gmMC:SendQuestionMC;
      
      private var showQuestion:ShowQuestionMC;
      
      private const MAXCHAR:int = 200;
      
      public function GMPanel()
      {
         super(366,360);
         title = Globalization.getString("chat.31");
         this.initMC();
         AppFacade.instance.registerMediator(new GMMediator(this));
      }
      
      private function initMC() : void
      {
         var _loc1_:UISkin = UIManager.getUISkin("tab_bg");
         _loc1_.x = -1;
         _loc1_.y = -4;
         pane.addChild(_loc1_);
         this.tabPane = new TabPane([Globalization.getString("chat.32"),Globalization.getString("chat.33")],5,68);
         this.tabPane.y = -5;
         pane.addChild(this.tabPane);
         _loc1_.setSize(230,this.tabPane.height);
         this.gmMC = new SendQuestionMC();
         this.gmMC.y = 35;
         this.gmMC.x = 4;
         this.tabPane.addToTab(this.gmMC,0);
         this.gmMC.btn_cancel.addEventListener("click",this.cancelHandler);
         this.gmMC.btn_ok.addEventListener("click",this.okHandler);
         this.gmMC.txt_input.addEventListener("focusIn",this.inFocusHandler);
         this.gmMC.txt_input.addEventListener("change",this.changeHandler);
         this.gmMC.txt_notice.addEventListener("link",this.onLink);
         this.gmMC.txt_notice.text = Globalization.getString("chat.24");
         this.showQuestion = new ShowQuestionMC();
         this.showQuestion.x = 4;
         this.showQuestion.y = 35;
         this.showQuestion.btn_close.addEventListener("click",this.cancelHandler);
         this.tabPane.addToTab(this.showQuestion,1);
      }
      
      private function changeHandler(param1:Event) : void
      {
         var _loc2_:int = 200 - this.gmMC.txt_input.text.length;
         if(_loc2_ >= 0)
         {
            this.gmMC.btn_ok.enabled = true;
            this.gmMC.txt_notice.text = StringUtil.substitute(Globalization.getString("chat.34"),_loc2_);
         }
         else
         {
            this.gmMC.btn_ok.enabled = false;
            this.gmMC.txt_notice.htmlText = Globalization.getString("chat.35");
         }
      }
      
      private function onLink(param1:TextEvent) : void
      {
         if(Environment.loadingParams.hasOwnProperty("bbsUrl") && Environment.loadingParams.bbsUrl != "")
         {
            navigateToURL(new URLRequest(Environment.loadingParams.bbsUrl),"_blank");
         }
         else
         {
            navigateToURL(new URLRequest("http://bbs.hzw.zuiyouxi.com/bbs/forumdisplay.php?fid=3&page=1"),"_blank");
         }
      }
      
      private function inFocusHandler(param1:FocusEvent) : void
      {
         var _loc2_:int = 200 - this.gmMC.txt_input.text.length;
         if(_loc2_ >= 0)
         {
            this.gmMC.btn_ok.enabled = true;
            this.gmMC.txt_notice.text = StringUtil.substitute(Globalization.getString("chat.34"),_loc2_);
         }
         else
         {
            this.gmMC.btn_ok.enabled = false;
            this.gmMC.txt_notice.htmlText = Globalization.getString("chat.35");
         }
      }
      
      private function okHandler(param1:MouseEvent) : void
      {
         var _loc2_:GMQuestionEvent = new GMQuestionEvent("sendQuestion");
         _loc2_.questionType = this.gmMC.selectedTypeIndex.toString();
         _loc2_.content = this.gmMC.txt_input.text;
         dispatchEvent(_loc2_);
      }
      
      private function cancelHandler(param1:MouseEvent) : void
      {
         close();
      }
      
      public function showQuestions(param1:Object) : void
      {
         var _loc3_:* = undefined;
         var _loc4_:Label = null;
         var _loc2_:Label = null;
         var _loc5_:int = 5;
         this.showQuestion.content.clearAllChild(this.showQuestion.content);
         for(_loc3_ in param1)
         {
            _loc4_ = this.createLabel(param1[_loc3_]);
            _loc4_.y = _loc5_;
            this.showQuestion.content.addChild(_loc4_);
            _loc5_ += _loc4_.textHeight + 5;
            _loc2_ = this.createLine();
            _loc2_.y = _loc5_;
            this.showQuestion.content.addChild(_loc2_);
            _loc5_ += _loc2_.textHeight + 5;
         }
      }
      
      private function createLine() : Label
      {
         var _loc1_:Label = null;
         _loc1_ = new Label("---------------------------------------------------------------",TextFormatLib.format_0xfff5ce_12px,[FilterLib.glow_0x272727]);
         _loc1_.x = 2;
         return _loc1_;
      }
      
      private function createLabel(param1:Object) : Label
      {
         var _loc4_:TextFormat = TextFormatLib.format_0xfff5ce_12px;
         _loc4_.leading = 4;
         _loc4_.letterSpacing = 1;
         var _loc2_:Label = new Label("",_loc4_,[FilterLib.glow_0x272727]);
         _loc4_.leading = 0;
         _loc4_.letterSpacing = 0;
         _loc2_.x = 2;
         _loc2_.multiline = true;
         _loc2_.wordWrap = true;
         _loc2_.mouseWheelEnabled = false;
         var _loc3_:* = "<font color = \'#00a8ff\'>" + MainData.getInstance().userData.uname + "：</font><br/>";
         _loc3_ += param1.question + "<br/>";
         _loc3_ += Globalization.getString("chat.37");
         _loc3_ += param1.answer == "" ? Globalization.getString("chat.38") : param1.answer;
         _loc2_.htmlText = _loc3_;
         _loc2_.width = 308;
         return _loc2_;
      }
      
      public function clearMsg() : void
      {
         this.gmMC.txt_input.text = "";
      }
      
      override public function dispose() : void
      {
         super.dispose();
         AppFacade.instance.removeMediator("chat.mvc.mediator.GMMediator");
      }
   }
}

