package chat.mvc.view
{
   import chat.event.SendMessageEvent;
   import chat.mvc.mediator.ChatHornMediator;
   import chat.mvc.proxy.MessageSend;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.geom.ColorTransform;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.items.ItemManager;
   import game.items.ItemQualityInfo;
   import game.items.framework.interfaces.IBasicInterface;
   import game.items.framework.items.Item;
   import game.items.framework.items.ItemFactory;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.modules.card.manager.CardManager;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.mvc.AppFacade;
   import game.net.BabelTimeSocket;
   import game.net.SocketDataEvent;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.button.RadioButton;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.text.RichTextArea;
   import mmo.ui.control.window.PopUpWindow;
   import mmo.ui.event.ButtonEvent;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.ModelUtilities;
   import util.time.TimeManager;
   
   public class HornInputPannel extends PopUpWindow
   {
      public static const NAME:String = "chat.mvc.view.HornInputPannel";
      
      public static const CLOSE_INPUT:String = "close_horn_input";
      
      private var inBg:UISkin;
      
      private var txtBg:UISkin;
      
      private var vipUI:UISkin;
      
      private var textbg1:UISkin;
      
      private var textbg2:UISkin;
      
      private var colorBtn:ImgButton;
      
      private var smileyBtn:ImgButton;
      
      private var sendBtn:Button;
      
      private var goldRadio:RadioButton;
      
      private var propRadio:RadioButton;
      
      private var input:RichTextArea;
      
      private var label1:Label;
      
      private var label2:Label;
      
      private var label3:Label;
      
      private var label4:Label;
      
      private var label5:Label;
      
      private var label6:Label;
      
      private var ll_goldnum:Label;
      
      private var ll_propname:Label;
      
      private var ll_propnum:Label;
      
      private var ll_textnum:Label;
      
      private const colorArr:Array = [16777215,16711680,65280,16776960,65535,16711935,16737792,7996390];
      
      private var colorBox:UIBox;
      
      private var colorSprite:Sprite;
      
      private var colorBg:UISkin;
      
      private var smeilyBox:FacePanel;
      
      private var hornType:int = 1;
      
      public var needGold:int;
      
      private var needVip:int;
      
      private var selfGold:Number;
      
      private var selfVip:int;
      
      private var propid:Number;
      
      private var propnum:int;
      
      private var selfpropnum:int;
      
      private var maxInputChar:int;
      
      private const faceNum:int = 3;
      
      private var sendCD:Boolean = false;
      
      private var hornStr0:String;
      
      private var hornStr1:String;
      
      private var hornStr2:String;
      
      private var hornStr3:String;
      
      private var hornStr4:String;
      
      private var hornStr5:String;
      
      private var hornStr6:String;
      
      private var hornStr7:String;
      
      private var hornStr8:String;
      
      private var hornStr9:String;
      
      private var hornStr10:String;
      
      private var hornStr11:String;
      
      private var hornStr12:String;
      
      private var hornStr13:String;
      
      private var hornStr14:String;
      
      public var judgeSever:Boolean = true;
      
      private var hornStr15:String;
      
      private var label7:Label;
      
      private var textbg3:UISkin;
      
      private var inPutH:int;
      
      private var blessing:Array;
      
      private var colorHTML:String = "<font color=\'#{_COLOR_}\'>{_DATA_}</font>";
      
      private var R_COLOR:RegExp = /{_COLOR_}/g;
      
      private var R_DATA:RegExp = /{_DATA_}/g;
      
      private var selectColor:String;
      
      public function HornInputPannel()
      {
         this.judgeSever = this.hornMediator.changeValue;
         if(this.judgeSever)
         {
            this.inPutH = 280;
         }
         else
         {
            this.inPutH = 240;
         }
         super(320,this.inPutH);
         this.initStr();
         this.initView();
         this.initData();
         this.hornMediator.registerInput(this);
         BabelTimeSocket.getInstance().regCallback("sc.exactivity.blessing",this._blessingSC);
      }
      
      private function initStr() : void
      {
         this.hornStr0 = Globalization.getString("chat.1");
         this.hornStr1 = Globalization.getString("horn.1");
         this.hornStr2 = Globalization.getString("horn.2");
         this.hornStr3 = Globalization.getString("horn.3");
         this.hornStr4 = Globalization.getString("horn.4");
         this.hornStr5 = Globalization.getString("horn.5");
         this.hornStr6 = Globalization.getString("horn.6");
         this.hornStr7 = Globalization.getString("horn.7");
         this.hornStr8 = Globalization.getString("horn.8");
         this.hornStr9 = Globalization.getString("horn.9");
         this.hornStr10 = Globalization.getString("horn.10");
         this.hornStr11 = Globalization.getString("horn.11");
         this.hornStr12 = Globalization.getString("horn.13");
         this.hornStr13 = Globalization.getString("horn.14");
         if(this.judgeSever)
         {
            this.hornStr14 = Globalization.getString("horn.15");
         }
         else
         {
            this.hornStr14 = Globalization.getString("horn.18");
         }
         this.hornStr15 = Globalization.getString("horn.16");
      }
      
      private function initView() : void
      {
         title = this.hornStr14;
         this.inBg = UIManager.getUISkin("group_bg");
         this.inBg.setSize(300,225);
         this.inBg.x = 5;
         this.inBg.y = 0;
         addChildToPane(this.inBg);
         this.textbg1 = UIManager.getUISkin("text_bg_2");
         this.textbg1.setSize(270,28);
         this.textbg1.x = 20;
         this.textbg1.y = 15;
         this.textbg2 = UIManager.getUISkin("text_bg_2");
         this.textbg2.setSize(270,28);
         this.textbg2.x = 20;
         this.textbg2.y = 52;
         this.txtBg = UIManager.getUISkin("pane_normal");
         this.txtBg.setSize(270,90);
         this.txtBg.x = 20;
         addChildToPane(this.txtBg);
         this.label1 = new Label(this.hornStr5,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.label2 = new Label(this.hornStr6,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.label3 = new Label(this.hornStr7,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.label4 = new Label(this.hornStr8,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.label5 = new Label(this.hornStr9,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.label6 = new Label(this.hornStr10,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         var _loc1_:TextFormat = TextFormatLib.format_0xFFF600_12px_verdana;
         _loc1_.align = "center";
         this.ll_goldnum = new Label("",_loc1_,[FilterLib.glow_0x272727],false);
         this.ll_goldnum.width = 25;
         this.ll_propname = new Label(this.hornStr11,TextFormatLib.format_0x00FF00_12px,[FilterLib.glow_0x272727]);
         this.ll_propnum = new Label("",TextFormatLib.format_verdana_0xff0000_12px,[FilterLib.glow_0x272727]);
         this.ll_textnum = new Label("",TextFormatLib.format_0x00FF00_12px,[FilterLib.glow_0x272727]);
         this.label1.x = 70;
         this.label2.x = 135;
         this.label3.x = 185;
         this.label4.x = 260;
         this.label5.x = 70;
         this.label6.x = 135;
         this.ll_goldnum.x = 160;
         this.ll_propname.x = 165;
         this.ll_propnum.x = 220;
         this.ll_textnum.x = 20;
         this.label1.y = this.label2.y = this.label3.y = this.label4.y = this.ll_goldnum.y = 20;
         this.label5.y = this.label6.y = this.ll_propname.y = this.ll_propnum.y = 57;
         addChildToPane(this.ll_textnum);
         if(this.judgeSever)
         {
            addChildToPane(this.textbg1);
            addChildToPane(this.textbg2);
            addChildToPane(this.label1);
            addChildToPane(this.label2);
            addChildToPane(this.label3);
            addChildToPane(this.label4);
            addChildToPane(this.label5);
            addChildToPane(this.label6);
            addChildToPane(this.ll_goldnum);
            addChildToPane(this.ll_propname);
            addChildToPane(this.ll_propnum);
         }
         else
         {
            this.inBg.setSize(300,185);
            this.textbg3 = UIManager.getUISkin("text_bg_2");
            this.textbg3.setSize(270,30);
            this.textbg3.x = 20;
            this.textbg3.y = 15;
            addChildToPane(this.textbg3);
            this.label7 = new Label("",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
            this.label7.x = 70;
            this.label7.y = 20;
            addChildToPane(this.label7);
         }
         this.input = new RichTextArea(250,78);
         addChildToPane(this.input);
         this.input.configXML = new XML(XmlManager.getXml("face").face.copy());
         this.input.textField.wordWrap = true;
         this.input.textField.multiline = true;
         this.input.textField.useRichTextClipboard = false;
         this.input.textField.defaultTextFormat = TextFormatLib.red_12px;
         this.input.textField.type = "input";
         this.input.textField.addEventListener("keyUp",this.keyupHandler);
         this.input.textField.addEventListener("keyDown",this.keydownHandler);
         this.input.textField.addEventListener("textInput",this.textInputHandler);
         this.input.x = 30;
         this.colorBtn = new ImgButton(UIManager.getMultiUISkin("HornColorBtn"));
         this.colorBtn.x = 130;
         this.smileyBtn = new ImgButton(UIManager.getMultiUISkin("HornSmeilyBtn"));
         this.smileyBtn.x = 160;
         this.sendBtn = new Button(this.hornStr12,TextFormatLib.format_0xFFB932_12px,70);
         this.sendBtn.x = 220;
         addChildToPane(this.colorBtn);
         addChildToPane(this.smileyBtn);
         addChildToPane(this.sendBtn);
         if(this.judgeSever)
         {
            this.txtBg.y = 87;
            this.ll_textnum.y = 190;
            this.input.y = 92;
            this.colorBtn.y = 190;
            this.smileyBtn.y = 190;
            this.sendBtn.y = 184;
            this.goldRadio = new RadioButton("");
            this.propRadio = new RadioButton("");
            this.goldRadio.x = 40;
            this.goldRadio.y = 20;
            this.propRadio.x = 40;
            this.propRadio.y = 57;
            this.goldRadio.isCheck = true;
            this.goldRadio.gpName = this.propRadio.gpName = "HornChoiceRadio";
            addChildToPane(this.goldRadio);
            addChildToPane(this.propRadio);
         }
         else
         {
            this.txtBg.y = 57;
            this.ll_textnum.y = 160;
            this.input.y = 62;
            this.colorBtn.y = 158;
            this.smileyBtn.y = 158;
            this.sendBtn.y = 152;
         }
         this.initColorBox();
         this.addEvent();
      }
      
      private function initData() : void
      {
         if(this.judgeSever)
         {
            this.needGold = XmlManager.speaker.Speaker.@costGold;
            this.maxInputChar = XmlManager.speaker.Speaker.@charCount;
         }
         else
         {
            this.needGold = XmlManager.cardXML.card_config.@broadCost;
            this.maxInputChar = XmlManager.cardXML.card_config.@wordLimit;
         }
         this.input.textField.maxChars = this.maxInputChar;
         var _loc1_:Array = <EMAIL>("|");
         this.propid = Number(_loc1_[0]);
         this.propnum = Number(_loc1_[1]);
         if(this.judgeSever)
         {
            this.ll_goldnum.setText(this.needGold.toString());
         }
         else
         {
            this.label7.htmlText = StringUtil.substitute(this.hornStr15,this.needGold);
         }
         this._christmas2013();
         if(this.blessing && this.blessing.length)
         {
            this.input.textField.text = this.blessing[Math.floor(Math.random() * this.blessing.length)];
         }
         this.setInputTextNum();
         if(this.judgeSever)
         {
            this.setVipStatu();
            this.setPropNum();
         }
      }
      
      private function _christmas2013() : void
      {
         var _loc5_:XML = null;
         var _loc3_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc2_:int = 0;
         var _loc6_:XMLList = XmlManager.chrismasWelfare.children();
         var _loc1_:Number = TimeManager.getInstance().getTime();
         _loc2_ = _loc6_.length() - 1;
         while(_loc2_ > -1)
         {
            _loc5_ = _loc6_[_loc2_];
            _loc3_ = ModelUtilities.timeStringToNumber(String(_loc5_.@opentime));
            _loc4_ = ModelUtilities.timeStringToNumber(String(_loc5_.@endtime));
            if(_loc1_ > _loc3_ && _loc1_ < _loc4_)
            {
               if(String(_loc5_.@words) != "")
               {
                  this.blessing = String(_loc5_.@words).split("|");
               }
            }
            _loc2_--;
         }
      }
      
      private function addEvent() : void
      {
         addEventListener("addedToStage",this.addToStageHandler);
         if(this.judgeSever)
         {
            this.goldRadio.addEventListener(ButtonEvent.Button_turnOn,this.goldRadioHandler);
            this.propRadio.addEventListener(ButtonEvent.Button_turnOn,this.propRadioHandler);
         }
         this.colorBtn.addEventListener("click",this.colorBtnHandler);
         this.smileyBtn.addEventListener("click",this.smileyBtnHandler);
         this.sendBtn.addEventListener("click",this.sendHandler);
      }
      
      private function addToStageHandler(param1:Event) : void
      {
         removeEventListener("addedToStage",this.addToStageHandler);
         stage.focus = this.input.textField;
      }
      
      private function setInputTextNum() : void
      {
         var _loc4_:int = this.input.textField.length;
         var _loc3_:int = this.maxInputChar - _loc4_;
         var _loc1_:String = this.hornStr4.replace("{0}",_loc3_.toString());
         this.ll_textnum.setText(_loc1_);
         var _loc2_:int = this.input.textField.htmlText.length;
         this.input.textField.setSelection(_loc2_,_loc2_);
      }
      
      private function setVipStatu() : void
      {
         this.needVip = this.getNeedVip();
         this.selfVip = MainData.getInstance().userData.vip;
         if(this.vipUI)
         {
            if(pane.contains(this.vipUI))
            {
               pane.removeChild(this.vipUI);
            }
         }
         else
         {
            this.vipUI = UIManager.getUISkin("v" + this.needVip);
         }
         this.vipUI.x = this.label4.x - this.vipUI.width;
         this.vipUI.y = this.label4.y;
         if(this.selfVip >= this.needVip)
         {
            this.label4.visible = false;
         }
         else
         {
            this.label4.visible = true;
            addChildToPane(this.vipUI);
         }
      }
      
      private function setPropNum() : void
      {
         this.selfpropnum = MainData.getInstance().bagData.userBag.numItems(this.propid);
         if(this.selfpropnum >= this.propnum)
         {
            this.ll_propnum.defaultTextFormat = TextFormatLib.format_0xFFF600_12px_verdana;
         }
         else
         {
            this.ll_propnum.defaultTextFormat = TextFormatLib.format_verdana_0xff0000_12px;
         }
         this.ll_propnum.setText(this.selfpropnum + "/" + this.propnum);
      }
      
      private function getNeedVip() : int
      {
         var _loc4_:XMLList = XmlManager.vipConfing.elements("vip");
         var _loc3_:int = int(_loc4_.length());
         var _loc1_:* = 0;
         var _loc2_:int = 0;
         while(_loc2_ < _loc3_)
         {
            if(_loc4_[_loc2_].@limitSpeaker == 1)
            {
               _loc1_ = _loc2_;
               break;
            }
            _loc2_++;
         }
         _loc4_ = null;
         return _loc1_;
      }
      
      private function judgeMessageSend() : Boolean
      {
         var _loc1_:String = null;
         var _loc2_:Boolean = false;
         if(this.input.text == "")
         {
            _loc1_ = this.hornStr0;
         }
         else if(this.judgeSever)
         {
            if(this.hornType == 1)
            {
               this.needVip = this.getNeedVip();
               this.selfVip = MainData.getInstance().userData.vip;
               if(this.selfVip < this.needVip)
               {
                  _loc1_ = this.hornStr1;
               }
               else
               {
                  this.selfGold = MainData.getInstance().userData.gold_num;
                  if(this.selfGold < this.needGold)
                  {
                     _loc1_ = this.hornStr2;
                  }
                  else
                  {
                     _loc2_ = true;
                  }
               }
            }
            else if(this.hornType == 2)
            {
               this.selfpropnum = MainData.getInstance().bagData.userBag.numItems(this.propid);
               if(this.selfpropnum < this.propnum)
               {
                  _loc1_ = this.hornStr3;
               }
               else
               {
                  _loc2_ = true;
               }
            }
         }
         else
         {
            this.selfGold = CardManager.getInstance().myself.bankGold;
            if(this.selfGold < this.needGold)
            {
               _loc1_ = this.hornStr2;
            }
            else
            {
               _loc2_ = true;
            }
         }
         if(!_loc2_)
         {
            this.showTips(_loc1_);
         }
         return _loc2_;
      }
      
      private function showTips(param1:String, param2:Point = null) : void
      {
         var _loc3_:Number = this.sendBtn.x + this.sendBtn.width / 2 - 14 * param1.length / 2;
         var _loc4_:Point = !!param2 ? param2 : localToGlobal(new Point(_loc3_,this.sendBtn.y + 15));
         AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
            "text":StringUtil.substitute(param1),
            "point":_loc4_
         });
      }
      
      private function initColorBox() : void
      {
         var _loc4_:uint = 0;
         var _loc3_:BitmapData = null;
         var _loc1_:ColorTransform = null;
         var _loc2_:Sprite = null;
         this.colorSprite = new Sprite();
         this.colorBox = new UIBox();
         this.colorBox.rowMaxChildrenNumber = 2;
         this.colorBox.lineMaxChildrenNumber = 4;
         for each(_loc4_ in this.colorArr)
         {
            _loc3_ = new BitmapData(20,20,false,16711680);
            _loc1_ = new ColorTransform();
            _loc1_.color = _loc4_;
            _loc3_.colorTransform(new Rectangle(0,0,20,20),_loc1_);
            _loc2_ = new Sprite();
            _loc2_.buttonMode = true;
            _loc2_.name = _loc4_.toString(16);
            _loc2_.addChild(new Bitmap(_loc3_));
            _loc2_.addEventListener("click",this.colorBoxHandler);
            this.colorBox.addChild(_loc2_);
         }
         this.colorBg = UIManager.getUISkin("HornColorBoxBg");
         this.colorBg.setSize(105,this.colorBox.height + 10);
         this.colorBg.x = this.colorBg.y = -2;
         this.colorBox.x = this.colorBox.y = 5;
         this.colorSprite.addChild(this.colorBg);
         this.colorSprite.addChild(this.colorBox);
      }
      
      private function showColorBox() : void
      {
         if(this.colorSprite)
         {
            this.colorSprite.x = this.colorBtn.x - 40;
            this.colorSprite.y = this.colorBtn.y - this.colorSprite.height;
            stage.addEventListener("click",this.colorStageHandler);
            addChildToPane(this.colorSprite);
         }
      }
      
      private function hideColorBox() : void
      {
         stage.removeEventListener("click",this.colorStageHandler);
         if(this.colorSprite)
         {
            if(pane.contains(this.colorSprite))
            {
               removeChildToPane(this.colorSprite);
            }
         }
      }
      
      private function showSmileyBox() : void
      {
         this.smeilyBox ||= new FacePanel();
         this.hideSmileyBox();
         this.smeilyBox.x = this.smileyBtn.x - 100;
         this.smeilyBox.y = this.smileyBtn.y - 190;
         if(!this.judgeSever)
         {
            this.smeilyBox.delTab(1);
         }
         this.smeilyBox.addEventListener("faceSelect",this.smileyBoxHandler);
         addChildToPane(this.smeilyBox);
      }
      
      private function hideSmileyBox() : void
      {
         if(this.smeilyBox)
         {
            this.smeilyBox.removeEventListener("faceSelect",this.smileyBoxHandler);
            if(pane.contains(this.smeilyBox))
            {
               removeChildToPane(this.smeilyBox);
            }
         }
      }
      
      public function setSendStatu() : void
      {
         this.sendCD = false;
         if(this.judgeSever)
         {
            this.setPropNum();
         }
      }
      
      private function colorStageHandler(param1:MouseEvent) : void
      {
         if(param1.target != this.colorSprite && this.colorSprite.parent)
         {
            this.hideColorBox();
         }
      }
      
      private function keyupHandler(param1:KeyboardEvent) : void
      {
         param1.stopImmediatePropagation();
         if(param1.keyCode == 13)
         {
            return;
         }
         this.setInputTextNum();
      }
      
      private function keydownHandler(param1:KeyboardEvent) : void
      {
         param1.stopImmediatePropagation();
         if(param1.keyCode == 13)
         {
            return;
         }
      }
      
      private function textInputHandler(param1:TextEvent) : void
      {
         var _loc8_:String = null;
         var _loc6_:String = null;
         var _loc7_:String = null;
         var _loc2_:RegExp = null;
         var _loc3_:String = null;
         var _loc5_:int = 0;
         var _loc4_:int = 0;
         if(param1.text == "\n")
         {
            param1.preventDefault();
            param1.stopPropagation();
         }
         else
         {
            _loc8_ = "\n";
            _loc6_ = "";
            _loc7_ = param1.text;
            _loc2_ = new RegExp(_loc8_,"g");
            if(_loc2_.test(param1.text))
            {
               param1.preventDefault();
            }
            else
            {
               _loc8_ = "\r";
               _loc2_ = new RegExp(_loc8_,"g");
               if(_loc2_.test(param1.text))
               {
                  param1.preventDefault();
               }
            }
            param1.preventDefault();
            param1.stopPropagation();
            _loc3_ = this.input.textField.htmlText;
            if(this.selectColor)
            {
               _loc7_ = this.colorHTML.replace(this.R_COLOR,this.selectColor).replace(this.R_DATA,param1.text);
            }
            this.input.textField.htmlText = _loc3_.replace(/<TEXTFORMAT LEADING="3"><P ALIGN="LEFT">/g,"").replace(/<\/P><\/TEXTFORMAT>/g,"") + _loc7_;
            _loc5_ = this.input.textField.length;
            _loc4_ = this.maxInputChar - _loc5_;
            if(_loc4_ < 1)
            {
               this.input.textField.htmlText = _loc3_;
            }
            this.setInputTextNum();
         }
      }
      
      private function goldRadioHandler(param1:Event) : void
      {
         this.hornType = 1;
      }
      
      private function propRadioHandler(param1:Event) : void
      {
         this.hornType = 2;
      }
      
      private function colorBtnHandler(param1:MouseEvent) : void
      {
         param1.stopImmediatePropagation();
         if(this.colorSprite.parent)
         {
            this.hideColorBox();
         }
         else
         {
            this.showColorBox();
         }
      }
      
      private function smileyBtnHandler(param1:MouseEvent) : void
      {
         if(this.smeilyBox && this.smeilyBox.parent)
         {
            this.hideSmileyBox();
         }
         else
         {
            this.showSmileyBox();
         }
      }
      
      private function sendHandler(param1:MouseEvent) : void
      {
         if(this.sendCD)
         {
            return;
         }
         var _loc3_:Boolean = this.judgeMessageSend();
         if(!_loc3_)
         {
            return;
         }
         this.sendCD = true;
         var _loc2_:MessageSend = new MessageSend();
         if(this.judgeSever)
         {
            _loc2_.sendChannel = "speaker";
            _loc2_.hornType = this.hornType;
         }
         else
         {
            _loc2_.sendChannel = "cardHorn";
         }
         _loc2_.messageText = this.input.richText;
         this.input.clear();
         if(this.blessing && this.blessing.length)
         {
            this.input.textField.text = this.blessing[Math.floor(Math.random() * this.blessing.length)];
         }
         dispatchEvent(new SendMessageEvent("sendMsg",_loc2_));
         this.setInputTextNum();
      }
      
      private function colorBoxHandler(param1:MouseEvent) : void
      {
         var _loc2_:int = this.input.textField.caretIndex;
         this.hideColorBox();
         param1.stopImmediatePropagation();
         this.selectColor = param1.target.name;
         stage.focus = this.input.textField;
      }
      
      private function smileyBoxHandler(param1:DataEvent) : void
      {
         var _loc2_:Point = null;
         this.hideSmileyBox();
         var _loc5_:int = this.input.textField.length;
         var _loc3_:int = this.input.text.length;
         var _loc4_:int = (_loc3_ - _loc5_) / 3;
         if(_loc5_ >= this.maxInputChar)
         {
            return;
         }
         if(_loc4_ >= 3)
         {
            _loc2_ = localToGlobal(new Point(this.smileyBtn.x - 20,this.smileyBtn.y + 15));
            this.showTips(this.hornStr13.replace("{0}",(3).toString()),_loc2_);
         }
         else
         {
            this.input.insertRichText(param1.data);
            this.setInputTextNum();
         }
      }
      
      private function colorBoxStageHandler(param1:MouseEvent) : void
      {
         this.hideColorBox();
      }
      
      override public function close() : void
      {
         BabelTimeSocket.getInstance().removeCallback("sc.exactivity.blessing",this._blessingSC);
         dispatchEvent(new Event("close_horn_input"));
         super.close();
      }
      
      private function _blessingSC(param1:SocketDataEvent) : void
      {
         var _loc9_:Object = null;
         var _loc7_:* = undefined;
         var _loc8_:Item = null;
         var _loc2_:IBasicInterface = null;
         var _loc3_:Array = null;
         var _loc6_:int = 0;
         var _loc5_:* = null;
         var _loc4_:int = 0;
         if(param1.data && !(param1.data is String))
         {
            if(param1.data.baginfo)
            {
               _loc9_ = param1.data.baginfo;
               for(_loc7_ in _loc9_)
               {
                  _loc8_ = ItemFactory.creatItem(_loc9_[_loc7_]);
                  MainData.getInstance().bagData.setGridItem(_loc7_,_loc8_);
               }
            }
            if(param1.data.iteminfo)
            {
               _loc3_ = param1.data.iteminfo as Array;
               _loc6_ = int(_loc3_.length);
               if(_loc6_)
               {
                  _loc5_ = Globalization.getString("equipment.11") + "\n";
                  _loc4_ = 0;
                  while(_loc4_ < _loc6_)
                  {
                     _loc2_ = ItemManager.getInstance().getItemTemplate(_loc3_[_loc4_].item_template_id);
                     _loc5_ += "<font color=\'" + MessageReceive.parseColor(ItemQualityInfo.getQualityColor(_loc2_.quality)) + "\'>" + _loc2_.name + "</font> * " + _loc3_[_loc4_].item_num + "\n";
                     _loc4_++;
                  }
                  MainData.getInstance().manlyData.christmasleftNum = MainData.getInstance().manlyData.christmasleftNum - 1;
                  if(MainData.getInstance().manlyData.christmasleftNum < 0)
                  {
                     MainData.getInstance().manlyData.christmasleftNum = 0;
                  }
                  AppFacade.instance.sendNotification("CHRISTMAS_LEFTNUM_REFRESH");
                  AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                     "text":_loc5_,
                     "textFormat":TextFormatLib.format_0x00FF00_14px
                  });
               }
            }
         }
      }
      
      private function get hornMediator() : ChatHornMediator
      {
         return AppFacade.instance.retrieveMediator("chat.mvc.mediator.ChatHornMediator") as ChatHornMediator;
      }
   }
}

