package chat.mvc.view
{
   import chat.mvc.mediator.ChatHornMediator;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.events.TimerEvent;
   import flash.geom.Point;
   import flash.utils.Timer;
   import game.data.MainData;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.modules.MenuInfo;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.modules.chat.text.TextLayoutManager;
   import game.mvc.AppFacade;
   import game.mvc.module.IModulePart;
   import mmo.Core;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.text.RichTextArea;
   
   public class HornOutputPannel extends UISprite implements IModulePart
   {
      public static const NAME:String = "chat.mvc.view.HornOutputPannel";
      
      public static const CLOSE:String = "close_horn_output";
      
      private const ow:Number = 430;
      
      private const tw:Number = 380;
      
      private var oh:Number = 60;
      
      private var th:Number = 40;
      
      private var msgList:Array = [];
      
      private var bg:UISkin;
      
      private var delay:int;
      
      private var comicContainer:Sprite;
      
      private var output:RichTextArea;
      
      private var comic:MovieClip;
      
      private var hornTimer:Timer;
      
      private var isRun:Boolean = false;
      
      private var link:TextEvent;
      
      private var faceBox:FacePanel;
      
      public var outPutJudge:Boolean = true;
      
      public function HornOutputPannel()
      {
         super();
         this.outPutJudge = this.hornMediator.changeValue;
         this.initFaceBox();
         this.initView();
         this.initData();
         this.hornMediator.registerOutput(this);
      }
      
      private function initView() : void
      {
         this.bg = UIManager.getUISkin("HornOutputBg");
         if(this.outPutJudge)
         {
            this.bg.setSize(430,this.oh);
         }
         else
         {
            this.bg.setSize(216,115);
         }
         addChild(this.bg);
         this.comicContainer = new Sprite();
         addChild(this.comicContainer);
         if(this.outPutJudge)
         {
            this.comic = AssetManager.getMc("HornBgMc");
         }
         else
         {
            this.comic = AssetManager.getMc("CardHornBgMc");
         }
         this.comicContainer.addChild(this.comic);
         if(this.outPutJudge)
         {
            this.output = new RichTextArea(380,this.th);
         }
         else
         {
            this.output = new RichTextArea(164,40);
         }
         var _loc1_:XML = new XML(XmlManager.getXml("face").child("face").copy());
         _loc1_ = _loc1_.appendChild(XmlManager.getXml("face").mc.children().copy());
         this.output.configXML = _loc1_;
         this.output.textField.wordWrap = true;
         this.output.textField.multiline = true;
         this.output.textField.addEventListener("link",this.linkHandler);
         this.output.textField.defaultTextFormat = TextFormatLib.format_0xffed89_14px;
         this.output.textField.filters = [FilterLib.glow_0x272727];
         this.output.textField.selectable = false;
         this.output.textField.mouseEnabled = false;
         if(this.outPutJudge)
         {
            this.comic.x = 5;
            this.comic.y = 0;
            this.output.x = 25;
            this.output.y = (this.oh - this.th) / 2;
         }
         else
         {
            this.bg.x = 3;
            this.bg.y = 320;
            this.comic.x = 4;
            this.comic.y = 333;
            this.output.x = 7;
            this.output.y = 333;
         }
         addChild(this.output);
         if(this.outPutJudge)
         {
            x = Core.stgW - 430 >> 1;
            y = 80;
         }
         mouseEnabled = false;
         mouseChildren = false;
         addEventListener("addedToStage",this.addToStageHandler);
         this.hornMediator.isHorn = true;
      }
      
      private function initData() : void
      {
         if(this.outPutJudge)
         {
            this.delay = int(XmlManager.speaker.Speaker.@intervalTime) * 1000;
         }
         else
         {
            this.delay = int(XmlManager.speaker.Speaker.@cardIntervalTime) * 1000;
         }
      }
      
      public function initFaceBox() : void
      {
         if(this.faceBox == null)
         {
            this.faceBox = new FacePanel();
         }
      }
      
      override public function dispose() : void
      {
         if(stage)
         {
            stage.removeEventListener("resize",this.resizeHandler);
         }
         super.dispose();
      }
      
      public function addMessage(param1:MessageReceive) : void
      {
         this.msgList.push(param1);
         if(!this.isRun)
         {
            this.showMessage();
         }
      }
      
      private function getGifUrl(param1:int) : String
      {
         return "bitmaps/face/face" + param1 + ".gif";
      }
      
      private function showMessage() : void
      {
         if(this.msgList.length == 0)
         {
            this.clearTimer();
            this.output.clear();
            this.isRun = false;
            dispatchEvent(new Event("close_horn_output"));
            return;
         }
         var _loc4_:String = "SIZE=\"12\" COLOR";
         var _loc3_:String = "SIZE=\"14\" COLOR";
         var _loc1_:String = TextLayoutManager.parseClipMessage(this.msgList.shift());
         var _loc2_:RegExp = new RegExp(_loc4_,"g");
         _loc1_ = _loc1_.replace(_loc2_,_loc3_);
         _loc4_ = "SIZE=\"32\"";
         _loc3_ = "SIZE=\"14\"";
         _loc2_ = new RegExp(_loc4_,"g");
         _loc1_ = _loc1_.replace(_loc2_,_loc3_);
         this.output.clear();
         this.output.appendRichText(_loc1_);
         this.output.autoAdjust();
         if(!this.outPutJudge && this.msgList.length > 1)
         {
            this.delay = int(XmlManager.speaker.Speaker.@waitIntervalTime) * 1000;
         }
         this.isRun = true;
         this.addTimer();
         this.resize();
      }
      
      private function resize() : void
      {
         this.th = this.output.textField.textHeight + 5;
         if(this.outPutJudge)
         {
            if(this.th > 130)
            {
               this.th = 130;
            }
            this.output.resizeTo(380,this.th);
         }
         else
         {
            if(this.th > 80)
            {
               if(this.th > 155)
               {
                  this.th = 160;
               }
               this.bg.x = 3;
               this.bg.y = 250;
               this.comic.x = 4;
               this.comic.y = 258;
               this.output.x = 7;
               this.output.y = 263;
            }
            else
            {
               this.bg.x = 3;
               this.bg.y = 320;
               this.comic.x = 4;
               this.comic.y = 328;
               this.output.x = 7;
               this.output.y = 333;
            }
            this.output.resizeTo(216,this.th);
         }
         this.oh = this.th + 30;
         if(this.oh < 60)
         {
            this.oh = 60;
         }
         if(this.oh > 160)
         {
            this.oh = 160;
         }
         if(this.outPutJudge)
         {
            this.bg.setSize(430,this.oh);
            this.output.y = this.oh - this.th >> 1;
         }
         else
         {
            this.bg.setSize(216,this.oh);
         }
         this.output.textField.scrollV = 0;
      }
      
      private function addToStageHandler(param1:Event) : void
      {
         removeEventListener("addedToStage",this.addToStageHandler);
         if(stage)
         {
            stage.addEventListener("resize",this.resizeHandler);
         }
      }
      
      private function resizeHandler(param1:Event) : void
      {
         if(this.outPutJudge)
         {
            x = Core.stgW - 430 >> 1;
         }
      }
      
      private function addTimer() : void
      {
         this.clearTimer();
         this.hornTimer = new Timer(this.delay);
         this.hornTimer.addEventListener("timer",this.timerHandler);
         this.hornTimer.start();
      }
      
      private function clearTimer() : void
      {
         if(this.hornTimer)
         {
            this.hornTimer.stop();
            this.hornTimer.removeEventListener("timer",this.timerHandler);
            this.hornTimer = null;
         }
      }
      
      private function timerHandler(param1:TimerEvent) : void
      {
         this.isRun = false;
         this.showMessage();
      }
      
      private function linkHandler(param1:TextEvent) : void
      {
         param1.stopImmediatePropagation();
         this.link = param1;
      }
      
      private function outputHandler(param1:MouseEvent) : void
      {
         var _loc4_:String = null;
         var _loc2_:String = null;
         var _loc3_:Point = null;
         if(this.link)
         {
            _loc4_ = this.link.text.split("_")[0];
            _loc2_ = this.link.text.split("_")[1];
            if(_loc4_ == "user")
            {
               if(int(_loc2_) == MainData.getInstance().userData.uid)
               {
                  return;
               }
               _loc3_ = new Point(stage.mouseX + 10,stage.mouseY);
               MenuInfo.show(_loc3_.x,_loc3_.y,int(_loc2_));
            }
         }
         this.link = null;
      }
      
      public function show(param1:Object) : void
      {
      }
      
      public function close() : void
      {
         this.clearTimer();
         this.output.clear();
         this.isRun = false;
         dispatchEvent(new Event("close_horn_output"));
      }
      
      override public function get isLive() : Boolean
      {
         return true;
      }
      
      override public function set isLive(param1:Boolean) : void
      {
      }
      
      private function get hornMediator() : ChatHornMediator
      {
         return AppFacade.instance.retrieveMediator("chat.mvc.mediator.ChatHornMediator") as ChatHornMediator;
      }
   }
}

