package chat.mvc.view
{
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.events.TimerEvent;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.utils.Timer;
   import game.Environment;
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   
   public class SpeakBoard extends UISprite
   {
      private var bg:UISkin;
      
      private var lbl:Label;
      
      private var btn_close:Button;
      
      private var _infos:Array;
      
      private var i:int;
      
      private var timer:Timer;
      
      public function SpeakBoard()
      {
         super();
         this.bg = UIManager.getUISkin("annouce_bg");
         addChild(this.bg);
         var _loc1_:UISkin = UIManager.getUISkin("speaker");
         _loc1_.x = 5;
         _loc1_.y = (this.bg.height - _loc1_.height) / 2;
         addChild(_loc1_);
         this.lbl = new Label("",TextFormatLib.format_0xFFF600_14px_underLine,[FilterLib.glow_0x272727]);
         this.lbl.x = _loc1_.x + _loc1_.width;
         this.lbl.wordWrap = true;
         this.lbl.multiline = true;
         this.lbl.width = 500;
         addChild(this.lbl);
         this.bg.setSize(this.lbl.x + this.lbl.textWidth + 25,this.lbl.textHeight + 8);
         this.btn_close = new Button("",null,14,UIManager.getMultiUISkin("btn_close"));
         this.btn_close.x = this.bg.width - this.btn_close.width - 5;
         this.btn_close.y = _loc1_.y;
         addChild(this.btn_close);
         this.btn_close.addEventListener("click",this.closeHandler);
         this.lbl.addEventListener("link",this.onLinkHandler);
         this.timer = new Timer(180000);
         this.timer.addEventListener("timer",this.timerChangeHandler);
         this.mouseEnabled = false;
      }
      
      private function timerChangeHandler(param1:TimerEvent) : void
      {
         this.i++;
         if(this.i == this._infos.length)
         {
            this.i = 0;
         }
         this.updateInfo();
      }
      
      public function setText(param1:Array) : void
      {
         this._infos = param1;
         if(this._infos.length == 0)
         {
            return;
         }
         this.i = 0;
         this.updateInfo();
         this.timer.start();
      }
      
      private function updateInfo() : void
      {
         var _loc1_:Object = this._infos[this.i];
         if(_loc1_.url != "")
         {
            this.lbl.htmlText = "<a href = \'event:" + _loc1_.url + "\'>" + _loc1_.word + "</a>";
         }
         else
         {
            this.lbl.htmlText = _loc1_.word;
         }
         this.bg.setSize(this.lbl.x + this.lbl.textWidth + 25,this.lbl.textHeight + 8);
         this.btn_close.x = this.bg.width - this.btn_close.width - 5;
      }
      
      private function closeHandler(param1:MouseEvent) : void
      {
         this.timer.stop();
         this.timer.reset();
         parent.removeChild(this);
      }
      
      private function onLinkHandler(param1:TextEvent) : void
      {
         var _loc2_:String = param1.text;
         if(_loc2_.indexOf("?") != -1)
         {
            _loc2_ += "&pid=" + Environment.loadingParams.pid;
         }
         else
         {
            _loc2_ += "?pid=" + Environment.loadingParams.pid;
         }
         navigateToURL(new URLRequest(_loc2_),"_blank");
      }
      
      override public function get width() : Number
      {
         return this.bg.width;
      }
   }
}

