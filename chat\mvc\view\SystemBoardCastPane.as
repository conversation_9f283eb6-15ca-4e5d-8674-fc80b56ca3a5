package chat.mvc.view
{
   import chat.mvc.mediator.BoardCastMediator;
   import chat.mvc.mediator.ChatHornMediator;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.utils.getTimer;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.modules.chat.msgInfo.MessageReceive;
   import game.modules.chat.text.TextLayoutManager;
   import game.mvc.AppFacade;
   import game.mvc.module.IModulePart;
   import mmo.Core;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.text.RichTextArea;
   
   public class SystemBoardCastPane extends UISprite implements IModulePart
   {
      private var txt1:RichTextArea;
      
      private var maskMC:Sprite;
      
      private var pane:Sprite;
      
      private var bg:UISkin;
      
      private var msgList:Array;
      
      private var mc:Sprite;
      
      private var preTime:Number;
      
      private var totalFrame:int;
      
      private var initPos:Number;
      
      private var _board:SpeakBoard;
      
      private var running:Boolean = false;
      
      private var _speed:Number = 90;
      
      public function SystemBoardCastPane()
      {
         super();
         this.x = 370;
         this.y = 100;
         this.msgList = [];
         isLive = true;
         mouseChildren = false;
         mouseEnabled = false;
         this.bg = UIManager.getUISkin("text_bg_8");
         this.bg.setSize(490,30);
         addChild(this.bg);
         this.bg.visible = false;
         this.pane = new Sprite();
         this.pane.x = 2.5;
         addChild(this.pane);
         this.maskMC = new Sprite();
         var _loc2_:BitmapData = new BitmapData(480,30);
         this.maskMC.addChild(new Bitmap(_loc2_));
         this.pane.addChild(this.maskMC);
         this.pane.mask = this.maskMC;
         this.mc = new Sprite();
         this.mc.y = 4;
         this.pane.addChild(this.mc);
         var _loc1_:XML = new XML(XmlManager.getXml("face").child("face").copy());
         _loc1_ = _loc1_.appendChild(XmlManager.getXml("face").mc.children().copy());
         this.txt1 = new RichTextArea(50,30);
         this.txt1.configXML = _loc1_;
         this.txt1.textField.defaultTextFormat = TextFormatLib.format_0xffed89_14px;
         this.txt1.textField.filters = [FilterLib.glow_0x272727];
         this.txt1.textField.multiline = false;
         this.txt1.textField.selectable = false;
         this.mc.addChild(this.txt1);
         AppFacade.instance.registerMediator(new BoardCastMediator(this));
         if(this.isHorn)
         {
            visible = false;
         }
         else
         {
            visible = true;
         }
      }
      
      private function moveHandler(param1:Event) : void
      {
         if(!this.running)
         {
            return;
         }
         var _loc3_:Number = getTimer();
         var _loc2_:Number = this.mc.x;
         this.mc.x = this.initPos - Math.ceil(this._speed * (_loc3_ - this.preTime) / 1000);
         if(this.mc.x <= -this.txt1.width)
         {
            if(this.msgList.length > 0)
            {
               this.updateText(this.msgList.shift());
            }
            else
            {
               removeEventListener("enterFrame",this.moveHandler);
               this.bg.visible = false;
               this.running = false;
               this.txt1.clear();
            }
         }
      }
      
      private function updateText(param1:MessageReceive) : void
      {
         this.mc.x = this.initPos = 480;
         this.txt1.richText = TextLayoutManager.parseClipMessage(param1);
         this.txt1.resizeTo(this.txt1.textField.textWidth + 60,30);
         this.txt1.autoAdjust();
         param1 = null;
      }
      
      public function addMsg(param1:MessageReceive) : void
      {
         this.msgList.push(param1);
         if(!this.running)
         {
            this.startMove();
            this.bg.visible = true;
         }
      }
      
      public function showAnnounceInfo(param1:Array) : void
      {
         if(param1.length == 0)
         {
            return;
         }
         this._board ||= new SpeakBoard();
         this._board.y = this.y + 32;
         this._board.setText(param1);
         this._board.x = (Core.stgW - this._board.width) / 2;
         this._board.parent || parent.addChild(this._board);
      }
      
      private function startMove() : void
      {
         this.updateText(this.msgList.shift());
         this.running = true;
         this.preTime = getTimer();
         this.totalFrame = Math.ceil(this.txt1.width / 3);
         addEventListener("enterFrame",this.moveHandler);
      }
      
      public function close() : void
      {
      }
      
      public function show(param1:Object) : void
      {
      }
      
      override public function dispose() : void
      {
         this.txt1.clear();
         this.msgList = null;
         removeEventListener("enterFrame",this.moveHandler);
         AppFacade.instance.removeMediator("chat.mvc.mediator.BoardCastMediator");
         super.dispose();
      }
      
      private function get isHorn() : Boolean
      {
         return this.hornMediator.isHorn;
      }
      
      private function get hornMediator() : ChatHornMediator
      {
         return AppFacade.instance.retrieveMediator("chat.mvc.mediator.ChatHornMediator") as ChatHornMediator;
      }
   }
}

