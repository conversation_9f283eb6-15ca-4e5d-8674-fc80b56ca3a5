package chat.mvc.view
{
   import flash.display.Sprite;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.ItemRender;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   
   public class TownTeamFightUserItem extends ItemRender
   {
      private var nameParent:Sprite;
      
      private var iconParent:Sprite;
      
      public function TownTeamFightUserItem()
      {
         super();
         this.iconParent = addChild(new Sprite()) as Sprite;
         this.nameParent = addChild(new Sprite()) as Sprite;
         this.nameParent.x = 20;
      }
      
      override public function setData(param1:Object, param2:Array = null) : void
      {
         var _loc3_:int = 0;
         var _loc6_:String = null;
         var _loc7_:Label = null;
         var _loc5_:* = param1;
         var _loc4_:* = param2;
         super.setData(_loc5_,_loc4_);
         while(this.iconParent.numChildren != 0)
         {
            this.iconParent.removeChildAt(0);
         }
         _loc3_ = int(XmlManager.roleConf.role.(@htid == _loc5_.htid)[0].@sex);
         _loc6_ = _loc3_ == 1 ? "boy" : "girl";
         _loc6_ = "leader";
         _loc5_.captain && (_loc6_);
         this.iconParent.addChild(UIManager.getUISkin(_loc6_));
         while(this.nameParent.numChildren != 0)
         {
            this.nameParent.removeChildAt(0);
         }
         _loc7_ = new Label("",TextFormatLib.format_verdana_0xffed89_12px);
         _loc7_.autoSize = "center";
         _loc7_.x = 50;
         _loc7_.text = StringUtil.substitute("Lv.{0} {1}",_loc5_.level,_loc5_.uname);
         this.nameParent.addChild(_loc7_);
      }
   }
}

