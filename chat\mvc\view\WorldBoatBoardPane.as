package chat.mvc.view
{
   import chat.mvc.mediator.ChatHornMediator;
   import chat.mvc.mediator.WorldBoatBoardMediator;
   import chat.mvc.proxy.WorldBoatBoardProxy;
   import com.greensock.TweenLite;
   import com.greensock.easing.Linear;
   import flash.display.Sprite;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import game.mvc.module.IModulePart;
   import game.mvc.module.ModuleManager;
   import mmo.Core;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.text.RichTextArea;
   
   public class WorldBoatBoardPane extends UISprite implements IModulePart
   {
      private var txt1:RichTextArea;
      
      private var txt2:RichTextArea;
      
      private var txt3:RichTextArea;
      
      private var txt4:RichTextArea;
      
      private var txt5:RichTextArea;
      
      private var txt6:RichTextArea;
      
      private var txt7:RichTextArea;
      
      private var txt8:RichTextArea;
      
      private var maskMC:Sprite;
      
      private var pane:Sprite;
      
      private var bg:UISkin;
      
      private var msgList:Array;
      
      private var mc:Sprite;
      
      private var mc1:Sprite;
      
      private var mc2:Sprite;
      
      private var mc3:Sprite;
      
      private var mc4:Sprite;
      
      private var mc5:Sprite;
      
      private var mc6:Sprite;
      
      private var mc7:Sprite;
      
      private var preTime:Number;
      
      private var initPos:Number;
      
      private var mcTimer:Timer;
      
      private var mcTimer1:Timer;
      
      private var mcTimer2:Timer;
      
      private var mcTimer3:Timer;
      
      private var mcTimer4:Timer;
      
      private var mcTimer5:Timer;
      
      private var mcTimer6:Timer;
      
      private var mcTimer7:Timer;
      
      private var judeTimer:Timer;
      
      private var xPos:int = 800;
      
      private var durTime:int = 15;
      
      private var timeId:int;
      
      private var _board:SpeakBoard;
      
      private var running:Boolean = false;
      
      private var running1:Boolean = false;
      
      private var running2:Boolean = false;
      
      private var running3:Boolean = false;
      
      private var running4:Boolean = false;
      
      private var running5:Boolean = false;
      
      private var running6:Boolean = false;
      
      private var running7:Boolean = false;
      
      private var _speed:Number = 90;
      
      public function WorldBoatBoardPane()
      {
         super();
         this.x = 0;
         this.y = 200;
         this.msgList = [];
         isLive = true;
         mouseChildren = false;
         mouseEnabled = false;
         this.bg = UIManager.getUISkin("text_bg_8");
         this.bg.setSize(1260,230);
         this.pane = new Sprite();
         this.pane.x = 780;
         addChild(this.pane);
         this.maskMC = new Sprite();
         this.addMc(0);
         this.addMc(1);
         this.addMc(2);
         this.addMc(3);
         this.addMc(4);
         this.addMc(5);
         this.addMc(6);
         this.addMc(7);
         AppFacade.instance.registerMediator(new WorldBoatBoardMediator(this));
         AppFacade.instance.registerProxy(new WorldBoatBoardProxy("chat.mvc.proxy.WorldBoatBoardProxy"));
         if(this.isHorn)
         {
            visible = false;
         }
         else
         {
            visible = true;
         }
      }
      
      private function addMc(param1:int) : void
      {
         var _loc3_:XML = new XML(XmlManager.getXml("face").child("face").copy());
         _loc3_ = _loc3_.appendChild(XmlManager.getXml("face").mc.children().copy());
         var _loc2_:int = 780;
         if(param1 == 0)
         {
            this.mc = new Sprite();
            this.mc.x = _loc2_;
            this.mc.y = 4;
            this.pane.addChild(this.mc);
            this.txt1 = new RichTextArea(50,40);
            this.txt1.configXML = _loc3_;
            this.txt1.textField.defaultTextFormat = TextFormatLib.format_0xf6ff08_25px;
            this.txt1.textField.filters = [FilterLib.glow_0x272727];
            this.txt1.textField.multiline = false;
            this.txt1.textField.selectable = false;
            this.mc.addChild(this.txt1);
         }
         else if(param1 == 1)
         {
            this.mc1 = new Sprite();
            this.mc1.x = _loc2_;
            this.mc1.y = 37;
            this.pane.addChild(this.mc1);
            this.txt2 = new RichTextArea(50,40);
            this.txt2.configXML = _loc3_;
            this.txt2.textField.defaultTextFormat = TextFormatLib.format_0xf6ff08_25px;
            this.txt2.textField.filters = [FilterLib.glow_0x272727];
            this.txt2.textField.multiline = false;
            this.txt2.textField.selectable = false;
            this.mc1.addChild(this.txt2);
         }
         else if(param1 == 2)
         {
            this.mc2 = new Sprite();
            this.mc2.x = _loc2_;
            this.mc2.y = 70;
            this.pane.addChild(this.mc2);
            this.txt3 = new RichTextArea(50,40);
            this.txt3.configXML = _loc3_;
            this.txt3.textField.defaultTextFormat = TextFormatLib.format_0xf6ff08_25px;
            this.txt3.textField.filters = [FilterLib.glow_0x272727];
            this.txt3.textField.multiline = false;
            this.txt3.textField.selectable = false;
            this.mc2.addChild(this.txt3);
         }
         else if(param1 == 3)
         {
            this.mc3 = new Sprite();
            this.mc3.x = _loc2_;
            this.mc3.y = 103;
            this.pane.addChild(this.mc3);
            this.txt4 = new RichTextArea(50,40);
            this.txt4.configXML = _loc3_;
            this.txt4.textField.defaultTextFormat = TextFormatLib.format_0xf6ff08_25px;
            this.txt4.textField.filters = [FilterLib.glow_0x272727];
            this.txt4.textField.multiline = false;
            this.txt4.textField.selectable = false;
            this.mc3.addChild(this.txt4);
         }
         else if(param1 == 4)
         {
            this.mc4 = new Sprite();
            this.mc4.x = _loc2_;
            this.mc4.y = 4;
            this.pane.addChild(this.mc4);
            this.txt5 = new RichTextArea(50,40);
            this.txt5.configXML = _loc3_;
            this.txt5.textField.defaultTextFormat = TextFormatLib.format_0xf6ff08_25px;
            this.txt5.textField.filters = [FilterLib.glow_0x272727];
            this.txt5.textField.multiline = false;
            this.txt5.textField.selectable = false;
            this.mc4.addChild(this.txt5);
         }
         else if(param1 == 5)
         {
            this.mc5 = new Sprite();
            this.mc5.x = _loc2_;
            this.mc5.y = 37;
            this.pane.addChild(this.mc5);
            this.txt6 = new RichTextArea(50,40);
            this.txt6.configXML = _loc3_;
            this.txt6.textField.defaultTextFormat = TextFormatLib.format_0xf6ff08_25px;
            this.txt6.textField.filters = [FilterLib.glow_0x272727];
            this.txt6.textField.multiline = false;
            this.txt6.textField.selectable = false;
            this.mc5.addChild(this.txt6);
         }
         else if(param1 == 6)
         {
            this.mc6 = new Sprite();
            this.mc6.x = _loc2_;
            this.mc6.y = 70;
            this.pane.addChild(this.mc6);
            this.txt7 = new RichTextArea(50,40);
            this.txt7.configXML = _loc3_;
            this.txt7.textField.defaultTextFormat = TextFormatLib.format_0xf6ff08_25px;
            this.txt7.textField.filters = [FilterLib.glow_0x272727];
            this.txt7.textField.multiline = false;
            this.txt7.textField.selectable = false;
            this.mc6.addChild(this.txt7);
         }
         else if(param1 == 7)
         {
            this.mc7 = new Sprite();
            this.mc7.x = _loc2_;
            this.mc7.y = 103;
            this.pane.addChild(this.mc7);
            this.txt8 = new RichTextArea(50,40);
            this.txt8.configXML = _loc3_;
            this.txt8.textField.defaultTextFormat = TextFormatLib.format_0xf6ff08_25px;
            this.txt8.textField.filters = [FilterLib.glow_0x272727];
            this.txt8.textField.multiline = false;
            this.txt8.textField.selectable = false;
            this.mc7.addChild(this.txt8);
         }
      }
      
      private function addTimer(param1:int) : void
      {
         this.timeId = param1;
         this.clearTimer(param1);
         if(this.timeId == 1)
         {
            this.mcTimer = new Timer(4000);
            this.mcTimer.addEventListener("timer",this.timerHandler);
            this.mcTimer.start();
         }
      }
      
      private function clearTimer(param1:int) : void
      {
         if(param1 == 1)
         {
            if(this.mcTimer)
            {
               this.mcTimer.stop();
               this.mcTimer.removeEventListener("timer",this.timerHandler);
               this.mcTimer = null;
            }
         }
      }
      
      private function cleanJudeTimer() : void
      {
         if(this.judeTimer)
         {
            this.judeTimer.stop();
            this.judeTimer.removeEventListener("timer",this.judeTimerHandler);
            this.judeTimer = null;
         }
      }
      
      private function timerHandler(param1:TimerEvent) : void
      {
         if(!this.running)
         {
            if(this.msgList.length > 0)
            {
               this.updateText(this.msgList.shift());
               this.bg.visible = true;
               this.mcMove();
            }
         }
         if(!this.running1)
         {
            if(this.msgList.length > 0)
            {
               this.updateText1(this.msgList.shift());
               this.bg.visible = true;
               this.mcMove1();
            }
         }
         if(!this.running2)
         {
            if(this.msgList.length > 0)
            {
               this.updateText2(this.msgList.shift());
               this.bg.visible = true;
               this.mcMove2();
            }
         }
         if(!this.running3)
         {
            if(this.msgList.length > 0)
            {
               this.updateText3(this.msgList.shift());
               this.bg.visible = true;
               this.mcMove3();
            }
         }
         if(this.msgList.length == 0)
         {
            this.cleanJudeTimer();
            this.clearTimer(1);
         }
         if(this.running && this.running1 && this.running2 && this.running3)
         {
            if(this.mc.x + this.mc.width < this.xPos)
            {
               if(!this.running4)
               {
                  this.startMove4();
                  this.bg.visible = true;
               }
            }
            if(this.mc1.x + this.mc1.width < this.xPos)
            {
               if(!this.running5)
               {
                  this.startMove5();
                  this.bg.visible = true;
               }
            }
            if(this.mc2.x + this.mc2.width < this.xPos)
            {
               if(!this.running6)
               {
                  this.startMove6();
                  this.bg.visible = true;
               }
            }
            if(this.mc3.x + this.mc3.width < this.xPos)
            {
               if(!this.running7)
               {
                  this.startMove7();
                  this.bg.visible = true;
               }
            }
         }
      }
      
      private function updateText(param1:String) : void
      {
         if(this.mc != null)
         {
            this.mc = null;
            this.addMc(0);
         }
         this.mc.x = this.initPos = 480;
         this.txt1.richText = param1;
         this.txt1.resizeTo(this.txt1.textField.textWidth + 5,40);
         this.txt1.textField.defaultTextFormat = TextFormatLib.format_0xf6ff08_25px;
         this.txt1.autoAdjust();
         param1 = null;
      }
      
      private function updateText1(param1:String) : void
      {
         if(this.mc1 != null)
         {
            this.mc1 = null;
            this.addMc(1);
         }
         this.mc1.x = this.initPos = 480;
         this.txt2.richText = param1;
         this.txt2.resizeTo(this.txt2.textField.textWidth + 5,40);
         this.txt2.autoAdjust();
         param1 = null;
      }
      
      private function updateText2(param1:String) : void
      {
         if(this.mc2 != null)
         {
            this.mc2 = null;
            this.addMc(2);
         }
         this.mc2.x = this.initPos = 480;
         this.txt3.richText = param1;
         this.txt3.resizeTo(this.txt3.textField.textWidth + 5,40);
         this.txt3.autoAdjust();
         param1 = null;
      }
      
      private function updateText3(param1:String) : void
      {
         if(this.mc3 != null)
         {
            this.mc3 = null;
            this.addMc(3);
         }
         this.mc3.x = this.initPos = 480;
         this.txt4.richText = param1;
         this.txt4.resizeTo(this.txt4.textField.textWidth + 5,40);
         this.txt4.autoAdjust();
         param1 = null;
      }
      
      private function updateText4(param1:String) : void
      {
         if(this.mc4 != null)
         {
            this.mc4 = null;
            this.addMc(4);
         }
         this.mc4.x = this.initPos = 480;
         this.txt5.richText = param1;
         this.txt5.resizeTo(this.txt5.textField.textWidth + 5,40);
         this.txt5.autoAdjust();
         param1 = null;
      }
      
      private function updateText5(param1:String) : void
      {
         if(this.mc5 != null)
         {
            this.mc5 = null;
            this.addMc(5);
         }
         this.mc5.x = this.initPos = 480;
         this.txt6.richText = param1;
         this.txt6.resizeTo(this.txt6.textField.textWidth + 5,40);
         this.txt6.autoAdjust();
         param1 = null;
      }
      
      private function updateText6(param1:String) : void
      {
         if(this.mc6 != null)
         {
            this.mc6 = null;
            this.addMc(6);
         }
         this.mc6.x = this.initPos = 480;
         this.txt7.richText = param1;
         this.txt7.resizeTo(this.txt7.textField.textWidth + 5,40);
         this.txt7.autoAdjust();
         param1 = null;
      }
      
      private function updateText7(param1:String) : void
      {
         if(this.mc7 != null)
         {
            this.mc7 = null;
            this.addMc(7);
         }
         this.mc7.x = this.initPos = 480;
         this.txt8.richText = param1;
         this.txt8.resizeTo(this.txt8.textField.textWidth + 5,40);
         this.txt8.autoAdjust();
         param1 = null;
      }
      
      private function addJudeTime() : void
      {
      }
      
      private function judeTimerHandler(param1:TimerEvent = null) : void
      {
         if(this.msgList.length == 0)
         {
            this.cleanJudeTimer();
         }
         if(this.running && this.running1 && this.running2 && this.running3)
         {
            if(this.mc.x + this.mc.width < this.xPos)
            {
               if(!this.running4)
               {
                  this.startMove4();
                  this.bg.visible = true;
               }
            }
            if(this.mc1.x + this.mc1.width < this.xPos)
            {
               if(!this.running5)
               {
                  this.startMove5();
                  this.bg.visible = true;
               }
            }
            if(this.mc2.x + this.mc2.width < this.xPos)
            {
               if(!this.running6)
               {
                  this.startMove6();
                  this.bg.visible = true;
               }
            }
            if(this.mc3.x + this.mc3.width < this.xPos)
            {
               if(!this.running7)
               {
                  this.startMove7();
                  this.bg.visible = true;
               }
            }
         }
      }
      
      public function addMsg(param1:String) : void
      {
         this.msgList.push(param1);
         if(!this.running)
         {
            this.startMove();
            this.bg.visible = true;
         }
         if(!this.running1)
         {
            this.startMove1();
            this.bg.visible = true;
         }
         if(!this.running2)
         {
            this.startMove2();
            this.bg.visible = true;
         }
         if(!this.running3)
         {
            this.startMove3();
            this.bg.visible = true;
         }
         if(ModuleManager.instance.chkModuleIsOpen("CrossServerShipFightModule"))
         {
            this.changeVisble(true);
         }
         else
         {
            this.changeVisble(false);
         }
      }
      
      public function changeVisble(param1:Boolean) : void
      {
         var _loc2_:int = 0;
         this.pane.visible = param1;
         if(!param1)
         {
            if(this.msgList.length != 0)
            {
               _loc2_ = this.msgList.length - 1;
               while(_loc2_ >= 0)
               {
                  this.msgList.pop();
                  _loc2_--;
               }
            }
         }
      }
      
      public function showAnnounceInfo(param1:Array) : void
      {
         if(param1.length == 0)
         {
            return;
         }
         this._board ||= new SpeakBoard();
         this._board.y = this.y + 32;
         this._board.setText(param1);
         this._board.x = (Core.stgW - this._board.width) / 2;
         this._board.parent || parent.addChild(this._board);
      }
      
      private function startMove() : void
      {
         if(this.msgList.length > 0)
         {
            this.updateText(this.msgList.shift());
            this.running = true;
            this.mcMove();
            if(this.msgList.length > 0)
            {
               this.addTimer(1);
            }
         }
      }
      
      private function mcMove() : void
      {
         TweenLite.killTweensOf(this.mc,true);
         var _loc1_:int = -(this.mc.width + Core.stgW);
         TweenLite.to(this.mc,this.durTime,{
            "delay":0,
            "ease":Linear.easeNone,
            "x":_loc1_,
            "y":this.mc.y,
            "onComplete":this._onCompleteProperty,
            "onCompleteParams":[this.mc]
         });
      }
      
      private function _onCompleteProperty(param1:Sprite) : void
      {
         this.running = false;
         this.bg.visible = false;
         this.txt1.clear();
         if(param1)
         {
            param1 = null;
         }
      }
      
      private function startMove1() : void
      {
         if(this.msgList.length > 0)
         {
            this.updateText1(this.msgList.shift());
            this.running1 = true;
            this.mcMove1();
         }
      }
      
      private function mcMove1() : void
      {
         TweenLite.killTweensOf(this.mc1,true);
         var _loc1_:int = -(this.mc1.width + Core.stgW);
         TweenLite.to(this.mc1,this.durTime,{
            "delay":4,
            "ease":Linear.easeNone,
            "x":_loc1_,
            "y":this.mc1.y,
            "onComplete":this._onCompleteProperty1,
            "onCompleteParams":[this.mc1]
         });
      }
      
      private function _onCompleteProperty1(param1:Sprite) : void
      {
         this.running1 = false;
         this.bg.visible = false;
         this.txt2.clear();
         if(param1)
         {
            param1 = null;
         }
      }
      
      private function startMove2() : void
      {
         if(this.msgList.length > 0)
         {
            this.updateText2(this.msgList.shift());
            this.running2 = true;
            this.mcMove2();
         }
      }
      
      private function mcMove2() : void
      {
         var _loc1_:int = -(this.mc2.width + Core.stgW);
         TweenLite.to(this.mc2,this.durTime,{
            "delay":2,
            "ease":Linear.easeNone,
            "x":_loc1_,
            "y":this.mc2.y,
            "onComplete":this._onCompleteProperty2,
            "onCompleteParams":[this.mc2]
         });
      }
      
      private function _onCompleteProperty2(param1:Sprite) : void
      {
         this.running2 = false;
         this.bg.visible = false;
         this.txt3.clear();
         TweenLite.killTweensOf(this.mc2,true);
         if(param1)
         {
            param1 = null;
         }
      }
      
      private function startMove3() : void
      {
         if(this.msgList.length > 0)
         {
            this.updateText3(this.msgList.shift());
            this.running3 = true;
            this.mcMove3();
         }
      }
      
      private function mcMove3() : void
      {
         var _loc1_:int = -(this.mc3.width + Core.stgW);
         TweenLite.to(this.mc3,this.durTime,{
            "delay":2,
            "ease":Linear.easeNone,
            "x":_loc1_,
            "y":this.mc3.y,
            "onComplete":this._onCompleteProperty3,
            "onCompleteParams":[this.mc3]
         });
      }
      
      private function _onCompleteProperty3(param1:Sprite) : void
      {
         this.running3 = false;
         this.bg.visible = false;
         this.txt4.clear();
         if(param1 && param1.parent)
         {
            param1.parent.removeChild(param1);
            param1 = null;
         }
      }
      
      private function startMove4() : void
      {
         if(this.msgList.length > 0)
         {
            this.updateText4(this.msgList.shift());
            this.running4 = true;
            this.mcMove4();
         }
      }
      
      private function mcMove4() : void
      {
         var _loc1_:int = -(this.mc4.width + Core.stgW);
         TweenLite.to(this.mc4,this.durTime,{
            "delay":2,
            "ease":Linear.easeNone,
            "x":_loc1_,
            "y":this.mc4.y,
            "onComplete":this._onCompleteProperty4,
            "onCompleteParams":[this.mc4]
         });
      }
      
      private function _onCompleteProperty4(param1:Sprite) : void
      {
         this.running4 = false;
         this.bg.visible = false;
         this.txt5.clear();
         if(param1)
         {
            param1 = null;
         }
      }
      
      private function startMove5() : void
      {
         if(this.msgList.length > 0)
         {
            this.updateText5(this.msgList.shift());
            this.running5 = true;
            this.mcMove5();
         }
      }
      
      private function mcMove5() : void
      {
         var _loc1_:int = -(this.mc5.width + Core.stgW);
         TweenLite.to(this.mc5,this.durTime,{
            "delay":2,
            "ease":Linear.easeNone,
            "x":_loc1_,
            "y":this.mc5.y,
            "onComplete":this._onCompleteProperty5,
            "onCompleteParams":[this.mc5]
         });
      }
      
      private function _onCompleteProperty5(param1:Sprite) : void
      {
         this.running5 = false;
         this.bg.visible = false;
         this.txt6.clear();
         if(param1)
         {
            param1 = null;
         }
      }
      
      private function startMove6() : void
      {
         if(this.msgList.length > 0)
         {
            this.updateText6(this.msgList.shift());
            this.running6 = true;
            this.mcMove6();
         }
      }
      
      private function mcMove6() : void
      {
         var _loc1_:int = -(this.mc6.width + Core.stgW);
         TweenLite.to(this.mc6,this.durTime,{
            "delay":2,
            "ease":Linear.easeNone,
            "x":_loc1_,
            "y":this.mc6.y,
            "onComplete":this._onCompleteProperty6,
            "onCompleteParams":[this.mc6]
         });
      }
      
      private function _onCompleteProperty6(param1:Sprite) : void
      {
         this.running6 = false;
         this.bg.visible = false;
         this.txt7.clear();
         if(param1)
         {
            param1 = null;
         }
      }
      
      private function startMove7() : void
      {
         if(this.msgList.length > 0)
         {
            this.updateText7(this.msgList.shift());
            this.running7 = true;
            this.mcMove7();
         }
      }
      
      private function mcMove7() : void
      {
         var _loc1_:int = -(this.mc7.width + Core.stgW);
         TweenLite.to(this.mc7,this.durTime,{
            "delay":2,
            "ease":Linear.easeNone,
            "x":_loc1_,
            "y":this.mc7.y,
            "onComplete":this._onCompleteProperty7,
            "onCompleteParams":[this.mc7]
         });
      }
      
      private function _onCompleteProperty7(param1:Sprite) : void
      {
         this.running7 = false;
         this.bg.visible = false;
         this.txt8.clear();
         if(param1 && param1.parent)
         {
            param1.parent.removeChild(param1);
            param1 = null;
         }
      }
      
      public function close() : void
      {
         this.dispose();
      }
      
      public function show(param1:Object) : void
      {
      }
      
      private function timeRemove() : void
      {
         this.clearTimer(1);
         this.cleanJudeTimer();
      }
      
      override public function dispose() : void
      {
         this.txt1.clear();
         this.txt2.clear();
         this.txt3.clear();
         this.txt4.clear();
         this.txt5.clear();
         this.txt6.clear();
         this.txt7.clear();
         this.txt8.clear();
         this.timeRemove();
         this.msgList = null;
         AppFacade.instance.removeMediator("chat.mvc.mediator.WorldBoatBoardMediator");
         AppFacade.instance.removeProxy("chat.mvc.proxy.WorldBoatBoardProxy");
         super.dispose();
      }
      
      private function get isHorn() : Boolean
      {
         return this.hornMediator.isHorn;
      }
      
      private function get hornMediator() : ChatHornMediator
      {
         return AppFacade.instance.retrieveMediator("chat.mvc.mediator.ChatHornMediator") as ChatHornMediator;
      }
   }
}

