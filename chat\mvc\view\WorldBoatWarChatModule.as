package chat.mvc.view
{
   import chat.event.WorldBoatMessageEvent;
   import chat.mvc.command.WorldBoatWarChatCommand;
   import chat.mvc.mediator.WorldBoatWarChatMediator;
   import chat.mvc.proxy.MessageSend;
   import chat.mvc.proxy.WorldBoatWarChatProxy;
   import flash.events.DataEvent;
   import flash.events.Event;
   import flash.events.KeyboardEvent;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import game.data.ModuleData;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.list.TileList;
   import mmo.ui.control.text.RichTextArea;
   import mmo.ui.control.window.PopUpWindow;
   import mmo.ui.event.ScrollBarEvent;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class WorldBoatWarChatModule extends PopUpWindow
   {
      public static const NAME:String = "chat.mvc.view.WorldBoatWarChatModule";
      
      public static const CLOSE_INPUT:String = "close_horn_input";
      
      private var inBg:UISkin;
      
      private var txtBg1:UISkin;
      
      private var txtBg2:UISkin;
      
      private var textbg1:UISkin;
      
      private var textbg2:UISkin;
      
      private var smileyBtn:ImgButton;
      
      private var sendBtn:Button;
      
      private var input:RichTextArea;
      
      private var label4:Label;
      
      private var ll_textnum:Label;
      
      private var smeilyBox:FacePanel;
      
      private var maxInputChar:int;
      
      private const faceNum:int = 3;
      
      private var sendCD:Boolean = false;
      
      private var chat_output:RichTextArea;
      
      private var _configXML:XML;
      
      private var scrollBar:ChatScrollBar_Y;
      
      public var userList:TileList;
      
      public var freeNum:int;
      
      public var freeLb:Label;
      
      public var goldCostLb:Label;
      
      public var goldSendNum:Label;
      
      private var closeButton:ImgButton;
      
      public var goldSkin:UISkin;
      
      public var chatGoldCost:int;
      
      public function WorldBoatWarChatModule()
      {
         super(300,215,UIManager.getUISkin("worldBoatResultBg"),false);
         this.allowDrag = false;
         this.closeButton = new ImgButton(UIManager.getMultiUISkin("btn_close"));
         addChild(this.closeButton);
         this.closeButton.x = 275;
         this.closeButton.y = 5;
         addChild(this.closeButton);
         this.isLive = true;
         this.initView();
         this.initData();
         this.closeButton.addEventListener("click",this.onClickHandler);
         AppFacade.instance.registerMediator(new WorldBoatWarChatMediator(this));
         AppFacade.instance.registerCommand("CS_WORLD_BOAT_TALKMSG",WorldBoatWarChatCommand);
         AppFacade.instance.registerCommand("CS_GET_WORLDBOAT_CHAT",WorldBoatWarChatCommand);
         AppFacade.instance.registerProxy(new WorldBoatWarChatProxy("chat.mvc.proxy.WorldBoatWarChatProxy"));
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
         this.close();
      }
      
      private function initView() : void
      {
         title = Globalization.getString("worldBoatChat.2");
         this.inBg = UIManager.getUISkin("worldBoatResultBlack");
         this.inBg.setSize(275,135);
         this.inBg.x = 5;
         this.inBg.y = -13;
         addChildToPane(this.inBg);
         this.txtBg1 = UIManager.getUISkin("black_bg3");
         this.txtBg1.setSize(275,135);
         this.txtBg1.x = 20;
         this.txtBg1.y = 50;
         this.smileyBtn = new ImgButton(UIManager.getMultiUISkin("HornSmeilyBtn"));
         this.smileyBtn.x = 5;
         this.smileyBtn.y = this.inBg.y + 143;
         this.txtBg2 = UIManager.getUISkin("pane_normal");
         this.txtBg2.setSize(205,25);
         this.txtBg2.x = 20;
         this.txtBg2.y = this.smileyBtn.y + 25;
         var _loc1_:UISkin = UIManager.getUISkin("worldBoatResultBlack");
         _loc1_.setSize(205,25);
         _loc1_.x = 27;
         _loc1_.y = this.inBg.y + 141;
         addChildToPane(_loc1_);
         this.ll_textnum = new Label("",TextFormatLib.format_0x00FF00_12px,[FilterLib.glow_0x272727]);
         this.ll_textnum.x = 20;
         this.ll_textnum.y = this.txtBg2.y + this.txtBg2.height + 5;
         this._configXML = new XML(XmlManager.getXml("face").face.copy());
         this.chat_output = new RichTextArea(275,135);
         this.chat_output.configXML = this._configXML;
         this.chat_output.x = this.inBg.x;
         this.chat_output.y = this.inBg.y;
         this.chat_output.textField.wordWrap = true;
         this.chat_output.textField.multiline = true;
         this.chat_output.textField.filters = [FilterLib.glow_0x272727];
         addChildToPane(this.chat_output);
         this.initScrollBar();
         this.input = new RichTextArea(205,25);
         this.input.x = 27;
         this.input.y = this.inBg.y + 143;
         addChildToPane(this.input);
         this.input.configXML = this._configXML;
         this.input.textField.wordWrap = true;
         this.input.textField.multiline = true;
         this.input.textField.useRichTextClipboard = false;
         this.input.textField.defaultTextFormat = TextFormatLib.white_12px;
         this.input.textField.type = "input";
         this.input.direction = "bottom";
         this.input.textField.addEventListener("keyUp",this.keyupHandler);
         this.input.textField.addEventListener("keyDown",this.keydownHandler);
         this.sendBtn = new Button(Globalization.getString("townteamfight.2"),TextFormatLib.format_0xFFB932_12px,50,UIManager.getMultiUISkin("btn_topMenu"));
         this.sendBtn.x = 233;
         this.sendBtn.y = this.input.y;
         this.sendBtn.setTextOffset(0,-1);
         addChildToPane(this.sendBtn);
         addChildToPane(this.smileyBtn);
         this.freeLb = new Label("",TextFormatLib.format_0xffed98_12px);
         this.freeLb.x = 80;
         this.freeLb.y = this.input.y + 60;
         addChild(this.freeLb);
         this.freeLb.visible = false;
         this.goldCostLb = new Label(Globalization.getString("worldBoatChat.3"),TextFormatLib.format_0xffed98_12px);
         this.goldCostLb.x = 80;
         this.goldCostLb.y = this.input.y + 60;
         addChild(this.goldCostLb);
         this.goldSkin = UIManager.getUISkin("gold");
         this.goldSkin.x = this.goldCostLb.x + 105;
         this.goldSkin.y = this.input.y + 60;
         addChild(this.goldSkin);
         this.goldSendNum = new Label("10",TextFormatLib.format_0xffed98_12px);
         this.goldSendNum.x = this.goldSkin.x + 18;
         this.goldSendNum.y = this.input.y + 60;
         addChild(this.goldSendNum);
         this.chatGoldCost = int(XmlManager.worldBoatWar.children().attribute("hornGold"));
         this.addEvent();
      }
      
      public function updateNum() : void
      {
         this.freeLb.text = StringUtil.substitute(Globalization.getString("worldBoatChat.1"),this.freeNum);
         this.goldSendNum.text = "" + this.chatGoldCost;
      }
      
      private function initScrollBar() : void
      {
         var _loc2_:Number = this.inBg.width;
         var _loc1_:Number = this.inBg.height;
         this.scrollBar = new ChatScrollBar_Y(this.chat_output.textField.height);
         this.scrollBar.x = 270;
         this.scrollBar.y = this.chat_output.y + 35;
         addChild(this.scrollBar);
         this.scrollBar.enabled = this.chat_output.textField.maxScrollV > 1;
         this.scrollBar.addEventListener(ScrollBarEvent.Progress_Update,this.progressUpdata);
         this.scrollBar.addEventListener("downClick",this.downCkickHandler);
         this.scrollBar.addEventListener("upClick",this.upClickhandler);
         this.chat_output.textField.addEventListener("scroll",this.textScroll);
      }
      
      private function progressUpdata(param1:Event) : void
      {
         this.chat_output.textField.removeEventListener("scroll",this.textScroll);
         this.chat_output.textField.scrollV = this.chat_output.textField.maxScrollV * this.scrollBar.progress;
         this.chat_output.textField.addEventListener("scroll",this.textScroll);
      }
      
      private function textScroll(param1:Event) : void
      {
         this.scrollBar.enabled = this.chat_output.textField.maxScrollV > 1;
         if(!this.scrollBar.enabled)
         {
            return;
         }
         this.scrollBar.resetScrollButtonSize(this.chat_output.textField.height,this.chat_output.textField.textHeight);
         if(this.chat_output.textField.scrollV == this.chat_output.textField.maxScrollV)
         {
            this.scrollBar.setProgress(1,false);
         }
         else
         {
            this.scrollBar.setProgress((this.chat_output.textField.scrollV - 1) / (this.chat_output.textField.maxScrollV - 1),false);
         }
      }
      
      private function upClickhandler(param1:Event) : void
      {
         param1.stopImmediatePropagation();
         var _loc2_:int = this.chat_output.textField.scrollV;
         if(_loc2_ > 1)
         {
            this.chat_output.textField.scrollV = _loc2_ - 1;
         }
      }
      
      private function downCkickHandler(param1:Event) : void
      {
         param1.stopImmediatePropagation();
         var _loc3_:int = this.chat_output.textField.scrollV;
         var _loc2_:int = this.chat_output.textField.maxScrollV;
         if(_loc3_ < _loc2_)
         {
            this.chat_output.textField.scrollV = _loc3_ + 1;
         }
      }
      
      private function initData() : void
      {
         this.maxInputChar = XmlManager.speaker.Speaker.@charCount;
         this.input.textField.maxChars = this.maxInputChar;
         var _loc1_:Array = <EMAIL>("|");
         this.setInputTextNum();
      }
      
      private function addEvent() : void
      {
         addEventListener("addedToStage",this.addToStageHandler);
         this.smileyBtn.addEventListener("click",this.smileyBtnHandler);
         this.sendBtn.addEventListener("click",this.sendHandler);
      }
      
      private function addToStageHandler(param1:Event) : void
      {
         removeEventListener("addedToStage",this.addToStageHandler);
         stage.focus = this.input.textField;
      }
      
      private function setInputTextNum() : void
      {
         var _loc3_:int = this.input.textField.length;
         var _loc2_:int = this.maxInputChar - _loc3_;
         var _loc1_:String = StringUtil.substitute(Globalization.getString("townteamfight.3"),_loc2_.toString());
         this.ll_textnum.setText(_loc1_);
      }
      
      private function judgeMessageSend() : Boolean
      {
         return true;
      }
      
      private function showTips(param1:String, param2:Point = null) : void
      {
         var _loc3_:Number = this.sendBtn.x + this.sendBtn.width / 2 - 14 * param1.length / 2;
         var _loc4_:Point = !!param2 ? param2 : localToGlobal(new Point(_loc3_,this.sendBtn.y + 15));
         AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
            "text":StringUtil.substitute(param1),
            "point":_loc4_
         });
      }
      
      private function showSmileyBox() : void
      {
         this.smeilyBox ||= new FacePanel();
         this.hideSmileyBox();
         this.smeilyBox.x = this.smileyBtn.x - 100;
         this.smeilyBox.y = this.smileyBtn.y - 190;
         this.smeilyBox.addEventListener("faceSelect",this.smileyBoxHandler);
         addChildToPane(this.smeilyBox);
      }
      
      private function hideSmileyBox() : void
      {
         if(this.smeilyBox)
         {
            this.smeilyBox.removeEventListener("faceSelect",this.smileyBoxHandler);
            if(pane.contains(this.smeilyBox))
            {
               removeChildToPane(this.smeilyBox);
            }
         }
      }
      
      private function keyupHandler(param1:KeyboardEvent) : void
      {
         var _loc2_:String = null;
         param1.stopImmediatePropagation();
         if(param1.keyCode == 13)
         {
            _loc2_ = this.input.text.replace("\r","");
            this.input.clear();
            this.input.appendRichText(_loc2_);
            if(this.input.text == "" || this.input.text == "\r")
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.1"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px
               });
               this.input.clear();
               return;
            }
            this.sendHandler(null);
         }
         else
         {
            this.setInputTextNum();
         }
      }
      
      private function keydownHandler(param1:KeyboardEvent) : void
      {
         param1.stopImmediatePropagation();
      }
      
      private function smileyBtnHandler(param1:MouseEvent) : void
      {
         if(this.smeilyBox && this.smeilyBox.parent)
         {
            this.hideSmileyBox();
         }
         else
         {
            this.showSmileyBox();
         }
      }
      
      private function sendHandler(param1:MouseEvent) : void
      {
         var _loc2_:MessageSend = null;
         if(this.judeSend())
         {
            if(this.input.text == "" || this.input.text == "\r")
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("chat.1"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px
               });
               return;
            }
            _loc2_ = new MessageSend();
            _loc2_.messageText = this.input.richText;
            this.input.clear();
            dispatchEvent(new WorldBoatMessageEvent("WORLD_BOAT_SENDMSG",_loc2_));
            this.setInputTextNum();
            return;
         }
         AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
            "text":Globalization.getString("worldBoatChat.4"),
            "textFormat":TextFormatLib.format_0xFF0000_12px
         });
         this.input.clear();
      }
      
      private function judeSend() : Boolean
      {
         var _loc1_:Boolean = false;
         if(this.freeNum > 0)
         {
            _loc1_ = true;
         }
         else if(ModuleData.crossServerShipFightGold >= this.chatGoldCost)
         {
            _loc1_ = true;
         }
         else
         {
            _loc1_ = false;
         }
         return _loc1_;
      }
      
      public function addMsg(param1:String) : void
      {
         this.chat_output.appendRichText(param1);
         this.chat_output.textField.scrollV = this.chat_output.textField.maxScrollV;
         this.scrollBar.setProgress(1,false);
      }
      
      public function clearMsg() : void
      {
         this.chat_output.clear();
      }
      
      private function smileyBoxHandler(param1:DataEvent) : void
      {
         var _loc2_:Point = null;
         this.hideSmileyBox();
         var _loc5_:int = this.input.textField.length;
         var _loc3_:int = this.input.text.length;
         var _loc4_:int = (_loc3_ - _loc5_) / 3;
         if(_loc5_ >= this.maxInputChar)
         {
            return;
         }
         if(_loc4_ >= 3)
         {
            _loc2_ = localToGlobal(new Point(this.smileyBtn.x - 20,this.smileyBtn.y + 15));
            this.showTips(StringUtil.substitute(Globalization.getString("townteamfight.4"),(3).toString()));
         }
         else
         {
            this.input.insertRichText(param1.data);
            this.setInputTextNum();
         }
         if(this.input.textField.textHeight > 30)
         {
            this.input.resizeTo(205,25);
            this.input.autoAdjust();
         }
      }
      
      override public function close() : void
      {
         this.clearMsg();
         super.close();
      }
      
      override public function dispose() : void
      {
         AppFacade.instance.removeMediator("chat.mvc.mediator.WorldBoatWarChatMediator");
         AppFacade.instance.removeCommand("CS_WORLD_BOAT_TALKMSG");
         AppFacade.instance.removeCommand("CS_GET_WORLDBOAT_CHAT");
         AppFacade.instance.removeProxy("chat.mvc.proxy.WorldBoatWarChatProxy");
         super.dispose();
      }
   }
}

