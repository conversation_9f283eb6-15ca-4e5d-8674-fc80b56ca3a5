package chat.mvc.view.mc
{
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.RadioButton;
   import mmo.ui.control.input.TextArea;
   import mmo.ui.control.label.Label;
   import mmo.utils.Guid;
   import util.Globalization;
   
   public class SendQuestionMC extends Sprite
   {
      public var selectedTypeIndex:int;
      
      public var radio_bug:DisplayObject;
      
      public var radio_complaints:DisplayObject;
      
      public var radio_advice:Sprite;
      
      public var radio_other:Sprite;
      
      public var txt_input:TextArea;
      
      public var txt_notice:Label;
      
      public var btn_cancel:Button;
      
      public var btn_ok:Button;
      
      public function SendQuestionMC()
      {
         var _loc1_:Label = null;
         super();
         var _loc4_:UISkin = UIManager.getUISkin("group_bg");
         _loc4_.setSize(350,256);
         addChild(_loc4_);
         var _loc3_:String = Guid.getUID();
         _loc1_ = new Label(Globalization.getString("chat.22"),TextFormatLib.format_0xfff5ce_12px,[FilterLib.glow_0x272727]);
         _loc1_.x = 20;
         _loc1_.y = 14;
         addChild(_loc1_);
         _loc1_ = new Label(Globalization.getString("chat.23"),TextFormatLib.format_0xfff5ce_12px,[FilterLib.glow_0x272727]);
         _loc1_.x = 20;
         _loc1_.y = 54;
         addChild(_loc1_);
         this.radio_bug = this.createRadio("1","BUG",_loc3_);
         this.radio_bug.x = 142;
         this.radio_bug.y = 14;
         addChild(this.radio_bug);
         this.radio_bug.addEventListener("click",this.changeRadioHandler);
         this.radio_complaints = this.createRadio("2",Globalization.getString("chat.14"),_loc3_);
         this.radio_complaints.x = 212;
         this.radio_complaints.y = 14;
         addChild(this.radio_complaints);
         this.radio_complaints.addEventListener("click",this.changeRadioHandler);
         this.radio_advice = this.createRadio("3",Globalization.getString("chat.15"),_loc3_);
         this.radio_advice.x = 282;
         this.radio_advice.y = 14;
         addChild(this.radio_advice);
         this.radio_advice.addEventListener("click",this.changeRadioHandler);
         this.radio_other = this.createRadio("4",Globalization.getString("chat.16"),_loc3_);
         this.radio_other.x = 72;
         this.radio_other.y = 14;
         addChild(this.radio_other);
         this.radio_other.addEventListener("click",this.changeRadioHandler);
         this.selectedTypeIndex = 4;
         _loc4_ = UIManager.getUISkin("split_line2");
         _loc4_.width = 338;
         _loc4_.x = 6;
         _loc4_.y = 42;
         addChild(_loc4_);
         var _loc2_:TextFormat = TextFormatLib.format_0xfff5ce_12px;
         _loc2_.leading = 3;
         this.txt_input = new TextArea("",288,162,_loc2_,UIManager.getUISkin("icon_bg"));
         _loc2_.leading = 0;
         this.txt_input.x = 56;
         this.txt_input.y = 48;
         addChild(this.txt_input);
         _loc4_ = UIManager.getUISkin("split_line2");
         _loc4_.width = 338;
         _loc4_.x = 6;
         _loc4_.y = 220;
         addChild(_loc4_);
         this.txt_notice = new Label(Globalization.getString("chat.24"),TextFormatLib.format_0xFFED89_12px,[FilterLib.glow_0x272727]);
         this.txt_notice.autoSize = "center";
         this.txt_notice.x = 15;
         this.txt_notice.y = 228;
         this.txt_notice.width = 314;
         addChild(this.txt_notice);
         this.btn_ok = new Button(Globalization.queding,TextFormatLib.format_0xFFB932_12px,64,UIManager.getMultiUISkin("btn_topMenu"));
         this.btn_ok.x = 78;
         this.btn_ok.y = 262;
         this.btn_ok.setTextOffset(0,-1);
         addChild(this.btn_ok);
         this.btn_cancel = new Button(Globalization.quxiao,TextFormatLib.format_0xFFB932_12px,64,UIManager.getMultiUISkin("btn_topMenu"));
         this.btn_cancel.x = 208;
         this.btn_cancel.y = 262;
         this.btn_cancel.setTextOffset(0,-1);
         addChild(this.btn_cancel);
      }
      
      private function createRadio(param1:String, param2:String, param3:String) : RadioButton
      {
         var _loc4_:RadioButton = new RadioButton(param2,TextFormatLib.format_0xFFB932_12px,param3,false,UIManager.getMultiUISkin("btn_radio2"));
         _loc4_.setTextOffset(28,2);
         _loc4_.name = param1;
         _loc4_.y = 2;
         addChild(_loc4_);
         _loc4_.dispatchEvent(new MouseEvent("click"));
         return _loc4_;
      }
      
      private function changeRadioHandler(param1:MouseEvent) : void
      {
         param1.stopImmediatePropagation();
         this.selectedTypeIndex = int(param1.currentTarget.name);
      }
   }
}

