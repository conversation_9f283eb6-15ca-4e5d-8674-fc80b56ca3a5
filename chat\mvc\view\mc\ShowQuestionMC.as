package chat.mvc.view.mc
{
   import flash.display.Sprite;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.scrollPane.ScrollPane;
   import util.Globalization;
   
   public class ShowQuestionMC extends Sprite
   {
      public var pane:ScrollPane;
      
      public var btn_close:Button;
      
      public var content:UISprite;
      
      public function ShowQuestionMC()
      {
         super();
         var _loc1_:UISkin = UIManager.getUISkin("group_bg");
         _loc1_.setSize(350,256);
         addChild(_loc1_);
         _loc1_ = UIManager.getUISkin("icon_bg");
         _loc1_.setSize(334,242);
         _loc1_.x = 9;
         _loc1_.y = 8;
         addChild(_loc1_);
         this.content = new UISprite();
         this.pane = new ScrollPane(334,242);
         this.pane.x = 10;
         this.pane.y = 8;
         this.pane.addToPane(this.content);
         addChild(this.pane);
         this.btn_close = new Button(Globalization.guanbi,TextFormatLib.format_0xFFB932_12px,64,UIManager.getMultiUISkin("btn_topMenu"));
         this.btn_close.x = 142;
         this.btn_close.y = 262;
         this.btn_close.setTextOffset(0,-1);
         addChild(this.btn_close);
      }
   }
}

