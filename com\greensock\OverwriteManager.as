package com.greensock
{
   import com.greensock.core.SimpleTimeline;
   import com.greensock.core.TweenCore;
   
   public class OverwriteManager
   {
      public static const version:Number = 6;
      
      public static const NONE:int = 0;
      
      public static const ALL_IMMEDIATE:int = 1;
      
      public static const AUTO:int = 2;
      
      public static const CONCURRENT:int = 3;
      
      public static const ALL_ONSTART:int = 4;
      
      public static const PREEXISTING:int = 5;
      
      public static var mode:int;
      
      public static var enabled:Boolean;
      
      public function OverwriteManager()
      {
         super();
      }
      
      public static function init(param1:int = 2) : int
      {
         TweenLite.overwriteManager = OverwriteManager;
         mode = param1;
         enabled = true;
         return mode;
      }
      
      public static function manageOverwrites(param1:TweenLite, param2:Object, param3:Array, param4:uint) : Boolean
      {
         var _loc14_:int = 0;
         var _loc15_:Boolean = false;
         var _loc18_:TweenLite = null;
         var _loc16_:uint = 0;
         var _loc17_:Number = NaN;
         var _loc6_:* = NaN;
         var _loc8_:TweenCore = null;
         var _loc7_:Number = NaN;
         var _loc10_:SimpleTimeline = null;
         if(param4 >= 4)
         {
            _loc16_ = param3.length;
            _loc14_ = 0;
            while(_loc14_ < _loc16_)
            {
               _loc18_ = param3[_loc14_];
               if(_loc18_ != param1)
               {
                  if(_loc18_.setEnabled(false,false))
                  {
                     _loc15_ = true;
                  }
               }
               else if(param4 == 5)
               {
                  break;
               }
               _loc14_++;
            }
            return _loc15_;
         }
         var _loc5_:Number = param1.startTime;
         var _loc12_:Array = [];
         var _loc13_:Array = [];
         var _loc11_:uint = 0;
         var _loc9_:uint = 0;
         _loc14_ = int(param3.length);
         while(_loc14_--)
         {
            _loc18_ = param3[_loc14_];
            if(!(_loc18_ == param1 || _loc18_.gc))
            {
               if(_loc18_.timeline != param1.timeline)
               {
                  if(!getGlobalPaused(_loc18_))
                  {
                     _loc13_[_loc11_++] = _loc18_;
                  }
               }
               else if(_loc18_.startTime <= _loc5_ && _loc18_.startTime + _loc18_.totalDuration > _loc5_ && !getGlobalPaused(_loc18_))
               {
                  _loc12_[_loc9_++] = _loc18_;
               }
            }
         }
         if(_loc11_ != 0)
         {
            _loc17_ = param1.cachedTimeScale;
            _loc6_ = _loc5_;
            _loc10_ = param1.timeline;
            while(_loc10_)
            {
               _loc17_ *= _loc10_.cachedTimeScale;
               _loc6_ += _loc10_.startTime;
               _loc10_ = _loc10_.timeline;
            }
            _loc5_ = _loc17_ * _loc6_;
            _loc14_ = int(_loc11_);
            while(_loc14_--)
            {
               _loc8_ = _loc13_[_loc14_];
               _loc17_ = _loc8_.cachedTimeScale;
               _loc6_ = _loc8_.startTime;
               _loc10_ = _loc8_.timeline;
               while(_loc10_)
               {
                  _loc17_ *= _loc10_.cachedTimeScale;
                  _loc6_ += _loc10_.startTime;
                  _loc10_ = _loc10_.timeline;
               }
               _loc7_ = _loc17_ * _loc6_;
               if(_loc7_ <= _loc5_ && (_loc7_ + _loc8_.totalDuration * _loc17_ > _loc5_ || _loc8_.cachedDuration == 0))
               {
                  _loc12_[_loc9_++] = _loc8_;
               }
            }
         }
         if(_loc9_ == 0)
         {
            return _loc15_;
         }
         _loc14_ = int(_loc9_);
         if(param4 == 2)
         {
            while(_loc14_--)
            {
               _loc18_ = _loc12_[_loc14_];
               if(_loc18_.killVars(param2))
               {
                  _loc15_ = true;
               }
               if(_loc18_.cachedPT1 == null && _loc18_.initted)
               {
                  _loc18_.setEnabled(false,false);
               }
            }
         }
         else
         {
            while(_loc14_--)
            {
               if(TweenLite(_loc12_[_loc14_]).setEnabled(false,false))
               {
                  _loc15_ = true;
               }
            }
         }
         return _loc15_;
      }
      
      public static function getGlobalPaused(param1:TweenCore) : Boolean
      {
         while(param1)
         {
            if(param1.cachedPaused)
            {
               return true;
            }
            param1 = param1.timeline;
         }
         return false;
      }
   }
}

