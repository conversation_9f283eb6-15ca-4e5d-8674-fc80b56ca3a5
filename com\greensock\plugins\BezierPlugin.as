package com.greensock.plugins
{
   import com.greensock.TweenLite;
   
   public class BezierPlugin extends TweenPlugin
   {
      public static const API:Number = 1;
      
      protected static const _RAD2DEG:Number = 57.29577951308232;
      
      protected var _target:Object;
      
      protected var _orientData:Array;
      
      protected var _orient:Boolean;
      
      protected var _future:Object;
      
      protected var _beziers:Object;
      
      public function BezierPlugin()
      {
         super();
         this._future = {};
         this.propName = "bezier";
         this.overwriteProps = [];
      }
      
      public static function parseBeziers(param1:Object, param2:Boolean = false) : Object
      {
         var _loc6_:int = 0;
         var _loc7_:Array = null;
         var _loc3_:Object = null;
         var _loc4_:* = null;
         var _loc5_:Object = {};
         if(param2)
         {
            for(_loc4_ in param1)
            {
               _loc7_ = param1[_loc4_];
               _loc5_[_loc4_] = _loc3_ = [];
               if(_loc7_.length > 2)
               {
                  _loc3_[_loc3_.length] = [_loc7_[0],_loc7_[1] - (_loc7_[2] - _loc7_[0]) / 4,_loc7_[1]];
                  _loc6_ = 1;
                  while(_loc6_ < _loc7_.length - 1)
                  {
                     _loc3_[_loc3_.length] = [_loc7_[_loc6_],_loc7_[_loc6_] + (_loc7_[_loc6_] - _loc3_[_loc6_ - 1][1]),_loc7_[_loc6_ + 1]];
                     _loc6_++;
                  }
               }
               else
               {
                  _loc3_[_loc3_.length] = [_loc7_[0],(_loc7_[0] + _loc7_[1]) / 2,_loc7_[1]];
               }
            }
         }
         else
         {
            for(_loc4_ in param1)
            {
               _loc7_ = param1[_loc4_];
               _loc5_[_loc4_] = _loc3_ = [];
               if(_loc7_.length > 3)
               {
                  _loc3_[_loc3_.length] = [_loc7_[0],_loc7_[1],(_loc7_[1] + _loc7_[2]) / 2];
                  _loc6_ = 2;
                  while(_loc6_ < _loc7_.length - 2)
                  {
                     _loc3_[_loc3_.length] = [_loc3_[_loc6_ - 2][2],_loc7_[_loc6_],(_loc7_[_loc6_] + _loc7_[_loc6_ + 1]) / 2];
                     _loc6_++;
                  }
                  _loc3_[_loc3_.length] = [_loc3_[_loc3_.length - 1][2],_loc7_[_loc7_.length - 2],_loc7_[_loc7_.length - 1]];
               }
               else if(_loc7_.length == 3)
               {
                  _loc3_[_loc3_.length] = [_loc7_[0],_loc7_[1],_loc7_[2]];
               }
               else if(_loc7_.length == 2)
               {
                  _loc3_[_loc3_.length] = [_loc7_[0],(_loc7_[0] + _loc7_[1]) / 2,_loc7_[1]];
               }
            }
         }
         return _loc5_;
      }
      
      override public function onInitTween(param1:Object, param2:*, param3:TweenLite) : Boolean
      {
         if(!(param2 is Array))
         {
            return false;
         }
         this.init(param3,param2 as Array,false);
         return true;
      }
      
      protected function init(param1:TweenLite, param2:Array, param3:Boolean) : void
      {
         var _loc4_:* = undefined;
         var _loc6_:Object = null;
         this._target = param1.target;
         var _loc8_:Object = param1.vars.isTV == true ? param1.vars.exposedVars : param1.vars;
         if(_loc8_.orientToBezier == true)
         {
            this._orientData = [["x","y","rotation",0,0.01]];
            this._orient = true;
         }
         else if(_loc8_.orientToBezier is Array)
         {
            this._orientData = _loc8_.orientToBezier;
            this._orient = true;
         }
         var _loc5_:Object = {};
         var _loc7_:int = 0;
         while(_loc7_ < param2.length)
         {
            for(_loc4_ in param2[_loc7_])
            {
               if(_loc5_[_loc4_] == undefined)
               {
                  _loc5_[_loc4_] = [param1.target[_loc4_]];
               }
               if(typeof param2[_loc7_][_loc4_] == "number")
               {
                  _loc5_[_loc4_].push(param2[_loc7_][_loc4_]);
               }
               else
               {
                  _loc5_[_loc4_].push(param1.target[_loc4_] + Number(param2[_loc7_][_loc4_]));
               }
            }
            _loc7_++;
         }
         for(this.overwriteProps[this.overwriteProps.length] in _loc5_)
         {
            if(_loc8_[_loc4_] != undefined)
            {
               if(typeof _loc8_[_loc4_] == "number")
               {
                  _loc5_[_loc4_].push(_loc8_[_loc4_]);
               }
               else
               {
                  _loc5_[_loc4_].push(param1.target[_loc4_] + Number(_loc8_[_loc4_]));
               }
               _loc6_ = {};
               _loc6_[_loc4_] = true;
               param1.killVars(_loc6_,false);
               delete _loc8_[_loc4_];
            }
         }
         this._beziers = parseBeziers(_loc5_,param3);
      }
      
      override public function killProps(param1:Object) : void
      {
         var _loc2_:* = undefined;
         for(_loc2_ in this._beziers)
         {
            if(_loc2_ in param1)
            {
               delete this._beziers[_loc2_];
            }
         }
         super.killProps(param1);
      }
      
      override public function set changeFactor(param1:Number) : void
      {
         var _loc12_:uint = 0;
         var _loc13_:Number = NaN;
         var _loc3_:Object = null;
         var _loc5_:Number = NaN;
         var _loc4_:Object = null;
         var _loc6_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc9_:int = 0;
         var _loc7_:* = null;
         var _loc8_:Array = null;
         var _loc10_:Number = NaN;
         var _loc11_:Object = null;
         var _loc14_:Boolean = false;
         if(param1 == 1)
         {
            for(_loc7_ in this._beziers)
            {
               _loc9_ = this._beziers[_loc7_].length - 1;
               this._target[_loc7_] = this._beziers[_loc7_][_loc9_][2];
            }
         }
         else
         {
            for(_loc7_ in this._beziers)
            {
               _loc12_ = uint(this._beziers[_loc7_].length);
               if(param1 < 0)
               {
                  _loc9_ = 0;
               }
               else if(param1 >= 1)
               {
                  _loc9_ = _loc12_ - 1;
               }
               else
               {
                  _loc9_ = _loc12_ * param1;
               }
               _loc13_ = (param1 - _loc9_ * (1 / _loc12_)) * _loc12_;
               _loc3_ = this._beziers[_loc7_][_loc9_];
               if(this.round)
               {
                  _loc5_ = _loc3_[0] + _loc13_ * (2 * (1 - _loc13_) * (_loc3_[1] - _loc3_[0]) + _loc13_ * (_loc3_[2] - _loc3_[0]));
                  this._target[_loc7_] = _loc5_ > 0 ? int(_loc5_ + 0.5) : int(_loc5_ - 0.5);
               }
               else
               {
                  this._target[_loc7_] = _loc3_[0] + _loc13_ * (2 * (1 - _loc13_) * (_loc3_[1] - _loc3_[0]) + _loc13_ * (_loc3_[2] - _loc3_[0]));
               }
            }
         }
         if(this._orient)
         {
            _loc9_ = int(this._orientData.length);
            _loc4_ = {};
            while(_loc9_--)
            {
               _loc8_ = this._orientData[_loc9_];
               _loc4_[_loc8_[0]] = this._target[_loc8_[0]];
               _loc4_[_loc8_[1]] = this._target[_loc8_[1]];
            }
            _loc11_ = this._target;
            _loc14_ = this.round;
            this._target = this._future;
            this.round = false;
            this._orient = false;
            _loc9_ = int(this._orientData.length);
            while(_loc9_--)
            {
               _loc8_ = this._orientData[_loc9_];
               this.changeFactor = param1 + (_loc8_[4] || 0.01);
               _loc10_ = Number(Number(_loc8_[3]) || 0);
               _loc6_ = this._future[_loc8_[0]] - _loc4_[_loc8_[0]];
               _loc2_ = this._future[_loc8_[1]] - _loc4_[_loc8_[1]];
               _loc11_[_loc8_[2]] = Math.atan2(_loc2_,_loc6_) * 57.29577951308232 + _loc10_;
            }
            this._target = _loc11_;
            this.round = _loc14_;
            this._orient = true;
         }
      }
   }
}

