package com.greensock.plugins
{
   import com.greensock.core.PropTween;
   import flash.filters.BitmapFilter;
   
   public class FilterPlugin extends TweenPlugin
   {
      public static const VERSION:Number = 2.03;
      
      public static const API:Number = 1;
      
      protected var _target:Object;
      
      protected var _type:Class;
      
      protected var _filter:BitmapFilter;
      
      protected var _index:int;
      
      protected var _remove:Boolean;
      
      public function FilterPlugin()
      {
         super();
      }
      
      protected function initFilter(param1:Object, param2:BitmapFilter, param3:Array) : void
      {
         var _loc8_:String = null;
         var _loc4_:int = 0;
         var _loc5_:HexColorsPlugin = null;
         var _loc7_:Array = this._target.filters;
         var _loc6_:Object = param1 is BitmapFilter ? {} : param1;
         this._index = -1;
         if(_loc6_.index != null)
         {
            this._index = _loc6_.index;
         }
         else
         {
            _loc4_ = int(_loc7_.length);
            while(_loc4_--)
            {
               if(_loc7_[_loc4_] is this._type)
               {
                  this._index = _loc4_;
                  break;
               }
            }
         }
         if(this._index == -1 || _loc7_[this._index] == null || _loc6_.addFilter == true)
         {
            this._index = _loc6_.index != null ? int(_loc6_.index) : int(_loc7_.length);
            _loc7_[this._index] = param2;
            this._target.filters = _loc7_;
         }
         this._filter = _loc7_[this._index];
         if(_loc6_.remove == true)
         {
            this._remove = true;
            this.onComplete = this.onCompleteTween;
         }
         _loc4_ = int(param3.length);
         while(_loc4_--)
         {
            _loc8_ = param3[_loc4_];
            if(_loc8_ in param1 && this._filter[_loc8_] != param1[_loc8_])
            {
               if(_loc8_ == "color" || _loc8_ == "highlightColor" || _loc8_ == "shadowColor")
               {
                  _loc5_ = new HexColorsPlugin();
                  _loc5_.initColor(this._filter,_loc8_,this._filter[_loc8_],param1[_loc8_]);
                  _tweens[_tweens.length] = new PropTween(_loc5_,"changeFactor",0,1,_loc8_,false);
               }
               else if(_loc8_ == "quality" || _loc8_ == "inner" || _loc8_ == "knockout" || _loc8_ == "hideObject")
               {
                  this._filter[_loc8_] = param1[_loc8_];
               }
               else
               {
                  addTween(this._filter,_loc8_,this._filter[_loc8_],param1[_loc8_],_loc8_);
               }
            }
         }
      }
      
      public function onCompleteTween() : void
      {
         var _loc2_:Array = null;
         var _loc1_:int = 0;
         if(this._remove)
         {
            _loc2_ = this._target.filters;
            if(!(_loc2_[this._index] is this._type))
            {
               _loc1_ = int(_loc2_.length);
               while(_loc1_--)
               {
                  if(_loc2_[_loc1_] is this._type)
                  {
                     _loc2_.splice(_loc1_,1);
                     break;
                  }
               }
            }
            else
            {
               _loc2_.splice(this._index,1);
            }
            this._target.filters = _loc2_;
         }
      }
      
      override public function set changeFactor(param1:Number) : void
      {
         var _loc3_:PropTween = null;
         var _loc4_:* = int(_tweens.length);
         var _loc2_:Array = this._target.filters;
         while(_loc4_--)
         {
            _loc3_ = _tweens[_loc4_];
            _loc3_.target[_loc3_.property] = _loc3_.start + _loc3_.change * param1;
         }
         if(!(_loc2_[this._index] is this._type))
         {
            _loc4_ = this._index = _loc2_.length;
            while(_loc4_--)
            {
               if(_loc2_[_loc4_] is this._type)
               {
                  this._index = _loc4_;
                  break;
               }
            }
         }
         _loc2_[this._index] = this._filter;
         this._target.filters = _loc2_;
      }
   }
}

