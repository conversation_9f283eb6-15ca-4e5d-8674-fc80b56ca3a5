package com.hurlant.crypto.hash
{
   import flash.utils.ByteArray;
   
   public class HMAC
   {
      private var hash:IHash;
      
      private var bits:uint;
      
      public function HMAC(param1:IHash, param2:uint = 0)
      {
         super();
         this.hash = param1;
         this.bits = param2;
      }
      
      public function getHashSize() : uint
      {
         if(this.bits != 0)
         {
            return this.bits / 8;
         }
         return this.hash.getHashSize();
      }
      
      public function compute(param1:ByteArray, param2:ByteArray) : ByteArray
      {
         var _loc7_:ByteArray = null;
         if(param1.length > this.hash.getInputSize())
         {
            _loc7_ = this.hash.hash(param1);
         }
         else
         {
            _loc7_ = new ByteArray();
            _loc7_.writeBytes(param1);
         }
         while(_loc7_.length < this.hash.getInputSize())
         {
            _loc7_[_loc7_.length] = 0;
         }
         var _loc8_:ByteArray = new ByteArray();
         var _loc3_:ByteArray = new ByteArray();
         var _loc4_:uint = 0;
         while(_loc4_ < _loc7_.length)
         {
            _loc8_[_loc4_] = _loc7_[_loc4_] ^ 0x36;
            _loc3_[_loc4_] = _loc7_[_loc4_] ^ 0x5C;
            _loc4_++;
         }
         _loc8_.position = _loc7_.length;
         _loc8_.writeBytes(param2);
         var _loc6_:ByteArray = this.hash.hash(_loc8_);
         _loc3_.position = _loc7_.length;
         _loc3_.writeBytes(_loc6_);
         var _loc5_:ByteArray = this.hash.hash(_loc3_);
         if(this.bits > 0 && this.bits < 8 * _loc5_.length)
         {
            _loc5_.length = this.bits / 8;
         }
         return _loc5_;
      }
      
      public function dispose() : void
      {
         this.hash = null;
         this.bits = 0;
      }
      
      public function toString() : String
      {
         return "hmac-" + (this.bits > 0 ? this.bits + "-" : "") + this.hash.toString();
      }
   }
}

