package com.hurlant.crypto.hash
{
   public class SHA1 extends SH<PERSON>ase implements IHash
   {
      public static const HASH_SIZE:int = 20;
      
      public function SHA1()
      {
         super();
      }
      
      override public function getHashSize() : uint
      {
         return 20;
      }
      
      override protected function core(param1:Array, param2:uint) : Array
      {
         var _loc8_:* = 0;
         var _loc9_:* = 0;
         var _loc12_:* = 0;
         var _loc13_:* = 0;
         var _loc16_:* = 0;
         var _loc14_:uint = 0;
         var _loc15_:uint = 0;
         param1[param2 >> 5] |= 128 << 24 - param2 % 32;
         param1[(param2 + 64 >> 9 << 4) + 15] = param2;
         var _loc4_:Array = [];
         var _loc6_:* = 1732584193;
         var _loc5_:* = 4023233417;
         var _loc7_:* = 2562383102;
         var _loc3_:* = 271733878;
         var _loc10_:* = 3285377520;
         var _loc11_:uint = 0;
         while(_loc11_ < param1.length)
         {
            _loc8_ = _loc6_;
            _loc9_ = _loc5_;
            _loc12_ = _loc7_;
            _loc13_ = _loc3_;
            _loc16_ = _loc10_;
            _loc14_ = 0;
            while(_loc14_ < 80)
            {
               if(_loc14_ < 16)
               {
                  _loc4_[_loc14_] = param1[_loc11_ + _loc14_] || 0;
               }
               else
               {
                  _loc4_[_loc14_] = this.rol(_loc4_[_loc14_ - 3] ^ _loc4_[_loc14_ - 8] ^ _loc4_[_loc14_ - 14] ^ _loc4_[_loc14_ - 16],1);
               }
               _loc15_ = this.rol(_loc6_,5) + this.ft(_loc14_,_loc5_,_loc7_,_loc3_) + _loc10_ + _loc4_[_loc14_] + this.kt(_loc14_);
               _loc10_ = _loc3_;
               _loc3_ = _loc7_;
               _loc7_ = this.rol(_loc5_,30);
               _loc5_ = _loc6_;
               _loc6_ = _loc15_;
               _loc14_++;
            }
            _loc6_ += _loc8_;
            _loc5_ += _loc9_;
            _loc7_ += _loc12_;
            _loc3_ += _loc13_;
            _loc10_ += _loc16_;
            _loc11_ += 16;
         }
         return [_loc6_,_loc5_,_loc7_,_loc3_,_loc10_];
      }
      
      private function rol(param1:uint, param2:uint) : uint
      {
         return param1 << param2 | param1 >>> 32 - param2;
      }
      
      private function ft(param1:uint, param2:uint, param3:uint, param4:uint) : uint
      {
         if(param1 < 20)
         {
            return param2 & param3 | ~param2 & param4;
         }
         if(param1 < 40)
         {
            return param2 ^ param3 ^ param4;
         }
         if(param1 < 60)
         {
            return param2 & param3 | param2 & param4 | param3 & param4;
         }
         return param2 ^ param3 ^ param4;
      }
      
      private function kt(param1:uint) : uint
      {
         return param1 < 20 ? 1518500249 : (uint(param1 < 40 ? 1859775393 : (uint(param1 < 60 ? 2400959708 : 3395469782))));
      }
      
      override public function toString() : String
      {
         return "sha1";
      }
   }
}

