package com.hurlant.crypto.hash
{
   import flash.utils.ByteArray;
   
   public class SHABase implements IHash
   {
      public function SHABase()
      {
         super();
      }
      
      public function getInputSize() : uint
      {
         return 64;
      }
      
      public function getHashSize() : uint
      {
         return 0;
      }
      
      public function hash(param1:ByteArray) : ByteArray
      {
         var _loc9_:uint = param1.length;
         var _loc7_:String = param1.endian;
         param1.endian = "bigEndian";
         var _loc8_:uint = _loc9_ * 8;
         while(param1.length % 4 != 0)
         {
            param1[param1.length] = 0;
         }
         param1.position = 0;
         var _loc2_:Array = [];
         var _loc3_:uint = 0;
         while(_loc3_ < param1.length)
         {
            _loc2_.push(param1.readUnsignedInt());
            _loc3_ += 4;
         }
         var _loc6_:Array = this.core(_loc2_,_loc8_);
         var _loc5_:ByteArray = new ByteArray();
         var _loc4_:uint = this.getHashSize() / 4;
         _loc3_ = 0;
         while(_loc3_ < _loc4_)
         {
            _loc5_.writeUnsignedInt(_loc6_[_loc3_]);
            _loc3_++;
         }
         param1.length = _loc9_;
         param1.endian = _loc7_;
         return _loc5_;
      }
      
      protected function core(param1:Array, param2:uint) : Array
      {
         return null;
      }
      
      public function toString() : String
      {
         return "sha";
      }
   }
}

