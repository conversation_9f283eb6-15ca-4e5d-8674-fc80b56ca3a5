package com.hurlant.crypto.rsa
{
   import com.hurlant.crypto.prng.Random;
   import com.hurlant.math.BigInteger;
   import com.hurlant.util.Memory;
   import flash.utils.ByteArray;
   
   public class RSAKey
   {
      public var e:int;
      
      public var n:BigInteger;
      
      public var d:BigInteger;
      
      public var p:BigInteger;
      
      public var q:BigInteger;
      
      public var dmp1:BigInteger;
      
      public var dmq1:BigInteger;
      
      public var coeff:BigInteger;
      
      protected var canDecrypt:Boolean;
      
      protected var canEncrypt:Boolean;
      
      public function RSAKey(param1:BigInteger, param2:int, param3:BigInteger = null, param4:BigInteger = null, param5:BigInteger = null, param6:BigInteger = null, param7:BigInteger = null, param8:BigInteger = null)
      {
         super();
         this.n = param1;
         this.e = param2;
         this.d = param3;
         this.p = param4;
         this.q = param5;
         this.dmp1 = param6;
         this.dmq1 = param7;
         this.coeff = param8;
         this.canEncrypt = this.n != null && this.e != 0;
         this.canDecrypt = this.canEncrypt && this.d != null;
      }
      
      public static function parsePublicKey(param1:String, param2:String) : RSAKey
      {
         return new RSAKey(new BigInteger(param1,16),parseInt(param2,16));
      }
      
      public static function parsePrivateKey(param1:String, param2:String, param3:String, param4:String = null, param5:String = null, param6:String = null, param7:String = null, param8:String = null) : RSAKey
      {
         if(param4 == null)
         {
            return new RSAKey(new BigInteger(param1,16),parseInt(param2,16),new BigInteger(param3,16));
         }
         return new RSAKey(new BigInteger(param1,16),parseInt(param2,16),new BigInteger(param3,16),new BigInteger(param4,16),new BigInteger(param5,16),new BigInteger(param6,16),new BigInteger(param7),new BigInteger(param8));
      }
      
      public static function generate(param1:uint, param2:String) : RSAKey
      {
         var _loc7_:BigInteger = null;
         var _loc6_:BigInteger = null;
         var _loc5_:BigInteger = null;
         var _loc9_:BigInteger = null;
         var _loc10_:Random = new Random();
         var _loc3_:uint = uint(param1 >> 1);
         var _loc4_:RSAKey = new RSAKey(null,0,null);
         _loc4_.e = parseInt(param2,16);
         var _loc8_:BigInteger = new BigInteger(param2,16);
         do
         {
            do
            {
               _loc4_.p = bigRandom(param1 - _loc3_,_loc10_);
            }
            while(!(_loc4_.p.subtract(BigInteger.ONE).gcd(_loc8_).compareTo(BigInteger.ONE) == 0 && _loc4_.p.isProbablePrime(10)));
            
            do
            {
               _loc4_.q = bigRandom(_loc3_,_loc10_);
            }
            while(!(_loc4_.q.subtract(BigInteger.ONE).gcd(_loc8_).compareTo(BigInteger.ONE) == 0 && _loc4_.q.isProbablePrime(10)));
            
            if(_loc4_.p.compareTo(_loc4_.q) <= 0)
            {
               _loc9_ = _loc4_.p;
               _loc4_.p = _loc4_.q;
               _loc4_.q = _loc9_;
            }
            _loc7_ = _loc4_.p.subtract(BigInteger.ONE);
            _loc6_ = _loc4_.q.subtract(BigInteger.ONE);
         }
         while(_loc5_ = _loc7_.multiply(_loc6_), _loc5_.gcd(_loc8_).compareTo(BigInteger.ONE) != 0);
         
         _loc4_.n = _loc4_.p.multiply(_loc4_.q);
         _loc4_.d = _loc8_.modInverse(_loc5_);
         _loc4_.dmp1 = _loc4_.d.mod(_loc7_);
         _loc4_.dmq1 = _loc4_.d.mod(_loc6_);
         _loc4_.coeff = _loc4_.q.modInverse(_loc4_.p);
         return _loc4_;
      }
      
      protected static function bigRandom(param1:int, param2:Random) : BigInteger
      {
         if(param1 < 2)
         {
            return BigInteger.nbv(1);
         }
         var _loc3_:ByteArray = new ByteArray();
         param2.nextBytes(_loc3_,param1 >> 3);
         _loc3_.position = 0;
         var _loc4_:BigInteger = new BigInteger(_loc3_);
         _loc4_.primify(param1,1);
         return _loc4_;
      }
      
      public function getBlockSize() : uint
      {
         return (this.n.bitLength() + 7) / 8;
      }
      
      public function dispose() : void
      {
         this.e = 0;
         this.n.dispose();
         this.n = null;
         Memory.gc();
      }
      
      public function encrypt(param1:ByteArray, param2:ByteArray, param3:uint, param4:Function = null) : void
      {
         this._encrypt(this.doPublic,param1,param2,param3,param4,2);
      }
      
      public function decrypt(param1:ByteArray, param2:ByteArray, param3:uint, param4:Function = null) : void
      {
         this._decrypt(this.doPrivate2,param1,param2,param3,param4,2);
      }
      
      public function sign(param1:ByteArray, param2:ByteArray, param3:uint, param4:Function = null) : void
      {
         this._encrypt(this.doPrivate2,param1,param2,param3,param4,1);
      }
      
      public function verify(param1:ByteArray, param2:ByteArray, param3:uint, param4:Function = null) : void
      {
         this._decrypt(this.doPublic,param1,param2,param3,param4,1);
      }
      
      private function _encrypt(param1:Function, param2:ByteArray, param3:ByteArray, param4:uint, param5:Function, param6:int) : void
      {
         var _loc10_:BigInteger = null;
         var _loc9_:BigInteger = null;
         if(param5 == null)
         {
            param5 = this.pkcs1pad;
         }
         if(param2.position >= param2.length)
         {
            param2.position = 0;
         }
         var _loc8_:uint = this.getBlockSize();
         var _loc7_:int = int(param2.position + param4);
         while(param2.position < _loc7_)
         {
            _loc10_ = new BigInteger(param5(param2,_loc7_,_loc8_,param6),_loc8_);
            _loc9_ = param1(_loc10_);
            _loc9_.toArray(param3);
         }
      }
      
      private function _decrypt(param1:Function, param2:ByteArray, param3:ByteArray, param4:uint, param5:Function, param6:int) : void
      {
         var _loc11_:BigInteger = null;
         var _loc10_:BigInteger = null;
         var _loc9_:ByteArray = null;
         if(param5 == null)
         {
            param5 = this.pkcs1unpad;
         }
         if(param2.position >= param2.length)
         {
            param2.position = 0;
         }
         var _loc7_:uint = this.getBlockSize();
         var _loc8_:int = int(param2.position + param4);
         while(param2.position < _loc8_)
         {
            _loc11_ = new BigInteger(param2,param4);
            _loc10_ = param1(_loc11_);
            _loc9_ = param5(_loc10_,_loc7_);
            param3.writeBytes(_loc9_);
         }
      }
      
      private function pkcs1pad(param1:ByteArray, param2:int, param3:uint, param4:uint = 2) : ByteArray
      {
         var _loc5_:int = 0;
         var _loc7_:ByteArray = new ByteArray();
         var _loc10_:uint = param1.position;
         param2 = Math.min(param2,param1.length,_loc10_ + param3 - 11);
         param1.position = param2;
         var _loc9_:int = param2 - 1;
         while(_loc9_ >= _loc10_ && param3 > 11)
         {
            _loc7_[--param3] = param1[_loc9_--];
         }
         _loc7_[--param3] = 0;
         var _loc8_:Random = new Random();
         while(param3 > 2)
         {
            _loc5_ = 0;
            while(_loc5_ == 0)
            {
               _loc5_ = param4 == 2 ? _loc8_.nextByte() : 255;
            }
            _loc7_[--param3] = _loc5_;
         }
         _loc7_[--param3] = param4;
         var _loc6_:* = --param3;
         _loc7_[_loc6_] = 0;
         return _loc7_;
      }
      
      private function pkcs1unpad(param1:BigInteger, param2:uint, param3:uint = 2) : ByteArray
      {
         var _loc6_:ByteArray = param1.toByteArray();
         var _loc4_:ByteArray = new ByteArray();
         var _loc5_:int = 0;
         while(_loc5_ < _loc6_.length && _loc6_[_loc5_] == 0)
         {
            _loc5_++;
         }
         if(_loc6_.length - _loc5_ != param2 - 1 || _loc6_[_loc5_] > 2)
         {
            return null;
         }
         _loc5_++;
         while(_loc6_[_loc5_] != 0)
         {
            _loc5_++;
            if(_loc5_ >= _loc6_.length)
            {
               return null;
            }
         }
         while(true)
         {
            _loc5_++;
            if(_loc5_ >= _loc6_.length)
            {
               break;
            }
            _loc4_.writeByte(_loc6_[_loc5_]);
         }
         _loc4_.position = 0;
         return _loc4_;
      }
      
      private function rawpad(param1:ByteArray, param2:int, param3:uint) : ByteArray
      {
         return param1;
      }
      
      public function toString() : String
      {
         return "rsa";
      }
      
      public function dump() : String
      {
         var _loc1_:* = "N=" + this.n.toString(16) + "\n" + "E=" + this.e.toString(16) + "\n";
         if(this.canDecrypt)
         {
            _loc1_ += "D=" + this.d.toString(16) + "\n";
            if(this.p != null && this.q != null)
            {
               _loc1_ += "P=" + this.p.toString(16) + "\n";
               _loc1_ += "Q=" + this.q.toString(16) + "\n";
               _loc1_ += "DMP1=" + this.dmp1.toString(16) + "\n";
               _loc1_ += "DMQ1=" + this.dmq1.toString(16) + "\n";
               _loc1_ += "IQMP=" + this.coeff.toString(16) + "\n";
            }
         }
         return _loc1_;
      }
      
      protected function doPublic(param1:BigInteger) : BigInteger
      {
         return param1.modPowInt(this.e,this.n);
      }
      
      protected function doPrivate2(param1:BigInteger) : BigInteger
      {
         if(this.p == null && this.q == null)
         {
            return param1.modPow(this.d,this.n);
         }
         var _loc4_:BigInteger = param1.mod(this.p).modPow(this.dmp1,this.p);
         var _loc2_:BigInteger = param1.mod(this.q).modPow(this.dmq1,this.q);
         while(_loc4_.compareTo(_loc2_) < 0)
         {
            _loc4_ = _loc4_.add(this.p);
         }
         return _loc4_.subtract(_loc2_).multiply(this.coeff).mod(this.p).multiply(this.q).add(_loc2_);
      }
      
      protected function doPrivate(param1:BigInteger) : BigInteger
      {
         if(this.p == null || this.q == null)
         {
            return param1.modPow(this.d,this.n);
         }
         var _loc3_:BigInteger = param1.mod(this.p).modPow(this.dmp1,this.p);
         var _loc2_:BigInteger = param1.mod(this.q).modPow(this.dmq1,this.q);
         while(_loc3_.compareTo(_loc2_) < 0)
         {
            _loc3_ = _loc3_.add(this.p);
         }
         return _loc3_.subtract(_loc2_).multiply(this.coeff).mod(this.p).multiply(this.q).add(_loc2_);
      }
   }
}

