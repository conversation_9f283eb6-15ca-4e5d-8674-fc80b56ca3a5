package com.worlize.gif
{
   import com.worlize.gif.blocks.ApplicationExtension;
   import com.worlize.gif.blocks.ColorTableBlock;
   import com.worlize.gif.blocks.CommentExtension;
   import com.worlize.gif.blocks.GraphicControlExtension;
   import com.worlize.gif.blocks.HeaderBlock;
   import com.worlize.gif.blocks.IGIFBlockCodec;
   import com.worlize.gif.blocks.ImageDataBlock;
   import com.worlize.gif.blocks.ImageDescriptorBlock;
   import com.worlize.gif.blocks.LogicalScreenDescriptorBlock;
   import com.worlize.gif.blocks.NetscapeExtension;
   import com.worlize.gif.blocks.PlainTextExtension;
   import com.worlize.gif.blocks.TrailerBlock;
   import com.worlize.gif.constants.DefaultPalette;
   import com.worlize.gif.errors.FileTypeError;
   import com.worlize.gif.errors.OutOfDataError;
   import com.worlize.gif.events.AsyncDecodeErrorEvent;
   import com.worlize.gif.events.GIFDecoderEvent;
   import flash.display.BitmapData;
   import flash.events.EventDispatcher;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.utils.ByteArray;
   
   public class GIFDecoder extends EventDispatcher
   {
      protected var data:ByteArray;
      
      protected var header:HeaderBlock;
      
      protected var lsd:LogicalScreenDescriptorBlock;
      
      protected var graphicControlExtension:GraphicControlExtension;
      
      protected var globalColorTable:ColorTableBlock;
      
      protected var fallbackColorTable:ColorTableBlock;
      
      protected var activeColorTable:ColorTableBlock;
      
      public var width:uint;
      
      public var height:uint;
      
      public var backgroundColor:uint = 4294967295;
      
      public var loopCount:uint = 1;
      
      public var frameDecodedCount:uint = 0;
      
      public var framesToDecode:uint = 0;
      
      public var frames:Vector.<GIFFrame>;
      
      private var startTime:uint;
      
      public var totalDecodeTime:uint;
      
      public var blockingDecodeTime:uint;
      
      private var _hasError:Boolean = false;
      
      protected var blockSequence:Vector.<IGIFBlockCodec>;
      
      public function GIFDecoder()
      {
         super();
      }
      
      public function get hasError() : Boolean
      {
         return this._hasError;
      }
      
      public function decodeBytes(param1:ByteArray) : void
      {
         var _loc2_:AsyncDecodeErrorEvent = null;
         var _loc4_:* = param1;
         _loc2_ = null;
         this.startTime = new Date().valueOf();
         this.data = _loc4_;
         this.data.endian = "littleEndian";
         this.blockSequence = new Vector.<IGIFBlockCodec>();
         this.frames = new Vector.<GIFFrame>();
         this.framesToDecode = 0;
         try
         {
            this.readMetadata();
            this.readContents();
         }
         catch(e:Error)
         {
            abortDecode();
            _hasError = true;
            _loc2_ = new AsyncDecodeErrorEvent("asyncDecodeError");
            _loc2_.text = e.message;
            dispatchEvent(_loc2_);
            return;
         }
         var _loc3_:uint = uint(new Date().valueOf());
         this.blockingDecodeTime = _loc3_ - this.startTime;
      }
      
      public function cleanup() : void
      {
         this.data.clear();
         this.data = null;
         var _loc1_:int = 0;
         while(_loc1_ < this.blockSequence.length)
         {
            this.blockSequence[_loc1_].dispose();
            _loc1_++;
         }
      }
      
      private function readMetadata() : void
      {
         this.header = new HeaderBlock();
         this.header.decode(this.data);
         this.blockSequence.push(this.header);
         this.lsd = new LogicalScreenDescriptorBlock();
         this.lsd.decode(this.data);
         this.width = this.lsd.width;
         this.height = this.lsd.height;
         this.blockSequence.push(this.lsd);
         if(this.lsd.hasgct)
         {
            this.globalColorTable = new ColorTableBlock();
            this.globalColorTable.numColors = this.lsd.gctNumColors;
            this.globalColorTable.decode(this.data);
            this.blockSequence.push(this.globalColorTable);
            this.backgroundColor = this.globalColorTable.table[this.lsd.backgroundColorIndex];
         }
         else
         {
            this.fallbackColorTable = new ColorTableBlock();
            this.fallbackColorTable.numColors = 256;
            this.fallbackColorTable.table = DefaultPalette.WINDOWS;
         }
      }
      
      private function readContents() : void
      {
         var _loc1_:uint = 0;
         var _loc2_:Boolean = false;
         while(!_loc2_)
         {
            if(this.data.bytesAvailable < 1)
            {
               throw new OutOfDataError("Out of data while looking for next block.");
            }
            _loc1_ = this.data.readUnsignedByte();
            switch(_loc1_)
            {
               case 33:
                  this.decodeExtension();
                  break;
               case 44:
                  this.decodeImage();
                  break;
               case 59:
                  this.blockSequence.push(new TrailerBlock());
                  _loc2_ = true;
                  break;
               default:
                  throw new FileTypeError("Invalid data encountered while decoding GIF stream.");
            }
         }
      }
      
      private function decodeExtension() : void
      {
         var _loc3_:CommentExtension = null;
         var _loc2_:PlainTextExtension = null;
         if(this.data.bytesAvailable < 1)
         {
            throw new OutOfDataError("Out of data while trying to read extension");
         }
         var _loc1_:uint = this.data.readUnsignedByte();
         switch(_loc1_)
         {
            case 255:
               this.decodeAppExtension();
               break;
            case 249:
               this.graphicControlExtension = new GraphicControlExtension();
               this.graphicControlExtension.decode(this.data);
               this.blockSequence.push(this.graphicControlExtension);
               break;
            case 254:
               _loc3_ = new CommentExtension();
               _loc3_.decode(this.data);
               this.blockSequence.push(_loc3_);
               break;
            case 1:
               _loc2_ = new PlainTextExtension();
               _loc2_.decode(this.data);
               this.blockSequence.push(_loc3_);
               break;
            default:
               throw new FileTypeError("Invalid GIF data - invalid extension type encountered.");
         }
      }
      
      private function decodeAppExtension() : void
      {
         var _loc2_:NetscapeExtension = null;
         var _loc1_:ApplicationExtension = new ApplicationExtension();
         _loc1_.decode(this.data);
         if(_loc1_.appIdentifier == "NETSCAPE" && _loc1_.appAuthCode == "2.0")
         {
            _loc2_ = new NetscapeExtension();
            _loc2_.decodeFromApplicationExtension(_loc1_);
            this.blockSequence.push(_loc2_);
            this.loopCount = _loc2_.loopCount;
         }
         else
         {
            this.blockSequence.push(_loc1_);
         }
      }
      
      private function decodeImage() : void
      {
         var _loc3_:ColorTableBlock = null;
         var _loc2_:ImageDescriptorBlock = new ImageDescriptorBlock();
         _loc2_.decode(this.data);
         this.blockSequence.push(_loc2_);
         if(_loc2_.haslct)
         {
            _loc3_ = new ColorTableBlock();
            _loc3_.numColors = _loc2_.lctNumColors;
            _loc3_.decode(this.data);
            this.blockSequence.push(_loc3_);
            this.activeColorTable = _loc3_;
         }
         else if(this.globalColorTable)
         {
            this.activeColorTable = this.globalColorTable;
         }
         else
         {
            this.activeColorTable = this.fallbackColorTable;
         }
         var _loc1_:ImageDataBlock = new ImageDataBlock();
         _loc1_.decode(this.data);
         this.blockSequence.push(_loc1_);
         this.frames.push(this.buildFrame(_loc2_,_loc1_));
         this.graphicControlExtension = null;
      }
      
      private function buildFrame(param1:ImageDescriptorBlock, param2:ImageDataBlock) : GIFFrame
      {
         var _loc7_:GraphicControlExtension = null;
         var _loc8_:ByteArray = new ByteArray();
         _loc8_.endian = "littleEndian";
         this.header.encode(_loc8_);
         var _loc3_:LogicalScreenDescriptorBlock = new LogicalScreenDescriptorBlock();
         _loc3_.pixelAspect = this.lsd.pixelAspect;
         _loc3_.width = param1.width;
         _loc3_.height = param1.height;
         _loc3_.backgroundColorIndex = this.lsd.backgroundColorIndex;
         _loc3_.gctColorResolution = this.lsd.gctColorResolution;
         _loc3_.gctNumColors = this.activeColorTable.numColors;
         _loc3_.hasgct = true;
         _loc3_.gctSorted = false;
         _loc3_.encode(_loc8_);
         this.activeColorTable.encode(_loc8_);
         if(this.graphicControlExtension)
         {
            _loc7_ = new GraphicControlExtension();
            _loc7_.delayTime = 0;
            _loc7_.userInputExpected = false;
            _loc7_.disposalMethod = this.graphicControlExtension.disposalMethod;
            _loc7_.hasTransparency = this.graphicControlExtension.hasTransparency;
            _loc7_.transparencyIndex = this.graphicControlExtension.transparencyIndex;
            _loc7_.encode(_loc8_);
         }
         var _loc4_:ImageDescriptorBlock = new ImageDescriptorBlock();
         _loc4_.haslct = false;
         _loc4_.lctNumColors = 2;
         _loc4_.lctSorted = false;
         _loc4_.width = param1.width;
         _loc4_.height = param1.height;
         _loc4_.offsetLeft = 0;
         _loc4_.offsetTop = 0;
         _loc4_.reserved = 0;
         _loc4_.interlaced = param1.interlaced;
         _loc4_.encode(_loc8_);
         param2.encode(_loc8_);
         var _loc6_:TrailerBlock = new TrailerBlock();
         _loc6_.encode(_loc8_);
         _loc8_.position = 0;
         var _loc5_:GIFFrame = new GIFFrame();
         _loc5_.gifData = _loc8_;
         _loc5_.left = param1.offsetLeft;
         _loc5_.top = param1.offsetTop;
         _loc5_.width = param1.width;
         _loc5_.height = param1.height;
         _loc5_.backgroundColor = this.activeColorTable.table[_loc3_.backgroundColorIndex];
         _loc5_.backgroundColorIndex = _loc3_.backgroundColorIndex;
         if(this.graphicControlExtension)
         {
            _loc5_.delayMs = this.graphicControlExtension.delayTime * 10;
            _loc5_.disposalType = this.graphicControlExtension.disposalMethod;
            _loc5_.hasTransparency = this.graphicControlExtension.hasTransparency;
            _loc5_.transparencyIndex = this.graphicControlExtension.transparencyIndex;
         }
         else
         {
            _loc5_.hasTransparency = false;
            _loc5_.transparencyIndex = 0;
            _loc5_.delayMs = 42;
            _loc5_.disposalType = 2;
         }
         this.framesToDecode++;
         _loc5_.addEventListener("decodeComplete",this.handleFrameDecodeComplete);
         _loc5_.addEventListener("asyncDecodeError",this.handleFrameAsyncDecodeError);
         _loc5_.decode();
         return _loc5_;
      }
      
      protected function renderCompositedFrames() : void
      {
         var _loc9_:GIFFrame = null;
         var _loc8_:* = null;
         var _loc6_:* = null;
         var _loc7_:BitmapData = null;
         var _loc10_:BitmapData = null;
         var _loc11_:uint = 0;
         var _loc14_:Rectangle = null;
         var _loc12_:uint = 0;
         var _loc13_:* = null;
         var _loc2_:uint = uint(new Date().valueOf());
         var _loc4_:uint = this.width;
         var _loc3_:uint = this.height;
         var _loc5_:uint = 0;
         while(_loc5_ < this.framesToDecode)
         {
            _loc9_ = this.frames[_loc5_];
            _loc7_ = _loc9_.bitmapData;
            _loc10_ = new BitmapData(_loc4_,_loc3_,true,16777215);
            if(_loc8_ != null)
            {
               _loc10_.copyPixels(_loc8_.bitmapData,new Rectangle(0,0,_loc4_,_loc3_),new Point(0,0));
               _loc12_ = _loc8_.disposalType;
               if(_loc12_ == 3 && _loc6_ == null || _loc12_ == 2)
               {
                  _loc11_ = _loc8_.backgroundColor;
                  _loc11_ = uint(_loc11_ & 0xFFFFFF);
                  _loc14_ = new Rectangle(_loc8_.left,_loc8_.top,_loc8_.width,_loc8_.height);
                  _loc10_.fillRect(_loc14_,_loc11_);
               }
               else if(_loc12_ == 0 || _loc12_ == 1)
               {
                  _loc6_ = _loc8_;
               }
               else if(_loc12_ == 3 && _loc5_ >= 1)
               {
                  _loc13_ = _loc6_;
                  _loc14_ = new Rectangle(_loc8_.left,_loc8_.top,_loc8_.width,_loc8_.height);
                  _loc10_.copyPixels(_loc13_.bitmapData,_loc14_,new Point(_loc8_.left,_loc8_.top));
               }
            }
            _loc14_ = new Rectangle(0,0,_loc9_.width,_loc9_.height);
            _loc10_.copyPixels(_loc9_.bitmapData,_loc14_,new Point(_loc9_.left,_loc9_.top),null,null,true);
            _loc9_.bitmapData.dispose();
            _loc9_.bitmapData = _loc10_;
            _loc8_ = _loc9_;
            _loc5_++;
         }
         var _loc1_:uint = uint(new Date().valueOf());
         this.blockingDecodeTime += _loc1_ - _loc2_;
      }
      
      public function encode() : ByteArray
      {
         var _loc3_:ByteArray = new ByteArray();
         _loc3_.endian = "littleEndian";
         var _loc2_:int = 0;
         var _loc1_:int = int(this.blockSequence.length);
         while(_loc2_ < _loc1_)
         {
            this.blockSequence[_loc2_].encode(_loc3_);
            _loc2_++;
         }
         _loc3_.position = 0;
         return _loc3_;
      }
      
      protected function handleFrameDecodeComplete(param1:GIFDecoderEvent) : void
      {
         var _loc3_:uint = 0;
         var _loc2_:GIFDecoderEvent = null;
         this.frameDecodedCount++;
         if(this.frameDecodedCount == this.framesToDecode && !this._hasError)
         {
            this.renderCompositedFrames();
            _loc3_ = uint(new Date().valueOf());
            this.totalDecodeTime = _loc3_ - this.startTime;
            _loc2_ = new GIFDecoderEvent("decodeComplete");
            dispatchEvent(_loc2_);
         }
      }
      
      protected function handleFrameAsyncDecodeError(param1:AsyncDecodeErrorEvent) : void
      {
         this.abortDecode();
         var _loc2_:AsyncDecodeErrorEvent = new AsyncDecodeErrorEvent("asyncDecodeError");
         _loc2_.text = "An error was encountered while Flash was decoding an image frame.";
         dispatchEvent(_loc2_);
      }
      
      protected function abortDecode() : void
      {
         var _loc2_:int = 0;
         var _loc1_:int = int(this.frames.length);
         while(_loc2_ < _loc1_)
         {
            this.frames[_loc2_].abortDecode();
            _loc2_++;
         }
         this.cleanup();
      }
   }
}

