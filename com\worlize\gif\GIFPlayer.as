package com.worlize.gif
{
   import com.worlize.gif.events.AsyncDecodeErrorEvent;
   import com.worlize.gif.events.GIFDecoderEvent;
   import com.worlize.gif.events.GIFPlayerEvent;
   import flash.display.Bitmap;
   import flash.events.Event;
   import flash.events.TimerEvent;
   import flash.utils.ByteArray;
   import flash.utils.Dictionary;
   import mmo.ui.control.IDispose;
   
   public class GIFPlayer extends Bitmap implements IDispose
   {
      private static var _id:uint;
      
      private var dicEvent:Object;
      
      public var autoPlay:Boolean;
      
      public var enableFrameSkipping:Boolean = false;
      
      private var wasPlaying:Boolean = false;
      
      private var minFrameDelay:Number = 30;
      
      private var useSmoothing:Boolean = false;
      
      private var lastQuantizationError:Number = 0;
      
      private var gifDecoder:GIFDecoder;
      
      private var currentLoop:uint;
      
      private var _loopCount:uint;
      
      private var _currentFrame:int = -1;
      
      private var _frameCount:uint = 0;
      
      private var _frames:Vector.<GIFFrame>;
      
      private var _ready:Boolean = false;
      
      private var _imageWidth:Number = 0;
      
      private var _imageHeight:Number = 0;
      
      private var sourcePlayer:Boolean;
      
      public var dispatchFrameEvt:Boolean;
      
      private var _instanceid:uint;
      
      private var isDecoding:Boolean;
      
      protected var _url:String;
      
      public function GIFPlayer(param1:Boolean = true)
      {
         super();
         this.dicEvent = {};
         this.autoPlay = param1;
         if(stage)
         {
            this.initMinFrameDelay();
         }
         _id++;
         this._instanceid = _id;
         this.addEventListener("addedToStage",this.initMinFrameDelay);
         this.addEventListener("removedFromStage",this.handleRemovedFromStage);
      }
      
      protected function handleRemovedFromStage(param1:Event) : void
      {
         this.wasPlaying = this.playing;
         this.stop();
         this.addEventListener("addedToStage",this.handleAddedToStage);
      }
      
      protected function handleAddedToStage(param1:Event) : void
      {
         this.removeEventListener("addedToStage",this.handleAddedToStage);
         if(this.wasPlaying)
         {
            this.play();
         }
      }
      
      override public function set smoothing(param1:Boolean) : void
      {
         if(this.useSmoothing != param1)
         {
            this.useSmoothing = param1;
            super.smoothing = param1;
         }
      }
      
      private function initMinFrameDelay(param1:Event = null) : void
      {
         this.minFrameDelay = 1000 / stage.frameRate;
      }
      
      public function loadBytes(param1:ByteArray, param2:String) : void
      {
         this.sourcePlayer = true;
         param1.position = 0;
         this._url = param2;
         var _loc3_:ByteArray = new ByteArray();
         _loc3_.writeBytes(param1,0,param1.length);
         _loc3_.position = 0;
         if(!this.isDecoding)
         {
            this.isDecoding = true;
            this.reset();
            this.gifDecoder.decodeBytes(_loc3_);
         }
      }
      
      public function reset() : void
      {
         this.stop();
         this.setReady(false);
         this.unloadDecoder();
         if(!this.sourcePlayer)
         {
            this._frames = new Vector.<GIFFrame>();
         }
         this.initDecoder();
      }
      
      protected function initDecoder() : void
      {
         this.gifDecoder = new GIFDecoder();
         this.gifDecoder.addEventListener("decodeComplete",this.handleDecodeComplete);
         this.gifDecoder.addEventListener("asyncDecodeError",this.handleAsyncDecodeError);
      }
      
      protected function unloadDecoder() : void
      {
         if(this.gifDecoder)
         {
            this.gifDecoder.removeEventListener("decodeComplete",this.handleDecodeComplete);
            this.gifDecoder.removeEventListener("asyncDecodeError",this.handleAsyncDecodeError);
            this.gifDecoder = null;
         }
      }
      
      protected function handleDecodeComplete(param1:GIFDecoderEvent) : void
      {
         if(!GifDataManager.instance.hasGif(this._url))
         {
            GifDataManager.instance.addGif(this._url,this);
         }
         this.isDecoding = false;
         this.currentLoop = 0;
         this._loopCount = this.gifDecoder.loopCount;
         this._frames = this.gifDecoder.frames;
         this._frameCount = this._frames.length;
         this._currentFrame = -1;
         this._imageWidth = this.gifDecoder.width;
         this._imageHeight = this.gifDecoder.height;
         this.gifDecoder.cleanup();
         this.unloadDecoder();
         this.setReady(true);
         dispatchEvent(new Event("totalFramesChange"));
         dispatchEvent(new GIFPlayerEvent("complete"));
         if(stage)
         {
            if(this.autoPlay)
            {
               this.gotoAndPlay(1);
            }
            else
            {
               this.gotoAndStop(1);
            }
         }
      }
      
      private function handleTimer(param1:TimerEvent) : void
      {
         this.step();
      }
      
      public function gotoAndPlay(param1:uint) : void
      {
         this.lastQuantizationError = 0;
         this.gotoF(param1 - 1);
         this.play();
      }
      
      public function gotoAndStop(param1:uint) : void
      {
         this.stop();
         this.lastQuantizationError = 0;
         this.gotoF(param1 - 1);
      }
      
      public function play() : void
      {
         if(this._frameCount <= 0)
         {
            throw new Error("Nothing to play");
         }
         if(this._frameCount == 1)
         {
            this.gotoF(0);
         }
         else
         {
            this.lastQuantizationError = 0;
            GifDataManager.instance.addToRenderList(this);
         }
      }
      
      public function stop() : void
      {
         GifDataManager.instance.removeFromRenderList(this);
      }
      
      private function quantizeFrameDelay(param1:Number, param2:Number) : Number
      {
         var _loc3_:Number = param1 + this.lastQuantizationError;
         var _loc4_:Number = Math.round(_loc3_ / param2) * param2;
         this.lastQuantizationError = _loc3_ - _loc4_;
         return _loc4_;
      }
      
      public function step() : void
      {
         if(this._currentFrame + 1 >= this._frameCount)
         {
            this.currentLoop++;
            if(this._loopCount == 0 || this.currentLoop < this._loopCount)
            {
               this.gotoF(0);
            }
            else
            {
               this.stop();
            }
         }
         else
         {
            this.gotoF(this._currentFrame + 1);
         }
      }
      
      private function gotoF(param1:uint) : void
      {
         var _loc4_:GIFPlayerEvent = null;
         if(param1 >= this._frameCount || param1 < 0)
         {
            throw new RangeError("The requested frame is out of bounds.");
         }
         if(param1 == this._currentFrame)
         {
            return;
         }
         this._currentFrame = param1;
         var _loc2_:Number = this.currentFrameObject.delayMs < 20 ? 100 : this.currentFrameObject.delayMs;
         var _loc3_:Number = Math.round(this.quantizeFrameDelay(_loc2_,this.minFrameDelay));
         _loc3_ = Math.max(_loc3_ - this.minFrameDelay / 2,0);
         if(_loc3_ == 0 && this.enableFrameSkipping)
         {
            this.step();
            return;
         }
         bitmapData = this.currentFrameObject.bitmapData;
         if(this.useSmoothing)
         {
            super.smoothing = true;
         }
         if(this.dispatchFrameEvt)
         {
            _loc4_ = new GIFPlayerEvent("frameRendered");
            _loc4_.frameIndex = this._currentFrame;
            dispatchEvent(_loc4_);
         }
      }
      
      public function setFramesData(param1:Vector.<GIFFrame>, param2:uint, param3:uint, param4:uint) : void
      {
         this._frames = param1;
         this.currentLoop = 0;
         this._loopCount = param2;
         this._frameCount = this._frames.length;
         this._currentFrame = -1;
         this._imageWidth = param3;
         this._imageHeight = param4;
         if(this.gifDecoder)
         {
            this.gifDecoder.cleanup();
         }
         this.unloadDecoder();
         this.setReady(true);
         if(this.autoPlay)
         {
            this.gotoAndPlay(1);
         }
         else
         {
            this.gotoAndStop(1);
         }
      }
      
      public function clone() : GIFPlayer
      {
         var _loc2_:GIFPlayer = new GIFPlayer(this.autoPlay);
         var _loc1_:Vector.<GIFFrame> = this._frames.concat();
         _loc2_.setFramesData(_loc1_,this.loopCount,this._imageWidth,this._imageHeight);
         return _loc2_;
      }
      
      public function dispose() : void
      {
         this.unloadDecoder();
         this.stop();
         if(this._frames == null)
         {
            return;
         }
         if(!this.sourcePlayer)
         {
            this._frames = new Vector.<GIFFrame>();
            this.removeEventListener("addedToStage",this.initMinFrameDelay);
            this.removeEventListener("removedFromStage",this.handleRemovedFromStage);
            this.bitmapData = null;
         }
         this.clearAllEvent();
      }
      
      override public function addEventListener(param1:String, param2:Function, param3:Boolean = false, param4:int = 0, param5:Boolean = false) : void
      {
         var _loc6_:Dictionary = null;
         if(!this.dicEvent[param1])
         {
            this.dicEvent[param1] = {
               "useCap":new Dictionary(),
               "noCap":new Dictionary()
            };
         }
         _loc6_ = param3 ? this.dicEvent[param1].useCap : this.dicEvent[param1].noCap;
         _loc6_[param2] = true;
         super.addEventListener(param1,param2,param3,param4,param5);
      }
      
      override public function removeEventListener(param1:String, param2:Function, param3:Boolean = false) : void
      {
         if(!this.dicEvent[param1])
         {
            return;
         }
         var _loc4_:Dictionary = param3 ? this.dicEvent[param1].useCap : this.dicEvent[param1].noCap;
         delete _loc4_[param2];
         super.removeEventListener(param1,param2,param3);
      }
      
      public function clearAllEvent() : void
      {
         var _loc4_:* = undefined;
         var _loc3_:* = undefined;
         var _loc1_:* = undefined;
         var _loc2_:Object = null;
         for(_loc4_ in this.dicEvent)
         {
            _loc2_ = this.dicEvent[_loc4_];
            for(_loc3_ in _loc2_.useCap)
            {
               delete _loc2_.useCap[_loc3_];
               super.removeEventListener(_loc4_,_loc3_ as Function,true);
            }
            for(_loc1_ in _loc2_.noCap)
            {
               delete _loc2_.noCap[_loc1_];
               super.removeEventListener(_loc4_,_loc1_ as Function,false);
            }
            delete this.dicEvent[_loc4_];
         }
      }
      
      public function get playing() : Boolean
      {
         return GifDataManager.instance.gifIsRunning(this);
      }
      
      [Bindable("frameRendered")]
      public function get currentFrame() : uint
      {
         return this._currentFrame + 1;
      }
      
      [Bindable("totalFramesChange")]
      public function get totalFrames() : uint
      {
         return this._frameCount;
      }
      
      [Bindable("readyChange")]
      public function get ready() : Boolean
      {
         return this._ready;
      }
      
      protected function setReady(param1:Boolean) : void
      {
         if(this._ready != param1)
         {
            this._ready = param1;
            dispatchEvent(new Event("readyChange"));
         }
      }
      
      public function get loopCount() : uint
      {
         return this._loopCount;
      }
      
      protected function get currentFrameObject() : GIFFrame
      {
         return this._frames[this._currentFrame];
      }
      
      protected function get previousFrameObject() : GIFFrame
      {
         if(this._currentFrame == 0)
         {
            return null;
         }
         return this._frames[this._currentFrame - 1];
      }
      
      protected function handleAsyncDecodeError(param1:AsyncDecodeErrorEvent) : void
      {
         this.isDecoding = false;
         dispatchEvent(param1.clone());
      }
      
      override public function get width() : Number
      {
         return this._imageWidth;
      }
      
      override public function get height() : Number
      {
         return this._imageHeight;
      }
      
      public function get url() : String
      {
         return this._url;
      }
      
      public function get frames() : Vector.<GIFFrame>
      {
         return this._frames.concat();
      }
      
      public function get imageWidth() : Number
      {
         return this._imageWidth;
      }
      
      public function get imageHeight() : Number
      {
         return this._imageHeight;
      }
      
      public function get id() : uint
      {
         return this._instanceid;
      }
   }
}

