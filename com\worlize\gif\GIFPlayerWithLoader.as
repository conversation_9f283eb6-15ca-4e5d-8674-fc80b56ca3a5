package com.worlize.gif
{
   import flash.events.Event;
   import flash.events.IOErrorEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   
   public class GIFPlayerWithLoader extends GIFPlayer
   {
      private var urlLoader:URLLoader;
      
      public function GIFPlayerWithLoader(param1:Boolean = true)
      {
         super(param1);
      }
      
      private function createLoader() : void
      {
         if(this.urlLoader)
         {
            if(this.urlLoader.bytesLoaded < this.urlLoader.bytesTotal)
            {
               this.urlLoader.close();
            }
            this.urlLoader.removeEventListener("complete",this.onComplete);
            this.urlLoader.removeEventListener("ioError",this.onIOError);
            this.urlLoader = null;
         }
         this.urlLoader = new URLLoader();
         this.urlLoader.dataFormat = "binary";
         this.urlLoader.addEventListener("complete",this.onComplete);
         this.urlLoader.addEventListener("ioError",this.onIOError);
      }
      
      private function removeLoader() : void
      {
         if(this.urlLoader)
         {
            if(this.urlLoader.bytesLoaded < this.urlLoader.bytesTotal)
            {
               this.urlLoader.close();
            }
            this.urlLoader.removeEventListener("complete",this.onComplete);
            this.urlLoader.removeEventListener("ioError",this.onIOError);
            this.urlLoader = null;
         }
      }
      
      private function onIOError(param1:IOErrorEvent) : void
      {
         dispatchEvent(param1);
         this.dispose();
      }
      
      private function onComplete(param1:Event) : void
      {
         this.loadBytes(param1.target.data,_url);
      }
      
      public function load(param1:URLRequest) : void
      {
         _url = param1.url;
         this.removeLoader();
         this.createLoader();
         this.urlLoader.load(param1);
      }
      
      override public function dispose() : void
      {
         this.removeLoader();
         super.dispose();
      }
   }
}

