package com.worlize.gif
{
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.utils.Dictionary;
   import mmo.Core;
   
   public class GifDataManager extends EventDispatcher
   {
      private static var _instance:GifDataManager;
      
      public var lib:Object;
      
      private var dic:Dictionary;
      
      private var renderList:Object;
      
      private var _renderCounter:Number;
      
      private var isRendering:Boolean;
      
      private var renderStepRate:uint = 6;
      
      private var renderSteper:uint = 0;
      
      public function GifDataManager()
      {
         super();
         this.lib = {};
         this.dic = new Dictionary(true);
         this.renderList = {};
         this._renderCounter = 0;
      }
      
      public static function get instance() : GifDataManager
      {
         if(_instance == null)
         {
            _instance = new GifDataManager();
         }
         return _instance;
      }
      
      public function gifIsRunning(param1:GIFPlayer) : Boolean
      {
         return this.renderList[param1.id] != null;
      }
      
      public function addToRenderList(param1:GIFPlayer) : void
      {
         if(this.renderList[param1.id] == null)
         {
            this._renderCounter++;
            this.renderList[param1.id] = param1;
         }
         if(!this.isRendering && this._renderCounter > 0)
         {
            this.startRender();
         }
      }
      
      public function removeFromRenderList(param1:GIFPlayer) : void
      {
         if(this.renderList[param1.id])
         {
            this._renderCounter--;
            delete this.renderList[param1.id];
         }
      }
      
      public function startRender() : void
      {
         this.isRendering = true;
         Core.stg.addEventListener("enterFrame",this.onRender);
         this.renderSteper = 0;
      }
      
      public function stopRender() : void
      {
         this.isRendering = false;
         Core.stg.removeEventListener("enterFrame",this.onRender);
      }
      
      public function onRender(param1:Event) : void
      {
         var _loc3_:* = null;
         var _loc2_:GIFPlayer = null;
         if(this._renderCounter <= 0)
         {
            this.stopRender();
            return;
         }
         this.renderSteper++;
         if(this.renderSteper >= this.renderStepRate)
         {
            this.renderSteper = 0;
            for(_loc3_ in this.renderList)
            {
               _loc2_ = this.renderList[_loc3_];
               _loc2_.step();
            }
         }
      }
      
      public function addGif(param1:String, param2:GIFPlayer) : Boolean
      {
         if(!this.hasGif(param1))
         {
            this.lib[param1] = param2;
            return true;
         }
         return false;
      }
      
      public function hasGif(param1:String) : Boolean
      {
         return this.lib[param1];
      }
      
      public function getClone(param1:String) : GIFPlayer
      {
         var _loc3_:GIFPlayer = null;
         var _loc2_:GIFPlayer = null;
         if(this.hasGif(param1))
         {
            _loc3_ = this.lib[param1];
            _loc2_ = _loc3_.clone();
            this.dic[_loc2_] = true;
            return _loc2_;
         }
         throw new Error("::bug id : 2012-11-29 17:15:51");
      }
      
      public function get renderCounter() : Number
      {
         return this._renderCounter;
      }
   }
}

