package com.worlize.gif.blocks
{
   import com.worlize.gif.errors.OutOfDataError;
   import flash.utils.ByteArray;
   import flash.utils.IDataInput;
   
   public class ColorTableBlock implements IGIFBlockCodec
   {
      public var numColors:uint;
      
      public var table:Vector.<uint>;
      
      protected var cachedEncodedBytes:ByteArray;
      
      public function ColorTableBlock()
      {
         super();
      }
      
      public function decode(param1:IDataInput) : void
      {
         var _loc4_:int = 3 * this.numColors;
         if(param1.bytesAvailable < _loc4_)
         {
            throw new OutOfDataError("Out of data while decoding color table");
         }
         var _loc2_:ByteArray = new ByteArray();
         _loc2_.writeByte(0);
         param1.readBytes(_loc2_,1,_loc4_);
         this.table = new Vector.<uint>(256);
         _loc2_.endian = "bigEndian";
         var _loc3_:uint = 0;
         while(_loc3_ < this.numColors)
         {
            _loc2_.position -= 1;
            this.table[_loc3_] = _loc2_.readUnsignedInt() | 4278190080;
            _loc3_++;
         }
      }
      
      public function encode(param1:ByteArray = null) : ByteArray
      {
         var _loc3_:uint = 0;
         var _loc2_:uint = 0;
         if(!this.cachedEncodedBytes)
         {
            this.cachedEncodedBytes = new ByteArray();
            this.cachedEncodedBytes.endian = "littleEndian";
            _loc3_ = 0;
            while(_loc3_ < this.numColors)
            {
               _loc2_ = this.table[_loc3_];
               this.cachedEncodedBytes.writeByte((_loc2_ & 0xFF0000) >> 16);
               this.cachedEncodedBytes.writeByte((_loc2_ & 0xFF00) >> 8);
               this.cachedEncodedBytes.writeByte(_loc2_ & 0xFF);
               _loc3_++;
            }
         }
         if(param1 == null)
         {
            param1 = new ByteArray();
            param1.endian = "littleEndian";
         }
         param1.writeBytes(this.cachedEncodedBytes);
         return param1;
      }
      
      public function dispose() : void
      {
         if(this.cachedEncodedBytes)
         {
            this.cachedEncodedBytes.clear();
            this.cachedEncodedBytes = null;
         }
      }
   }
}

