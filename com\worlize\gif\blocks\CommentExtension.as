package com.worlize.gif.blocks
{
   import flash.utils.ByteArray;
   import flash.utils.IDataInput;
   
   public class CommentExtension implements IGIFBlockCodec
   {
      public var text:String;
      
      public function CommentExtension()
      {
         super();
      }
      
      public function decode(param1:IDataInput) : void
      {
         var _loc2_:ByteArray = DataBlock.decodeDataBlocks(param1);
         this.text = _loc2_.readMultiByte(_loc2_.length,"ascii");
      }
      
      public function encode(param1:ByteArray = null) : ByteArray
      {
         if(param1 == null)
         {
            param1 = new ByteArray();
            param1.endian = "littleEndian";
         }
         var _loc2_:ByteArray = new ByteArray();
         _loc2_.writeMultiByte(this.text,"ascii");
         param1.writeByte(33);
         param1.writeByte(254);
         param1.writeBytes(DataBlock.encodeDataBlocks(_loc2_));
         return param1;
      }
      
      public function dispose() : void
      {
      }
   }
}

