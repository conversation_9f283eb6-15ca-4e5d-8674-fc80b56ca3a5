package com.worlize.gif.blocks
{
   import com.worlize.gif.errors.FileTypeError;
   import flash.utils.ByteArray;
   import flash.utils.IDataInput;
   
   public class PlainTextExtension implements IGIFBlockCodec
   {
      public var text:String;
      
      public var metadataBlock:DataBlock;
      
      public function PlainTextExtension()
      {
         super();
      }
      
      public function decode(param1:IDataInput) : void
      {
         var _loc2_:* = param1;
         var _loc3_:ByteArray = null;
         try
         {
            this.metadataBlock = new DataBlock();
            this.metadataBlock.decode(_loc2_);
            _loc3_ = DataBlock.decodeDataBlocks(_loc2_);
         }
         catch(e:FileTypeError)
         {
            throw new FileTypeError("Error while decoding a plain text block.");
         }
         this.text = _loc3_.readMultiByte(_loc3_.length,"ascii");
      }
      
      public function encode(param1:ByteArray = null) : ByteArray
      {
         if(param1 == null)
         {
            param1 = new ByteArray();
            param1.endian = "littleEndian";
         }
         param1.writeByte(33);
         param1.writeByte(1);
         param1.writeBytes(this.metadataBlock.encode());
         var _loc2_:ByteArray = new ByteArray();
         _loc2_.writeMultiByte(this.text,"ascii");
         param1.writeBytes(DataBlock.encodeDataBlocks(_loc2_));
         return param1;
      }
      
      public function dispose() : void
      {
      }
   }
}

