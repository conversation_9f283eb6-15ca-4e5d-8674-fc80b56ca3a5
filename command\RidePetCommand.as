package command
{
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   import proxy.RidePetProxy;
   
   public class RidePetCommand extends SimpleCommand
   {
      public function RidePetCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc3_:Object = null;
         var _loc2_:RidePetProxy = facade.retrieveProxy("RidePetProxy") as RidePetProxy;
         switch(param1.getName())
         {
            case "CS_GET_RIDE_INFO":
               _loc2_.getInfo();
               break;
            case "CS_RIDE_PET":
               _loc3_ = param1.getBody();
               _loc2_.ridePet(_loc3_);
               break;
            case "CS_DEMOUNT_PET":
               _loc2_.demousePet();
               break;
            case "CS_HIDE_RIDE_PET":
               _loc3_ = param1.getBody();
               _loc2_.hidePet(_loc3_);
               break;
            case "CS_GETHORSEMANSHIP_INFO":
               _loc2_.horsemanship();
               break;
            case "CS_TRAIN_RIDING":
               _loc3_ = param1.getBody();
               _loc2_.trainRiding(_loc3_);
               break;
            case "CS_GETCOLLECTRIDE_GETINFO":
               _loc2_.getRidingInfo();
               break;
            case "CS_GETROLEPRIZE":
               _loc2_.getRidingPrizeInfo(int(param1.getBody()));
               break;
            case "CS_RIDE_UPGRADE":
               _loc2_.upgradeMount(int(param1.getBody()));
         }
      }
   }
}

