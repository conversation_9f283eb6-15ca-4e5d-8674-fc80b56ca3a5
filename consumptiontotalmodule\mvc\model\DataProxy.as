package consumptiontotalmodule.mvc.model
{
   import consumptiontotalmodule.mvc.model.vo.DataVO;
   import game.data.MainData;
   import game.items.framework.items.ItemFactory;
   import org.puremvc.as3.interfaces.IProxy;
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class DataProxy extends Proxy implements IProxy
   {
      public static const NAME:String = "ConsumptionTotalVOProxy";
      
      private var _dataVO:DataVO;
      
      public function DataProxy()
      {
         super("ConsumptionTotalVOProxy",new DataVO());
      }
      
      override public function onRegister() : void
      {
         this._dataVO = getData() as DataVO;
      }
      
      public function setWindowInitExecuted(param1:Boolean) : void
      {
         this._dataVO.windowInitExecuted = param1;
      }
      
      public function getWindowInitExecuted() : Boolean
      {
         return this._dataVO.windowInitExecuted;
      }
      
      public function setNeedInitDataNum(param1:int) : void
      {
         this._dataVO.needInitDataNum = param1;
      }
      
      public function getNeedInitDataNum() : int
      {
         return this._dataVO.needInitDataNum;
      }
      
      public function setAlreadyInitDataNum(param1:int) : void
      {
         this._dataVO.alreadyInitDataNum = param1;
      }
      
      public function getAlreadyInitDataNum() : int
      {
         return this._dataVO.alreadyInitDataNum;
      }
      
      public function updateAlreadyInitDataNum() : void
      {
         this._dataVO.alreadyInitDataNum++;
         this.checkDataAlready();
      }
      
      public function checkDataAlready() : void
      {
         if(this.getAlreadyInitDataNum() == this.getNeedInitDataNum())
         {
            sendNotification("ConsumptionTotalVOInitReady",[this.getDataXML(),this.getDataInfo()]);
         }
      }
      
      public function getDataSource() : void
      {
         if(this.getWindowInitExecuted())
         {
            this.setAlreadyInitDataNum(0);
         }
         else
         {
            this.setWindowInitExecuted(true);
         }
         this.updateAlreadyInitDataNum();
      }
      
      public function setDataXML(param1:Vector.<Vector.<String>>) : void
      {
         this._dataVO.dataXML = param1;
         sendNotification("ConsumptionTotalVOXMLReady",param1);
         this.updateAlreadyInitDataNum();
      }
      
      public function getDataXML() : Vector.<Vector.<String>>
      {
         return this._dataVO.dataXML;
      }
      
      public function setDataInfoState(param1:String) : void
      {
         this._dataVO.dataInfoState = param1;
      }
      
      public function setDataInfo(param1:Object) : void
      {
         this._dataVO.dataInfo = param1;
         sendNotification("ConsumptionTotalVOInfoReady",[param1.gold_accum,this._dataVO.dataXML]);
         this.updateAlreadyInitDataNum();
      }
      
      public function getDataInfo() : Object
      {
         return this._dataVO.dataInfo;
      }
      
      public function getLastGridsNum() : int
      {
         return MainData.getInstance().bagData.userBag.getLastGridsNum();
      }
      
      public function setDataRewardState(param1:String) : void
      {
         this._dataVO.dataRewardState = param1;
      }
      
      public function getDataRewardState() : String
      {
         return this._dataVO.dataRewardState;
      }
      
      public function setDataReward(param1:Object) : void
      {
         this._dataVO.dataReward = (param1 as Array)[0];
         sendNotification("ConsumptionTotalVORewardReady",[this._dataVO.dataRewardState,(param1 as Array)[1],this._dataVO.dataReward]);
      }
      
      public function getDataReward() : Object
      {
         return this._dataVO.dataReward;
      }
      
      public function updateRelevantData(param1:Object) : void
      {
         var _loc4_:* = null;
         var _loc2_:* = undefined;
         var _loc3_:* = null;
         MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num + Number(param1.belly);
         MainData.getInstance().userData.experience_num = MainData.getInstance().userData.experience_num + int(param1.experiece);
         MainData.getInstance().userData.cur_execution = MainData.getInstance().userData.cur_execution + int(param1.execution);
         if(param1.grid)
         {
            _loc4_ = param1.grid;
            for(_loc2_ in _loc4_)
            {
               _loc3_ = ItemFactory.creatItem(_loc4_[_loc2_]);
               MainData.getInstance().bagData.getBagByGridIndex(_loc2_).addGrid(_loc2_,_loc3_);
            }
         }
      }
   }
}

