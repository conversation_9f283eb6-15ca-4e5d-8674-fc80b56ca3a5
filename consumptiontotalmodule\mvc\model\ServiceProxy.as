package consumptiontotalmodule.mvc.model
{
   import consumptiontotalmodule.mvc.utils.VectorUtilities;
   import consumptiontotalmodule.mvc.view.utils.TextStyle;
   import game.manager.XmlManager;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.Core;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.IProxy;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   import util.time.TimeManager;
   
   public class ServiceProxy extends Proxy implements IProxy
   {
      public static const NAME:String = "ConsumptionTotalServiceProxy";
      
      private var _dataProxy:DataProxy;
      
      public function ServiceProxy()
      {
         super("ConsumptionTotalServiceProxy");
      }
      
      override public function onRegister() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.spend.getInfo",this._readyInfoData);
         BabelTimeSocket.getInstance().regCallback("re.spend.getReward",this._readyRewardData);
         this._dataProxy = facade.retrieveProxy("ConsumptionTotalVOProxy") as DataProxy;
      }
      
      public function removeSocketCallback() : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.spend.getInfo",this._readyInfoData);
         BabelTimeSocket.getInstance().removeCallback("re.spend.getReward",this._readyRewardData);
      }
      
      public function getConsumptionTotalXML() : void
      {
         var _loc22_:int = 0;
         var _loc13_:String = null;
         var _loc30_:String = null;
         var _loc31_:String = null;
         var _loc6_:String = null;
         var _loc12_:String = null;
         var _loc14_:String = null;
         var _loc32_:String = null;
         var _loc28_:String = null;
         var _loc23_:String = null;
         var _loc33_:String = null;
         var _loc5_:String = null;
         var _loc19_:String = null;
         var _loc7_:* = null;
         var _loc24_:* = null;
         var _loc10_:Number = NaN;
         var _loc29_:* = null;
         var _loc11_:* = null;
         var _loc25_:Number = NaN;
         var _loc17_:* = null;
         var _loc1_:int = 0;
         var _loc16_:* = null;
         var _loc21_:* = null;
         var _loc4_:int = 0;
         var _loc20_:* = [];
         var _loc8_:XMLList = XmlManager.xiaofei_leiji_xml.xiaofei_leiji;
         var _loc18_:int = int(_loc8_.length());
         var _loc3_:Number = TimeManager.getInstance().getTime();
         _loc1_ = 0;
         var _loc26_:Array = Core.autoactive.data;
         var _loc2_:String = "0";
         var _loc27_:String = "0";
         _loc22_ = 0;
         while(_loc22_ < _loc26_.length)
         {
            if(_loc26_[_loc22_].hasOwnProperty("a_name") && _loc26_[_loc22_].a_name == "xiaofei")
            {
               _loc2_ = _loc26_[_loc22_].stime;
               _loc27_ = _loc26_[_loc22_].etime;
               if(_loc2_ != "0")
               {
                  _loc13_ = _loc2_.substring(0,4);
                  _loc30_ = _loc2_.substr(4,2);
                  _loc31_ = _loc2_.substr(6,2);
                  _loc6_ = _loc2_.substr(8,2);
                  _loc12_ = _loc2_.substr(10,2);
                  _loc14_ = _loc2_.substr(12,2);
                  _loc2_ = _loc13_ + "-" + _loc30_ + "-" + _loc31_ + "-" + _loc6_ + "-" + _loc12_ + "-" + _loc14_;
                  _loc32_ = _loc27_.substring(0,4);
                  _loc28_ = _loc27_.substr(4,2);
                  _loc23_ = _loc27_.substr(6,2);
                  _loc33_ = _loc27_.substr(8,2);
                  _loc5_ = _loc27_.substr(10,2);
                  _loc19_ = _loc27_.substr(12,2);
                  _loc27_ = _loc32_ + "-" + _loc28_ + "-" + _loc23_ + "-" + _loc33_ + "-" + _loc5_ + "-" + _loc19_;
               }
            }
            _loc22_++;
         }
         while(_loc1_ < _loc18_)
         {
            _loc17_ = _loc8_[_loc1_];
            if(_loc2_ != "")
            {
               _loc7_ = _loc2_.split("-");
            }
            else
            {
               _loc7_ = String(_loc17_.@beginTime).split("-");
            }
            _loc7_[1]--;
            _loc24_ = new Date(Number(_loc7_[0]),Number(_loc7_[1]),Number(_loc7_[2]),Number(_loc7_[3]),Number(_loc7_[4]),Number(_loc7_[5]));
            _loc10_ = Number(_loc24_.getTime());
            if(_loc2_ != "")
            {
               _loc29_ = _loc27_.split("-");
            }
            else
            {
               _loc29_ = String(_loc17_.@endTime).split("-");
            }
            _loc29_[1]--;
            _loc11_ = new Date(Number(_loc29_[0]),Number(_loc29_[1]),Number(_loc29_[2]),Number(_loc29_[3]),Number(_loc29_[4]),Number(_loc29_[5]));
            _loc25_ = Number(_loc11_.getTime());
            if(_loc3_ >= _loc10_ && _loc3_ <= _loc25_)
            {
               _loc20_[_loc20_.length] = _loc17_;
               if(_loc16_ == null)
               {
                  _loc16_ = StringUtil.substitute(Globalization.getString("prize.5"),_loc7_[0],TextStyle.addZero(++_loc7_[1]),_loc7_[2],_loc7_[3],_loc7_[4],_loc7_[5]) + "--" + StringUtil.substitute(Globalization.getString("prize.5"),_loc29_[0],TextStyle.addZero(++_loc29_[1]),_loc29_[2],_loc29_[3],_loc29_[4],_loc29_[5]);
               }
            }
            _loc1_++;
         }
         var _loc15_:int = int(_loc20_.length);
         var _loc9_:Vector.<Vector.<String>> = new Vector.<Vector.<String>>(_loc15_,true);
         _loc4_ = 0;
         while(_loc4_ < _loc15_)
         {
            _loc21_ = new Vector.<String>(6,true);
            _loc17_ = _loc20_[_loc4_];
            _loc21_ = VectorUtilities.getFixedString([String(_loc17_.@ID),String(_loc17_.@money),String(_loc17_.@info),String(_loc17_.@icons),String(_loc17_.@rewardInfo),String(_loc17_.@goods),_loc16_]);
            _loc9_[_loc4_] = _loc21_;
            _loc4_++;
         }
         this._dataProxy.setDataXML(_loc9_);
      }
      
      public function getInfo() : void
      {
         BabelTimeSocket.getInstance().sendMessage("spend.getInfo",new SocketCallback("re.spend.getInfo"));
      }
      
      private function _readyInfoData(param1:SocketDataEvent) : void
      {
         var _loc2_:String = param1.data.ret;
         this._dataProxy.setDataInfoState(_loc2_);
         if(_loc2_ == "ok")
         {
            this._dataProxy.setDataInfo(param1.data.res);
         }
      }
      
      public function getReward(param1:String, param2:String) : void
      {
         BabelTimeSocket.getInstance().sendMessage("spend.getReward",new SocketCallback("re.spend.getReward",[param2]),param1);
      }
      
      private function _readyRewardData(param1:SocketDataEvent) : void
      {
         this._dataProxy.setDataRewardState(param1.data.ret);
         this._dataProxy.setDataReward([param1.data.res,param1.callbackParames[0]]);
      }
   }
}

