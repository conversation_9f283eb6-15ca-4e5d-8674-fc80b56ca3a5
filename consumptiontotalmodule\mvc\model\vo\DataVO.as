package consumptiontotalmodule.mvc.model.vo
{
   public class DataVO
   {
      public var windowInitExecuted:Boolean;
      
      public var needInitDataNum:int;
      
      public var alreadyInitDataNum:int;
      
      public var dataXML:Vector.<Vector.<String>>;
      
      public var dataInfoState:String;
      
      public var dataInfo:Object;
      
      public var dataRewardState:String;
      
      public var dataReward:Object;
      
      public function DataVO()
      {
         super();
      }
   }
}

