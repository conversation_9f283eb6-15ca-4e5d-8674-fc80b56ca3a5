package consumptiontotalmodule.mvc.utils
{
   import flash.display.Sprite;
   
   public class VectorUtilities
   {
      public function VectorUtilities()
      {
         super();
      }
      
      public static function getFixedRegExp(param1:Array) : Vector.<RegExp>
      {
         var _loc2_:Vector.<RegExp> = Vector.<RegExp>(param1);
         _loc2_.fixed = true;
         return _loc2_;
      }
      
      public static function getFixedString(param1:Array) : Vector.<String>
      {
         var _loc2_:Vector.<String> = Vector.<String>(param1);
         _loc2_.fixed = true;
         return _loc2_;
      }
      
      public static function getFixedSprite(param1:Array) : Vector.<Sprite>
      {
         var _loc2_:Vector.<Sprite> = Vector.<Sprite>(param1);
         _loc2_.fixed = true;
         return _loc2_;
      }
   }
}

