package consumptiontotalmodule.mvc.view.utils
{
   public class GL
   {
      public static const WAIT_DATA:String = "waitData.1";
      
      public static const TIP:String = "consumptionTotal.1";
      
      public static const INFO:String = "consumptionTotal.2";
      
      public static const MONEY:String = "Gl.43";
      
      public static const REWARD:String = "openprize.8";
      
      public static const REWARD_OVER:String = "openprize.9";
      
      public static const BGA_NO_TIP:String = "bag.15";
      
      public static const OVER:String = "teamModule.32";
      
      public static const REWARD_TIP:String = "consumptionTotal.3";
      
      public static const INFO_DET:String = "consumptionTotal.4";
      
      public static const INFO_DATE:String = "consumptionTotal.5";
      
      public static const TIME_TEMPLATE:String = "prize.5";
      
      public function GL()
      {
         super();
      }
   }
}

