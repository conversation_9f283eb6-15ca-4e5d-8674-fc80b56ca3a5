package consumptiontotalmodule.mvc.view.utils
{
   import consumptiontotalmodule.mvc.utils.VectorUtilities;
   import flash.display.DisplayObjectContainer;
   import flash.filters.GlowFilter;
   import flash.text.StyleSheet;
   import flash.text.TextField;
   
   public class TextStyle
   {
      public static const CHAR_COMMA:String = ",";
      
      public static const CHAR_AND:String = "&";
      
      public static const CHAR_UNDERLINE:String = "_";
      
      public static const CHAR_HYPHEN:String = "-";
      
      public static const CHAR_ELLIPSIS:String = "…";
      
      public static const CHAR_SlASH:String = "/";
      
      public static const CHAR_VERTICAL_BARS:String = "|";
      
      public static const CHAR_COMMA_CN:String = "，";
      
      public static const CHAR_BREAK:String = "\n";
      
      public static const CHAR_RETURN_WRAP:String = "\r\n";
      
      public static const P_START:String = "<p>";
      
      public static const P_END:String = "</p>";
      
      public static const BR:String = "<br>";
      
      public static const SPAN_END:String = "</span>";
      
      public static const CLASS:String = "class";
      
      public static const INIT_CSS:String = "p{leading:2;text-align:center;}";
      
      private static const T_CSS_SPAN:String = " .SPAN_{_COLOR_}{color:#{_COLOR_};} ";
      
      private static const T_HTML_SPAN:String = "<span class=\'SPAN_{_COLOR_}\'>";
      
      public static const R_CN_COMMA_DUN:RegExp = /[，、]/g;
      
      public static const R_SPAN_6:RegExp = /{_SPAN_(.{6})_}/g;
      
      public static const R_SPAN_END:RegExp = /{_SPAN_END_}/g;
      
      public static const R_BR:RegExp = /{_BR_}/g;
      
      private static const R_COLOR:RegExp = /{_COLOR_}/g;
      
      public function TextStyle()
      {
         super();
      }
      
      public static function replaceStyle(param1:String, param2:Vector.<RegExp>, param3:Vector.<String>) : Vector.<String>
      {
         var replFN:Function;
         var rep:String = null;
         var css:String = null;
         var color:String = null;
         var i:int = 0;
         var content:String = param1;
         var regExps:Vector.<RegExp> = param2;
         var reps:Vector.<String> = param3;
         css = "";
         var max:int = regExps.length - 1;
         i = max;
         while(i > -1)
         {
            replFN = function():String
            {
               if(reps[i] == "class")
               {
                  color = arguments[1];
                  if(css.indexOf(color) == -1)
                  {
                     css += " .SPAN_{_COLOR_}{color:#{_COLOR_};} ".replace(R_COLOR,color);
                  }
                  rep = "<span class=\'SPAN_{_COLOR_}\'>".replace(R_COLOR,color);
               }
               else
               {
                  rep = reps[i];
               }
               return rep;
            };
            content = content.replace(regExps[i],replFN);
            i--;
         }
         return VectorUtilities.getFixedString([content,css]);
      }
      
      public static function createFieldHTMLText(param1:Vector.<String>, param2:Number = 0, param3:Number = 0, param4:Number = 0, param5:Number = 0, param6:String = "", param7:Array = null, param8:DisplayObjectContainer = null) : TextField
      {
         var _loc10_:* = null;
         var _loc11_:TextField = new TextField();
         if(param8)
         {
            param8.addChild(_loc11_);
         }
         _loc11_.x = param4;
         _loc11_.y = param5;
         _loc11_.filters = param7 || [new GlowFilter(2565927,1,2,2,10)];
         _loc11_.multiline = true;
         _loc11_.wordWrap = true;
         _loc11_.selectable = false;
         _loc11_.mouseWheelEnabled = false;
         var _loc9_:* = param6;
         if(param1[1])
         {
            _loc9_ += param1[1];
         }
         if(_loc9_)
         {
            _loc10_ = new StyleSheet();
            _loc10_.parseCSS(_loc9_);
            _loc11_.styleSheet = _loc10_;
            _loc11_.htmlText = "<p>" + param1[0] + "</p>";
         }
         else
         {
            _loc11_.htmlText = param1[0];
         }
         if(param2)
         {
            _loc11_.width = param2;
         }
         else
         {
            _loc11_.width = _loc11_.textWidth + 4;
         }
         if(param3)
         {
            _loc11_.height = param3;
         }
         else
         {
            _loc11_.height = _loc11_.textHeight + 4;
         }
         return _loc11_;
      }
      
      public static function formatTime(param1:Number, param2:Number = NaN, param3:Number = NaN, param4:Boolean = false, param5:String = "") : String
      {
         var _loc6_:* = NaN;
         var _loc8_:* = NaN;
         var _loc7_:* = NaN;
         var _loc10_:String = "";
         var _loc13_:String = "";
         var _loc11_:String = "";
         var _loc12_:* = "";
         var _loc9_:* = param1;
         if(param2 || param2 == 0)
         {
            _loc9_ = param2 - param1;
         }
         if(_loc9_ <= 0 || (param3 || param3 == 0) && param1 < param3)
         {
            _loc6_ = 0;
            _loc8_ = 0;
            _loc7_ = 0;
            if(param5)
            {
               _loc12_ = param5;
            }
         }
         else
         {
            if(_loc9_ == param1)
            {
               _loc6_ = Number(new Date(_loc9_).getHours());
            }
            else
            {
               _loc6_ = int(_loc9_ / 3600000);
            }
            _loc8_ = int(_loc9_ / 60000 % 60);
            _loc7_ = int(_loc9_ / 1000 % 60);
         }
         if(!_loc12_)
         {
            if(param4)
            {
               _loc6_ = NaN;
            }
            if(_loc6_ || _loc6_ == 0)
            {
               _loc10_ = _loc6_ > 9 ? String(_loc6_) + " : " : "0" + _loc6_ + " : ";
            }
            if(_loc8_ || _loc8_ == 0)
            {
               _loc13_ = _loc8_ > 9 ? String(_loc8_) + " : " : "0" + _loc8_ + " : ";
            }
            if(_loc7_ || _loc7_ == 0)
            {
               _loc11_ = _loc7_ > 9 ? String(_loc7_) : "0" + _loc7_;
            }
            _loc12_ = _loc10_ + _loc13_ + _loc11_;
         }
         return _loc12_;
      }
      
      public static function addZero(param1:Number) : String
      {
         return param1 > 9 ? String(param1) : "0" + param1;
      }
      
      public static function getfiniteDecimals(param1:Number, param2:int, param3:Boolean = true) : String
      {
         var _loc4_:* = null;
         var _loc6_:int = 0;
         var _loc7_:Array = String(param1).split(".");
         var _loc5_:String = _loc7_[0];
         if(_loc7_.length == 2)
         {
            _loc4_ = String(_loc7_[1]).substr(0,param2);
            if(param3)
            {
               _loc6_ = _loc4_.length - 1;
               while(_loc6_ > -1)
               {
                  if(_loc4_.charAt(_loc6_) != "0")
                  {
                     break;
                  }
                  _loc4_ = _loc4_.slice(0,_loc6_);
                  _loc6_--;
               }
            }
            if(_loc4_.length)
            {
               _loc5_ += "." + _loc4_;
            }
         }
         return _loc5_;
      }
   }
}

