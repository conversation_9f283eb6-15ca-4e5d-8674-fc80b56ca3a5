package controler
{
   public class BattleConstNames
   {
      public static const PHYSICAL_INDEX:uint = 1;
      
      public static const KILL_INDEX:uint = 2;
      
      public static const MAGIC_INDEX:uint = 3;
      
      public static const WIND_INDEX:uint = 4;
      
      public static const THUDER_INDEX:uint = 5;
      
      public static const WATER_INDEX:uint = 6;
      
      public static const FIRE_INDEX:uint = 7;
      
      public static const ATTACKS:uint = 1;
      
      public static const MAGIC_TYPE:uint = 999;
      
      public static const ATTACK_ANGRY:uint = 11;
      
      public static const ATTACK_CLOSE:uint = 2;
      
      public static const ATTACK_FAR:uint = 3;
      
      public static const ATTACK_MAGIC_SINGLE:uint = 4;
      
      public static const ATTACK_MAGIC_MULITY:uint = 5;
      
      public static const ATTACK_MAGIC_SINGLE_MULITY:uint = 6;
      
      public static const REMOTE_FEET:uint = 7;
      
      public static const FIX_RANGE:uint = 3;
      
      public static const REMOTE:uint = 4;
      
      public static const REMOTE_FIX:uint = 5;
      
      public static const SAME_LINE_ARMY_FIX:uint = 6;
      
      public static const SAME_LINE_SELF_FIX:uint = 10;
      
      public static const HP_CHANGE:uint = 0;
      
      public static const MAX_HP_CHANGE:uint = 2;
      
      public static const RAGE_CHANGE:uint = 1;
      
      public static const SKILL_ATTACK_CLOSE:uint = 1;
      
      public static const BUFF_BEFOR_ACTION:uint = 1;
      
      public static const BUFF_WHEN_ACTION:uint = 2;
      
      public static const BUFF_AFTER_ACTION:uint = 3;
      
      public static const ATTACK:String = "ATTACK";
      
      public static const MOVE_TO:String = "MOVE_TO";
      
      public static const MOVE_BACK:String = "MOVE_BACK";
      
      public static const MAGIC:String = "MAGIC";
      
      public static const DEFENSE:String = "DEFENSE";
      
      public static const SPRITE_ANIAMTIIN_KEY_FRAME:String = "sprite_aniamtiin_key_frame";
      
      public static const EVENT_FOR_BUFF_DEBUG:String = "event_for_buff_debug";
      
      public static const NULL_NODE:uint = 9999;
      
      public static const NODE_ANIMATION:String = "animation";
      
      public static const NODE_SPLITE:String = "D";
      
      public static const NODE_ONE_BY_ONE:String = "oneByOne";
      
      public static const NODE_IN_ONE_TIME:String = "inOneTime";
      
      public static const NODE_ONE_BY_ONE_STRICK:String = "oneByOneStrick";
      
      public function BattleConstNames()
      {
         super();
      }
   }
}

