package controler
{
   import flash.events.EventDispatcher;
   
   public class Event<PERSON>rige extends EventDispatcher
   {
      private static var _instance:EventBrige;
      
      public function EventBrige()
      {
         super();
      }
      
      public static function get instance() : EventBrige
      {
         if(!_instance)
         {
            _instance = new EventBrige();
         }
         return _instance;
      }
   }
}

