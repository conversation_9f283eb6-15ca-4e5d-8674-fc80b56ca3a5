package controler
{
   import controler.animation.ADT.AttackADT;
   import controler.animation.ADT.DefenseADTList;
   import flash.utils.Dictionary;
   
   public final class FightCommandParser
   {
      private var _next:uint;
      
      private var _battleData:Array;
      
      private var _attADT:AttackADT;
      
      private var _defenseDataList:DefenseADTList;
      
      private var _round:int;
      
      public function FightCommandParser()
      {
         super();
      }
      
      public function startParse() : void
      {
         var _loc2_:uint = this._battleData.length;
         if(this._next >= _loc2_)
         {
            return;
         }
         if(!this._defenseDataList)
         {
            this._defenseDataList = new DefenseADTList();
         }
         this._defenseDataList.data = this._battleData[this._next];
         var _loc1_:AttackADT = new AttackADT();
         _loc1_.attackData = this._battleData[this._next];
         this._attADT = _loc1_;
         if(this._battleData[this._next].hasOwnProperty("round"))
         {
            this._round = int(this._battleData[this._next]["round"]);
         }
         if(_loc1_.hasSkill)
         {
            if(this._defenseDataList && this._defenseDataList.beAttackList && this._defenseDataList.beAttackList.length > 0)
            {
               this._attADT.fatal = this._defenseDataList.beAttackList[0].fatal;
            }
         }
         this._next++;
      }
      
      public function get finished() : Boolean
      {
         return this._next >= this._battleData.length;
      }
      
      public function dispose() : void
      {
         this._defenseDataList = null;
      }
      
      public function get attADT() : AttackADT
      {
         return this._attADT;
      }
      
      public function get defenseADTList() : DefenseADTList
      {
         return this._defenseDataList;
      }
      
      public function get battleData() : Array
      {
         return this._battleData;
      }
      
      public function set battleData(param1:Array) : void
      {
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         var _loc5_:Object = null;
         var _loc4_:Object = null;
         var _loc8_:Object = param1[0];
         var _loc6_:Array = [];
         _loc6_.push(_loc8_);
         var _loc7_:Array = _loc8_.arrReaction;
         if(_loc8_.round == 0)
         {
            _loc2_ = int(param1.length);
            _loc3_ = 1;
            while(_loc3_ < _loc2_)
            {
               _loc5_ = param1[_loc3_];
               if(_loc5_.round == 0)
               {
                  _loc4_ = _loc5_.arrReaction[0];
                  _loc7_.push(_loc4_);
               }
               else
               {
                  _loc6_.push(_loc5_);
               }
               _loc3_++;
            }
            this._battleData = _loc6_;
         }
         else
         {
            this._battleData = param1;
         }
         this._next = 0;
      }
      
      public function get next() : uint
      {
         return this._next;
      }
      
      public function set defenseDataList(param1:DefenseADTList) : void
      {
         this._defenseDataList = param1;
      }
      
      public function get round() : int
      {
         return this._round;
      }
      
      public function findDeathSkillData(param1:int) : Object
      {
         var _loc3_:Object = null;
         if(param1 == 0)
         {
            return null;
         }
         var _loc4_:int = int(this._battleData.length);
         var _loc2_:int = int(this._next);
         while(_loc2_ < _loc4_)
         {
            _loc3_ = this._battleData[_loc2_];
            if(_loc3_.attacker == param1)
            {
               this._battleData.splice(_loc2_,1);
               return _loc3_;
            }
            _loc2_++;
         }
         return null;
      }
      
      private function addSecondPlayer(param1:Array, param2:Dictionary) : void
      {
         var _loc3_:Object = null;
         for each(_loc3_ in param1)
         {
            param2[_loc3_.id] = _loc3_;
         }
      }
      
      public function getBenchInfo() : Array
      {
         var _loc5_:Dictionary = null;
         var _loc6_:uint = 0;
         var _loc1_:uint = 0;
         var _loc2_:AttackADT = null;
         var _loc4_:Array = null;
         var _loc3_:Object = null;
         var _loc8_:Boolean = false;
         var _loc7_:Array = this._attADT.benchInfoArr;
         if(_loc7_ && _loc7_.length > 0)
         {
            _loc8_ = true;
            _loc5_ ||= new Dictionary();
            this.addSecondPlayer(_loc7_,_loc5_);
         }
         if(this._defenseDataList.hasComplexSkill && this._defenseDataList.isHasBench())
         {
            _loc8_ = true;
            _loc6_ = this._defenseDataList.subDenfseCommand.length;
            _loc5_ ||= new Dictionary();
            _loc1_ = 0;
            while(_loc1_ < _loc6_)
            {
               _loc2_ = new AttackADT();
               _loc2_.attackData = this._defenseDataList.subDenfseCommand[_loc1_].data;
               _loc7_ = _loc2_.benchInfoArr;
               if(_loc7_ && _loc7_.length > 0)
               {
                  this.addSecondPlayer(_loc7_,_loc5_);
               }
               _loc1_++;
            }
         }
         if(_loc8_)
         {
            _loc4_ = [];
            for each(_loc3_ in _loc5_)
            {
               _loc4_.push(_loc3_);
            }
            return _loc4_;
         }
         return null;
      }
      
      public function getRebornInfo() : Array
      {
         var _loc5_:Dictionary = null;
         var _loc6_:uint = 0;
         var _loc1_:uint = 0;
         var _loc2_:AttackADT = null;
         var _loc4_:Array = null;
         var _loc3_:Object = null;
         var _loc8_:Boolean = false;
         var _loc7_:Array = this._attADT.rebornInfoArr;
         if(_loc7_ && _loc7_.length > 0)
         {
            _loc8_ = true;
            _loc5_ ||= new Dictionary();
            this.addSecondPlayer(_loc7_,_loc5_);
         }
         if(this._defenseDataList.hasComplexSkill && this._defenseDataList.isHasReborn())
         {
            _loc8_ = true;
            _loc6_ = this._defenseDataList.subDenfseCommand.length;
            _loc5_ ||= new Dictionary();
            _loc1_ = 0;
            while(_loc1_ < _loc6_)
            {
               _loc2_ = new AttackADT();
               _loc2_.attackData = this._defenseDataList.subDenfseCommand[_loc1_].data;
               _loc7_ = _loc2_.rebornInfoArr;
               if(_loc7_ && _loc7_.length > 0)
               {
                  this.addSecondPlayer(_loc7_,_loc5_);
               }
               _loc1_++;
            }
         }
         if(_loc8_)
         {
            _loc4_ = [];
            for each(_loc3_ in _loc5_)
            {
               _loc4_.push(_loc3_);
            }
            return _loc4_;
         }
         return null;
      }
   }
}

