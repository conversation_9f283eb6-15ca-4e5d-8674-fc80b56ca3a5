package controler
{
   import flash.events.Event;
   import flash.utils.Dictionary;
   import flash.utils.Timer;
   import mmo.utils.FrameCenter;
   
   public class FrameCenterBaseOnTimer extends FrameCenter
   {
      private var _timer:Timer;
      
      private var _dic:Dictionary;
      
      private var _delay:uint = 5;
      
      private var _speed:Number = 1;
      
      public function FrameCenterBaseOnTimer()
      {
         super();
      }
      
      override public function addFrame(param1:Function) : void
      {
         this._dic[param1] = param1;
      }
      
      override public function hasFrame(param1:Function) : Bo<PERSON>an
      {
         return this._dic[param1];
      }
      
      override public function removeFrame(param1:Function) : void
      {
         delete this._dic[param1];
      }
      
      override public function start() : void
      {
         this._dic = new Dictionary(true);
         this.addTimer();
      }
      
      private function addTimer() : void
      {
         this.removeTimer();
         this._timer = new Timer(uint(this._delay / this._speed));
         this._timer.addEventListener("timer",this.run);
         this._timer.start();
      }
      
      private function removeTimer() : void
      {
         if(this._timer)
         {
            this._timer.stop();
            this._timer.removeEventListener("timer",this.run);
            this._timer = null;
         }
      }
      
      override public function stop() : void
      {
         this._dic = null;
         this.removeTimer();
      }
      
      public function speedUpTo(param1:Number) : void
      {
         if(param1 > 0 && param1 <= this._delay)
         {
            this._speed = param1;
         }
         else
         {
            this._speed = 30;
         }
         this.addTimer();
      }
      
      override public function run(param1:Event) : void
      {
         var _loc2_:Function = null;
         for each(_loc2_ in this._dic)
         {
            _loc2_.call();
         }
      }
   }
}

