package controler.animation.ADT
{
   public class AttackADT implements IAttackADT
   {
      private var _attackData:Object;
      
      private var _fatal:Boolean;
      
      private const ACTOR_ID:String = "attacker";
      
      private const SKILL_ID:String = "action";
      
      private const UNDER_ATTACKERS:String = "arrReaction";
      
      private const RAGE_TAGE_NAME:String = "rage";
      
      private const BENCH_INFO:String = "arrBench";
      
      private const REBORN_INFO:String = "arrReborn";
      
      private var _buffInfo:BuffADT;
      
      public function AttackADT()
      {
         super();
      }
      
      public function set attackData(param1:Object) : void
      {
         this._attackData = param1;
         this._buffInfo = new BuffADT(this._attackData);
      }
      
      public function get round() : int
      {
         return this._attackData.round;
      }
      
      public function get fatal() : Bo<PERSON><PERSON>
      {
         return this._fatal;
      }
      
      public function get rage() : int
      {
         var _loc1_:int = 0;
         if(this._attackData && this._attackData.hasOwnProperty("rage"))
         {
            return int(this._attackData["rage"]);
         }
         return 0;
      }
      
      public function resetRage() : void
      {
         if(this._attackData["rage"])
         {
            delete this._attackData["rage"];
         }
      }
      
      public function get actorID() : uint
      {
         return uint(this._attackData["attacker"]);
      }
      
      public function get skillID() : uint
      {
         return uint(this._attackData["action"]);
      }
      
      public function get hasSkill() : Boolean
      {
         return this._attackData && this._attackData.hasOwnProperty("action") && this._attackData["action"] != 0;
      }
      
      public function get attackAction() : uint
      {
         return 0;
      }
      
      public function get buffInfo() : BuffADT
      {
         return this._buffInfo;
      }
      
      public function set fatal(param1:Boolean) : void
      {
         this._fatal = param1;
      }
      
      public function get underAttackers() : Array
      {
         return this._attackData["arrReaction"] as Array;
      }
      
      public function get targetId() : uint
      {
         if(this._attackData.hasOwnProperty("defender"))
         {
            return uint(this._attackData["defender"]);
         }
         return 0;
      }
      
      public function dispose() : void
      {
      }
      
      public function get benchInfoArr() : Array
      {
         if(this._attackData && this._attackData.hasOwnProperty("arrBench"))
         {
            return this._attackData["arrBench"];
         }
         return null;
      }
      
      public function get rebornInfoArr() : Array
      {
         if(this._attackData && this._attackData.hasOwnProperty("arrReborn"))
         {
            return this._attackData["arrReborn"];
         }
         return null;
      }
   }
}

