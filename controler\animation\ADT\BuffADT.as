package controler.animation.ADT
{
   public class BuffADT
   {
      private var _addBuffList:Array;
      
      private var _removeBuffList:Array;
      
      private var _imBufflist:Array;
      
      private var _buffDamageList:Vector.<BuffDamageADT>;
      
      private var TAG_ENBUFFER:String = "enBuffer";
      
      private var TAG_DEBUFFER:String = "deBuffer";
      
      private var TAG_IMBUFFER:String = "imBuffer";
      
      private var TAG_BUFFER:String = "buffer";
      
      private var _data:Object;
      
      public function BuffADT(param1:Object = null)
      {
         super();
         this._data = param1;
         this._data && this.parse();
      }
      
      public function set data(param1:Object) : void
      {
         this._data = param1;
         this.parse();
      }
      
      private function parse() : void
      {
         trace("buffer攻击:" + this._data["defender"]);
         var _loc3_:uint = 0;
         var _loc1_:uint = 0;
         var _loc2_:BuffDamageADT = null;
         this._addBuffList = this.getTagValue(this.TAG_ENBUFFER) as Array;
         this._removeBuffList = this.getTagValue(this.TAG_DEBUFFER) as Array;
         this._imBufflist = this.getTagValue(this.TAG_IMBUFFER) as Array;
         var _loc4_:Array = this.getTagValue(this.TAG_BUFFER) as Array;
         if(_loc4_ && _loc4_.length > 0)
         {
            this._buffDamageList = new Vector.<BuffDamageADT>();
            _loc3_ = _loc4_.length;
            _loc1_ = 0;
            while(_loc1_ < _loc3_)
            {
               _loc2_ = new BuffDamageADT(_loc4_[_loc1_]);
               this._buffDamageList.push(_loc2_);
               _loc1_++;
            }
         }
      }
      
      private function getTagValue(param1:String) : *
      {
         if(this._data && this._data.hasOwnProperty(param1))
         {
            return this._data[param1];
         }
         return null;
      }
      
      public function get addBuffList() : Array
      {
         return this._addBuffList;
      }
      
      public function get removeBuffList() : Array
      {
         return this._removeBuffList;
      }
      
      public function get imBufflist() : Array
      {
         return this._imBufflist;
      }
      
      public function get buffDamageList() : Vector.<BuffDamageADT>
      {
         return this._buffDamageList;
      }
      
      public function get hasBuffInfo() : Boolean
      {
         if(this._addBuffList && this._addBuffList.length > 0 || this._buffDamageList && this._buffDamageList.length > 0 || this._imBufflist && this._imBufflist.length > 0 || this._removeBuffList && this._removeBuffList.length > 0)
         {
            return true;
         }
         return false;
      }
      
      public function get allBuffDamage() : int
      {
         var _loc2_:int = 0;
         var _loc1_:int = 0;
         var _loc3_:int = 0;
         if(this._buffDamageList && this._buffDamageList.length > 0)
         {
            _loc2_ = int(this._buffDamageList.length);
            _loc1_ = 0;
            while(_loc1_ < _loc2_)
            {
               if(this._buffDamageList[_loc1_].isHpChange)
               {
                  _loc3_ += this._buffDamageList[_loc1_].damage;
               }
               _loc1_++;
            }
         }
         return _loc3_;
      }
   }
}

