package controler.animation.ADT
{
   public class BuffDamageADT
   {
      private var _id:uint;
      
      private var _type:int;
      
      private var _damage:int;
      
      private var _sourceData:Object;
      
      private var TAG_ID:String = "bufferId";
      
      private var TAG_TYPE:String = "type";
      
      private var TAG_DAMAGE:String = "data";
      
      public function BuffDamageADT(param1:Object)
      {
         super();
         this._sourceData = param1;
         this._type = this.getTagValue(this.TAG_TYPE);
         this._damage = this.getTagValue(this.TAG_DAMAGE);
         this._id = this.getTagValue(this.TAG_ID);
         trace("bufid=" + this._id + " type=" + this._type + " damage=" + this._damage);
      }
      
      private function getTagValue(param1:String) : *
      {
         if(this._sourceData && this._sourceData.hasOwnProperty(param1))
         {
            return this._sourceData[param1];
         }
         return null;
      }
      
      public function get id() : uint
      {
         return this._id;
      }
      
      public function get type() : int
      {
         return this._type;
      }
      
      public function get damage() : int
      {
         return this._damage;
      }
      
      public function get isHpChange() : Boolean
      {
         if(this.type == 9)
         {
            return true;
         }
         return false;
      }
      
      public function get isRageChange() : Boolean
      {
         if(this.type == 28)
         {
            return true;
         }
         return false;
      }
      
      public function get isMaxHpChange() : Boolean
      {
         if(this.type == 8)
         {
            return true;
         }
         return false;
      }
   }
}

