package controler.animation.ADT
{
   public class DefenseADT implements IDefenseADT
   {
      private var _defenseData:Object;
      
      private var _fireDamage:int;
      
      private var _windDamage:int;
      
      private var _waterDamage:int;
      
      private var _thuderDamage:int;
      
      private var _normalDamage:int;
      
      private var _magicDamage:int;
      
      private var _killDamage:int;
      
      private var _demageKindsNumber:uint;
      
      private var _totalDamage:int;
      
      private var _magicID:uint;
      
      private var _actionType:uint;
      
      private var _deathSkillId:uint;
      
      private var _buffInfo:BuffADT;
      
      private const FATAL_TAG:String = "fatal";
      
      private const TAG_ID:String = "defender";
      
      private const TAG_DEATH_SKILL_ID:String = "deathSkill";
      
      private const DAMAGE_INFO_TAGE_NAME:String = "arrDamage";
      
      private const BUFF_ADD_TAGE_NAME:String = "enBuffer";
      
      private const BUFF_REMOVE_TAGE_NAME:String = "deBuffer";
      
      private const MAGIC_START_TARGET_ID:String = "defender";
      
      private const BUFF:String = "buffer";
      
      private const TAGE_TAG:String = "rage";
      
      public function DefenseADT()
      {
         super();
      }
      
      private function parse() : void
      {
         var _loc1_:Array = this._defenseData["arrDamage"];
         this.setValue(_loc1_);
         this._actionType = this._defenseData.reaction;
         this._deathSkillId = !!this._defenseData.hasOwnProperty("deathSkill") ? uint(this._defenseData["deathSkill"]) : 0;
         this._buffInfo = new BuffADT(this._defenseData);
      }
      
      private function setValue(param1:Array) : void
      {
         var _loc4_:uint = 0;
         var _loc2_:uint = 0;
         var _loc3_:Object = null;
         if(param1)
         {
            _loc4_ = param1.length;
            _loc2_ = 0;
            while(_loc2_ < _loc4_)
            {
               _loc3_ = param1[_loc2_];
               this.setDamageData(_loc3_["damageType"],_loc3_["damageValue"]);
               trace("被攻击者:" + this._defenseData["defender"] + " " + "damageType=" + _loc3_["damageType"] + " damageValue=" + _loc3_["damageValue"]);
               _loc2_++;
            }
         }
      }
      
      private function setDamageData(param1:uint, param2:int) : void
      {
         switch(int(param1) - 1)
         {
            case 0:
               this._normalDamage += param2;
               break;
            case 1:
               this._killDamage = param2;
               this._normalDamage += this._killDamage;
               break;
            case 2:
               this._magicDamage = param2;
               this._normalDamage += this._magicDamage;
               break;
            case 3:
               this._windDamage = param2;
               break;
            case 4:
               this._thuderDamage = param2;
               break;
            case 5:
               this._waterDamage = param2;
               break;
            case 6:
               this._fireDamage = param2;
         }
         if(param2 != 0)
         {
            this._demageKindsNumber++;
            this._totalDamage += param2;
         }
      }
      
      public function get haveSkillDamageInfo() : Boolean
      {
         return this._defenseData.hasOwnProperty("arrDamage") || Boolean(this._defenseData.hasOwnProperty("deBuffer")) || Boolean(this._defenseData.hasOwnProperty("rage")) || Boolean(this._defenseData.hasOwnProperty("enBuffer")) || Boolean(this._defenseData.hasOwnProperty("buffer"));
      }
      
      public function setDefenseData(param1:Object, param2:uint) : void
      {
         if(param1 != this._defenseData)
         {
            this.reset();
            this._defenseData = param1;
            this.parse();
         }
         if(this._magicID != param2)
         {
            this._magicID = param2;
         }
      }
      
      public function get rage() : int
      {
         if(this._defenseData && this._defenseData.hasOwnProperty("rage"))
         {
            return int(this._defenseData["rage"]);
         }
         return 0;
      }
      
      public function resetRage() : void
      {
         if(this._defenseData && this._defenseData.hasOwnProperty("rage"))
         {
            delete this._defenseData["rage"];
         }
      }
      
      public function get uid() : uint
      {
         return !!this._defenseData ? uint(this._defenseData["defender"]) : 0;
      }
      
      public function getAttackValueByName(param1:uint) : int
      {
         var _loc3_:Array = null;
         var _loc2_:int = 0;
         if(this._defenseData && this._defenseData.hasOwnProperty("arrDamage"))
         {
            _loc3_ = this._defenseData["arrDamage"];
            this.setValue(_loc3_);
            _loc2_ = 0;
            if(_loc3_[param1])
            {
               _loc2_ = int(_loc3_[param1].value);
            }
            if(_loc2_ != 0)
            {
               this._demageKindsNumber++;
            }
            return _loc2_;
         }
         return 0;
      }
      
      public function reset() : void
      {
         this._fireDamage = 0;
         this._windDamage = 0;
         this._waterDamage = 0;
         this._thuderDamage = 0;
         this._normalDamage = 0;
         this._totalDamage = 0;
         this._demageKindsNumber = 0;
         this._defenseData = null;
      }
      
      public function dispose() : void
      {
      }
      
      public function get attackKindsNumber() : uint
      {
         return this._demageKindsNumber;
      }
      
      public function get totalHurt() : int
      {
         return this._totalDamage;
      }
      
      public function get allDamage() : int
      {
         var _loc1_:int = 0;
         if(this._buffInfo && this._buffInfo.hasBuffInfo)
         {
            _loc1_ = this._buffInfo.allBuffDamage;
         }
         return this._totalDamage + _loc1_;
      }
      
      public function get fireDamage() : int
      {
         return this._fireDamage;
      }
      
      public function get windDamage() : int
      {
         return this._windDamage;
      }
      
      public function get waterDamage() : int
      {
         return this._waterDamage;
      }
      
      public function get thuderDamage() : int
      {
         return this._thuderDamage;
      }
      
      public function get normalDamage() : int
      {
         return this._normalDamage;
      }
      
      public function get magicID() : uint
      {
         return this._magicID;
      }
      
      public function get actionType() : uint
      {
         return this._actionType;
      }
      
      public function set actionType(param1:uint) : void
      {
         this._actionType = param1;
      }
      
      public function get buffInfo() : BuffADT
      {
         return this._buffInfo;
      }
      
      public function get fatal() : Boolean
      {
         if(this._defenseData && this._defenseData.hasOwnProperty("fatal"))
         {
            return true;
         }
         return false;
      }
      
      public function get deathSkillId() : uint
      {
         return this._deathSkillId;
      }
   }
}

