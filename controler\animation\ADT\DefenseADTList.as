package controler.animation.ADT
{
   import controler.FightCommandParser;
   import sourceManager.SourceManager;
   
   public class DefenseADTList
   {
      private var _data:Object;
      
      private var _list:Vector.<DefenseADT>;
      
      private var _skillId:uint;
      
      private var _skillStartTargetID:uint;
      
      private var _rage:int;
      
      private var _subDenfseCommand:Vector.<DefenseADTList>;
      
      private const RECATION_TAG:String = "arrReaction";
      
      private const SKILL_TAG:String = "action";
      
      private const SKILL_START_ID:String = "defender";
      
      private const BUFF_ADD_TAGE_NAME:String = "arrEnBuffer";
      
      private const BUFF_REMOVE_TAGE_NAME:String = "arrDebuffer";
      
      private const RAGE_TAGE_NAME:String = "rage";
      
      private const TAG_SUB_SKILL:String = "arrChild";
      
      private const DEATH_SKILL:String = "deathSkill";
      
      private const REBIRTH:String = "rebirth";
      
      public function DefenseADTList()
      {
         super();
      }
      
      private function reset() : void
      {
         this._list = new Vector.<DefenseADT>();
         this._data = null;
         if(this._subDenfseCommand)
         {
            this._subDenfseCommand.length = 0;
         }
      }
      
      private function parse() : void
      {
         var _loc9_:Array = null;
         var _loc7_:uint = 0;
         var _loc8_:uint = 0;
         var _loc13_:DefenseADT = null;
         var _loc14_:Array = null;
         var _loc17_:int = 0;
         var _loc15_:Array = null;
         var _loc16_:Object = null;
         var _loc2_:DefenseADTList = null;
         var _loc4_:uint = 0;
         var _loc3_:uint = 0;
         var _loc5_:DefenseADTList = null;
         var _loc1_:Array = null;
         var _loc11_:int = 0;
         var _loc12_:Object = null;
         var _loc6_:DefenseADTList = null;
         var _loc10_:FightCommandParser = SourceManager.instance.commandData;
         if(this._data)
         {
            if(this._data.hasOwnProperty("round"))
            {
               trace("=============================" + this._data.round);
            }
            if(this._data.hasOwnProperty("defender"))
            {
               this._skillStartTargetID = uint(this._data["defender"]);
            }
            if(this._data.hasOwnProperty("action"))
            {
               this._skillId = uint(this._data["action"]);
            }
            if(this._data.hasOwnProperty("rage"))
            {
               this._rage = int(this._data["rage"]);
            }
            else
            {
               this._rage = 0;
            }
            if(this._data.hasOwnProperty("arrReaction"))
            {
               _loc9_ = this._data["arrReaction"] as Array;
               _loc7_ = _loc9_.length;
               _loc8_ = 0;
               while(_loc8_ < _loc7_)
               {
                  _loc13_ = new DefenseADT();
                  _loc13_.setDefenseData(_loc9_[_loc8_],this._skillId);
                  this._list.push(_loc13_);
                  _loc8_++;
               }
            }
            if(this.hasComplexSkill)
            {
               if(this._subDenfseCommand)
               {
                  this._subDenfseCommand.length = 0;
               }
               else
               {
                  this._subDenfseCommand = new Vector.<DefenseADTList>();
               }
               _loc14_ = this.deathSkillIds();
               for each(_loc17_ in _loc14_)
               {
                  _loc16_ = _loc10_.findDeathSkillData(_loc17_);
                  if(_loc16_)
                  {
                     _loc2_ = new DefenseADTList();
                     _loc2_.data = _loc16_;
                     this._subDenfseCommand.push(_loc2_);
                  }
               }
               _loc15_ = this._data["arrChild"];
               if(_loc15_ && _loc15_.length > 0)
               {
                  _loc4_ = _loc15_.length;
                  _loc3_ = 0;
                  while(_loc3_ < _loc4_)
                  {
                     _loc5_ = new DefenseADTList();
                     _loc5_.data = _loc15_[_loc3_];
                     this._subDenfseCommand.push(_loc5_);
                     _loc1_ = _loc5_.deathSkillIds();
                     for each(_loc11_ in _loc1_)
                     {
                        _loc12_ = _loc10_.findDeathSkillData(_loc11_);
                        if(_loc12_)
                        {
                           _loc6_ = new DefenseADTList();
                           _loc6_.data = _loc12_;
                           this._subDenfseCommand.push(_loc6_);
                        }
                     }
                     _loc3_++;
                  }
               }
            }
         }
      }
      
      public function get hasDefenseAction() : Boolean
      {
         return this._data.hasOwnProperty("arrReaction");
      }
      
      public function get hasComplexSkill() : Boolean
      {
         return this._data && this._data.hasOwnProperty("arrChild");
      }
      
      public function set data(param1:Object) : void
      {
         this.reset();
         this._data = param1;
         this.parse();
      }
      
      public function get data() : Object
      {
         return this._data;
      }
      
      public function get list() : Vector.<DefenseADT>
      {
         var _loc3_:uint = this._list.length;
         var _loc2_:Vector.<DefenseADT> = new Vector.<DefenseADT>();
         var _loc1_:uint = 0;
         while(_loc1_ < _loc3_)
         {
            _loc2_.push(this._list[_loc1_]);
            _loc1_++;
         }
         return _loc2_;
      }
      
      public function getCurrentLevelPersonList() : Object
      {
         var _loc2_:int = 0;
         var _loc4_:Object = {};
         var _loc3_:int = int(this._list.length);
         var _loc1_:int = 0;
         while(_loc1_ < _loc3_)
         {
            _loc2_ = 0;
            if(_loc4_[this._list[_loc1_].uid] != null)
            {
               _loc2_ = int(_loc4_[this._list[_loc1_].uid]);
            }
            _loc4_[this._list[_loc1_].uid] = _loc2_ + this._list[_loc1_].allDamage;
            _loc1_++;
         }
         return _loc4_;
      }
      
      public function getAllChildrenSkillList() : Array
      {
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         var _loc1_:Array = null;
         var _loc2_:int = 0;
         var _loc6_:Array = [];
         if(this.currentLevelSkill > 0)
         {
            _loc6_.push(this.currentLevelSkill);
         }
         if(this.subDenfseCommand && this.subDenfseCommand.length > 0)
         {
            _loc5_ = int(this.subDenfseCommand.length);
            _loc3_ = 0;
            _loc4_ = 0;
            while(_loc4_ < _loc5_)
            {
               _loc1_ = this.subDenfseCommand[_loc4_].getAllChildrenSkillList();
               _loc2_ = 0;
               while(_loc2_ < _loc1_.length)
               {
                  _loc3_ = int(_loc1_[_loc2_]);
                  if(_loc3_ > 0 && _loc6_.indexOf(_loc3_) < 0)
                  {
                     _loc6_.push(_loc3_);
                  }
                  _loc2_++;
               }
               _loc4_++;
            }
         }
         return _loc6_;
      }
      
      public function getAllChildrenPersonList() : Object
      {
         var _loc5_:int = 0;
         var _loc1_:int = 0;
         var _loc2_:Object = null;
         var _loc3_:* = null;
         var _loc7_:Object = {};
         var _loc6_:int = int(this._list.length);
         var _loc4_:int = 0;
         while(_loc4_ < _loc6_)
         {
            _loc5_ = 0;
            if(_loc7_[this._list[_loc4_].uid] != null)
            {
               _loc5_ = int(_loc7_[this._list[_loc4_].uid]);
            }
            _loc7_[this._list[_loc4_].uid] = _loc5_ + this._list[_loc4_].allDamage;
            _loc5_ = 0;
            _loc4_++;
         }
         if(this.subDenfseCommand && this.subDenfseCommand.length >= 1)
         {
            _loc6_ = int(this.subDenfseCommand.length);
            _loc1_ = 0;
            while(_loc1_ < _loc6_)
            {
               _loc2_ = this.subDenfseCommand[_loc1_].getCurrentLevelPersonList();
               for(_loc3_ in _loc2_)
               {
                  if(_loc7_[_loc3_] != null)
                  {
                     _loc5_ = int(_loc7_[_loc3_]);
                  }
                  _loc7_[_loc3_] = _loc5_ + _loc2_[_loc3_];
                  _loc5_ = 0;
               }
               _loc1_++;
            }
         }
         return _loc7_;
      }
      
      public function get beAttackList() : Vector.<DefenseADT>
      {
         var _loc3_:uint = this._list.length;
         var _loc2_:Vector.<DefenseADT> = new Vector.<DefenseADT>();
         var _loc1_:uint = 0;
         while(_loc1_ < _loc3_)
         {
            if(this._list[_loc1_].haveSkillDamageInfo || this._list[_loc1_].actionType == 2)
            {
               _loc2_.push(this._list[_loc1_]);
            }
            _loc1_++;
         }
         return _loc2_;
      }
      
      public function get skillStartTargetID() : uint
      {
         return this._skillStartTargetID;
      }
      
      public function get rage() : int
      {
         return this._rage;
      }
      
      public function get subDenfseCommand() : Vector.<DefenseADTList>
      {
         return this._subDenfseCommand;
      }
      
      public function get currentLevelSkill() : int
      {
         return this._skillId;
      }
      
      public function deathSkillIds() : Array
      {
         var _loc2_:DefenseADT = null;
         var _loc4_:int = int(this._list.length);
         var _loc3_:Array = [];
         var _loc1_:int = 0;
         while(_loc1_ < _loc4_)
         {
            _loc2_ = this._list[_loc1_] as DefenseADT;
            if(_loc2_.deathSkillId != 0)
            {
               _loc3_.push(_loc2_.uid);
            }
            _loc1_++;
         }
         return _loc3_;
      }
      
      public function isHasBench() : Boolean
      {
         var _loc1_:AttackADT = null;
         var _loc2_:Array = null;
         var _loc4_:uint = this._subDenfseCommand.length;
         var _loc3_:uint = 0;
         while(_loc3_ < _loc4_)
         {
            _loc1_ = new AttackADT();
            _loc1_.attackData = this._subDenfseCommand[_loc3_].data;
            _loc2_ = _loc1_.benchInfoArr;
            if(_loc2_ && _loc2_.length > 0)
            {
               return true;
            }
            _loc3_++;
         }
         return false;
      }
      
      public function isHasReborn() : Boolean
      {
         var _loc1_:AttackADT = null;
         var _loc2_:Array = null;
         var _loc4_:uint = this._subDenfseCommand.length;
         var _loc3_:uint = 0;
         while(_loc3_ < _loc4_)
         {
            _loc1_ = new AttackADT();
            _loc1_.attackData = this._subDenfseCommand[_loc3_].data;
            _loc2_ = _loc1_.rebornInfoArr;
            if(_loc2_ && _loc2_.length > 0)
            {
               return true;
            }
            _loc3_++;
         }
         return false;
      }
   }
}

