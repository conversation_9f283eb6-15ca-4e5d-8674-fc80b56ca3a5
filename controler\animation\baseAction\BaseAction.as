package controler.animation.baseAction
{
   import display.IRenderAble;
   import display.RenderManager;
   import flash.display.DisplayObjectContainer;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import flash.utils.getQualifiedClassName;
   import model.PlayerListModel;
   import sourceManager.SourceManager;
   
   public class BaseAction extends EventDispatcher implements IAction, IRenderAble
   {
      protected var _preEnd:Boolean;
      
      protected var _deepStart:int = -1;
      
      protected var _yIndex:int;
      
      protected var _animationDeep:uint;
      
      protected var _deepFixTarget:BaseAction;
      
      protected var _deepFixContainer:DisplayObjectContainer;
      
      protected var _upTarget:Boolean = true;
      
      protected var _hasDispatched:Boolean;
      
      private var _autoDispose:Boolean;
      
      public var nickName:String = "";
      
      public function BaseAction()
      {
         super();
      }
      
      public function start() : void
      {
      }
      
      public function stop() : void
      {
      }
      
      public function resume() : void
      {
      }
      
      protected function addListeners(param1:IEventDispatcher) : void
      {
         if(param1)
         {
            param1.addEventListener("BASE_ACTION_END",this.handleActionEnd);
            param1.addEventListener("BASE_ACTION_PRE_END",this.handleActionPreEnd);
         }
      }
      
      protected function removeListeners(param1:IEventDispatcher) : void
      {
         if(param1)
         {
            param1.removeEventListener("BASE_ACTION_END",this.handleActionEnd);
            param1.removeEventListener("BASE_ACTION_PRE_END",this.handleActionPreEnd);
         }
      }
      
      protected function handleActionEnd(param1:Event) : void
      {
         this.removeListeners(param1.target as IEventDispatcher);
         param1.stopImmediatePropagation();
         this.dispatchCompleteEvent();
      }
      
      protected function handleActionPreEnd(param1:Event) : void
      {
         param1.stopImmediatePropagation();
         (param1.target as IEventDispatcher).removeEventListener("BASE_ACTION_PRE_END",this.handleActionPreEnd);
         this.dispatchPreCompleteEvent();
      }
      
      protected function dispatchCompleteEvent() : void
      {
         if(this._autoDispose)
         {
            this.dispose();
         }
         this.dispatchEvent(new Event("BASE_ACTION_END"));
      }
      
      protected function dispatchPreCompleteEvent() : void
      {
         this._preEnd = true;
         this.dispatchEvent(new Event("BASE_ACTION_PRE_END"));
      }
      
      public function get actionName() : String
      {
         return getQualifiedClassName(this);
      }
      
      public function traceActionName() : void
      {
      }
      
      public function upDate() : void
      {
      }
      
      public function dispose() : void
      {
         this._deepFixContainer = null;
         this._deepFixTarget = null;
      }
      
      public function get deepStart() : uint
      {
         this._deepStart = PlayerListModel.instance.getPlayerLineMaxDeep(this.yIndex);
         return this._deepStart;
      }
      
      public function set deepStart(param1:uint) : void
      {
         this._deepStart = param1;
      }
      
      public function set yIndex(param1:int) : void
      {
         this._yIndex = param1;
      }
      
      public function get yIndex() : int
      {
         return this._yIndex;
      }
      
      protected function refreshDeep() : void
      {
         var _loc1_:int = int(RenderManager.instance.getSameLineAnmiationNumber(this.yIndex));
         if(this._deepFixContainer && this._deepFixContainer.parent)
         {
            this._animationDeep = this._deepFixContainer.parent.getChildIndex(this._deepFixContainer) + 1;
            if(this.upTarget)
            {
               this._animationDeep++;
            }
            if(this._deepFixContainer.parent && this._deepFixContainer.parent.numChildren - 1 >= this._animationDeep + _loc1_)
            {
               this._animationDeep += _loc1_;
            }
         }
         else if(this._deepFixTarget)
         {
            this._animationDeep = this._deepFixTarget.animationDeep;
            if(this.upTarget)
            {
               this._animationDeep++;
            }
            if(SourceManager.instance.warLayer.numChildren - 1 >= this._animationDeep + _loc1_)
            {
               this._animationDeep += _loc1_;
            }
         }
         else
         {
            if(_loc1_ <= 0)
            {
               _loc1_ = 1;
            }
            this._animationDeep = this.deepStart + _loc1_;
         }
      }
      
      public function get animationDeep() : uint
      {
         return this._animationDeep;
      }
      
      public function get deepFixTarget() : BaseAction
      {
         return this._deepFixTarget;
      }
      
      public function set deepFixTarget(param1:BaseAction) : void
      {
         this._deepFixTarget = param1;
      }
      
      public function set upTarget(param1:Boolean) : void
      {
         this._upTarget = param1;
      }
      
      public function get upTarget() : Boolean
      {
         return this._upTarget;
      }
      
      public function get deepFixContainer() : DisplayObjectContainer
      {
         return this._deepFixContainer;
      }
      
      public function set deepFixContainer(param1:DisplayObjectContainer) : void
      {
         this._deepFixContainer = param1;
      }
      
      public function get hasDispatched() : Boolean
      {
         return this._hasDispatched;
      }
      
      public function set hasDispatched(param1:Boolean) : void
      {
         this._hasDispatched = param1;
      }
      
      public function set animationDeep(param1:uint) : void
      {
         this._animationDeep = param1;
      }
   }
}

