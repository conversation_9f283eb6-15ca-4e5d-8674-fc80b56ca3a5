package controler.animation.control
{
   import controler.animation.baseAction.IAction;
   import flash.events.Event;
   import flash.events.IEventDispatcher;
   
   public class BaseActionCollection
   {
      private var list:Vector.<IAction> = new Vector.<IAction>();
      
      public function BaseActionCollection()
      {
         super();
      }
      
      public function pushOneCommand(param1:IAction) : void
      {
         if(param1)
         {
            this.list.push(param1);
            (param1 as IEventDispatcher).addEventListener("BASE_ACTION_END",this.onEnd);
         }
      }
      
      private function onEnd(param1:Event) : void
      {
         var _loc2_:int = int(this.list.indexOf(param1.target as IAction));
         if(_loc2_ >= 0)
         {
            (param1.target as IAction).dispose();
            (param1.target as IEventDispatcher).removeEventListener("BASE_ACTION_END",this.onEnd);
            this.list.splice(_loc2_,1);
         }
      }
      
      public function dispose() : void
      {
         var _loc2_:int = int(this.list.length);
         var _loc1_:int = 0;
         while(_loc1_ < _loc2_)
         {
            (this.list[_loc1_] as IEventDispatcher).removeEventListener("BASE_ACTION_END",this.onEnd);
            this.list[_loc1_].dispose();
            _loc1_++;
         }
         this.list.length = 0;
      }
   }
}

