package copyHookOn.command
{
   import game.modules.onhook.proxy.CopyOnHookProxy;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class CopyOnHookCommand extends SimpleCommand
   {
      private var _proxy:CopyOnHookProxy;
      
      public function CopyOnHookCommand()
      {
         super();
         this._proxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc2_:Array = param1.getBody() as Array;
         switch(param1.getName())
         {
            case "CS_ENTER_COPY_HOOKON":
               this._proxy.onEnterAttack(_loc2_[0]);
               break;
            case "CS_START_ATTACK":
               this._proxy.onStartAttack(_loc2_[0],_loc2_[1],_loc2_[2]);
               break;
            case "CS_UPDATE_REWARD_INFO":
               this._proxy.onAttacking();
               break;
            case "CS_END_ATTACK_BYGOLD":
               this._proxy.onEndGoldAttack();
               break;
            case "CS_ONCE_ATTACK_BYGOLD":
               this._proxy.onOnceGoldAttack();
               break;
            case "CS_CANCEl_ATTACK":
               this._proxy.onCancelAttack();
         }
      }
   }
}

