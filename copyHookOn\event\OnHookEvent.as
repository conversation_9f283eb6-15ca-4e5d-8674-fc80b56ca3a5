package copyHookOn.event
{
   import flash.events.Event;
   
   public class OnHookEvent extends Event
   {
      public static const SELECT_ARMY:String = "SelectArmy";
      
      public static const CONTINUE_ATTACK:String = "ContinueAttack";
      
      public static const START_ATTACK:String = "StartAttack";
      
      public static const ITEM_SELECT:String = "ItemSelect";
      
      public static const CANCEL_HOOK:String = "Cancel";
      
      private var _armyId:int;
      
      public function OnHookEvent(param1:String, param2:int)
      {
         this._armyId = param2;
         super(param1);
      }
      
      public function get id() : int
      {
         return this._armyId;
      }
      
      override public function clone() : Event
      {
         return new OnHookEvent(type,this.id);
      }
      
      override public function toString() : String
      {
         return formatToString("HookOnEvent");
      }
   }
}

