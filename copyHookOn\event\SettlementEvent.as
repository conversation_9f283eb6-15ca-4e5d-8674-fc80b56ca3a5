package copyHookOn.event
{
   import flash.events.Event;
   
   public class SettlementEvent extends Event
   {
      public static const SETTLEMENT_ATTACK:String = "SettlementAttack";
      
      public static const IMMEDIATELY_DONE:String = "ImmediatelyDone";
      
      public static const ONCE_DONE:String = "OnceDone";
      
      public static const CANCEL_HOOKON:String = "CancelHookOn";
      
      public static const DONE_AUTO:String = "DoneHookOn";
      
      public static const SETTLE_CONFIRM:String = "SelectConfirm";
      
      public var num:int = 0;
      
      public function SettlementEvent(param1:String, param2:int = 0)
      {
         super(param1);
         this.num = param2;
      }
      
      override public function clone() : Event
      {
         return new SettlementEvent(type);
      }
      
      override public function toString() : String
      {
         return formatToString("SettlementEvent");
      }
   }
}

