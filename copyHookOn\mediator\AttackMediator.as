package copyHookOn.mediator
{
   import copyHookOn.command.CopyOnHookCommand;
   import copyHookOn.event.SettlementEvent;
   import copyHookOn.view.AttackingWindow;
   import flash.events.Event;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.modules.onhook.proxy.CopyOnHookProxy;
   import game.mvc.module.ModuleParams;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.event.WindowEvent;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   
   public class AttackMediator extends PirateMediator
   {
      public static const NAME:String = "AttackMediator";
      
      private var _attackingWindow:AttackingWindow;
      
      private var _enterType:Boolean = false;
      
      private var _selAttackNum:int;
      
      private var _selArmyId:int;
      
      private var _selCopyId:int;
      
      private var _offLineTimes:int = 0;
      
      public function AttackMediator(param1:Object = null)
      {
         super("AttackMediator",param1);
         this._attackingWindow = param1 as AttackingWindow;
         this._attackingWindow.showHander = this.showHandler;
         this._attackingWindow.addEventListener("SettlementAttack",this.attackingHandler);
         this._attackingWindow.addEventListener("ImmediatelyDone",this.immediatelyHandler);
         this._attackingWindow.addEventListener("OnceDone",this.onceDoneHandler);
         this._attackingWindow.addEventListener("CancelHookOn",this.cancelHookOnHandler);
         this._attackingWindow.addEventListener("DoneHookOn",this.doneAutoHandler);
      }
      
      private function showHandler(param1:Object) : void
      {
         this._selArmyId = int(param1[0]);
         this._selAttackNum = int(param1[1]);
         this._enterType = Boolean(param1[2]);
         this._selCopyId = int(param1[3]);
         this._offLineTimes = int(param1[4]);
         checkDataAvialable(this.onComplete);
      }
      
      private function onComplete() : void
      {
         this.setAttackInfo();
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().userData,MainData.getInstance().bagData,MainData.getInstance().groupData,MainData.getInstance().ownFormationsData,MainData.getInstance().closeGoldNoticeData];
      }
      
      private function setAttackInfo() : void
      {
         var _loc1_:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         this._attackingWindow.setArmyInfo(_loc1_.setAttackAimData(this._selArmyId));
         this._attackingWindow.setAttackStatus(_loc1_.setAttackData(this._selAttackNum,this._enterType),this._enterType);
      }
      
      private function attackingHandler(param1:SettlementEvent) : void
      {
         sendNotification("CS_UPDATE_REWARD_INFO");
      }
      
      private function immediatelyHandler(param1:SettlementEvent) : void
      {
         var needGold:int = 0;
         var userGold:int = 0;
         var check:Function = null;
         var event:SettlementEvent = param1;
         check = function():void
         {
            if(userGold >= needGold)
            {
               sendNotification("CS_END_ATTACK_BYGOLD");
            }
            else
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("copyOnHook.41"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px
               });
            }
         };
         var proxy:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         needGold = proxy.immeDoneNeedGold();
         userGold = MainData.getInstance().userData.gold_num;
         var tipsText:String = proxy.isableNumFull ? StringUtil.substitute(Globalization.getString("copyOnHook.40"),needGold) : StringUtil.substitute(Globalization.getString("copyOnHook.42"),needGold);
         if(proxy.isableNumFull)
         {
            if(MainData.getInstance().closeGoldNoticeData.isCloseGoldNotice(22))
            {
               check();
            }
            else
            {
               PopUpCenter.confirmWin(tipsText,check,null,0,true);
            }
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("copyOnHook.45"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
         }
      }
      
      private function onceDoneHandler(param1:SettlementEvent) : void
      {
         var needGold:int = 0;
         var userGold:int = 0;
         var check:Function = null;
         var event:SettlementEvent = param1;
         check = function():void
         {
            if(userGold >= needGold)
            {
               sendNotification("CS_ONCE_ATTACK_BYGOLD");
            }
            else
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("copyOnHook.44"),
                  "textFormat":TextFormatLib.format_0x00FF00_14px
               });
            }
         };
         var proxy:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         needGold = 5;
         userGold = MainData.getInstance().userData.gold_num;
         if(proxy.isableNumPer)
         {
            if(MainData.getInstance().closeGoldNoticeData.isCloseGoldNotice(26))
            {
               check();
            }
            else
            {
               PopUpCenter.confirmWin(StringUtil.substitute(Globalization.getString("copyOnHook.43"),needGold),check,null,0,true);
            }
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("copyOnHook.45"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
         }
      }
      
      private function cancelHookOnHandler(param1:SettlementEvent) : void
      {
         var event:SettlementEvent = param1;
         PopUpCenter.confirmWin(Globalization.getString("copyOnHook.46"),function():void
         {
            sendNotification("CS_CANCEl_ATTACK");
         },null,0,true);
      }
      
      private function doneAutoHandler(param1:SettlementEvent) : void
      {
         var _loc2_:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
      }
      
      private function updateAttackRewardInfo() : void
      {
         var _loc1_:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         this._attackingWindow.setAttackStatus(_loc1_.setAttackData(this._selAttackNum,false),false);
      }
      
      private function doneOnHook() : void
      {
         sendNotification("HANDLE_MODULE",new ModuleParams("CopyHookOnOver",ModuleParams.act_Open,[1,0,this._selAttackNum]));
         if(GameScene.isOpenFullScreenScene)
         {
            PopUpCenter.getWinByName("CopyHookOnOver").visible = false;
         }
         this._attackingWindow.stopTimer();
         this._attackingWindow.dispatchEvent(new Event(WindowEvent.Window_Close));
      }
      
      private function cancelOnHook() : void
      {
         var _loc1_:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         this._attackingWindow.stopTimer();
         this._attackingWindow.dispatchEvent(new Event(WindowEvent.Window_Close));
         if(_loc1_.alreadyAttackTimes > 0)
         {
            sendNotification("HANDLE_MODULE",new ModuleParams("CopyHookOnOver",ModuleParams.act_Open,[1,0,_loc1_.alreadyAttackTimes]));
         }
         else
         {
            sendNotification("HANDLE_MODULE",new ModuleParams("CopyHookOn",ModuleParams.act_Open,{"copyId":this._selCopyId}));
         }
      }
      
      override public function onRegister() : void
      {
         facade.registerCommand("CS_UPDATE_REWARD_INFO",CopyOnHookCommand);
         facade.registerCommand("CS_END_ATTACK_BYGOLD",CopyOnHookCommand);
         facade.registerCommand("CS_ONCE_ATTACK_BYGOLD",CopyOnHookCommand);
         facade.registerCommand("CS_CANCEl_ATTACK",CopyOnHookCommand);
         facade.registerCommand("CS_OFF_LINE_HOOKON",CopyOnHookCommand);
      }
      
      override public function onRemove() : void
      {
         facade.removeCommand("CS_UPDATE_REWARD_INFO");
         facade.removeCommand("CS_END_ATTACK_BYGOLD");
         facade.removeCommand("CS_ONCE_ATTACK_BYGOLD");
         facade.removeCommand("CS_CANCEl_ATTACK");
         facade.removeCommand("CS_OFF_LINE_HOOKON");
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         switch(param1.getName())
         {
            case "SC_SELARMY_REWARD_ITEM":
               this.updateAttackRewardInfo();
               break;
            case "SC_END_ATTACK_BYGOLD":
               this.doneOnHook();
               break;
            case "SC_CANCEL_ATTACK":
               this.cancelOnHook();
         }
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_SELARMY_REWARD_ITEM","SC_END_ATTACK_BYGOLD","SC_CANCEL_ATTACK"];
      }
   }
}

