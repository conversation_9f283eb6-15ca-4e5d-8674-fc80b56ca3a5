package copyHookOn.mediator
{
   import copyHookOn.command.CopyOnHookCommand;
   import copyHookOn.event.OnHookEvent;
   import copyHookOn.view.CopyOnHookWindow;
   import copyHookOn.view.StartWindow;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.modules.onhook.data.ArmyData;
   import game.modules.onhook.proxy.CopyOnHookProxy;
   import game.modules.task.model.TaskTools;
   import game.mvc.module.ModuleParams;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   
   public class CopyOnHookMediator extends PirateMediator
   {
      public static const NAME:String = "CopyHookOnMediator";
      
      private var _copyHookOnWindow:CopyOnHookWindow;
      
      private var _startWindow:StartWindow;
      
      private var _copyId:int = 0;
      
      private var _defaultArmyId:int = 0;
      
      private var _defaultCount:int = 1;
      
      private var _taskArmyArr:Array;
      
      private var _selectArmyId:int = -1;
      
      private var _selectAttackNum:int;
      
      public function CopyOnHookMediator(param1:Object = null)
      {
         super("CopyHookOnMediator",param1);
         this._copyHookOnWindow = param1 as CopyOnHookWindow;
         this._copyHookOnWindow.showHander = this.showHandler;
         this._copyHookOnWindow.addEventListener("SelectArmy",this.selectArmyHandler);
         this._copyHookOnWindow.addEventListener("ContinueAttack",this.continueAttackHandler);
         this._startWindow = this._copyHookOnWindow.startWindow;
         this._startWindow.addEventListener("StartAttack",this.startAttackHandler);
         this._startWindow.addEventListener("ItemSelect",this.comboBoxSelHandler);
         this._copyHookOnWindow.copyOnHookUI.changeCopyFun = this.changeCopyHandler;
      }
      
      private function showHandler(param1:Object) : void
      {
         this._copyId = param1.copyId as int;
         this._taskArmyArr = TaskTools.copyArmyTask(this._copyId);
         this._taskArmyArr.sortOn("armyId");
         if(param1.armyId > 0)
         {
            this._defaultArmyId = param1.armyId as int;
            this._defaultCount = param1.count as int;
         }
         else if(this._taskArmyArr.length != 0)
         {
            this._defaultArmyId = this._taskArmyArr[0].armyId;
            this._defaultCount = this._taskArmyArr[0].surplusCount;
         }
         else
         {
            this._defaultArmyId = 0;
            this._defaultCount = 1;
         }
         checkDataAvialable(this.onComplete);
      }
      
      private function onComplete() : void
      {
         sendNotification("CS_ENTER_COPY_HOOKON",[this._copyId]);
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().userData];
      }
      
      private function changeCopyHandler(param1:int) : void
      {
         this._copyId = param1;
         this._taskArmyArr = TaskTools.copyArmyTask(this._copyId);
         this._taskArmyArr.sortOn("armyId");
         if(this._taskArmyArr.length != 0)
         {
            this._defaultArmyId = this._taskArmyArr[0].armyId;
            this._defaultCount = this._taskArmyArr[0].surplusCount;
         }
         else
         {
            this._defaultArmyId = 0;
            this._defaultCount = 1;
         }
         sendNotification("CS_ENTER_COPY_HOOKON",[this._copyId]);
      }
      
      private function selectArmyHandler(param1:OnHookEvent) : void
      {
         var _loc2_:ArmyData = this.getSelArmyInfo(param1.id);
         if(_loc2_ != null)
         {
            this._copyHookOnWindow.setSelArmyAward(_loc2_);
         }
      }
      
      private function getSelArmyInfo(param1:int) : ArmyData
      {
         var _loc4_:ArmyData = null;
         var _loc2_:int = 0;
         var _loc5_:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         var _loc3_:Array = _loc5_.getCanOnHookArmys(this._copyId);
         if(param1 != -1)
         {
            _loc2_ = 0;
            while(_loc2_ < _loc3_.length)
            {
               _loc4_ = _loc3_[_loc2_];
               if(_loc4_ && _loc4_.armyId == param1)
               {
                  return _loc4_;
               }
               _loc2_++;
            }
         }
         return null;
      }
      
      private function continueAttackHandler(param1:OnHookEvent) : void
      {
         var _loc2_:CopyOnHookProxy = null;
         var _loc3_:Object = null;
         var _loc4_:int = 0;
         this._selectArmyId = param1.id;
         var _loc5_:int = 1;
         for each(_loc3_ in this._taskArmyArr)
         {
            if(_loc3_.armyId == this._selectArmyId)
            {
               _loc5_ = int(_loc3_.surplusCount);
               break;
            }
         }
         _loc4_ = MainData.getInstance().userData.cur_execution;
         _loc5_ = _loc4_ >= _loc5_ ? _loc5_ : _loc4_;
         _loc2_ = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         _loc2_.selArmyId = param1.id;
         PopUpCenter.addPopUp("copyHookOn.view.StartWindow",this._startWindow,true,true);
         MainData.getInstance().userData.bindSetter("cur_execution",this.changeCurExeHandler);
         this._startWindow.setStartAttackInfo(_loc2_.setAttackAimData(this._selectArmyId),0,_loc5_);
      }
      
      private function changeCurExeHandler(param1:int) : void
      {
         var _loc2_:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         this._startWindow.setStartAttackInfo(_loc2_.setAttackAimData(this._selectArmyId),1);
      }
      
      private function comboBoxSelHandler(param1:OnHookEvent) : void
      {
         var _loc2_:CopyOnHookProxy = null;
         if(param1.id == 0)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("copyOnHook.47"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
         }
         else
         {
            _loc2_ = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
            this._startWindow.setStartAttackInfo(_loc2_.setAttackAimData(this._selectArmyId),1);
         }
      }
      
      private function cancelAttackHandler(param1:OnHookEvent) : void
      {
         PopUpCenter.removePopUp(this._startWindow.name);
      }
      
      private function startAttackHandler(param1:OnHookEvent) : void
      {
         var _loc2_:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         this._selectAttackNum = param1.id;
         sendNotification("CS_START_ATTACK",[this._copyId,this._selectArmyId,this._selectAttackNum]);
      }
      
      private function startAttack() : void
      {
         sendNotification("HANDLE_MODULE",new ModuleParams("CopyHookOnAttack",ModuleParams.act_Open,[this._selectArmyId,this._selectAttackNum,0,this._copyId,0],true));
         this.doneHookOn();
      }
      
      private function doneHookOn() : void
      {
         PopUpCenter.removePopUp(this._startWindow.name);
         this._copyHookOnWindow.close();
         MainData.getInstance().userData.unBindSetter("cur_execution",this.changeCurExeHandler);
      }
      
      override public function onRegister() : void
      {
         facade.registerCommand("CS_ENTER_COPY_HOOKON",CopyOnHookCommand);
         facade.registerCommand("CS_START_ATTACK",CopyOnHookCommand);
      }
      
      override public function onRemove() : void
      {
         facade.removeCommand("CS_ENTER_COPY_HOOKON");
         facade.removeCommand("CS_START_ATTACK");
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc2_:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         switch(param1.getName())
         {
            case "SC_ENTER_COPY_HOOKON":
               this._copyHookOnWindow.setOnHookInfo(_loc2_.getCanOnHookArmys(this._copyId),_loc2_.isOnHookAll,this._defaultArmyId);
               break;
            case "SC_START_ATTACK":
               this.startAttack();
         }
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_START_ATTACK","SC_ENTER_COPY_HOOKON"];
      }
   }
}

