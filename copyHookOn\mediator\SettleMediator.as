package copyHookOn.mediator
{
   import copyHookOn.event.SettlementEvent;
   import copyHookOn.view.SettleWindow;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.modules.onhook.data.ArmyData;
   import game.modules.onhook.proxy.CopyOnHookProxy;
   
   public class SettleMediator extends PirateMediator
   {
      public static const NAME:String = "SettleMediator";
      
      private var _settleWindow:SettleWindow;
      
      private var _type:int = 0;
      
      private var _offLineTimes:int = 0;
      
      private var _num:int;
      
      public function SettleMediator(param1:Object = null)
      {
         super("SettleMediator",param1);
         this._settleWindow = param1 as SettleWindow;
         this._settleWindow.showHander = this.showHandler;
         this._settleWindow.addEventListener("SelectConfirm",this.settleHandler);
      }
      
      private function showHandler(param1:Object) : void
      {
         this._type = param1[0];
         this._offLineTimes = param1[1];
         this._num = param1[2];
         checkDataAvialable(this.onComplete);
      }
      
      private function onComplete() : void
      {
         this.setSettlementInfo();
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().userData,MainData.getInstance().bagData,MainData.getInstance().groupData,MainData.getInstance().ownFormationsData];
      }
      
      private function setSettlementInfo() : void
      {
         var _loc3_:int = 0;
         var _loc1_:int = 0;
         var _loc5_:CopyOnHookProxy = facade.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
         var _loc4_:ArmyData = _loc5_.setAttackAimData(_loc5_.selArmyId);
         var _loc2_:Array = _loc4_.defeatPlan.split("/");
         if(_loc2_.length >= 2)
         {
            _loc3_ = int(_loc2_[0]);
            _loc1_ = int(_loc2_[1]);
            _loc3_ = Math.min(_loc3_ + this._num,_loc1_);
            _loc4_.defeatPlan = _loc3_ + "/" + _loc1_;
         }
         this._settleWindow.setAttackedArmyInfo(_loc4_);
         this._settleWindow.setSettleInfo(_loc5_.setSettleData(this._type));
      }
      
      private function settleHandler(param1:SettlementEvent) : void
      {
         this._settleWindow.close();
      }
      
      override public function onRegister() : void
      {
      }
      
      override public function onRemove() : void
      {
      }
   }
}

