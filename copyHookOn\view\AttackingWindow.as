package copyHookOn.view
{
   import copyHookOn.event.SettlementEvent;
   import copyHookOn.mediator.AttackMediator;
   import copyHookOn.view.ui.AttackUI;
   import flash.events.Event;
   import flash.events.TimerEvent;
   import flash.utils.Timer;
   import game.modules.onhook.data.ArmyData;
   import game.modules.onhook.data.AttackData;
   import game.mvc.AppFacade;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class AttackingWindow extends PopUpWindow
   {
      public static const SETTLE_TIME:int = 120000;
      
      private var _attackUI:AttackUI;
      
      private var _settleTimer:Timer;
      
      public function AttackingWindow()
      {
         super(533,343);
         this.isLive = true;
         this.title = Globalization.getString("copyOnHook.4");
         this._attackUI = new AttackUI();
         pane.addChild(this._attackUI);
         this.init();
         AppFacade.instance.registerMediator(new AttackMediator(this));
      }
      
      private function init() : void
      {
         this._attackUI.setAttackView();
         this._attackUI.clickBtnFun = this.clickBtnHandler;
         this._settleTimer = new Timer(120000);
         this._settleTimer.addEventListener("timer",this.settleHandler);
      }
      
      private function clickBtnHandler(param1:Button) : void
      {
         switch(param1)
         {
            case this._attackUI.immediatelyBtn:
               this.dispatchEvent(new SettlementEvent("ImmediatelyDone"));
               break;
            case this._attackUI.onceBtn:
               this.dispatchEvent(new SettlementEvent("OnceDone"));
               break;
            case this._attackUI.cancelBtn:
               this.dispatchEvent(new SettlementEvent("CancelHookOn"));
         }
      }
      
      private function settleHandler(param1:TimerEvent) : void
      {
         if(this._settleTimer.currentCount >= 1)
         {
            this._settleTimer.delay = 120000;
         }
         this.dispatchEvent(new SettlementEvent("SettlementAttack"));
      }
      
      public function stopTimer() : void
      {
         this._settleTimer.stop();
         this._attackUI.countTimer.stop();
      }
      
      public function setArmyInfo(param1:ArmyData) : void
      {
         this._attackUI.setAttackArmyData(param1);
      }
      
      public function setAttackStatus(param1:AttackData, param2:Boolean) : void
      {
         if(param2)
         {
            this._settleTimer.delay = param1.reLgoinThisSurTime * 1000;
         }
         else
         {
            this._settleTimer.delay = 120000;
         }
         this._settleTimer.reset();
         this._settleTimer.start();
         this._attackUI.setAttackingInfo(param1,1);
      }
      
      override protected function onExitBtnClick(param1:Event) : void
      {
         this.dispatchEvent(new SettlementEvent("CancelHookOn"));
      }
      
      override public function close() : void
      {
      }
      
      override public function get posHeight() : Number
      {
         return 343;
      }
   }
}

