package copyHookOn.view
{
   import copyHookOn.event.OnHookEvent;
   import copyHookOn.mediator.CopyOnHookMediator;
   import copyHookOn.view.ui.CopyOnHookUI;
   import game.manager.UIManager;
   import game.modules.onhook.data.ArmyData;
   import game.mvc.AppFacade;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.window.PopUpWindow;
   
   public class CopyOnHookWindow extends PopUpWindow
   {
      public var copyOnHookUI:CopyOnHookUI;
      
      public var startWindow:StartWindow;
      
      public function CopyOnHookWindow()
      {
         super(533,370);
         this.isLive = false;
         this.setTitleImageData(UIManager.getUISkin("title_continueAttack").bitmapData);
         this.copyOnHookUI = new CopyOnHookUI();
         pane.addChild(this.copyOnHookUI);
         this.startWindow = new StartWindow();
         this.init();
         AppFacade.instance.registerMediator(new CopyOnHookMediator(this));
      }
      
      private function init() : void
      {
         this.copyOnHookUI.addEventListener("SelectArmy",this.selArmyHandler);
         this.copyOnHookUI.addEventListener("ContinueAttack",this.attackHandler);
      }
      
      private function selArmyHandler(param1:OnHookEvent) : void
      {
         this.dispatchEvent(new OnHookEvent("SelectArmy",param1.id));
      }
      
      private function attackHandler(param1:OnHookEvent) : void
      {
         this.dispatchEvent(new OnHookEvent("ContinueAttack",param1.id));
      }
      
      public function setOnHookInfo(param1:Array, param2:Boolean, param3:int = 0) : void
      {
         this.copyOnHookUI.setOnHookInfo(param1,param2,param3);
      }
      
      public function setSelArmyAward(param1:ArmyData) : void
      {
         this.copyOnHookUI.setArmyAwardInfo(param1);
      }
      
      override public function get posHeight() : Number
      {
         return 350;
      }
      
      override public function close() : void
      {
         super.close();
         if(PopUpCenter.containsWin("copyHookOn.view.StartWindow"))
         {
            this.startWindow.close();
         }
      }
      
      override public function dispose() : void
      {
         AppFacade.instance.removeMediator("CopyHookOnMediator");
         super.dispose();
      }
   }
}

