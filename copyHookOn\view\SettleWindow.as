package copyHookOn.view
{
   import copyHookOn.event.SettlementEvent;
   import copyHookOn.mediator.SettleMediator;
   import copyHookOn.view.ui.AttackUI;
   import game.modules.onhook.data.ArmyData;
   import game.modules.onhook.data.AttackData;
   import game.mvc.AppFacade;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class SettleWindow extends PopUpWindow
   {
      private var _settleUI:AttackUI;
      
      public function SettleWindow()
      {
         super(550,360);
         this.isLive = true;
         this.title = Globalization.getString("copyOnHook.3");
         this._settleUI = new AttackUI();
         pane.addChild(this._settleUI);
         this.init();
         AppFacade.instance.registerMediator(new SettleMediator(this));
      }
      
      private function init() : void
      {
         this._settleUI.setSettleView();
         this._settleUI.clickBtnFun = this.confirmHandler;
      }
      
      public function setAttackedArmyInfo(param1:ArmyData) : void
      {
         this._settleUI.setEndAttackArmyData(param1);
      }
      
      public function setSettleInfo(param1:AttackData) : void
      {
         this._settleUI.setAttackingInfo(param1,0);
      }
      
      public function confirmHandler(param1:Button) : void
      {
         this.dispatchEvent(new SettlementEvent("SelectConfirm"));
      }
      
      override public function get posHeight() : Number
      {
         return 360;
      }
   }
}

