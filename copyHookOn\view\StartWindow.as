package copyHookOn.view
{
   import copyHookOn.event.OnHookEvent;
   import copyHookOn.view.ui.StartUI;
   import game.modules.onhook.data.ArmyData;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class StartWindow extends PopUpWindow
   {
      public static const START_WIN_NAME:String = "copyHookOn.view.StartWindow";
      
      private var _startUI:StartUI;
      
      public function StartWindow()
      {
         super(354,336);
         this.title = Globalization.getString("copyOnHook.2");
         this.isLive = true;
         this._startUI = new StartUI();
         pane.addChild(this._startUI);
         this.init();
      }
      
      private function init() : void
      {
         this._startUI.addEventListener("ItemSelect",this.selNumHandler);
         this._startUI.addEventListener("StartAttack",this.startAtkHandler);
         this._startUI.addEventListener("Cancel",this.cancelAtkHandler);
      }
      
      public function setStartAttackInfo(param1:ArmyData, param2:int, param3:int = 1) : void
      {
         this._startUI.setAttackArmyInfo(param1,param2,param3);
      }
      
      private function selNumHandler(param1:OnHookEvent) : void
      {
         this.dispatchEvent(new OnHookEvent("ItemSelect",param1.id));
      }
      
      private function startAtkHandler(param1:OnHookEvent) : void
      {
         this.dispatchEvent(new OnHookEvent("StartAttack",param1.id));
      }
      
      private function cancelAtkHandler(param1:OnHookEvent) : void
      {
         this.close();
      }
   }
}

