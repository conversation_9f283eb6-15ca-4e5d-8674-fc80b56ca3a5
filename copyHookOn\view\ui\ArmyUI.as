package copyHookOn.view.ui
{
   import flash.display.Sprite;
   import flash.text.TextFormat;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   
   public class ArmyUI extends Sprite
   {
      public var armyIconBg:UISkin;
      
      public var armyIcon:Icon;
      
      public var armyLevel:Label;
      
      public var armyName:Label;
      
      public var armyId:int;
      
      public var isAttack:Boolean = true;
      
      public var armyPlan:Label;
      
      public function ArmyUI()
      {
         super();
         this.armyIconBg = UIManager.getUISkin("icon_bg");
         this.armyIconBg.width = 70;
         this.armyIconBg.height = 70;
         this.addChild(this.armyIconBg);
         this.armyIcon = new Icon();
         this.armyIcon.buttonMode = true;
         this.addChild(this.armyIcon);
         this.armyLevel = new Label("",TextFormatLib.format_Verdana_0xFFB932_12px,null,true);
         this.armyLevel.y = 72;
         this.armyLevel.autoSize = "center";
         this.armyLevel.width = 70;
         this.addChild(this.armyLevel);
         this.armyName = new Label("",TextFormatLib.format_0xFFB932_12px,null,true);
         this.armyName.y = 86;
         this.armyName.autoSize = "center";
         this.armyName.width = 70;
         this.addChild(this.armyName);
         var _loc1_:TextFormat = new TextFormat("Verdana",12,16711680);
         _loc1_.align = "right";
         this.armyPlan = new Label("08/88",_loc1_,null,true);
         this.armyPlan.defaultTextFormat = _loc1_;
         this.armyPlan.x = 30;
         this.armyPlan.y = 54;
         this.armyPlan.autoSize = "right";
         this.addChild(this.armyPlan);
         this.armyPlan.visible = false;
      }
      
      public function setArmyPlan(param1:String) : void
      {
         var _loc3_:Array = null;
         var _loc2_:TextFormat = null;
         try
         {
            _loc3_ = null;
            _loc2_ = null;
            if(param1 != null)
            {
               _loc3_ = param1.split("/");
               if(_loc3_.length >= 2)
               {
                  if(int(_loc3_[0]) >= int(_loc3_[1]))
                  {
                     this.armyPlan.textColor = 65280;
                  }
                  else
                  {
                     this.armyPlan.textColor = 16711680;
                  }
               }
               this.armyPlan.text = param1;
               this.armyPlan.visible = true;
            }
         }
         catch(e:Error)
         {
            trace("##默认不给88号怪");
         }
      }
   }
}

