package copyHookOn.view.ui
{
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.manager.UIManager;
   import game.modules.onhook.data.ArmyData;
   import game.modules.onhook.data.AttackData;
   import game.modules.onhook.proxy.CopyOnHookProxy;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   import mmo.utils.CountTimer;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class AttackUI extends UISprite
   {
      private var _awardSprite:Sprite;
      
      private var _awardBg:UISkin;
      
      private var _awardTitle:Label;
      
      private var _textBg1:UISkin;
      
      private var _textBg2:UISkin;
      
      private var _textBg3:UISkin;
      
      private var _bellyLabel:Label;
      
      private var _bellyValue:Label;
      
      private var _experienceLabel:Label;
      
      private var _experienceValue:Label;
      
      private var _expLabel:Label;
      
      private var _expValue:Label;
      
      private var _awardItemSprite:Sprite;
      
      private var _awardItemBg:UISkin;
      
      private var _awardItemTitle:Label;
      
      private var _awardItemScroll:ScrollPane;
      
      private var _itemContainer:Sprite;
      
      private var _attackSprite:Sprite;
      
      private var _attackBg:UISkin;
      
      private var _attackTitleBg:UISkin;
      
      private var _attackAimTitle:Label;
      
      private var _attackArmy:ArmyUI;
      
      private var _textBg4:UISkin;
      
      private var _textBg5:UISkin;
      
      private var _textBg6:UISkin;
      
      private var _textBg7:UISkin;
      
      private var _attackTitle:Label;
      
      private var _useTimeLabel:Label;
      
      private var _useTimeValue:Label;
      
      private var _attackNumLabel:Label;
      
      private var _attackNumValue:Label;
      
      private var _usePointLabel:Label;
      
      private var _usePointValue:Label;
      
      public var immediatelyBtn:Button;
      
      public var onceBtn:Button;
      
      public var cancelBtn:Button;
      
      public var settleConfirmBtn:Button;
      
      private var _descSprite:Sprite;
      
      private var _attackDescBg:UISkin;
      
      private var _descText1:Label;
      
      private var _descText2:Label;
      
      private var _descText3:Label;
      
      private var _descText4:Label;
      
      public var clickBtnFun:Function;
      
      public var countTimer:CountTimer;
      
      public function AttackUI()
      {
         super();
         this._awardSprite = new Sprite();
         this._awardSprite.x = 7;
         this.addChild(this._awardSprite);
         this._awardBg = UIManager.getUISkin("pane_bg");
         this._awardBg.width = 198;
         this._awardBg.height = 110;
         this._awardSprite.addChild(this._awardBg);
         this._awardTitle = new Label(Globalization.getString("copyOnHook.5"),TextFormatLib.format_0xebce82_12px,null,true);
         this._awardTitle.y = 2;
         this._awardTitle.autoSize = "center";
         this._awardTitle.width = 198;
         this._awardSprite.addChild(this._awardTitle);
         this._textBg1 = UIManager.getUISkin("areaBack");
         this._textBg1.width = 198;
         this._textBg1.height = 24;
         this._textBg1.y = 34;
         this._awardSprite.addChild(this._textBg1);
         this._textBg2 = UIManager.getUISkin("areaBack");
         this._textBg2.width = 198;
         this._textBg2.height = 24;
         this._textBg2.y = 63;
         this._awardSprite.addChild(this._textBg2);
         this._experienceLabel = new Label(Globalization.getString("copyOnHook.6"),TextFormatLib.format_0xFFB932_12px,null,true);
         this._experienceLabel.y = 36;
         this._experienceLabel.autoSize = "center";
         this._experienceLabel.width = 80;
         this._awardSprite.addChild(this._experienceLabel);
         this._experienceValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center,null,true);
         this._experienceValue.x = 80;
         this._experienceValue.y = 36;
         this._experienceValue.autoSize = "center";
         this._experienceValue.width = 100;
         this._awardSprite.addChild(this._experienceValue);
         this._expLabel = new Label(Globalization.getString("copyOnHook.7"),TextFormatLib.format_0xFFB932_12px,null,true);
         this._expLabel.y = 65;
         this._expLabel.autoSize = "center";
         this._expLabel.width = 80;
         this._awardSprite.addChild(this._expLabel);
         this._expValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center,null,true);
         this._expValue.x = 80;
         this._expValue.y = 65;
         this._expValue.autoSize = "center";
         this._expValue.width = 100;
         this._awardSprite.addChild(this._expValue);
         this._awardItemSprite = new Sprite();
         this._awardItemSprite.x = 7;
         this._awardItemSprite.y = 115;
         this.addChild(this._awardItemSprite);
         this._awardItemBg = UIManager.getUISkin("pane_bg");
         this._awardItemBg.width = 198;
         this._awardItemBg.height = 176;
         this._awardItemSprite.addChild(this._awardItemBg);
         this._awardItemTitle = new Label(Globalization.getString("copyOnHook.8"),TextFormatLib.format_0xebce82_12px,null,true);
         this._awardItemTitle.y = 2;
         this._awardItemTitle.autoSize = "center";
         this._awardItemTitle.width = 198;
         this._awardItemSprite.addChild(this._awardItemTitle);
         this._itemContainer = new Sprite();
         this._itemContainer.y = 5;
         this._awardItemScroll = new ScrollPane(182,139);
         this._awardItemScroll.x = 12;
         this._awardItemScroll.y = 27;
         this._awardItemSprite.addChild(this._awardItemScroll);
         this._awardItemScroll.addToPane(this._itemContainer);
         this._attackSprite = new Sprite();
         this._attackSprite.x = 213;
         this.addChild(this._attackSprite);
         this._attackBg = UIManager.getUISkin("group_bg");
         this._attackBg.width = 306;
         this._attackBg.height = 191;
         this._attackSprite.addChild(this._attackBg);
         this._attackTitleBg = UIManager.getUISkin("descBorder");
         this._attackTitleBg.width = 70;
         this._attackTitleBg.height = 20;
         this._attackTitleBg.x = 21;
         this._attackTitleBg.y = 7;
         this._attackSprite.addChild(this._attackTitleBg);
         this._attackAimTitle = new Label(Globalization.getString("copyOnHook.9"),TextFormatLib.format_0xFFB932_12px,null,true);
         this._attackAimTitle.x = 21;
         this._attackAimTitle.y = 7;
         this._attackAimTitle.autoSize = "center";
         this._attackAimTitle.width = 70;
         this._attackSprite.addChild(this._attackAimTitle);
         this._attackArmy = new ArmyUI();
         this._attackArmy.x = 21;
         this._attackArmy.y = 27;
         this._attackSprite.addChild(this._attackArmy);
         this._textBg4 = UIManager.getUISkin("areaBack");
         this._textBg4.width = 190;
         this._textBg4.height = 29;
         this._textBg4.x = 112;
         this._textBg4.y = 13;
         this._attackSprite.addChild(this._textBg4);
         this._textBg5 = UIManager.getUISkin("areaBack");
         this._textBg5.width = 190;
         this._textBg5.height = 24;
         this._textBg5.x = 112;
         this._textBg5.y = 51;
         this._attackSprite.addChild(this._textBg5);
         this._textBg6 = UIManager.getUISkin("areaBack");
         this._textBg6.width = 190;
         this._textBg6.height = 24;
         this._textBg6.x = 112;
         this._textBg6.y = 80;
         this._attackSprite.addChild(this._textBg6);
         this._textBg7 = UIManager.getUISkin("areaBack");
         this._textBg7.width = 190;
         this._textBg7.height = 24;
         this._textBg7.x = 112;
         this._textBg7.y = 109;
         this._attackSprite.addChild(this._textBg7);
         this._attackTitle = new Label("",TextFormatLib.format_0x00a8ff_14px,null,true);
         this._attackTitle.x = 112;
         this._attackTitle.y = 16;
         this._attackTitle.autoSize = "center";
         this._attackTitle.width = 190;
         this._attackSprite.addChild(this._attackTitle);
         this._useTimeLabel = new Label(Globalization.getString("copyOnHook.10"),TextFormatLib.format_0xFFB932_12px,null,true);
         this._useTimeLabel.x = 112;
         this._useTimeLabel.y = 53;
         this._useTimeLabel.autoSize = "center";
         this._useTimeLabel.width = 80;
         this._attackSprite.addChild(this._useTimeLabel);
         this._useTimeValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center,null,true);
         this._useTimeValue.x = 192;
         this._useTimeValue.y = 53;
         this._useTimeValue.autoSize = "center";
         this._useTimeValue.width = 100;
         this._attackSprite.addChild(this._useTimeValue);
         this._attackNumLabel = new Label(Globalization.getString("copyOnHook.11"),TextFormatLib.format_0xFFB932_12px,null,true);
         this._attackNumLabel.x = 112;
         this._attackNumLabel.y = 82;
         this._attackNumLabel.autoSize = "center";
         this._attackNumLabel.width = 80;
         this._attackSprite.addChild(this._attackNumLabel);
         this._attackNumValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center,null,true);
         this._attackNumValue.x = 192;
         this._attackNumValue.y = 82;
         this._attackNumValue.autoSize = "center";
         this._attackNumValue.width = 100;
         this._attackSprite.addChild(this._attackNumValue);
         this._usePointLabel = new Label(Globalization.getString("copyOnHook.12"),TextFormatLib.format_0xFFB932_12px,null,true);
         this._usePointLabel.x = 112;
         this._usePointLabel.y = 111;
         this._usePointLabel.autoSize = "center";
         this._usePointLabel.width = 80;
         this._attackSprite.addChild(this._usePointLabel);
         this._usePointValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center,null,true);
         this._usePointValue.x = 192;
         this._usePointValue.y = 111;
         this._usePointValue.autoSize = "center";
         this._usePointValue.width = 100;
         this._attackSprite.addChild(this._usePointValue);
         this.immediatelyBtn = new Button(Globalization.getString("copyOnHook.13"),null,78);
         this.immediatelyBtn.x = 23;
         this.immediatelyBtn.y = 152;
         this._attackSprite.addChild(this.immediatelyBtn);
         this.immediatelyBtn.addEventListener("click",this.clickBtnHandler);
         this.immediatelyBtn.setToolTip(Globalization.getString("copyOnHook.14"),"centerBottom");
         this.onceBtn = new Button(Globalization.getString("copyOnHook.15"),null,78);
         this.onceBtn.x = 116;
         this.onceBtn.y = 152;
         this._attackSprite.addChild(this.onceBtn);
         this.onceBtn.addEventListener("click",this.clickBtnHandler);
         this.onceBtn.setToolTip(Globalization.getString("copyOnHook.16"),"centerBottom");
         this.cancelBtn = new Button(Globalization.getString("copyOnHook.17"),null,78);
         this.cancelBtn.x = 206;
         this.cancelBtn.y = 152;
         this._attackSprite.addChild(this.cancelBtn);
         this.cancelBtn.addEventListener("click",this.clickBtnHandler);
         this.settleConfirmBtn = new Button(Globalization.getString("copyOnHook.18"),null,78);
         this.settleConfirmBtn.x = 116;
         this.settleConfirmBtn.y = 152;
         this._attackSprite.addChild(this.settleConfirmBtn);
         this.settleConfirmBtn.addEventListener("click",this.clickBtnHandler);
         this._descSprite = new Sprite();
         this._descSprite.x = 213;
         this._descSprite.y = 193;
         this.addChild(this._descSprite);
         this._attackDescBg = UIManager.getUISkin("group_bg");
         this._attackDescBg.width = 306;
         this._attackDescBg.height = 97;
         this._descSprite.addChild(this._attackDescBg);
         this._descText1 = new Label(Globalization.getString("copyOnHook.19"),TextFormatLib.format_0xFFB932_12px);
         this._descText1.x = 21;
         this._descText1.y = 8;
         this._descSprite.addChild(this._descText1);
         this._descText2 = new Label(Globalization.getString("copyOnHook.20"),TextFormatLib.format_0xFFB932_12px);
         this._descText2.x = 21;
         this._descText2.y = 29;
         this._descSprite.addChild(this._descText2);
         this._descText3 = new Label(Globalization.getString("copyOnHook.21"),TextFormatLib.format_0xFFB932_12px);
         this._descText3.x = 21;
         this._descText3.y = 49;
         this._descSprite.addChild(this._descText3);
         this.countTimer = new CountTimer();
         this.countTimer.addEventListener("CountTimer_EVENT_TIMER",this.countTimeHandler);
         this.countTimer.addEventListener("CountTimer_EVENT_TIMERCOMPLETE",this.countCompleteHandler);
      }
      
      private function clickBtnHandler(param1:MouseEvent) : void
      {
         var _loc2_:Button = param1.target as Button;
         this.clickBtnFun && this.clickBtnFun(_loc2_);
      }
      
      public function setAttackArmyData(param1:ArmyData) : void
      {
         if(param1 != null)
         {
            this._attackArmy.armyIcon.setData(param1.armyIcon);
            this._attackArmy.armyName.text = param1.armyName;
            this._attackArmy.armyLevel.text = "Lv" + param1.armyLevel;
            this._attackArmy.setArmyPlan(param1.defeatPlan);
            this._attackArmy.armyPlan.visible = param1.defeatPlan != "0/0";
         }
      }
      
      public function setEndAttackArmyData(param1:ArmyData) : void
      {
         if(param1 != null)
         {
            this._attackArmy.armyIcon.setData(param1.armyIcon);
            this._attackArmy.armyName.text = param1.armyName;
            this._attackArmy.armyLevel.text = "Lv" + param1.armyLevel;
            this._attackArmy.setArmyPlan(param1.defeatPlan);
            this._attackArmy.armyPlan.visible = param1.defeatPlan != "0/0";
         }
      }
      
      public function setAttackingInfo(param1:AttackData, param2:int) : void
      {
         var _loc10_:int = 0;
         var _loc11_:int = 0;
         var _loc3_:Slot = null;
         var _loc5_:SlotTemplete = null;
         var _loc9_:CopyOnHookProxy = null;
         var _loc8_:ArmyData = null;
         var _loc7_:Array = null;
         var _loc4_:int = 0;
         var _loc6_:int = 0;
         this._experienceValue.text = param1.rewardExperience.toString();
         this._expValue.text = param1.rewardExps.toString();
         while(this._itemContainer.numChildren)
         {
            (this._itemContainer.getChildAt(0) as Slot).dispose();
            this._itemContainer.removeChildAt(0);
         }
         if(param1.rewardItems != null && param1.rewardItems.length != 0)
         {
            _loc10_ = int(param1.rewardItems.length);
            _loc11_ = 0;
            while(_loc11_ < _loc10_)
            {
               _loc3_ = new Slot();
               _loc5_ = new SlotTemplete();
               _loc5_.tempID = param1.rewardItems[_loc11_].tempId;
               _loc5_.num = param1.rewardItems[_loc11_].num;
               _loc3_.setItem(_loc5_,false,false,false,false);
               this._itemContainer.addChild(_loc3_);
               _loc3_.x = int(_loc11_ % 3) * 57;
               _loc3_.y = int(_loc11_ / 3) * 55;
               _loc11_++;
            }
         }
         this._attackNumValue.text = param1.surplusNum.toString();
         this._usePointValue.text = param1.surplusAction.toString();
         if(param2 == 1)
         {
            _loc9_ = AppFacade.instance.retrieveProxy("CopyOnHookProxy") as CopyOnHookProxy;
            _loc8_ = _loc9_.setAttackAimData(_loc9_.selArmyId);
            _loc7_ = _loc8_.defeatPlan.split("/");
            if(_loc7_.length >= 2)
            {
               _loc4_ = int(_loc7_[0]);
               _loc6_ = int(_loc7_[1]);
               _loc4_ = Math.min(_loc4_ + param1.attackingTimes - 1,_loc6_);
               _loc8_.defeatPlan = _loc4_ + "/" + _loc6_;
            }
            this.setEndAttackArmyData(_loc8_);
            this._attackTitle.text = StringUtil.substitute(Globalization.getString("copyOnHook.23"),param1.attackingTimes);
            this.countTimer.start(param1.surplusTime);
         }
         else
         {
            this._attackTitle.text = StringUtil.substitute(Globalization.getString("copyOnHook.24"));
            this._useTimeValue.text = CountTimer.formatTime(param1.surplusTime,0);
         }
      }
      
      private function countTimeHandler(param1:Event) : void
      {
         this._useTimeValue.text = this.countTimer.nextTimeFormat;
      }
      
      private function countCompleteHandler(param1:Event) : void
      {
         this.countTimer.stop();
      }
      
      public function setAttackView() : void
      {
         this._useTimeLabel.text = Globalization.getString("copyOnHook.10");
         this._attackNumLabel.text = Globalization.getString("copyOnHook.11");
         this._usePointLabel.text = Globalization.getString("copyOnHook.12");
         this.immediatelyBtn.visible = true;
         this.onceBtn.visible = true;
         this.cancelBtn.visible = true;
         this.settleConfirmBtn.visible = false;
      }
      
      public function setSettleView() : void
      {
         this._useTimeLabel.text = Globalization.getString("copyOnHook.25");
         this._attackNumLabel.text = Globalization.getString("copyOnHook.26");
         this._usePointLabel.text = Globalization.getString("copyOnHook.27");
         this.immediatelyBtn.visible = false;
         this.onceBtn.visible = false;
         this.cancelBtn.visible = false;
         this.settleConfirmBtn.visible = true;
      }
   }
}

