package copyHookOn.view.ui
{
   import flash.display.Sprite;
   import game.items.ItemManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.slot.Slot;
   import mmo.ui.control.slot.SlotTemplete;
   
   public class AwardItemUI extends Sprite
   {
      public var itemId:int;
      
      private var itemName:Label;
      
      public var itemDropDesc:Label;
      
      private var itemSolt:Slot;
      
      public function AwardItemUI()
      {
         super();
         this.init();
      }
      
      private function init() : void
      {
         this.itemSolt = new Slot();
         this.addChild(this.itemSolt);
         this.itemName = new Label("",TextFormatLib.format_0xFFB932_12px,null,true);
         this.itemName.x = this.itemSolt.width + 2;
         this.itemName.y = 4;
         this.itemName.autoSize = "center";
         this.itemName.width = 85;
         this.addChild(this.itemName);
         this.itemDropDesc = new Label("",TextFormatLib.format_0xFFFFFF_12px,null,true);
         this.itemDropDesc.x = this.itemSolt.width + 2;
         this.itemDropDesc.y = 24;
         this.itemDropDesc.autoSize = "center";
         this.itemDropDesc.width = 85;
         this.addChild(this.itemDropDesc);
      }
      
      public function setItemShow(param1:int) : void
      {
         var _loc2_:SlotTemplete = new SlotTemplete();
         _loc2_.tempID = param1;
         this.itemSolt.setItem(_loc2_,false,false,false,false);
         this.itemName.text = ItemManager.getInstance().getItemTemplate(param1.toString()).name;
      }
   }
}

