package copyHookOn.view.ui
{
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.ParaEvent;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.xmlParsers.copy.Copy;
   import game.xmlParsers.copy.CopyManager;
   import game.xmlParsers.copyOnHook.CopyOnHookManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.copyList.CopySimpleList;
   
   public class CopyNavigate extends Sprite
   {
      private var _copyNameBtn:Button;
      
      private var _preBtn:Button;
      
      private var _nextBtn:Button;
      
      private var _curCopyId:int;
      
      public var copyListSelect:CopySimpleList;
      
      public var copyChangeFun:Function;
      
      public function CopyNavigate()
      {
         super();
         this._preBtn = new Button("",null,23,UIManager.getMultiUISkin("button_left"));
         this._preBtn.y = 3;
         this.addChild(this._preBtn);
         this._preBtn.visible = false;
         this._copyNameBtn = new Button("",TextFormatLib.format_0xFFB932_12px,118,UIManager.getMultiUISkin("button_big"));
         this._copyNameBtn.x = 27;
         this.addChild(this._copyNameBtn);
         this._nextBtn = new Button("",null,23,UIManager.getMultiUISkin("button_right"));
         this._nextBtn.x = 148;
         this._nextBtn.y = 3;
         this.addChild(this._nextBtn);
         this._nextBtn.visible = false;
         this.copyListSelect = new CopySimpleList(true);
         this.copyListSelect.x = 28;
         this.copyListSelect.y = -155;
         this.addChild(this.copyListSelect);
         this.copyListSelect.visible = false;
         this.addEventListener("EnterCopyByList",this.onSelectCopyHandler);
         this.addEventListener("click",this.onClickBtnHandler);
      }
      
      private function onSelectCopyHandler(param1:ParaEvent) : void
      {
         this._curCopyId = int(param1.para);
         this.setCopyData(this._curCopyId);
         this.copyChangeFun && this.copyChangeFun(this._curCopyId);
      }
      
      private function onClickBtnHandler(param1:MouseEvent) : void
      {
         var _loc2_:Button = null;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc3_:* = param1;
         _loc2_ = _loc3_.target as Button;
         if(!_loc2_)
         {
            return;
         }
         var _loc4_:XML = XmlManager.getXml("copy_chain").children().(@id == _curCopyId)[0];
         _loc6_ = int(_loc4_.@prevCopy);
         _loc5_ = int(_loc4_.@nextCopy);
         switch(_loc2_)
         {
            case this._preBtn:
               this._curCopyId = _loc6_;
               this.setCopyData(this._curCopyId);
               this.copyChangeFun && this.copyChangeFun(this._curCopyId);
               break;
            case this._nextBtn:
               this._curCopyId = _loc5_;
               this.setCopyData(this._curCopyId);
               this.copyChangeFun && this.copyChangeFun(this._curCopyId);
               break;
            case this._copyNameBtn:
               this.copyListSelect.changeCopy(this._curCopyId);
               this.copyListSelect.visible = true;
               _loc3_.stopImmediatePropagation();
         }
      }
      
      public function setCopyData(param1:int) : void
      {
         var _loc6_:XML = null;
         var _loc7_:int = 0;
         var _loc5_:int = 0;
         var _loc8_:Array = null;
         var _loc2_:Array = null;
         var _loc4_:Copy = null;
         var _loc3_:* = param1;
         this._curCopyId = _loc3_;
         _loc6_ = XmlManager.getXml("copy_chain").children().(@id == _curCopyId)[0];
         _loc7_ = int(_loc6_.@prevCopy);
         _loc5_ = int(_loc6_.@nextCopy);
         _loc8_ = CopyOnHookManager.instance.autoCopyArr;
         _loc2_ = MainData.getInstance().copyData.getCanEnterCopyIDs();
         this._preBtn.visible = _loc8_.indexOf(_loc7_) != -1 && _loc2_.indexOf(_loc7_) != -1 ? true : false;
         this._nextBtn.visible = _loc8_.indexOf(_loc5_) != -1 && _loc2_.indexOf(_loc5_) != -1 ? true : false;
         _loc4_ = CopyManager.getCopy(_loc3_);
         this._copyNameBtn.text = _loc4_.name;
      }
   }
}

