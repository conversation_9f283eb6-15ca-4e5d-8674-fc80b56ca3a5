package copyHookOn.view.ui
{
   import copyHookOn.event.OnHookEvent;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.filters.GlowFilter;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import game.manager.UIManager;
   import game.modules.onhook.data.ArmyData;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import util.Globalization;
   
   public class CopyOnHookUI extends UISprite
   {
      public static const ARMY_MAX_VIEW:int = 5;
      
      public static const ITEM_MAX_VIEW:int = 4;
      
      private static const GLOW:GlowFilter = new GlowFilter(16761091,1,5,5);
      
      private var _armySprite:Sprite;
      
      private var _leftArrow:Button;
      
      private var _rightArrow:Button;
      
      private var _armyListArr:Array;
      
      private var _armyInfoListArr:Array;
      
      private var _awardSprite:Sprite;
      
      private var _bellyValue:Label;
      
      private var _experienceValue:Label;
      
      private var _expValue:Label;
      
      private var _awardItemSprite:Sprite;
      
      private var _itemScrollPane:ScrollPane;
      
      private var _itemBox:UIBox;
      
      private var _attackBtn:Button;
      
      private var _copySelPageBtn:CopyNavigate;
      
      private var _curSelArmy:Icon;
      
      private var _changeIndex:int = 5;
      
      private var _curSelArmyId:int = 0;
      
      private var _isOnHookAll:Boolean = false;
      
      private var _copyListRect:Rectangle;
      
      public var changeCopyFun:Function;
      
      public function CopyOnHookUI()
      {
         var _loc7_:UISkin = null;
         var _loc11_:Label = null;
         var _loc12_:UISkin = null;
         var _loc15_:UISkin = null;
         var _loc13_:Label = null;
         var _loc14_:Label = null;
         var _loc2_:UISkin = null;
         var _loc4_:Label = null;
         var _loc3_:UISkin = null;
         var _loc5_:Label = null;
         var _loc1_:Label = null;
         var _loc9_:ArmyUI = null;
         this._armyListArr = [];
         this._armyInfoListArr = [];
         super();
         var _loc10_:Sprite = new Sprite();
         this.addChild(_loc10_);
         var _loc8_:UISkin = UIManager.getUISkin("areaBack");
         _loc8_.width = 523;
         _loc8_.height = 116;
         _loc10_.addChild(_loc8_);
         this._leftArrow = new Button("",null,16,UIManager.getMultiUISkin("copy_choooser_left"));
         this._leftArrow.y = 10;
         _loc10_.addChild(this._leftArrow);
         this._rightArrow = new Button("",null,16,UIManager.getMultiUISkin("copy_choooser_right"));
         this._rightArrow.x = -this._rightArrow.width + 523;
         this._rightArrow.y = 10;
         _loc10_.addChild(this._rightArrow);
         this._armySprite = new Sprite();
         this._armySprite.x = 23;
         this._armySprite.y = 7;
         _loc10_.addChild(this._armySprite);
         var _loc6_:int = 0;
         while(_loc6_ < 5)
         {
            _loc9_ = new ArmyUI();
            _loc9_.x = _loc6_ * 102;
            this._armyListArr.push(_loc9_);
            _loc6_++;
         }
         this._awardSprite = new Sprite();
         this._awardSprite.x = 7;
         this._awardSprite.y = 145;
         this.addChild(this._awardSprite);
         _loc7_ = UIManager.getUISkin("pane_bg");
         _loc7_.setSize(198,140);
         this._awardSprite.addChild(_loc7_);
         _loc11_ = new Label(Globalization.getString("copyOnHook.5"),TextFormatLib.format_0xebce82_12px,null,true);
         _loc11_.y = 2;
         _loc11_.autoSize = "center";
         _loc11_.width = 198;
         this._awardSprite.addChild(_loc11_);
         _loc12_ = UIManager.getUISkin("areaBack");
         _loc12_.width = 198;
         _loc12_.height = 24;
         _loc12_.y = 43;
         this._awardSprite.addChild(_loc12_);
         _loc15_ = UIManager.getUISkin("areaBack");
         _loc15_.width = 198;
         _loc15_.height = 24;
         _loc15_.y = 72;
         this._awardSprite.addChild(_loc15_);
         _loc13_ = new Label(Globalization.getString("copyOnHook.6"),TextFormatLib.format_0xFFB932_12px,null,true);
         _loc13_.y = 45;
         _loc13_.autoSize = "center";
         _loc13_.width = 90;
         this._awardSprite.addChild(_loc13_);
         this._experienceValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center,null,true);
         this._experienceValue.x = 80;
         this._experienceValue.y = 45;
         this._experienceValue.autoSize = "center";
         this._experienceValue.width = 90;
         this._awardSprite.addChild(this._experienceValue);
         _loc14_ = new Label(Globalization.getString("copyOnHook.7"),TextFormatLib.format_0xFFB932_12px,null,true);
         _loc14_.y = 74;
         _loc14_.autoSize = "center";
         _loc14_.width = 90;
         this._awardSprite.addChild(_loc14_);
         this._expValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center,null,true);
         this._expValue.autoSize = "center";
         this._expValue.x = 80;
         this._expValue.y = 74;
         this._expValue.width = 90;
         this._awardSprite.addChild(this._expValue);
         _loc2_ = UIManager.getUISkin("areaBack");
         _loc2_.y = 120;
         _loc2_.setSize(523,20);
         this.addChild(_loc2_);
         _loc4_ = new Label(Globalization.getString("copyOnHook.52"),TextFormatLib.format_0x00FF00_12px_s,null,true);
         _loc4_.x = 140;
         _loc4_.y = 119;
         this.addChild(_loc4_);
         this._awardItemSprite = new Sprite();
         this._awardItemSprite.x = 212;
         this._awardItemSprite.y = 145;
         this.addChild(this._awardItemSprite);
         _loc3_ = UIManager.getUISkin("pane_bg");
         _loc3_.setSize(304,140);
         this._awardItemSprite.addChild(_loc3_);
         _loc5_ = new Label(Globalization.getString("copyOnHook.8"),TextFormatLib.format_0xebce82_12px,null,true);
         _loc5_.y = 2;
         _loc5_.autoSize = "center";
         _loc5_.width = 304;
         this._awardItemSprite.addChild(_loc5_);
         this._itemScrollPane = new ScrollPane(290,108);
         this._itemScrollPane.x = 10;
         this._itemScrollPane.y = 28;
         this._awardItemSprite.addChild(this._itemScrollPane);
         this._itemBox = new UIBox();
         this._itemBox.type = 4;
         this._itemBox.lineMaxChildrenNumber = 2;
         this._itemBox.rowSpace = 4;
         this._itemBox.lineSpace = 4;
         this._itemBox.fixWidth = 142;
         this._itemScrollPane.addToPane(this._itemBox);
         _loc1_ = new Label(Globalization.getString("copyOnHook.51"),TextFormatLib.format_0xFFB932_12px,null,true);
         _loc1_.x = 8;
         _loc1_.y = 295;
         this.addChild(_loc1_);
         this._attackBtn = new Button(Globalization.getString("copy.19"),null,80);
         this._attackBtn.x = 415;
         this._attackBtn.y = 290;
         this.addChild(this._attackBtn);
         this._copySelPageBtn = new CopyNavigate();
         this._copySelPageBtn.x = 206;
         this._copySelPageBtn.y = 290;
         this.addChild(this._copySelPageBtn);
         this.addEventListener("click",this.onClickHandler);
         this._copyListRect = new Rectangle(0,0,120,150);
         this._copySelPageBtn.copyChangeFun = this.selCopyHandler;
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
         var _loc2_:Point = null;
         var _loc3_:Object = param1.target;
         switch(_loc3_)
         {
            case this._leftArrow:
               this._changeIndex -= 1;
               this.setArrow(this._armyInfoListArr);
               this.refreshArmyData(this._armyInfoListArr);
               this.changeSelArmy();
               break;
            case this._rightArrow:
               this._changeIndex += 1;
               this.setArrow(this._armyInfoListArr);
               this.refreshArmyData(this._armyInfoListArr);
               this.changeSelArmy();
               break;
            case this._attackBtn:
               this.dispatchEvent(new OnHookEvent("ContinueAttack",this._curSelArmyId));
               break;
            default:
               _loc2_ = this._copySelPageBtn.copyListSelect.localToGlobal(new Point(0,0));
               this._copyListRect.x = _loc2_.x;
               this._copyListRect.y = _loc2_.y;
               if(!this._copyListRect.contains(stage.mouseX,stage.mouseY))
               {
                  if(this._copySelPageBtn.copyListSelect.visible)
                  {
                     this._copySelPageBtn.copyListSelect.visible = false;
                  }
               }
         }
      }
      
      private function setArrow(param1:Array) : void
      {
         var _loc2_:int = int(param1.length);
         if(_loc2_ <= 5)
         {
            this._leftArrow.mouseEnabled = false;
            this._rightArrow.mouseEnabled = false;
            this._leftArrow.filters = [FilterLib.enbaleFilter];
            this._rightArrow.filters = [FilterLib.enbaleFilter];
            return;
         }
         this._leftArrow.mouseEnabled = true;
         this._rightArrow.mouseEnabled = true;
         this._leftArrow.filters = this._rightArrow.filters = [];
         if(this._changeIndex <= 5)
         {
            this._leftArrow.mouseEnabled = false;
            this._leftArrow.filters = [FilterLib.enbaleFilter];
         }
         if(this._changeIndex >= _loc2_)
         {
            this._rightArrow.mouseEnabled = false;
            this._rightArrow.filters = [FilterLib.enbaleFilter];
         }
      }
      
      private function refreshArmyData(param1:Array) : void
      {
         var _loc2_:ArmyData = null;
         var _loc3_:* = 0;
         var _loc6_:int = this._changeIndex - 5;
         var _loc4_:int = param1.length >= 5 ? this._changeIndex : int(param1.length);
         var _loc5_:int = int(param1.length);
         while(this._armySprite.numChildren)
         {
            this._armySprite.removeChildAt(0);
         }
         if(param1 != null && param1.length != 0)
         {
            _loc3_ = _loc6_;
            while(_loc3_ < _loc4_)
            {
               _loc2_ = param1[_loc3_];
               if(_loc2_ != null)
               {
                  this._armyListArr[_loc3_ - _loc6_].armyId = _loc2_.armyId;
                  this._armyListArr[_loc3_ - _loc6_].armyIcon.setData(_loc2_.armyIcon);
                  this._armyListArr[_loc3_ - _loc6_].armyLevel.text = "Lv." + _loc2_.armyLevel;
                  this._armyListArr[_loc3_ - _loc6_].armyName.text = _loc2_.armyName;
                  this._armyListArr[_loc3_ - _loc6_].setArmyPlan(_loc2_.defeatPlan);
                  this._armySprite.addChild(this._armyListArr[_loc3_ - _loc6_]);
                  this._armyListArr[_loc3_ - _loc6_].armyIcon.addEventListener("click",this.selectArmyHandler);
                  this._armyListArr[_loc3_ - _loc6_].filters = [];
                  this._armyListArr[_loc3_ - _loc6_].isAttack = true;
                  this._armyListArr[_loc3_ - _loc6_].armyIcon.unSetToolTip();
               }
               if(!this._isOnHookAll)
               {
                  if(this._armyListArr[_loc3_ - _loc6_].armyId == param1[_loc5_ - 1].armyId)
                  {
                     this._armyListArr[_loc3_ - _loc6_].filters = [FilterLib.enbaleFilter];
                     this._armyListArr[_loc3_ - _loc6_].isAttack = false;
                     this._armyListArr[_loc3_ - _loc6_].armyIcon.setToolTip(Globalization.getString("copyOnHook.29"),"centerBottom");
                  }
               }
               _loc3_++;
            }
         }
      }
      
      private function selCopyHandler(param1:int) : void
      {
         this.changeCopyFun && this.changeCopyFun(param1);
      }
      
      private function selCurArmy(param1:Icon) : void
      {
         if(this._curSelArmy != null)
         {
            (this._curSelArmy.parent as ArmyUI).armyIconBg.filters = [];
         }
         if(param1 != null)
         {
            this._curSelArmy = param1;
            (this._curSelArmy.parent as ArmyUI).armyIconBg.filters = [GLOW];
            if((this._curSelArmy.parent as ArmyUI).isAttack)
            {
               this._attackBtn.filters = [];
               this._attackBtn.mouseEnabled = true;
            }
            else
            {
               this._attackBtn.filters = [FilterLib.enbaleFilter];
               this._attackBtn.mouseEnabled = false;
            }
         }
      }
      
      private function selectArmyHandler(param1:MouseEvent) : void
      {
         this.selCurArmy(param1.target as Icon);
         var _loc3_:ArmyUI = this._curSelArmy.parent as ArmyUI;
         var _loc2_:int = _loc3_.armyId;
         this._curSelArmyId = _loc2_;
         this.dispatchEvent(new OnHookEvent("SelectArmy",_loc2_));
      }
      
      private function changeSelArmy() : void
      {
         var _loc1_:ArmyUI = null;
         for each(_loc1_ in this._armyListArr)
         {
            if(_loc1_.armyId == this._curSelArmyId)
            {
               this.selCurArmy(_loc1_.armyIcon);
               return;
            }
         }
         this.selCurArmy(null);
      }
      
      public function setOnHookInfo(param1:Array, param2:Boolean, param3:int) : void
      {
         var _loc6_:* = null;
         var _loc4_:ArmyData = null;
         var _loc5_:int = 0;
         this._changeIndex = 5;
         this._armyInfoListArr = param1;
         this._isOnHookAll = param2;
         for each(_loc4_ in param1)
         {
            if(_loc4_.armyId == param3)
            {
               _loc6_ = _loc4_;
               _loc5_ = int(param1.indexOf(_loc4_));
               if(_loc5_ >= 5)
               {
                  this._changeIndex = _loc5_ + 1;
               }
               break;
            }
         }
         this.setArrow(param1);
         this.refreshArmyData(param1);
         if(_loc6_)
         {
            this.setArmyAwardInfo(_loc6_);
            this._curSelArmyId = param3;
            this.changeSelArmy();
         }
         else
         {
            this.setArmyAwardInfo(param1[0]);
            this.selCurArmy(this._armyListArr[0].armyIcon);
            this._curSelArmyId = this._armyListArr[0].armyId;
         }
      }
      
      public function setArmyAwardInfo(param1:ArmyData) : void
      {
         var _loc4_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:AwardItemUI = null;
         this._itemBox.dispose();
         if(param1 != null)
         {
            this._copySelPageBtn.setCopyData(param1.copyId);
            this._experienceValue.text = param1.rewardExperience.toString();
            this._expValue.text = param1.rewardExp.toString();
            if(param1.itemId != null && param1.itemId.length != 0)
            {
               _loc4_ = int(param1.itemId.length);
               _loc2_ = 0;
               while(_loc2_ < _loc4_)
               {
                  _loc3_ = new AwardItemUI();
                  if(param1.itemDropDesc[_loc2_] != "" && param1.itemDropDesc[_loc2_] != null)
                  {
                     _loc3_.itemDropDesc.text = param1.itemDropDesc[_loc2_];
                  }
                  _loc3_.setItemShow(param1.itemId[_loc2_]);
                  this._itemBox.addChild(_loc3_);
                  _loc2_++;
               }
               this._itemBox.refresh();
            }
         }
      }
   }
}

