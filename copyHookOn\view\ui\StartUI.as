package copyHookOn.view.ui
{
   import copyHookOn.event.OnHookEvent;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import game.manager.UIManager;
   import game.modules.onhook.data.ArmyData;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.comboBox.ComboBox;
   import mmo.ui.control.input.TextInput;
   import mmo.ui.control.label.Label;
   import mmo.ui.event.ComboBoxEvent;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class StartUI extends UISprite
   {
      public static const ATTACK_ONCE_TIME:int = 2;
      
      private static const SELKEY:String = "selNum";
      
      private var _attackSprite:Sprite;
      
      private var _attackInfoBg:UISkin;
      
      private var _attackTitleBg:UISkin;
      
      private var _attackTitle:Label;
      
      private var _armyUI:ArmyUI;
      
      private var _textBg1:UISkin;
      
      private var _textBg2:UISkin;
      
      private var _textBg3:UISkin;
      
      private var _attackNumLabel:Label;
      
      private var _comboBox:ComboBox;
      
      private var _textInpubBg:UISkin;
      
      private var _textInput:TextInput;
      
      private var _attackNumInput:TextField;
      
      private var _attackNumSel:Array;
      
      private var _actionPointLabel:Label;
      
      private var _actionPointValue:Label;
      
      private var _needTimeLabel:Label;
      
      private var _needTimeValue:Label;
      
      private var _startAttackBtn:Button;
      
      private var _cancelBtn:Button;
      
      private var _descSprite:Sprite;
      
      private var _attackDescBg:UISkin;
      
      private var _descText1:Label;
      
      private var _descText2:Label;
      
      private var _descText3:Label;
      
      private var _descText4:Label;
      
      private var _attackNumMax:int = 0;
      
      private var _selAttackNum:int = 0;
      
      public function StartUI()
      {
         super();
         this._attackNumSel = [{"selNum":5},{"selNum":10},{"selNum":20},{"selNum":30},{"selNum":40},{"selNum":50},{"selNum":200},{"selNum":500},{"selNum":Globalization.getString("copyOnHook.30")}];
         this._attackSprite = new Sprite();
         this._attackSprite.x = 6;
         this.addChild(this._attackSprite);
         this._attackInfoBg = UIManager.getUISkin("group_bg");
         this._attackInfoBg.width = 335;
         this._attackInfoBg.height = 180;
         this._attackSprite.addChild(this._attackInfoBg);
         this._attackTitleBg = UIManager.getUISkin("descBorder");
         this._attackTitleBg.width = 70;
         this._attackTitleBg.height = 20;
         this._attackTitleBg.x = 26;
         this._attackTitleBg.y = 7;
         this._attackSprite.addChild(this._attackTitleBg);
         this._attackTitle = new Label(Globalization.getString("copyOnHook.9"),TextFormatLib.format_0xFFB932_12px,null,true);
         this._attackTitle.x = 26;
         this._attackTitle.y = 7;
         this._attackTitle.autoSize = "center";
         this._attackTitle.width = 70;
         this._attackSprite.addChild(this._attackTitle);
         this._armyUI = new ArmyUI();
         this._armyUI.x = 26;
         this._armyUI.y = 27;
         this._attackSprite.addChild(this._armyUI);
         this._textBg1 = UIManager.getUISkin("areaBack");
         this._textBg1.width = 196;
         this._textBg1.height = 24;
         this._textBg1.x = 128;
         this._textBg1.y = 21;
         this._attackSprite.addChild(this._textBg1);
         this._textBg2 = UIManager.getUISkin("areaBack");
         this._textBg2.width = 196;
         this._textBg2.height = 24;
         this._textBg2.x = 128;
         this._textBg2.y = 59;
         this._attackSprite.addChild(this._textBg2);
         this._textBg3 = UIManager.getUISkin("areaBack");
         this._textBg3.width = 196;
         this._textBg3.height = 24;
         this._textBg3.x = 128;
         this._textBg3.y = 97;
         this._attackSprite.addChild(this._textBg3);
         this._attackNumLabel = new Label(Globalization.getString("copyOnHook.31"),TextFormatLib.format_0xFFB932_12px);
         this._attackNumLabel.x = 140;
         this._attackNumLabel.y = 23;
         this._attackSprite.addChild(this._attackNumLabel);
         this._comboBox = new ComboBox(70,this._attackNumSel,"selNum",this._attackNumSel.length);
         this._comboBox.bind(this._attackNumSel);
         this._comboBox.x = 224;
         this._comboBox.y = 23;
         this._attackSprite.addChild(this._comboBox);
         this._comboBox.selectedIndex = 0;
         this._comboBox.addEventListener(ComboBoxEvent.Item_Selected,this.comboSelHandler);
         this._attackNumInput = new TextField();
         this._attackNumInput.type = "input";
         this._attackNumInput.defaultTextFormat = TextFormatLib.format_0xFFFFFF_12px_center;
         this._attackNumInput.text = "1";
         this._attackNumInput.width = 50;
         this._attackNumInput.height = 18;
         this._attackNumInput.backgroundColor = 0;
         this._attackNumInput.background = true;
         this._attackNumInput.x = 225;
         this._attackNumInput.y = 24;
         this._attackSprite.addChild(this._attackNumInput);
         this._attackNumInput.restrict = "0-9";
         this._attackNumInput.addEventListener("change",this.onTextInputChange);
         this._actionPointLabel = new Label(Globalization.getString("copyOnHook.32"),TextFormatLib.format_0xFFB932_12px);
         this._actionPointLabel.x = 140;
         this._actionPointLabel.y = 61;
         this._attackSprite.addChild(this._actionPointLabel);
         this._actionPointValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center);
         this._actionPointValue.x = 224;
         this._actionPointValue.y = 61;
         this._attackSprite.addChild(this._actionPointValue);
         this._needTimeLabel = new Label(Globalization.getString("copyOnHook.33"),TextFormatLib.format_0xFFB932_12px);
         this._needTimeLabel.x = 140;
         this._needTimeLabel.y = 99;
         this._attackSprite.addChild(this._needTimeLabel);
         this._needTimeValue = new Label("",TextFormatLib.format_0xFFFFFF_12px_center);
         this._needTimeValue.x = 224;
         this._needTimeValue.y = 99;
         this._attackSprite.addChild(this._needTimeValue);
         this._startAttackBtn = new Button(Globalization.getString("copyOnHook.34"),null,80);
         this._startAttackBtn.x = 72;
         this._startAttackBtn.y = 136;
         this._attackSprite.addChild(this._startAttackBtn);
         this._startAttackBtn.addEventListener("click",this.startAttackHandler);
         this._cancelBtn = new Button(Globalization.getString("copyOnHook.17"),null,80);
         this._cancelBtn.x = 188;
         this._cancelBtn.y = 136;
         this._attackSprite.addChild(this._cancelBtn);
         this._cancelBtn.addEventListener("click",this.cancelAttackHandler);
         this._descSprite = new Sprite();
         this._descSprite.x = 6;
         this._descSprite.y = 188;
         this.addChild(this._descSprite);
         this._attackDescBg = UIManager.getUISkin("group_bg");
         this._attackDescBg.width = 335;
         this._attackDescBg.height = 97;
         this._descSprite.addChild(this._attackDescBg);
         this._descText1 = new Label(Globalization.getString("copyOnHook.19"),TextFormatLib.format_0xFFB932_12px);
         this._descText1.x = 21;
         this._descText1.y = 8;
         this._descSprite.addChild(this._descText1);
         this._descText2 = new Label(Globalization.getString("copyOnHook.20"),TextFormatLib.format_0xFFB932_12px);
         this._descText2.x = 21;
         this._descText2.y = 29;
         this._descSprite.addChild(this._descText2);
         this._descText3 = new Label(Globalization.getString("copyOnHook.21"),TextFormatLib.format_0xFFB932_12px);
         this._descText3.x = 21;
         this._descText3.y = 49;
         this._descSprite.addChild(this._descText3);
         this.setChildIndex(_attackSprite,this.numChildren - 1);
      }
      
      public function setAttackArmyInfo(param1:ArmyData, param2:int, param3:int) : void
      {
         var _loc8_:int = 0;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc7_:int = 0;
         var _loc6_:int = 0;
         if(param1 != null)
         {
            this._armyUI.armyIcon.setData(param1.armyIcon);
            this._armyUI.armyLevel.text = "Lv" + param1.armyLevel;
            this._armyUI.armyName.text = param1.armyName;
            this._armyUI.setArmyPlan(param1.defeatPlan);
            this._attackNumMax = param1.attactNumMax;
            if(param2 == 0)
            {
               this._attackNumInput.text = String(param3);
            }
            this._selAttackNum = int(this._attackNumInput.text);
            _loc8_ = param1.spendActionsPoint * this._selAttackNum;
            _loc4_ = param1.userActionsPoints;
            this._actionPointValue.text = StringUtil.substitute("{0}/{1}",_loc8_,_loc4_);
            _loc5_ = 2 * this._selAttackNum;
            _loc7_ = _loc5_ / 60;
            _loc6_ = _loc5_ % 60;
            if(_loc7_ > 0)
            {
               this._needTimeValue.text = StringUtil.substitute(Globalization.getString("copyOnHook.35"),_loc7_,_loc6_);
            }
            else
            {
               this._needTimeValue.text = StringUtil.substitute(Globalization.getString("copyOnHook.36"),_loc6_);
            }
         }
      }
      
      private function comboSelHandler(param1:Event) : void
      {
         if(this._comboBox.selectedData.selNum == Globalization.getString("copyOnHook.30"))
         {
            this._attackNumInput.text = String(this._attackNumMax);
            this.dispatchEvent(new OnHookEvent("ItemSelect",1));
         }
         else if(int(this._comboBox.selectedData.selNum) > this._attackNumMax)
         {
            this.dispatchEvent(new OnHookEvent("ItemSelect",0));
         }
         else
         {
            this._attackNumInput.text = this._comboBox.selectedData.selNum;
            this.dispatchEvent(new OnHookEvent("ItemSelect",1));
         }
      }
      
      private function onTextInputChange(param1:Event) : void
      {
         if(int(param1.target.text) > this._attackNumMax)
         {
            param1.target.text = this._attackNumMax.toString();
         }
         param1.target.text = int(param1.target.text);
         this.dispatchEvent(new OnHookEvent("ItemSelect",1));
      }
      
      private function startAttackHandler(param1:MouseEvent) : void
      {
         if(this._attackNumMax == 0)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("copyOnHook.37"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            return;
         }
         if(this._selAttackNum == 0)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("copyOnHook.38"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            return;
         }
         if(this._selAttackNum > this._attackNumMax)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("copyOnHook.39"),
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            return;
         }
         this.dispatchEvent(new OnHookEvent("StartAttack",this._selAttackNum));
      }
      
      private function cancelAttackHandler(param1:MouseEvent) : void
      {
         this.dispatchEvent(new OnHookEvent("Cancel",0));
      }
   }
}

