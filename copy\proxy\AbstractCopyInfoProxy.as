package copy.proxy
{
   import flash.events.Event;
   import flash.events.ProgressEvent;
   import flash.net.URLLoader;
   import flash.net.URLRequest;
   import flash.utils.ByteArray;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.bag.GridItem;
   import game.data.battle.BattleModuleParams;
   import game.data.group.GroupData;
   import game.items.framework.items.ItemFactory;
   import game.manager.UrlManager;
   import game.mvc.module.ModuleParams;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import game.xmlParsers.copy.AbstractCopy;
   import game.xmlParsers.copy.Army;
   import game.xmlParsers.copy.CopyManager;
   import mmo.Config;
   import mmo.Core;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   import util.time.TimeManager;
   
   public class AbstractCopyInfoProxy extends Proxy
   {
      private var _loc3_:URLLoader;
      
      protected var socket:BabelTimeSocket = BabelTimeSocket.getInstance();
      
      public var afterBattle:Boolean;
      
      private var _lastBattleWin:Boolean;
      
      private var _skipArmyId:int = 0;
      
      public var canSkipMovie:Boolean = false;
      
      public var skipMovie:Boolean = false;
      
      private var _currentCopyID:int;
      
      private var _cxml:XML;
      
      private var _modelAssetsArr:Array;
      
      private var isGc:Boolean = false;
      
      public var _isNew:Boolean = false;
      
      public function AbstractCopyInfoProxy(param1:String = null, param2:Object = null)
      {
         super(param1,param2);
      }
      
      public function get skipArmyId() : int
      {
         return this._skipArmyId;
      }
      
      public function set skipArmyId(param1:int) : void
      {
         this._skipArmyId = param1;
      }
      
      public function get isNew() : Boolean
      {
         return this._isNew;
      }
      
      public function set isNew(param1:Boolean) : void
      {
         this._isNew = param1;
      }
      
      public function lastBattleWin() : Boolean
      {
         return this._lastBattleWin;
      }
      
      public function get pass() : Boolean
      {
         return false;
      }
      
      public function get progress() : Number
      {
         return 0;
      }
      
      public function get buriedCopyOpen() : Boolean
      {
         return false;
      }
      
      public function isCommonCopy() : Boolean
      {
         return true;
      }
      
      public function get copy() : AbstractCopy
      {
         return CopyManager.getAbstractCopy(this.copyID);
      }
      
      public function get copyID() : int
      {
         return 0;
      }
      
      public function get raidTimes() : int
      {
         return 0;
      }
      
      public function getDefeatArmies() : Array
      {
         return [];
      }
      
      public function checkArmyCanAttackable(param1:int) : String
      {
         var _loc6_:Array = this.getDefeatArmies();
         var _loc4_:Army = CopyManager.getArmy(param1);
         if(_loc6_.indexOf(param1.toString()) != -1)
         {
            if(_loc4_.cannotAttackTwice)
            {
               return Globalization.getString("copy.45");
            }
            return "ok";
         }
         var _loc5_:Array = _loc4_.armiesBefore;
         var _loc2_:int = int(_loc5_.length);
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_)
         {
            if(_loc6_.indexOf(_loc5_[_loc3_]) == -1)
            {
               return StringUtil.substitute(Globalization.getString("copy.46"),CopyManager.getArmy(int(_loc5_[_loc3_])).name);
            }
            _loc3_++;
         }
         if(_loc4_.taskNeed == 0)
         {
            return "ok";
         }
         if(!MainData.getInstance().taskData.hasExecutingTask(_loc4_.taskNeed))
         {
            return StringUtil.substitute(Globalization.getString("copy.47"),_loc4_.taskNeed);
         }
         return "ok";
      }
      
      protected function parseData(param1:int, param2:Object) : void
      {
         var _loc3_:Number = NaN;
         var _loc4_:int = 0;
         if(!this.canEnterBattle(param2))
         {
            return;
         }
         var _loc5_:ModuleParams = new ModuleParams("MonsterInfoWin",ModuleParams.act_Close);
         sendNotification("HANDLE_MODULE",_loc5_);
         this.afterBattle = true;
         param2.hasOwnProperty("curHp") && this.handleHP(param2.curHp);
         param2.hasOwnProperty("bloodPackage") && this.handleBloodPackage(param2.bloodPackage);
         param2.hasOwnProperty("reward") && this.handleReward(param2.reward);
         var _loc6_:int = int(param2.appraisal);
         this._lastBattleWin = this.checkWinByAppraisal(_loc6_);
         param2.hasOwnProperty("appraisal") && this.handleAppraisal(_loc6_,param1);
         if(MainData.getInstance().userData.available)
         {
            _loc3_ = TimeManager.setTimezoneOffset(param2.cd * 1000).time / 1000;
            MainData.getInstance().userData.fight_cdtime = _loc3_;
            if(this._lastBattleWin)
            {
               _loc4_ = CopyManager.getArmy(param1).mobilityCost;
               if(_loc4_ > 0)
               {
                  if(MainData.getInstance().userData.copy_execution > 0)
                  {
                     MainData.getInstance().userData.copy_execution--;
                     MainData.getInstance().userData.cur_execution = MainData.getInstance().userData.cur_execution - (_loc4_ - 1);
                  }
                  else
                  {
                     MainData.getInstance().userData.cur_execution = MainData.getInstance().userData.cur_execution - _loc4_;
                  }
               }
            }
         }
         this.startBattle(param2.fightRet,param1);
      }
      
      protected function handleAppraisal(param1:int, param2:int) : void
      {
      }
      
      protected function hasReward(param1:Object) : Boolean
      {
         var _loc2_:* = null;
         var _loc4_:int = 0;
         var _loc3_:* = param1;
         for(_loc2_ in _loc3_)
         {
            return true;
         }
         return false;
      }
      
      protected function handleReward(param1:Object) : void
      {
         var _loc8_:GroupData = null;
         var _loc2_:* = null;
         var _loc3_:Object = null;
         var _loc6_:Vector.<GridItem> = null;
         var _loc5_:GridItem = null;
         var _loc4_:int = 0;
         if(!this.hasReward(param1))
         {
            return;
         }
         if(MainData.getInstance().userData.available)
         {
            MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num + int(param1.belly);
            MainData.getInstance().groupData.roleModle.exp = MainData.getInstance().groupData.roleModle.exp + int(param1.exp);
            MainData.getInstance().userData.experience_num = MainData.getInstance().userData.experience_num + int(param1.experience);
            MainData.getInstance().userData.prestige_num = MainData.getInstance().userData.prestige_num + int(param1.prestige);
         }
         if(!param1.arrHero)
         {
            return;
         }
         var _loc9_:* = param1.arrHero;
         if(MainData.getInstance().groupData.available)
         {
            _loc8_ = MainData.getInstance().groupData;
            for(_loc2_ in _loc9_)
            {
               _loc8_.addExpTo(_loc2_,_loc9_[_loc2_].current_exp,_loc9_[_loc2_].current_level);
            }
         }
         var _loc7_:* = param1.equip;
         if(_loc7_ && MainData.getInstance().bagData.available)
         {
            _loc3_ = _loc7_.bag;
            _loc6_ = Vector.<GridItem>([]);
            for(_loc2_ in _loc3_)
            {
               _loc5_ = new GridItem(_loc2_,ItemFactory.creatItem(_loc3_[_loc2_]));
               _loc6_.push(_loc5_);
               MainData.getInstance().bagData.setGridItems(_loc6_);
            }
         }
         if(_loc7_ && MainData.getInstance().tavernData.available)
         {
            if(_loc7_.heroID > 0)
            {
               _loc4_ = int(_loc7_.heroID);
               MainData.getInstance().tavernData.addHeroID2RecruitList(_loc4_);
            }
         }
      }
      
      private function handleHP(param1:Object) : void
      {
         var _loc2_:* = null;
         if(!param1)
         {
            return;
         }
         if(MainData.getInstance().groupData.available)
         {
            for(_loc2_ in param1)
            {
               MainData.getInstance().groupData.getHeroDataByHeroID(_loc2_).curHp = int(param1[_loc2_]);
            }
         }
      }
      
      private function handleBloodPackage(param1:int) : void
      {
         if(MainData.getInstance().groupData.available)
         {
            MainData.getInstance().userData.blood_package = param1;
         }
      }
      
      private function canEnterBattle(param1:Object) : Boolean
      {
         if(param1 == "hp")
         {
            sendNotification("BLOOD_OVER");
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("copy.38"),
               "textFormat":TextFormatLib.format_0xFF0000_12px_center
            });
            return false;
         }
         if(param1 == "cd")
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("copy.42"),
               "textFormat":TextFormatLib.format_0xFF0000_12px_center
            });
            return false;
         }
         return true;
      }
      
      protected function startBattle(param1:String, param2:int, param3:Boolean = false) : void
      {
         var _loc7_:ModuleParams = null;
         var _loc11_:Army = CopyManager.getArmy(param2);
         var _loc4_:Array = [];
         var _loc6_:Array = [];
         var _loc10_:Array = _loc11_.battleRoundsTalkID;
         var _loc9_:int = int(_loc10_.length);
         var _loc8_:int = 0;
         while(_loc8_ < _loc9_)
         {
            _loc4_[_loc8_] = int(_loc10_[_loc8_][0]);
            _loc6_[_loc8_] = int(_loc10_[_loc8_][1]);
            _loc8_++;
         }
         var _loc5_:BattleModuleParams = new BattleModuleParams();
         _loc5_.battleString = param1;
         _loc5_.isChallenged = param3 || _loc11_.defeated;
         _loc5_.roundList = _loc4_;
         _loc5_.talkidList = _loc6_;
         _loc5_.endTalkid = _loc11_.battelEndTalkID;
         _loc5_.isFirstFight = ArmyDataProxy(facade.retrieveProxy("ArmyDataProxy")).armyID == 16;
         if(this.canSkipMovie && this.skipMovie)
         {
            _loc7_ = new ModuleParams("module_battle_result",ModuleParams.act_Open,_loc5_);
            _loc7_.isCenter = true;
            _loc7_.isModel = false;
            sendNotification("HANDLE_MODULE",_loc7_);
            sendNotification("BATTLE_PLAY_END");
            this._skipArmyId = param2;
         }
         else
         {
            sendNotification("ENTER_BATTLE",_loc5_);
         }
      }
      
      protected function checkWinByAppraisal(param1:int) : Boolean
      {
         return param1 < 8;
      }
      
      protected function get isFirstPass() : Boolean
      {
         return this.copyInfo.getIsFirstPass();
      }
      
      public function get copyInfo() : Object
      {
         return data;
      }
      
      public function get currentCopyID() : int
      {
         return this._currentCopyID;
      }
      
      public function set currentCopyID(param1:int) : void
      {
         this._currentCopyID = param1;
      }
      
      protected function get reEnterCopyMsg() : String
      {
         return "";
      }
      
      protected function get csEnterCopyMsg() : String
      {
         return "";
      }
      
      protected function get gameScene() : int
      {
         return 0;
      }
      
      protected function get closeModuleName() : String
      {
         return "";
      }
      
      public function sendEnterMsg() : void
      {
         this.socket.regCallback(this.reEnterCopyMsg,this.enterCopyCallback);
         this.socket.sendMessage(this.csEnterCopyMsg,new SocketCallback(this.reEnterCopyMsg),this.currentCopyID);
      }
      
      protected function enterCopyCallback(param1:SocketDataEvent) : void
      {
         this.socket.removeCallback(this.reEnterCopyMsg,this.enterCopyCallback);
         if(param1.data == "ok")
         {
            this.loadCopyXMLData(this.currentCopyID);
         }
         else
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("copy.26"),
               "textFormat":TextFormatLib.greenbig_12px
            });
         }
      }
      
      private function loadCopyXMLData(param1:int) : void
      {
         var cID:int = 0;
         cID = param1;
         var files:Array = UrlManager.getMonsterXmls(null,cID);
         Core.dataLib.load(files,function():void
         {
            loadCopyXMLData2(cID);
         },this.progress_Handler);
      }
      
      private function progress_Handler() : void
      {
         sendNotification("CONTROL_LOADING_CONTEXT",[Core.dataLib.progress,Globalization.getString("copy.23")]);
      }
      
      private function loadCopyXMLData2(param1:int) : void
      {
         var _loc2_:String = Config.mainAssetsPath + UrlManager.getCopyMapData(param1);
         this._loc3_ = new URLLoader();
         this._loc3_.dataFormat = "binary";
         this._loc3_.load(new URLRequest(_loc2_));
         this._loc3_.addEventListener("complete",this.onCXMLComplete);
         this._loc3_.addEventListener("progress",this.onCXMLProgress);
         this._loc3_.addEventListener("open",this.onCXMLOpen);
      }
      
      private function onCXMLOpen(param1:Event) : void
      {
         sendNotification("CONTROL_LOADING_START");
      }
      
      private function onCXMLProgress(param1:ProgressEvent) : void
      {
         var _loc2_:int = 100 * param1.bytesLoaded / param1.bytesTotal;
         sendNotification("CONTROL_LOADING_CONTEXT",[_loc2_,Globalization.getString("copy.27")]);
      }
      
      private function onCXMLComplete(param1:Event) : void
      {
         param1.currentTarget.removeEventListener("complete",this.onCXMLComplete);
         if(this._loc3_.data as ByteArray)
         {
            if(Main.isEncryptedWM(this._loc3_.data as ByteArray))
            {
               this._cxml = XML(Main.LxEncrypt(this._loc3_.data as ByteArray));
            }
            else
            {
               this._cxml = XML(param1.target.data);
            }
         }
         else
         {
            this._cxml = XML(param1.target.data);
         }
         this.parseCXML(this._cxml);
      }
      
      private function parseCXML(param1:XML) : void
      {
         var _loc2_:XML = null;
         var _loc3_:String = null;
         var _loc5_:String = null;
         var _loc4_:int = 0;
         this._modelAssetsArr = [];
         var _loc8_:String = param1.background[0].toString();
         var _loc6_:String = param1.foreground[0].toString();
         if(_loc8_ != "")
         {
            this._modelAssetsArr.push(UrlManager.getCopyMapAssets("backgrounds",_loc8_));
         }
         if(_loc6_ != "")
         {
            this._modelAssetsArr.push(UrlManager.getCopyMapAssets("foregrounds",_loc6_));
         }
         var _loc7_:XMLList = param1..look;
         for each(_loc2_ in _loc7_)
         {
            _loc3_ = <EMAIL>();
            _loc5_ = UrlManager.getCopyMapAssets("models",_loc3_);
            _loc4_ = int(_loc2_.@armyID);
            if(_loc3_ != "" && this._modelAssetsArr.indexOf(_loc5_) == -1)
            {
               this._modelAssetsArr.push(_loc5_);
            }
         }
         Core.dataLib.load(this._modelAssetsArr,this.onLoadAssetsComplete,this.assetLoading);
      }
      
      private function assetLoading() : void
      {
         sendNotification("CONTROL_LOADING_CONTEXT",[Core.dataLib.proPercent,Globalization.getString("copy.28")]);
      }
      
      protected function onLoadAssetsComplete() : void
      {
         var _loc1_:ModuleParams = new ModuleParams(this.closeModuleName,ModuleParams.act_Close);
         sendNotification("HANDLE_MODULE",_loc1_);
         GameScene.loadSceneAndLeaveCheck(this.gameScene,this.switchScene);
         sendNotification("CONTROL_LOADING_END");
      }
      
      protected function get _copy() : AbstractCopy
      {
         return CopyManager.getAbstractCopy(this.currentCopyID);
      }
      
      protected function switchScene(param1:Object = null, param2:SocketDataEvent = null) : void
      {
         GameScene.switchToScene(this.gameScene,{
            "ecp":this._copy,
            "copyID":this.currentCopyID,
            "cxml":this._cxml
         });
      }
      
      public function disposeModelAssets() : void
      {
         var _loc1_:String = null;
         for each(_loc1_ in this._modelAssetsArr)
         {
            Core.dataLib.disposeData(_loc1_);
         }
      }
   }
}

