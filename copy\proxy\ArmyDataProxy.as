package copy.proxy
{
   import game.data.MainData;
   import game.data.copy.CopyInfo;
   import game.data.formation.FormationData;
   import game.xmlParsers.copy.Army;
   import game.xmlParsers.copy.CopyManager;
   import game.xmlParsers.copy.Monstersquad;
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class ArmyDataProxy extends Proxy
   {
      public static const NAME:String = "ArmyDataProxy";
      
      private var _armyID:int;
      
      public var serverDefeat:int;
      
      public var userDefeat:int;
      
      public function ArmyDataProxy()
      {
         super("ArmyDataProxy");
      }
      
      public function get armyID() : int
      {
         return this._armyID;
      }
      
      public function set armyID(param1:int) : void
      {
         this._armyID = param1;
      }
      
      public function getCurFormationHeroList() : Array
      {
         return MainData.getInstance().ownFormationsData.getFormationDataByID(MainData.getInstance().userData.cur_formation).heroList;
      }
      
      public function getCurFormation() : FormationData
      {
         return MainData.getInstance().ownFormationsData.getFormationDataByID(MainData.getInstance().userData.cur_formation);
      }
      
      public function getCurFormationHeroHtidList() : Array
      {
         var _loc3_:Array = this.getCurFormationHeroList();
         var _loc2_:Array = [];
         var _loc1_:uint = 0;
         while(_loc1_ < _loc3_.length)
         {
            if(int(_loc3_[_loc1_]) == 0)
            {
               _loc2_[_loc1_] = 0;
            }
            else
            {
               _loc2_[_loc1_] = MainData.getInstance().groupData.getHeroDataByHeroID(_loc3_[_loc1_]).htid;
            }
            _loc1_++;
         }
         return _loc2_;
      }
      
      public function getArmy() : Army
      {
         return CopyManager.getArmy(this._armyID);
      }
      
      public function getMonstersquad() : Monstersquad
      {
         return CopyManager.getMonstersquad(this.getArmy().monstersquadID);
      }
      
      public function isNPCArmy() : Boolean
      {
         return this.getArmy().type == 2;
      }
      
      public function get defeatTimes() : int
      {
         var _loc1_:CopyInfoProxy = facade.retrieveProxy("CopyDataProxy") as CopyInfoProxy;
         return CopyInfo(_loc1_.getData()).getDefeatArmyTimes(this.armyID);
      }
      
      public function needGuide() : Boolean
      {
         var _loc1_:CopyInfoProxy = facade.retrieveProxy("CopyDataProxy") as CopyInfoProxy;
         return this.armyID == 16 && this.defeatTimes == 0;
      }
   }
}

