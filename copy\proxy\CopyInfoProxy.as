package copy.proxy
{
   import game.data.MainData;
   import game.data.copy.CopyInfo;
   import game.mvc.module.ModuleParams;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import game.xmlParsers.copy.AbstractCopy;
   import game.xmlParsers.copy.Army;
   import game.xmlParsers.copy.Copy;
   import game.xmlParsers.copy.CopyManager;
   import game.xmlParsers.copy.CopyPrize;
   import game.xmlParsers.copy.TeamConfigManager;
   import mmo.ui.control.PopUpCenter;
   import util.Globalization;
   import util.array.deleteTheSameElementInArray;
   
   public class CopyInfoProxy extends AbstractCopyInfoProxy
   {
      public static const NAME:String = "CopyDataProxy";
      
      private var _lastCopyID:int = -1;
      
      private var _copyInfo:CopyInfo;
      
      private var _attackableArmies:Array;
      
      public function CopyInfoProxy()
      {
         super("CopyDataProxy");
         socket.regCallback("copy.copyTeamPrize",this.getCopyTeamPrize);
      }
      
      private function getCopyTeamPrize(param1:SocketDataEvent) : void
      {
         var _loc4_:Object = param1.data;
         var _loc2_:int = int(_loc4_.copyID);
         var _loc3_:int = int(_loc4_.prizeID);
         if(_loc2_ == this.copyID)
         {
            this._copyInfo.addNewPrizeID(_loc3_);
            this._copyInfo.curGetStar += CopyPrize.instance.getCopyPrizeScroes(_loc3_);
         }
      }
      
      override public function get pass() : Boolean
      {
         return this._copyInfo.passed;
      }
      
      override public function get progress() : Number
      {
         return this._copyInfo.progress;
      }
      
      override public function get buriedCopyOpen() : Boolean
      {
         return this._copyInfo.buriedCopyOpen;
      }
      
      override public function isCommonCopy() : Boolean
      {
         return copy.type == "0";
      }
      
      override public function get copyID() : int
      {
         return this._copyInfo.copyID;
      }
      
      override public function get raidTimes() : int
      {
         return this._copyInfo.raidTimes;
      }
      
      override public function getDefeatArmies() : Array
      {
         return this._copyInfo.defeatArmyIDs;
      }
      
      public function get lastCopyID() : int
      {
         return this._lastCopyID;
      }
      
      override public function setData(param1:Object) : void
      {
         if(this._copyInfo)
         {
            this._lastCopyID = this._copyInfo.copyID;
         }
         this._copyInfo = param1 as CopyInfo;
         super.setData(param1);
         MainData.getInstance().copyData.bindSetter("currentRpEnemies",this.refreshRpEnemy);
      }
      
      private function refreshRpEnemy(param1:Object) : void
      {
         sendNotification("refreshRpEnemy",param1);
      }
      
      public function get defeatAndppearArmies() : Array
      {
         var filterfun:Function = null;
         filterfun = function(param1:*, param2:int, param3:Array):Boolean
         {
            var _loc4_:* = undefined;
            _loc4_ = param1;
            var _loc5_:* = param2;
            var _loc6_:* = param3;
            try
            {
               return Boolean(CopyManager.getCopyTeam(_loc4_));
            }
            catch(err:Error)
            {
               var _loc9_:* = !CopyManager.getArmy(_loc4_).disappearedAfterDefeat;
            }
            return _loc9_;
         };
         var arr:Array = this.getDefeatArmies().slice();
         return arr.filter(filterfun);
      }
      
      public function getArmyIDByIndex(param1:int) : int
      {
         return CopyManager.getCopy(this.copyID).armies[param1];
      }
      
      public function get attackableArmies() : Array
      {
         var _loc7_:Array = null;
         var _loc4_:int = 0;
         var _loc5_:int = 0;
         var _loc3_:Army = null;
         var _loc12_:Array = this.getDefeatArmies();
         var _loc11_:Array = _loc12_.slice();
         var _loc9_:int = int(_loc12_.length);
         var _loc10_:Copy = CopyManager.getCopy(this.copyID);
         var _loc1_:int = 0;
         while(_loc1_ < _loc9_)
         {
            _loc7_ = CopyManager.getArmy(_loc12_[_loc1_]).armiesBehinde;
            _loc4_ = int(_loc7_.length);
            _loc5_ = 0;
            while(_loc5_ < _loc4_)
            {
               _loc3_ = CopyManager.getArmy(_loc7_[_loc5_]) as Army;
               if(_loc11_.indexOf(_loc3_.id) == -1)
               {
                  if(_loc3_.taskNeed == 0)
                  {
                     _loc11_.push(_loc7_[_loc5_]);
                  }
                  else if(MainData.getInstance().taskData.hasExecutingTask(_loc3_.taskNeed))
                  {
                     _loc11_.push(_loc7_[_loc5_]);
                  }
               }
               _loc5_++;
            }
            _loc1_++;
         }
         var _loc2_:Array = _loc10_.armies;
         var _loc8_:int = int(_loc2_.length);
         _loc1_ = 0;
         while(_loc1_ < _loc8_)
         {
            _loc3_ = CopyManager.getArmy(_loc2_[_loc1_]);
            if(_loc11_.indexOf(_loc3_) == -1)
            {
               if(_loc3_.armiesBefore.length == 0)
               {
                  if(_loc3_.taskNeed == 0)
                  {
                     _loc11_.push(_loc2_[_loc1_]);
                  }
                  else if(MainData.getInstance().taskData.hasTask(_loc3_.taskNeed))
                  {
                     _loc11_.push(_loc2_[_loc1_]);
                  }
               }
            }
            _loc1_++;
         }
         return deleteTheSameElementInArray(_loc11_);
      }
      
      public function attackArmy(param1:*) : void
      {
         socket.regCallback("re.copy.attack",this.attackArmyComplete);
         socket.sendMessage.apply(null,["copy.attack",new SocketCallback("re.copy.attack",[param1[1]])].concat(param1));
      }
      
      private function attackArmyComplete(param1:SocketDataEvent) : void
      {
         socket.removeCallback("re.copy.attack",this.attackArmyComplete);
         this.parseData(param1.callbackParames[0],param1.data);
      }
      
      override protected function parseData(param1:int, param2:Object) : void
      {
         var _loc3_:Array = null;
         var _loc4_:int = 0;
         if(param2.hasOwnProperty("prizeIDs"))
         {
            _loc3_ = param2.prizeIDs;
            for each(_loc4_ in _loc3_)
            {
               this._copyInfo.addNewPrizeID(_loc4_);
               this._copyInfo.curGetStar += CopyPrize.instance.getCopyPrizeScroes(_loc4_);
            }
         }
         super.parseData(param1,param2);
         if(isFirstPass)
         {
         }
      }
      
      override protected function handleAppraisal(param1:int, param2:int) : void
      {
         if(!checkWinByAppraisal(param1))
         {
            return;
         }
         var _loc3_:int = int(CopyInfo(getData()).defeatArmy(param2,param1));
         if(_loc3_)
         {
            MainData.getInstance().copyData.canEnterCopyIDs.push(_loc3_);
         }
      }
      
      public function enterCopy(param1:int) : void
      {
         currentCopyID = param1;
         this.getCopyInfo(param1);
      }
      
      public function getCopyInfo(param1:int) : void
      {
         socket.regCallback("re.copy.getCopyInfo",this.getCopyInfoBack);
         socket.sendMessage("copy.getCopyInfo",new SocketCallback("re.copy.getCopyInfo"),param1);
      }
      
      public function getCopyInfoBack(param1:SocketDataEvent) : void
      {
         socket.removeCallback("re.copy.getCopyInfo",this.getCopyInfoBack);
         if(param1.data.copyInfo == false)
         {
            PopUpCenter.alertWin(Globalization.getString("copy.48"));
         }
         else
         {
            if(param1.data.liuxiang.attR && param1.data.liuxiang.attR > 1)
            {
               MainData.getInstance().copyData.attR = param1.data.liuxiang.attR;
            }
            if(param1.data.liuxiang.defR && param1.data.liuxiang.defR > 1)
            {
               MainData.getInstance().copyData.attD = param1.data.liuxiang.defR;
            }
            if(param1.data.liuxiang.boss)
            {
               MainData.getInstance().copyData.bossP = param1.data.liuxiang.boss;
            }
            MainData.getInstance().copyData.currentCopyID = param1.data.copyInfo.copy_id;
            MainData.getInstance().copyData.addCopy(param1.data.copyInfo.copy_id,param1.data);
            this.setData(MainData.getInstance().copyData.getCurrentCopyInfo());
            sendEnterMsg();
         }
      }
      
      override protected function get _copy() : AbstractCopy
      {
         return CopyManager.getCopy(currentCopyID) as Copy;
      }
      
      override protected function get reEnterCopyMsg() : String
      {
         return "re.copy.enterCopy";
      }
      
      override protected function get csEnterCopyMsg() : String
      {
         return "copy.enterCopy";
      }
      
      override protected function get gameScene() : int
      {
         return 2;
      }
      
      override protected function get closeModuleName() : String
      {
         return "CopyChooser";
      }
      
      private function get isPass() : Boolean
      {
         return this._copyInfo.passed;
      }
      
      public function needGuide() : Boolean
      {
         return this.copyID == 1 && MainData.getInstance().taskData.hasExecutingTask(17);
      }
      
      public function enterTeamModule(param1:int) : void
      {
         MainData.getInstance().userData.autoJoinTeamLock = true;
         var _loc2_:ModuleParams = new ModuleParams("CopyLegionChooseModule",ModuleParams.act_Open);
         _loc2_.isCenter = true;
         _loc2_.isModel = false;
         _loc2_.data = {"autoJoinRoomId":param1};
         sendNotification("HANDLE_MODULE",_loc2_);
      }
      
      private function CallbackTeamGetGroupArmyDefeatNum(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.copy.getGroupArmyDefeatNum",this.CallbackTeamGetGroupArmyDefeatNum);
         var _loc2_:ModuleParams = new ModuleParams("TeamModule",ModuleParams.act_Open);
         _loc2_.isCenter = true;
         _loc2_.data = {
            "roomId":param1.callbackParames[0],
            "current":param1.data,
            "max":TeamConfigManager.getCopyTeamTotalAttackTimes(),
            "isActivity":false
         };
         sendNotification("HANDLE_MODULE",_loc2_);
      }
   }
}

