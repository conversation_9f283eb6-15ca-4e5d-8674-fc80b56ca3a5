package copy.view
{
   import copy.view.mc.BossInfoMC;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.utils.getDefinitionByName;
   import game.data.MainData;
   import game.data.group.HeroDataUtil;
   import game.data.skill.SkillInfoADT;
   import game.events.PageNavigatorEvent;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import game.modules.seeObjectInfo.command.OtherHeroGiftInfoADT;
   import mmo.ui.control.slot.SlotTemplete;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class BossInfoPane extends PopUpWindow
   {
      public static const NAME:String = "bossInfoPane";
      
      private var stringOfAtt:Number;
      
      private var stringOfAttDef:Number;
      
      private var stringOfKill:Number;
      
      private var stringOfKillDef:Number;
      
      private var stringOfMgi:Number;
      
      private var stringOfMgiDef:Number;
      
      private var _stringOfAtt:String;
      
      private var _stringOfAttDef:String;
      
      private var _stringOfKill:String;
      
      private var _stringOfKillDef:String;
      
      private var _stringOfMgi:String;
      
      private var _stringOfMgiDef:String;
      
      private var _num:Bitmap;
      
      private var _valueString:String;
      
      private var _propertyEffect:MovieClip;
      
      private var infoMC:BossInfoMC;
      
      private var skills:Array;
      
      private var currentPage:int;
      
      public function BossInfoPane()
      {
         super(516,378);
         title = Globalization.getString("copy.77");
         isLive = true;
         pane.y -= 8;
         pane.x = 10;
         this.infoMC = new BossInfoMC();
         pane.addChild(this.infoMC);
         this.infoMC.pageNavigator.addEventListener("pageChange",this.changePage_Handler);
      }
      
      private function changePage_Handler(param1:PageNavigatorEvent) : void
      {
         var _loc2_:int = 0;
         var _loc3_:SkillInfoADT = null;
         this.currentPage = param1.currentPage;
         var _loc4_:uint = 0;
         while(_loc4_ < 2)
         {
            _loc2_ = (this.currentPage - 1) * 2 + _loc4_;
            if(this.skills && this.skills[_loc2_])
            {
               _loc3_ = this.skills[_loc2_];
               if(_loc3_.id != 0)
               {
                  this.infoMC["skill" + (_loc4_ + 1)].visible = true;
                  this.infoMC["skill" + (_loc4_ + 1)].txt_name.text = _loc3_.name;
                  this.infoMC["skill" + (_loc4_ + 1)].txt_desc.text = _loc3_.des;
                  this.infoMC["skill" + (_loc4_ + 1)].icon.setData(_loc3_.rangeIMGURL);
               }
            }
            else
            {
               this.infoMC["skill" + (_loc4_ + 1)].visible = false;
            }
            _loc4_++;
         }
      }
      
      private function showGiftEffect(param1:Sprite, param2:String) : void
      {
         var _loc4_:Class = null;
         var _loc3_:DisplayObject = null;
         if(param1.numChildren > 0)
         {
            param1.removeChildAt(0);
         }
         try
         {
            if(param2.indexOf("SSSS") != -1)
            {
               _loc3_ = AssetManager.getMc(param2);
               _loc3_.x = 17;
               _loc3_.y = 7;
            }
            else
            {
               _loc3_ = UIManager.getUISkin(param2);
            }
         }
         catch(e:Error)
         {
            trace("没有这个资源");
         }
         if(_loc3_ && (_loc3_.width > 0 && _loc3_.height > 0))
         {
            param1.addChild(_loc3_);
            return;
         }
         this._valueString = param2;
         _loc4_ = null;
         try
         {
            _loc4_ = getDefinitionByName(this._valueString) as Class;
            _loc3_ = new _loc4_();
            if(_loc3_ is MovieClip)
            {
               this._propertyEffect = _loc3_ as MovieClip;
               this._propertyEffect.y = 10;
               this._propertyEffect.x = 15;
               param1.addChild(this._propertyEffect);
            }
            else
            {
               this._num.bitmapData = _loc3_ as BitmapData;
               param1.addChild(this._num);
            }
            return;
         }
         catch(e:*)
         {
         }
      }
      
      public function setData(param1:int, param2:String, param3:int, param4:String, param5:Array, param6:Array, param7:OtherHeroGiftInfoADT) : void
      {
         var _loc8_:SlotTemplete = null;
         this.infoMC.txt_name.text = "Lv." + param3 + " " + param2;
         this.infoMC.txt_name.textColor = HeroDataUtil.indexHeroNameColor(param1);
         this.infoMC.txt_bossFeature.text = param4;
         this.infoMC.img.refresh(param1);
         this.infoMC.img.standMC && this.infoMC.img.standMC.play();
         this.infoMC.slot1.clearInfo();
         this.infoMC.slot2.clearInfo();
         this.infoMC.slot3.clearInfo();
         var _loc10_:int = param6.length + 1;
         var _loc9_:int = 1;
         while(_loc9_ < _loc10_)
         {
            if(param6[_loc9_ - 1])
            {
               _loc8_ = new SlotTemplete();
               _loc8_.tempID = param6[_loc9_ - 1];
               this.infoMC["slot" + _loc9_].setItem(_loc8_,false,false,false,false);
            }
            _loc9_++;
         }
         this.skills = param5;
         if(param5.length > 2)
         {
            this.infoMC.pageNavigator.visible = true;
         }
         else
         {
            this.infoMC.pageNavigator.visible = false;
         }
         this.removeUI();
         this.stringOfAtt = param7.valueOfAtt * MainData.getInstance().copyData.attR;
         this.stringOfKill = param7.valueOfKill * MainData.getInstance().copyData.attR;
         this.stringOfMgi = param7.valueOfMgi * MainData.getInstance().copyData.attR;
         this.stringOfAttDef = param7.valueOfAttDef * MainData.getInstance().copyData.attD;
         this.stringOfKillDef = param7.valueOfKillDef * MainData.getInstance().copyData.attD;
         this.stringOfMgiDef = param7.valueOfMgiDef * MainData.getInstance().copyData.attD;
         if(MainData.getInstance().copyData.bossP && MainData.getInstance().copyData.bossP[param1])
         {
            this.stringOfAtt *= MainData.getInstance().copyData.bossP[param1]["attR"];
            this.stringOfKill *= MainData.getInstance().copyData.bossP[param1]["attR"];
            this.stringOfMgi *= MainData.getInstance().copyData.bossP[param1]["attR"];
            this.stringOfAttDef *= MainData.getInstance().copyData.bossP[param1]["defR"];
            this.stringOfKillDef *= MainData.getInstance().copyData.bossP[param1]["defR"];
            this.stringOfMgiDef *= MainData.getInstance().copyData.bossP[param1]["defR"];
         }
         this._stringOfAtt = HeroDataUtil.getHeroAttGiftLevelString(this.stringOfAtt);
         this._stringOfAttDef = HeroDataUtil.getHeroDefGiftLevelString(this.stringOfAttDef);
         this._stringOfKill = HeroDataUtil.getHeroAttGiftLevelString(this.stringOfKill);
         this._stringOfKillDef = HeroDataUtil.getHeroDefGiftLevelString(this.stringOfKillDef);
         this._stringOfMgi = HeroDataUtil.getHeroAttGiftLevelString(this.stringOfMgi);
         this._stringOfMgiDef = HeroDataUtil.getHeroDefGiftLevelString(this.stringOfMgiDef);
         this.showGiftEffect(this.infoMC.phyattack_mc,this._stringOfAtt);
         this.infoMC.phyattack_mc.setToolTip(this.stringOfAtt + "");
         this.showGiftEffect(this.infoMC.phydefend_mc,this._stringOfAttDef);
         this.infoMC.phydefend_mc.setToolTip(this.stringOfAttDef + "");
         this.showGiftEffect(this.infoMC.killattack_mc,this._stringOfKill);
         this.infoMC.killattack_mc.setToolTip(this.stringOfKill + "");
         this.showGiftEffect(this.infoMC.killdefend_mc,this._stringOfKillDef);
         this.infoMC.killdefend_mc.setToolTip(this.stringOfKillDef + "");
         this.showGiftEffect(this.infoMC.magicattack_mc,this._stringOfMgi);
         this.infoMC.magicattack_mc.setToolTip(this.stringOfMgi + "");
         this.showGiftEffect(this.infoMC.magicdefend_mc,this._stringOfMgiDef);
         this.infoMC.magicdefend_mc.setToolTip(this.stringOfMgiDef + "");
         this.infoMC.pageNavigator.dispatchEvent(new PageNavigatorEvent("pageChange",1));
         this.infoMC.pageNavigator.init(1,Math.ceil(param5.length / 2));
      }
      
      override public function close() : void
      {
         this.infoMC.img.standMC && this.infoMC.img.standMC.stop();
         super.close();
      }
      
      public function removeUI() : void
      {
         this.infoMC.phyattack_mc.numChildren && this.infoMC.phyattack_mc.removeChildAt(0);
         this.infoMC.phydefend_mc.numChildren && this.infoMC.phydefend_mc.removeChildAt(0);
         this.infoMC.killattack_mc.numChildren && this.infoMC.killattack_mc.removeChildAt(0);
         this.infoMC.killdefend_mc.numChildren && this.infoMC.killdefend_mc.removeChildAt(0);
         this.infoMC.magicattack_mc.numChildren && this.infoMC.magicattack_mc.removeChildAt(0);
         this.infoMC.magicdefend_mc.numChildren && this.infoMC.magicdefend_mc.removeChildAt(0);
      }
   }
}

