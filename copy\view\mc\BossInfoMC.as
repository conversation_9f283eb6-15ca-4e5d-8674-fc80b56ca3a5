package copy.view.mc
{
   import flash.display.Sprite;
   import game.manager.UIManager;
   import game.modules.user.view.HeroInfoBoard.heroInfoPanel.propertyInfo.HeroStandAnimation;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   import mmo.ui.control.slot.Slot;
   import util.Globalization;
   
   public class BossInfoMC extends Sprite
   {
      public var txt_name:Label;
      
      public var img:HeroStandAnimation;
      
      public var label_bossFeature:Label;
      
      public var label_attacktype:Label;
      
      public var label_natureSkill:Label;
      
      public var txt_bossFeature:Label;
      
      public var skill1:SkillPaneMC;
      
      public var skill2:SkillPaneMC;
      
      public var pageNavigator:PageNavigator;
      
      public var slot1:Slot;
      
      public var slot2:Slot;
      
      public var slot3:Slot;
      
      public var txt_phy_att:Label;
      
      public var txt_phy_def:Label;
      
      public var txt_mag_att:Label;
      
      public var txt_mag_def:Label;
      
      public var txt_kil_att:Label;
      
      public var txt_kil_def:Label;
      
      public var phyattack_mc:UISprite;
      
      public var killattack_mc:UISprite;
      
      public var magicattack_mc:UISprite;
      
      public var killdefend_mc:UISprite;
      
      public var magicdefend_mc:UISprite;
      
      public var phydefend_mc:UISprite;
      
      public function BossInfoMC()
      {
         var _loc8_:UISkin = null;
         super();
         var _loc10_:UISkin = UIManager.getUISkin("group_bg");
         _loc10_.setSize(184,320);
         _loc10_.y = 8;
         addChild(_loc10_);
         this.img = new HeroStandAnimation();
         this.img.y = 40;
         this.img.x = 27;
         addChild(this.img);
         this.slot1 = new Slot();
         this.slot1.x = 10;
         this.slot1.y = 270;
         addChild(this.slot1);
         this.slot2 = new Slot();
         this.slot2.x = 70;
         this.slot2.y = 270;
         addChild(this.slot2);
         this.slot3 = new Slot();
         this.slot3.x = 130;
         this.slot3.y = 270;
         addChild(this.slot3);
         this.txt_name = new Label("",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.txt_name.autoSize = "center";
         this.txt_name.x = 0;
         this.txt_name.y = 25;
         this.txt_name.width = 184;
         addChild(this.txt_name);
         _loc8_ = UIManager.getUISkin("title_bg_2");
         _loc8_.width = 70;
         _loc8_.x = 184;
         _loc8_.y = 9;
         addChild(_loc8_);
         this.label_bossFeature = new Label(Globalization.guaiwujieshao,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.label_bossFeature.x = 186;
         this.label_bossFeature.y = 10;
         addChild(this.label_bossFeature);
         var _loc6_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc6_.setSize(316,76);
         _loc6_.x = 184;
         _loc6_.y = 34;
         addChild(_loc6_);
         this.txt_bossFeature = new Label("",TextFormatLib.format_0xFFF5CE_12px,[FilterLib.glow_0x272727]);
         this.txt_bossFeature.x = 200;
         this.txt_bossFeature.y = 36;
         this.txt_bossFeature.multiline = true;
         this.txt_bossFeature.wordWrap = true;
         this.txt_bossFeature.width = 304;
         addChild(this.txt_bossFeature);
         var _loc7_:UISkin = UIManager.getUISkin("title_bg_2");
         _loc7_.width = 70;
         _loc7_.x = 184;
         _loc7_.y = 112;
         addChild(_loc7_);
         this.label_natureSkill = new Label(Globalization.tianfu,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.label_natureSkill.x = 200;
         this.label_natureSkill.y = 114;
         addChild(this.label_natureSkill);
         var _loc11_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc11_.setSize(316,23);
         _loc11_.x = 184;
         _loc11_.y = 137;
         addChild(_loc11_);
         var _loc12_:Label = new Label(Globalization.wugong + "：",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc12_.x = 200;
         _loc12_.y = 140;
         addChild(_loc12_);
         this.phyattack_mc = new UISprite();
         this.phyattack_mc.x = 245;
         this.phyattack_mc.y = 142;
         addChild(this.phyattack_mc);
         var _loc15_:Label = new Label(Globalization.wufang + "：",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc15_.x = 340;
         _loc15_.y = 140;
         addChild(_loc15_);
         this.phydefend_mc = new UISprite();
         this.phydefend_mc.x = 385;
         this.phydefend_mc.y = 142;
         addChild(this.phydefend_mc);
         var _loc13_:Label = new Label(Globalization.bisha + "：",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc13_.x = 200;
         _loc13_.y = 160;
         addChild(_loc13_);
         this.killattack_mc = new UISprite();
         this.killattack_mc.x = 245;
         this.killattack_mc.y = 162;
         addChild(this.killattack_mc);
         var _loc14_:Label = new Label(Globalization.bifang + "：",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc14_.x = 340;
         _loc14_.y = 160;
         addChild(_loc14_);
         this.killdefend_mc = new UISprite();
         this.killdefend_mc.x = 385;
         this.killdefend_mc.y = 162;
         addChild(this.killdefend_mc);
         var _loc2_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc2_.setSize(316,23);
         _loc2_.x = 184;
         _loc2_.y = 178;
         addChild(_loc2_);
         var _loc4_:Label = new Label(Globalization.mogong + "：",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc4_.x = 200;
         _loc4_.y = 180;
         addChild(_loc4_);
         this.magicattack_mc = new UISprite();
         this.magicattack_mc.x = 245;
         this.magicattack_mc.y = 182;
         addChild(this.magicattack_mc);
         var _loc3_:Label = new Label(Globalization.mofang + "：",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc3_.x = 340;
         _loc3_.y = 180;
         addChild(_loc3_);
         this.magicdefend_mc = new UISprite();
         this.magicdefend_mc.x = 385;
         this.magicdefend_mc.y = 182;
         addChild(this.magicdefend_mc);
         var _loc5_:UISkin = UIManager.getUISkin("title_bg_2");
         _loc5_.width = 70;
         _loc5_.x = 184;
         _loc5_.y = 204;
         addChild(_loc5_);
         this.label_attacktype = new Label(Globalization.jineng,TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.label_attacktype.x = 200;
         this.label_attacktype.y = 206;
         addChild(this.label_attacktype);
         var _loc1_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc1_.setSize(316,52);
         _loc1_.x = 184;
         _loc1_.y = 230;
         addChild(_loc1_);
         var _loc9_:UISkin = UIManager.getUISkin("text_bg_2");
         _loc9_.setSize(316,52);
         _loc9_.x = 184;
         _loc9_.y = 284;
         addChild(_loc9_);
         this.skill1 = new SkillPaneMC();
         this.skill1.x = 186;
         this.skill1.y = 232;
         addChild(this.skill1);
         this.skill2 = new SkillPaneMC();
         this.skill2.x = 186;
         this.skill2.y = 286;
         addChild(this.skill2);
         this.pageNavigator = new PageNavigator();
         this.pageNavigator.x = 190;
         this.pageNavigator.y = 286;
         addChild(this.pageNavigator);
         this.pageNavigator.init(0,0);
      }
   }
}

