package copy.view.mc
{
   import flash.display.Sprite;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.Icon;
   import mmo.ui.control.label.Label;
   
   public class SkillPaneMC extends Sprite
   {
      public var icon:Icon;
      
      public var txt_desc:Label;
      
      public var txt_name:Label;
      
      public function SkillPaneMC()
      {
         super();
         this.icon = new Icon();
         this.icon.x = 22;
         addChild(this.icon);
         this.txt_desc = new Label("",TextFormatLib.format_0xFFF5CE_12px,[FilterLib.glow_0x272727]);
         this.txt_desc.x = 72;
         this.txt_desc.width = 236;
         this.txt_desc.multiline = true;
         this.txt_desc.wordWrap = true;
         addChild(this.txt_desc);
         this.txt_name = new Label("",TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.txt_name.autoSize = "center";
         this.txt_name.y = 30;
         this.txt_name.x = 0;
         this.txt_name.width = 72;
         addChild(this.txt_name);
      }
   }
}

