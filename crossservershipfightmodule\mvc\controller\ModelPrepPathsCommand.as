package crossservershipfightmodule.mvc.controller
{
   import crossservershipfightmodule.mvc.model.DataProxy;
   import crossservershipfightmodule.mvc.view.CrossServerShipFightMediator;
   import crossservershipfightmodule.mvc.view.components.FightLayerComp;
   import flash.geom.Point;
   import flash.utils.Dictionary;
   import org.puremvc.as3.interfaces.ICommand;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class ModelPrepPathsCommand extends SimpleCommand implements ICommand
   {
      public function ModelPrepPathsCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc10_:Point = null;
         var _loc2_:Point = null;
         var _loc4_:Point = null;
         var _loc8_:Point = null;
         var _loc7_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc3_:Number = NaN;
         this._dataProxy.fightVO.paths = new Dictionary();
         var _loc11_:FightLayerComp = (facade.retrieveMediator("crossservershipfightmodule.mvc.view.CrossServerShipFightMediator") as CrossServerShipFightMediator).comp.fightLayer;
         var _loc9_:Point = _loc11_.roleLayer.localToGlobal(new Point(0,0));
         this._dataProxy.fightVO.roadsNum = _loc11_.portalsA.numChildren;
         var _loc5_:int = this._dataProxy.fightVO.roadsNum - 1;
         while(_loc5_ > -1)
         {
            _loc10_ = _loc11_.portalsA.getChildAt(_loc5_).localToGlobal(new Point(0,0));
            _loc2_ = _loc11_.portalsB.getChildAt(_loc5_).localToGlobal(new Point(0,0));
            _loc4_ = new Point(_loc10_.x - _loc9_.x,_loc10_.y - _loc9_.y);
            _loc8_ = new Point(_loc2_.x - _loc9_.x,_loc2_.y - _loc9_.y);
            _loc7_ = Math.abs(_loc8_.x - _loc4_.x);
            _loc6_ = Math.abs(_loc8_.y - _loc4_.y);
            _loc3_ = Math.sqrt(Math.pow(_loc7_,2) + Math.pow(_loc6_,2));
            this._dataProxy.fightVO.paths["_" + String(_loc5_)] = [_loc4_,_loc8_,_loc3_,_loc7_,_loc6_];
            _loc5_--;
         }
         this._dataProxy.setTransferWalk();
         this._dataProxy.fightVO.roadState = 1;
         if(this._dataProxy.fightVO.roadsNum == 5)
         {
            this._dataProxy.fightVO.roadState = 2;
         }
      }
      
      private function get _dataProxy() : DataProxy
      {
         return facade.retrieveProxy("crossservershipfightmodule.mvc.model.DataProxy") as DataProxy;
      }
   }
}

