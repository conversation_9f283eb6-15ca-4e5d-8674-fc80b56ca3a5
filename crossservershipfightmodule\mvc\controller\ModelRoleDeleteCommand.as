package crossservershipfightmodule.mvc.controller
{
   import crossservershipfightmodule.mvc.model.DataProxy;
   import crossservershipfightmodule.mvc.model.vo.RoleVO;
   import crossservershipfightmodule.mvc.view.CrossServerShipFightMediator;
   import crossservershipfightmodule.mvc.view.components.FightLayerComp;
   import crossservershipfightmodule.mvc.view.utils.GL;
   import mmo.ui.control.UISprite;
   import org.puremvc.as3.interfaces.ICommand;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   import util.time.TimeManager;
   
   public class ModelRoleDeleteCommand extends SimpleCommand implements ICommand
   {
      private var _comp:FightLayerComp;
      
      private var _roleLayer:UISprite;
      
      public function ModelRoleDeleteCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         this._comp = (facade.retrieveMediator("crossservershipfightmodule.mvc.view.CrossServerShipFightMediator") as CrossServerShipFightMediator).comp.fightLayer;
         this._roleLayer = this._comp.roleLayer;
         var _loc2_:Array = param1.getBody() as Array;
         switch(_loc2_[0])
         {
            case 0:
               this._deleteRoleBatch(_loc2_[1],_loc2_[0]);
               break;
            case 1:
               this._deleteRoleBatch(_loc2_[1],_loc2_[0]);
               break;
            case 2:
               this._deleteRoleBatch(_loc2_[1],_loc2_[0]);
               this._changeWin("_" + _loc2_[2][0],_loc2_[2][1]);
               break;
            case 3:
         }
      }
      
      private function _deleteRoleBatch(param1:Array, param2:int) : void
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         _loc3_ = param1.length - 1;
         while(_loc3_ > -1)
         {
            if(param1[_loc3_] is Number)
            {
               _loc4_ = int(param1[_loc3_]);
            }
            else
            {
               _loc4_ = int(param1[_loc3_].id);
            }
            this._deleteRole("_" + _loc4_,param2);
            _loc3_--;
         }
      }
      
      private function _deleteRole(param1:String, param2:int) : void
      {
         var _loc4_:String = null;
         var _loc5_:Boolean = false;
         var _loc3_:RoleVO = null;
         if(this._dataProxy.fightVO.roleAll[param1])
         {
            _loc3_ = this._dataProxy.fightVO.roleAll[param1];
            if(this._dataProxy.fightVO.myID == _loc3_.id)
            {
               _loc5_ = true;
               this._dataProxy.fightVO.joinCD = TimeManager.getInstance().getTime() + this._dataProxy.fightVO.joinCDTime;
               sendNotification("CROSS_SERVER_SHIP_FIGHT_MY_STATE",[2,GL.JOIN_CD_TIP]);
               this._dataProxy.fightVO.roleGoOnState = false;
            }
            switch(param2)
            {
               case 0:
               case 1:
                  break;
               case 2:
                  _loc4_ = "meffect_24";
            }
            this._comp.removeRole(_loc3_,_loc5_,_loc4_);
            _loc3_ = null;
            delete this._dataProxy.fightVO.roleAll[param1];
         }
      }
      
      private function _changeWin(param1:String, param2:int) : void
      {
         var _loc3_:RoleVO = null;
         var _loc4_:Object = null;
         if(this._dataProxy.fightVO.roleAll[param1])
         {
            _loc3_ = this._dataProxy.fightVO.roleAll[param1];
            _loc4_ = _loc3_.nameColor;
            _loc3_.nameColor = this._dataProxy.winStreakNameColor(param2);
            if(_loc4_ != null && _loc4_ != _loc3_.nameColor)
            {
               this._comp.changeNameColor(param1,_loc3_.nameColor);
            }
         }
      }
      
      private function get _dataProxy() : DataProxy
      {
         return facade.retrieveProxy("crossservershipfightmodule.mvc.model.DataProxy") as DataProxy;
      }
   }
}

