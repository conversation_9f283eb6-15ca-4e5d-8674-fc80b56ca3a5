package crossservershipfightmodule.mvc.controller
{
   import crossservershipfightmodule.mvc.model.DataProxy;
   import crossservershipfightmodule.mvc.model.vo.RoleVO;
   import crossservershipfightmodule.mvc.view.CrossServerShipFightMediator;
   import crossservershipfightmodule.mvc.view.components.FightLayerComp;
   import crossservershipfightmodule.mvc.view.utils.GL;
   import flash.geom.Point;
   import mmo.Core;
   import mmo.ui.control.UISprite;
   import org.puremvc.as3.interfaces.ICommand;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class ModelRoleRefreshCommand extends SimpleCommand implements ICommand
   {
      private var _comp:FightLayerComp;
      
      private var _roleLayer:UISprite;
      
      private var _fightMediator:CrossServerShipFightMediator;
      
      public function ModelRoleRefreshCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         var _loc10_:String = null;
         var _loc11_:int = 0;
         var _loc15_:Array = null;
         var _loc16_:* = null;
         var _loc19_:RoleVO = null;
         var _loc17_:Array = null;
         var _loc18_:Point = null;
         var _loc3_:Point = null;
         var _loc5_:Number = NaN;
         var _loc4_:Number = NaN;
         var _loc7_:Number = NaN;
         var _loc2_:Number = NaN;
         var _loc13_:Number = NaN;
         var _loc14_:Number = NaN;
         var _loc9_:Number = NaN;
         var _loc6_:Number = NaN;
         var _loc21_:* = null;
         var _loc8_:Boolean = false;
         var _loc20_:Boolean = false;
         var _loc22_:* = false;
         this._fightMediator = facade.retrieveMediator("crossservershipfightmodule.mvc.view.CrossServerShipFightMediator") as CrossServerShipFightMediator;
         this._comp = this._fightMediator.comp.fightLayer;
         this._roleLayer = this._comp.roleLayer;
         var _loc12_:Object = param1.getBody();
         if(_loc12_.roadState)
         {
            if(_loc12_.roadState != this._dataProxy.fightVO.roadState)
            {
               this._comp.changePortals(_loc12_.roadState,this._dataProxy.fightVO.isAttack);
            }
            this._dataProxy.fightVO.roadState = _loc12_.roadState;
         }
         if(_loc12_.transfer)
         {
            this._dataProxy.fightVO.transfer = _loc12_.transfer;
         }
         if(_loc12_.refreshMs)
         {
            this._dataProxy.fightVO.refreshMs = _loc12_.refreshMs;
            this._dataProxy.fightVO.refreshStageFrames = this._dataProxy.fightVO.refreshMs / (1000 / Core.stg.frameRate);
         }
         if(_loc12_.roadLength)
         {
            this._dataProxy.fightVO.roadLength = _loc12_.roadLength;
         }
         if(_loc12_.road && _loc12_.road.length)
         {
            _loc15_ = _loc12_.road;
            _loc11_ = _loc15_.length - 1;
            while(_loc11_ > -1)
            {
               _loc10_ = "_" + _loc15_[_loc11_].id;
               if(this._dataProxy.fightVO.roleAll[_loc10_])
               {
                  _loc19_ = this._dataProxy.fightVO.roleAll[_loc10_];
                  _loc2_ = _loc19_.stopX;
                  _loc13_ = _loc19_.curHp;
               }
               else
               {
                  _loc19_ = new RoleVO();
                  _loc19_.keyName = _loc10_;
                  _loc20_ = _loc8_ = true;
               }
               for(_loc16_ in _loc15_[_loc11_])
               {
                  _loc19_[_loc16_] = _loc15_[_loc11_][_loc16_];
               }
               for(_loc16_ in _loc19_.extra)
               {
                  _loc19_[_loc16_] = _loc19_.extra[_loc16_];
               }
               if(_loc19_.roadIds == null)
               {
                  _loc19_.roadIds = [_loc19_.transferId % this._dataProxy.fightVO.roadsNum];
               }
               _loc17_ = this._dataProxy.fightVO.paths["_" + _loc19_.roadIds.join("_")];
               _loc18_ = _loc17_[0];
               _loc3_ = _loc17_[1];
               _loc5_ = Number(_loc17_[2]);
               _loc4_ = Number(_loc17_[3]);
               _loc7_ = Number(_loc17_[4]);
               if(String(_loc13_) != "NaN" && int(_loc13_) != _loc19_.curHp)
               {
                  this._comp.changeHP(_loc10_,_loc19_.curHp,_loc19_.maxHp);
               }
               if(_loc19_.stageStop == null || String(_loc19_.stopX) != "NaN" && _loc2_ != _loc19_.stopX)
               {
                  if(_loc19_.stopX <= 0)
                  {
                     _loc19_.stageStop = _loc18_;
                  }
                  else if(_loc19_.stopX >= this._dataProxy.fightVO.roadLength)
                  {
                     _loc19_.stageStop = _loc3_;
                  }
                  else
                  {
                     _loc19_.stageStop = this._coordinateTransformation(_loc19_.stopX,_loc5_,_loc4_,_loc7_,_loc18_);
                  }
                  if(String(_loc19_.stopX) != "NaN" && _loc2_ != _loc19_.stopX)
                  {
                     this._dataProxy.fightVO.transferWalk[_loc19_.transferId] = true;
                  }
                  _loc2_ = NaN;
               }
               if(_loc8_)
               {
                  _loc19_.stopA = _loc18_;
                  _loc19_.stopB = _loc3_;
                  if(String(_loc19_.speed) == "NaN")
                  {
                     _loc19_.speed = this._dataProxy.fightVO.speed;
                  }
                  _loc19_.refreshDis = this._dataProxy.fightVO.refreshMs * _loc19_.speed;
                  _loc19_.stageSpeed = _loc19_.speed / this._dataProxy.fightVO.roadLength * _loc5_ * (1000 / Core.stg.frameRate);
                  _loc9_ = _loc3_.x - _loc18_.x;
                  _loc6_ = _loc3_.y - _loc18_.y;
                  _loc14_ = Math.atan2(_loc6_,_loc9_);
                  _loc19_.isAttackVX = Math.cos(_loc14_) * _loc19_.stageSpeed;
                  _loc19_.isAttackVY = Math.sin(_loc14_) * _loc19_.stageSpeed;
                  _loc19_.isAttackOffsetX = _loc19_.isAttackVX * this._dataProxy.fightVO.refreshStageFrames;
                  _loc19_.isAttackOffsetY = _loc19_.isAttackVY * this._dataProxy.fightVO.refreshStageFrames;
                  if(_loc19_.transferId < this._dataProxy.fightVO.roadsNum)
                  {
                     _loc19_.isAttack = true;
                  }
                  _loc19_.roadID = _loc19_.roadIds[0];
                  _loc19_.nameColor = this._dataProxy.winStreakNameColor(_loc19_.winStreak);
                  if(_loc19_.name.indexOf(".s") == -1)
                  {
                     _loc19_.headTitle = _loc19_.name + ".s" + _loc19_.serverid;
                  }
                  else
                  {
                     _loc19_.headTitle = _loc19_.name;
                  }
                  _loc19_.boatid = _loc19_.tid;
                  _loc19_.tipInfo = "<font color=\'#ff0000\'>" + this._dataProxy.fightVO.boatConfig[_loc19_.boatid] + "</font> Lv." + _loc19_.boatlv + "\n" + GL.ATTACK_LEVEL + _loc19_.boat_attack + "\n" + GL.DEFEND_LEVEL + _loc19_.boat_defence + "\n" + GL.HP_LEVEL + _loc19_.boat_hp;
                  if(_loc19_.isAttack)
                  {
                     _loc19_.vx = _loc19_.isAttackVX;
                     _loc19_.vy = _loc19_.isAttackVY;
                     _loc19_.textureName = "crossservershipfightmodule.Back_" + _loc19_.boatid;
                  }
                  else
                  {
                     _loc19_.vx = -_loc19_.isAttackVX;
                     _loc19_.vy = -_loc19_.isAttackVY;
                     _loc19_.textureName = "crossservershipfightmodule.Front_" + _loc19_.boatid;
                  }
                  if(_loc19_.roadX <= 0)
                  {
                     _loc21_ = _loc18_;
                  }
                  else if(_loc19_.roadX >= this._dataProxy.fightVO.roadLength)
                  {
                     _loc21_ = _loc3_;
                  }
                  else
                  {
                     _loc21_ = this._coordinateTransformation(_loc19_.roadX,_loc5_,_loc4_,_loc7_,_loc18_);
                  }
                  _loc19_.realX = _loc21_.x;
                  _loc19_.realY = _loc21_.y;
                  _loc22_ = _loc19_.id == this._dataProxy.fightVO.myID;
                  if(_loc22_)
                  {
                     this._dataProxy.fightVO.roleGoOnState = true;
                  }
                  this._comp.createRole(_loc19_,_loc22_,this._dataProxy.fightVO.isAttack);
                  _loc8_ = false;
               }
               this._dataProxy.fightVO.roleAll[_loc10_] = _loc19_;
               _loc11_--;
            }
            if(_loc20_)
            {
               this._comp.sortRole();
            }
         }
      }
      
      private function _coordinateTransformation(param1:Number, param2:Number, param3:Number, param4:Number, param5:Point) : Point
      {
         var _loc6_:Number = param1 / this._dataProxy.fightVO.roadLength * param2;
         var _loc9_:Number = _loc6_ * (param3 / param2);
         var _loc8_:Number = _loc6_ * (param4 / param2);
         return new Point(param5.x + _loc9_,param5.y - _loc8_);
      }
      
      private function get _dataProxy() : DataProxy
      {
         return facade.retrieveProxy("crossservershipfightmodule.mvc.model.DataProxy") as DataProxy;
      }
   }
}

