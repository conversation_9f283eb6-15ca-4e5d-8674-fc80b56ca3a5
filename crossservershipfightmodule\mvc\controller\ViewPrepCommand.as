package crossservershipfightmodule.mvc.controller
{
   import crossservershipfightmodule.mvc.view.CrossServerShipFightMediator;
   import crossservershipfightmodule.mvc.view.components.CrossServerShipFightComp;
   import org.puremvc.as3.interfaces.ICommand;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class ViewPrepCommand extends SimpleCommand implements ICommand
   {
      public function ViewPrepCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         facade.registerMediator(new CrossServerShipFightMediator(param1.getBody() as CrossServerShipFightComp));
      }
   }
}

