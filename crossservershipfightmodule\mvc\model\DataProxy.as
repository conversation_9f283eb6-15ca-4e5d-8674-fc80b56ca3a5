package crossservershipfightmodule.mvc.model
{
   import crossservershipfightmodule.mvc.model.vo.FightVO;
   import crossservershipfightmodule.mvc.view.utils.GL;
   import flash.utils.Dictionary;
   import game.data.MainData;
   import game.data.ModuleData;
   import game.manager.XmlManager;
   import mmo.ext.font.TextFormatLib;
   import org.puremvc.as3.interfaces.IProxy;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.ModelUtilities;
   import util.VectorUtilities;
   import util.time.TimeManager;
   
   public class DataProxy extends Proxy implements IProxy
   {
      public static const NAME:String = "crossservershipfightmodule.mvc.model.DataProxy";
      
      private var _fightVO:FightVO;
      
      public function DataProxy()
      {
         super("crossservershipfightmodule.mvc.model.DataProxy");
         this._fightVO = new FightVO();
      }
      
      public function xmlConfig() : void
      {
         this._winStreakConfig();
         this._rankRewardConfig();
         var _loc5_:XML = XmlManager.worldBoatWar.children()[0];
         this._durationTime(String(_loc5_.@openTime));
         this._fightVO.readyStartTime = int(_loc5_.@countDown) * 1000;
         this._fightVO.canflauntTop = int(_loc5_.@showLimit);
         this._fightVO.encourageLevelMax = Vector.<int>(String(_loc5_.@encourageTop).split("|"));
         this._fightVO.encourageCDTime = int(_loc5_.@encourageCD) * 1000;
         this._fightVO.encourageNeedGold = int(_loc5_.@encourageGold);
         this._fightVO.encourageAdd = Vector.<int>(String(_loc5_.@encourageAdd).split("|"));
         this._fightVO.joinCDTime = int(_loc5_.@jionCD) * 1000;
         this._fightVO.joinCDGold = int(_loc5_.@jionGold);
         this._fightVO.joinCDGoldIncrease = int(_loc5_.@jionGoldAdd);
         this._fightVO.autoNeedVip = int(_loc5_.@autoVip);
         this._fightVO.autoType = int(_loc5_.@autoType);
         this._fightVO.aotuCDTime = int(_loc5_.@autoTime) * 1000;
         this._fightVO.reportMax = 100;
         this._fightVO.speed = Number(_loc5_.@speed);
         this._fightVO.roadLength = Number(_loc5_.@distance);
         this._fightVO.boatConfig = new Dictionary();
         var _loc4_:Array = String(_loc5_.@boatView).split(",");
         var _loc2_:Array = String(_loc4_[0]).split("|");
         var _loc3_:Array = String(_loc4_[1]).split("|");
         var _loc1_:int = _loc2_.length - 1;
         while(_loc1_ > -1)
         {
            this._fightVO.boatConfig[_loc2_[_loc1_]] = _loc3_[_loc1_];
            _loc1_--;
         }
         this._fightVO.fightTakeTime = Number(_loc5_.@readyTime);
      }
      
      private function _durationTime(param1:String) : void
      {
         var _loc2_:String = null;
         var _loc3_:Array = null;
         var _loc4_:int = 0;
         this._fightVO.eventTime = new Vector.<Number>();
         var _loc7_:Number = TimeManager.getInstance().getTime();
         var _loc5_:Date = new Date();
         _loc5_.setTime(_loc7_);
         var _loc6_:Array = param1.split(",");
         for each(_loc2_ in _loc6_)
         {
            _loc3_ = _loc2_.split("|");
            this._fightVO.eventTime[0] = ModelUtilities.hoursMinSecTimeStringToNumber(_loc3_[1],_loc7_);
            this._fightVO.eventTime[1] = ModelUtilities.hoursMinSecTimeStringToNumber(_loc3_[2],_loc7_);
            this._fightVO.durationTime = this._fightVO.eventTime[1] - this._fightVO.eventTime[0];
            _loc4_ = int(_loc3_[0]);
            if(_loc4_ == 7)
            {
               _loc4_ = 0;
            }
            if(_loc5_.getDay() == _loc4_)
            {
               break;
            }
         }
      }
      
      private function _roleConfig() : void
      {
         var _loc1_:XML = null;
         var _loc2_:XMLList = XmlManager.worldBoat.children();
         this._fightVO.boatConfig = new Dictionary();
         for each(_loc1_ in _loc2_)
         {
            this._fightVO.boatConfig[String(_loc1_.@boatLevel)] = [String(_loc1_.@name),String(_loc1_.@boatImg)];
         }
         _loc2_ = XmlManager.worldNeptune.children();
         this._fightVO.nepSkin = new Dictionary();
         for each(_loc1_ in _loc2_)
         {
            this._fightVO.nepSkin[String(_loc1_.@id)] = String(_loc1_.@Img);
         }
      }
      
      private function _winStreakConfig() : void
      {
         var _loc4_:XML = null;
         var _loc6_:XMLList = XmlManager.worldBoat_win_streak.children();
         var _loc5_:int = int(_loc6_.length());
         var _loc3_:Vector.<Vector.<String>> = new Vector.<Vector.<String>>(_loc5_,true);
         var _loc1_:Vector.<int> = new Vector.<int>();
         var _loc2_:int = 0;
         while(_loc2_ < _loc5_)
         {
            _loc4_ = _loc6_[_loc2_];
            _loc3_[_loc2_] = VectorUtilities.getFixedString([String(_loc4_.@killNum),String(_loc4_.@winStreak),String(_loc4_.@winStreakEnd),String(_loc4_.@color)]);
            _loc1_[_loc2_] = int(_loc4_.@killNum);
            _loc2_++;
         }
         if(_loc1_.length > 3)
         {
            _loc1_.splice(2,1);
         }
         _loc1_.fixed = true;
         this._fightVO.winStreakConfig = _loc3_;
         this._fightVO.winStreakNameColorTransform = _loc1_;
      }
      
      private function _rankRewardConfig() : void
      {
         var _loc3_:XML = null;
         var _loc5_:XMLList = XmlManager.worldBoat_reward.children();
         var _loc4_:int = int(_loc5_.length());
         var _loc2_:Vector.<Vector.<int>> = new Vector.<Vector.<int>>(_loc4_,true);
         var _loc1_:int = 0;
         while(_loc1_ < _loc4_)
         {
            _loc3_ = _loc5_[_loc1_];
            _loc2_[_loc1_] = Vector.<int>([int(_loc3_.@rank),int(_loc3_.@belly),int(_loc3_.@experience),int(_loc3_.@gold),int(_loc3_.@prestige),int(_loc3_.@execution),int(_loc3_.@items),int(_loc3_.@resource)]);
            _loc1_++;
         }
         this._fightVO.rankRewardConfig = _loc2_;
      }
      
      public function enterData(param1:Object) : void
      {
         if(param1.user.groupId == param1.attacker.groupId)
         {
            this._fightVO.isAttack = true;
         }
         this._fightVO.myBoatLevel = param1.user.extra.boatlv;
         this._fightVO.myBoatSkin = "crossservershipfightmodule.Front_" + param1.user.extra.boatid;
         this._fightVO.boatValueNum = param1.user.extra.battleval;
         this.updatetAttr({
            "attackLevel":param1.user.attackLevel,
            "defendLevel":param1.user.defendLevel,
            "hp":int(param1.user.extra.info.inspireAddHp),
            "nepAttackLevel":param1.user.extra.nepAttackLevel,
            "honour":param1.user.extra.info.honour,
            "score":param1.user.extra.info.score,
            "winStreakTimes":param1.user.winStreak
         },false);
         this._fightVO.top = param1.user.extra.topN;
         var _loc2_:Number = TimeManager.getInstance().getTime();
         this._fightVO.joinCD = param1.user.canJoinTime * 1000;
         this._fightVO.readyJoinCD = param1.user.readyTime * 1000;
         if(this._fightVO.joinCD > this._fightVO.readyJoinCD)
         {
            this._fightVO.readyJoinCD = 0;
         }
         else
         {
            this._fightVO.joinCD = 0;
         }
         if(this._fightVO.joinCD <= _loc2_)
         {
            this._fightVO.joinCD = 0;
         }
         if(this._fightVO.readyJoinCD <= _loc2_)
         {
            this._fightVO.readyJoinCD = 0;
         }
         this._fightVO.encourageCD = param1.user.canInspreTime * 1000;
         this._fightVO.removeJoinCDTimes = param1.user.extra.info.removeJoinCd;
         this._fightVO.encourageFreeTimes = param1.user.extra.inspire_free_count;
         ModuleData.crossServerShipFightGold = param1.user.extra.gold;
         this._fightVO.myID = param1.user.extra.uuid;
         this._fightVO.curNepetId = param1.user.extra.cur_nep_id;
         this._fightVO.petFreeCount = param1.user.extra.nepFree_count;
         this._fightVO.petFightCD = param1.user.extra.nep_fight_cd * 1000;
         if(param1.user.extra.vip_free_nepattack == 1)
         {
            ModuleData.crossServerPetIsFree = true;
         }
         else
         {
            ModuleData.crossServerPetIsFree = false;
         }
         this._fightVO.petFightNode = false;
         this._fightVO.firstFight = param1.user.isFirst;
         this._fightVO.startTime = this._fightVO.eventTime[0] + 1000 * this._fightVO.fightTakeTime;
         this._fightVO.endTime = TimeManager.setTimezoneOffset(param1.field.endTime).time;
         if(this._fightVO.endTime > this._fightVO.eventTime[1])
         {
            this._fightVO.endTime = this._fightVO.eventTime[1];
         }
         this._fightVO.refreshMs = param1.refreshMs;
         sendNotification("CROSS_SERVER_SHIP_FIGHT_ROLE_REFRESH",param1.field);
         sendNotification("CROSS_SERVER_SHIP_FIGHT_ENTER");
      }
      
      public function updatetAttr(param1:Object, param2:Boolean) : void
      {
         var _loc3_:String = "";
         if(param1.attackLevel != null)
         {
            this._fightVO.attackLevel += param1.attackLevel;
            this._fightVO.attackValue = this._fightVO.attackLevel * this._fightVO.encourageAdd[0];
         }
         if(param1.defendLevel != null)
         {
            this._fightVO.defendLevel += param1.defendLevel;
            this._fightVO.defendValue = this._fightVO.defendLevel * this._fightVO.encourageAdd[1];
         }
         if(param1.hp != null)
         {
            this._fightVO.hpValue += param1.hp;
         }
         if(param1.nepAttackLevel != null)
         {
            this._fightVO.nepAttackLevel += param1.nepAttackLevel;
         }
         if(this._fightVO.boatValue == null)
         {
            this._fightVO.boatValue = this._fightVO.boatValueNum;
         }
         if(param1.winStreakTimes != null)
         {
            this._fightVO.winStreakTimes += param1.winStreakTimes;
         }
         if(param1.honour != null)
         {
            if(param1.honour > 0)
            {
               _loc3_ += "<font color=\'#F77105\'>" + GL.HONOUR + ": +" + param1.honour + "</font> ";
            }
            this._fightVO.honour += Number(param1.honour);
         }
         if(param1.score != null)
         {
            if(param1.score > 0)
            {
               _loc3_ += GL.POINTS + ": +" + param1.score + " ";
            }
            else if(param1.score < 0)
            {
               _loc3_ += "<font color=\'#F13F53\'>" + GL.POINTS + ": " + param1.score + "</font> ";
            }
            this._fightVO.score += Number(param1.score);
         }
         if(param1.moduleGold != null)
         {
            ModuleData.crossServerShipFightGold += Number(param1.moduleGold);
         }
         if(param1.gold != null)
         {
            if(param1.gold > 0)
            {
               _loc3_ += GL.GOLD + ": +" + param1.gold + " ";
            }
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num + Number(param1.gold);
         }
         if(param1.belly != null)
         {
            if(param1.belly > 0)
            {
               _loc3_ += GL.BELLY + ": +" + param1.belly + " ";
            }
            MainData.getInstance().userData.belly_num = MainData.getInstance().userData.belly_num + Number(param1.belly);
         }
         if(param1.experience != null)
         {
            if(param1.experience > 0)
            {
               _loc3_ += GL.EXPERIECE + ": +" + param1.experience + " ";
            }
            MainData.getInstance().userData.experience_num = MainData.getInstance().userData.experience_num + Number(param1.experience);
         }
         if(param1.execution != null)
         {
            if(param1.execution > 0)
            {
               _loc3_ += GL.EXECUTION + ": +" + param1.execution + " ";
            }
            MainData.getInstance().userData.cur_execution = MainData.getInstance().userData.cur_execution + Number(param1.execution);
         }
         if(param1.prestige != null)
         {
            if(param1.prestige > 0)
            {
               _loc3_ += GL.PRESTIGE + ": +" + param1.prestige + " ";
            }
            MainData.getInstance().userData.prestige_num = MainData.getInstance().userData.prestige_num + Number(param1.prestige);
         }
         if(param2)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":_loc3_,
               "textFormat":TextFormatLib.format_0x00FF00_16px_bold,
               "runTime":0.5,
               "delay":3,
               "queue":true,
               "offsetY":-5
            });
         }
      }
      
      public function setTransferWalk() : void
      {
         var _loc2_:int = this._fightVO.roadsNum * 2;
         this._fightVO.transferWalk = new Vector.<Boolean>(_loc2_,true);
         var _loc1_:int = _loc2_ - 1;
         while(_loc1_ > -1)
         {
            this._fightVO.transferWalk[_loc1_] = true;
            _loc1_--;
         }
      }
      
      public function winStreakNameColor(param1:int) : Object
      {
         var _loc4_:Object = 16777215;
         var _loc2_:Array = [43263,16711680,16515327];
         var _loc3_:int = 2;
         while(_loc3_ > -1)
         {
            if(param1 >= this._fightVO.winStreakNameColorTransform[_loc3_])
            {
               _loc4_ = _loc2_[_loc3_];
               break;
            }
            _loc3_--;
         }
         return _loc4_;
      }
      
      public function getTransferID(param1:int) : int
      {
         var _loc2_:int = 0;
         if(!this._fightVO.isAttack)
         {
            _loc2_ = this._fightVO.roadsNum;
         }
         return _loc2_ + param1;
      }
      
      public function setNowPortalsState(param1:String) : void
      {
         if(this._fightVO.auto)
         {
            this._fightVO.oldPortalsState = param1;
         }
         else
         {
            this._fightVO.nowPortalsState = param1;
         }
      }
      
      public function get fightVO() : FightVO
      {
         return this._fightVO;
      }
   }
}

