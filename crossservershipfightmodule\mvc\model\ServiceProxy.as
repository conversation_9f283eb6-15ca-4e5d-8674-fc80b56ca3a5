package crossservershipfightmodule.mvc.model
{
   import crossservershipfightmodule.mvc.view.utils.GL;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.data.ModuleData;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mmo.ext.font.TextFormatLib;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.IProxy;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.Globalization;
   import util.time.TimeManager;
   
   public class ServiceProxy extends Proxy implements IProxy
   {
      public static const NAME:String = "crossservershipfightmodule.mvc.model.ServiceProxy";
      
      private var _dataProxy:DataProxy;
      
      private var _socket:BabelTimeSocket;
      
      public function ServiceProxy()
      {
         super("crossservershipfightmodule.mvc.model.ServiceProxy");
      }
      
      override public function onRegister() : void
      {
         this._dataProxy = facade.retrieveProxy("crossservershipfightmodule.mvc.model.DataProxy") as DataProxy;
         this._socket = BabelTimeSocket.getInstance("SOCKET_GENERAL_BOAT_CHALLENGE");
         this._regSocketCallback();
         super.onRegister();
      }
      
      override public function onRemove() : void
      {
         this._socket.sendMessage("worldboat.leave",new SocketCallback("re.worldboat.leave"));
         this._removeSocketCallback();
         this._socket.close();
         super.onRemove();
      }
      
      private function _regSocketCallback() : void
      {
         this._socket.regCallback("re.worldboat.getEnterInfo",this._enteredRE);
         this._socket.regCallback("re.worldboat.join",this._joinRE);
         this._socket.regCallback("re.worldboat.inspire",this._inspireRE);
         this._socket.regCallback("re.worldboat.removeJoinCd",this._removeJoinCDRE);
         this._socket.regCallback("re.worldboat.getBridInfo",this._getBridInfoRE);
         BabelTimeSocket.getInstance().regCallback("re.worldboat.peacock",this._peacockRE);
         this._socket.regCallback("sc.worldboat.refresh",this._refreshSC);
         this._socket.regCallback("sc.worldboat.fightResult",this._fightResultSC);
         this._socket.regCallback("sc.worldboat.scoreTopN",this._topSC);
         this._socket.regCallback("sc.worldboat.fightWin",this._fightWinSC);
         this._socket.regCallback("sc.worldboat.fightLose",this._fightLoseSC);
         this._socket.regCallback("sc.worldboat.touchDown",this._touchDownSC);
         this._socket.regCallback("sc.worldboat.battleEnd",this._battleEndSC);
         this._socket.regCallback("sc.worldboat.reckon",this._reckonSC);
         this._socket.regCallback("sc.worldboat.msg",this._sendmsgSC);
         this._socket.regCallback("sc.worldboat.brinfo",this._fightResultDataSC);
         this._socket.regCallback("re.worldboat.nepAttack",this._petFightRE);
      }
      
      private function _removeSocketCallback() : void
      {
         this._socket.removeCallback("re.worldboat.getEnterInfo",this._enteredRE);
         this._socket.removeCallback("re.worldboat.join",this._joinRE);
         this._socket.removeCallback("re.worldboat.inspire",this._inspireRE);
         this._socket.removeCallback("re.worldboat.removeJoinCd",this._removeJoinCDRE);
         this._socket.removeCallback("re.worldboat.getBridInfo",this._getBridInfoRE);
         BabelTimeSocket.getInstance().removeCallback("re.worldboat.peacock",this._peacockRE);
         this._socket.removeCallback("sc.worldboat.refresh",this._refreshSC);
         this._socket.removeCallback("sc.worldboat.fightResult",this._fightResultSC);
         this._socket.removeCallback("sc.worldboat.scoreTopN",this._topSC);
         this._socket.removeCallback("sc.worldboat.fightWin",this._fightWinSC);
         this._socket.removeCallback("sc.worldboat.fightLose",this._fightLoseSC);
         this._socket.removeCallback("sc.worldboat.touchDown",this._touchDownSC);
         this._socket.removeCallback("sc.worldboat.battleEnd",this._battleEndSC);
         this._socket.removeCallback("sc.worldboat.reckon",this._reckonSC);
         this._socket.removeCallback("sc.worldboat.msg",this._sendmsgSC);
         this._socket.removeCallback("sc.worldboat.brinfo",this._fightResultDataSC);
         this._socket.removeCallback("re.worldboat.nepAttack",this._petFightRE);
      }
      
      public function enteredCS() : void
      {
         this._socket.sendMessage("worldboat.getEnterInfo",new SocketCallback("re.worldboat.getEnterInfo"));
      }
      
      private function _enteredRE(param1:SocketDataEvent) : void
      {
         if(param1.data.ret == "ok")
         {
            this._dataProxy.enterData(param1.data.res);
         }
      }
      
      public function joinCS(param1:int, param2:Number, param3:Number) : void
      {
         this._socket.sendMessage("worldboat.join",new SocketCallback("re.worldboat.join",[param2,param3]),param1);
      }
      
      private function _joinRE(param1:SocketDataEvent) : void
      {
         var _loc3_:String = null;
         var _loc2_:Array = null;
         this._dataProxy.fightVO.serverRespondTime = 0;
         switch(param1.data.ret)
         {
            case "ok":
               sendNotification("CROSS_SERVER_SHIP_FIGHT_MY_STATE",[1,GL.PORTALS_ENTERED]);
               sendNotification("CROSS_SERVER_SHIP_FIGHT_TOP",param1.data.topN);
               sendNotification("CROSS_SERVER_SHIP_FIGHT_ATTR",[param1.data.reward,true]);
               _loc3_ = GL.PORTALS_ENTER_OK;
               _loc2_ = param1.callbackParames as Array;
               _loc2_[2] = TimeManager.setTimezoneOffset(param1.data.outTime * 1000).time;
               if(!this._dataProxy.fightVO.roleGoOnState)
               {
                  sendNotification("CROSS_SERVER_SHIP_FIGHT_GO_ON_CD",_loc2_);
               }
               break;
            case "full":
               _loc3_ = GL.PORTALS_FULL;
               break;
            case "battling":
               _loc3_ = GL.PORTALS_ENTERED;
               this._dataProxy.setNowPortalsState(_loc3_);
               break;
            case "cdtime":
               _loc3_ = GL.JOIN_CD_TIP;
               this._dataProxy.fightVO.joinCD = TimeManager.getInstance().getTime() + param1.data.cdtime * 1000;
               sendNotification("CROSS_SERVER_SHIP_FIGHT_MY_STATE",[2,_loc3_]);
               break;
            case "waitTime":
               _loc3_ = GL.READY_JOIN_CD_TIP;
               this._dataProxy.fightVO.readyJoinCD = TimeManager.getInstance().getTime() + param1.data.waitTime * 1000;
               sendNotification("CROSS_SERVER_SHIP_FIGHT_MY_STATE",[3,_loc3_]);
               break;
            case "lack_hp":
               _loc3_ = GL.PORTALS_LACK_HP;
               break;
            case "err":
               _loc3_ = Globalization.getString("worldBoatErr.1");
               this._dataProxy.setNowPortalsState("");
         }
         sendNotification("POP_TEXT_TIPS",{
            "text":_loc3_,
            "textFormat":TextFormatLib.format_0x00FF00_14px,
            "runTime":0.5,
            "delay":3,
            "queue":true,
            "offsetY":-5
         });
      }
      
      public function inspireCS() : void
      {
         this._socket.sendMessage("worldboat.inspire",new SocketCallback("re.worldboat.inspire"));
      }
      
      private function _inspireRE(param1:SocketDataEvent) : void
      {
         var _loc3_:int = 0;
         var _loc4_:* = "";
         var _loc2_:Object = {};
         switch(param1.data.ret)
         {
            case "ok":
               if(this._dataProxy.fightVO.encourageFreeTimes > 0)
               {
                  this._dataProxy.fightVO.encourageFreeTimes--;
               }
               else
               {
                  _loc2_.moduleGold = -param1.data.res.cost;
                  _loc4_ = StringUtil.substitute(GL.ENCOURAGE_OK_GOLD,param1.data.res.cost);
               }
               _loc3_ = int(param1.data.res.attackLevel) - this._dataProxy.fightVO.attackLevel;
               if(_loc3_ > 0)
               {
                  _loc4_ += GL.ATTACK_LEVEL + ": +" + _loc3_ * this._dataProxy.fightVO.encourageAdd[0];
                  _loc2_.attackLevel = _loc3_;
               }
               _loc3_ = int(param1.data.res.defendLevel) - this._dataProxy.fightVO.defendLevel;
               if(_loc3_ > 0)
               {
                  _loc4_ += GL.DEFEND_LEVEL + ": +" + _loc3_ * this._dataProxy.fightVO.encourageAdd[1];
                  _loc2_.defendLevel = _loc3_;
               }
               _loc3_ = int(param1.data.res.addHp) - this._dataProxy.fightVO.hpValue;
               if(_loc3_ > 0)
               {
                  _loc4_ += GL.HP_LEVEL + ": +" + _loc3_;
                  _loc2_.hp = _loc3_;
               }
               sendNotification("CROSS_SERVER_SHIP_FIGHT_ATTR",[_loc2_,false]);
               break;
            case "full":
               _loc4_ = GL.ENCOURAGE_FAIL_LIMIT;
               break;
            case "no_inspire":
               _loc2_.moduleGold = -param1.data.res.cost;
               sendNotification("CROSS_SERVER_SHIP_FIGHT_ATTR",[_loc2_,false]);
               _loc4_ = "<font color=\'#F13F53\'>" + StringUtil.substitute(GL.ENCOURAGE_FAIL,param1.data.res.cost) + "</font>";
               sendNotification("CROSS_SERVER_SHIP_FIGHT_ENCOURAGE_CD",0);
               break;
            case "cdtime":
               sendNotification("CROSS_SERVER_SHIP_FIGHT_ENCOURAGE_CD",TimeManager.getInstance().getTime() + param1.data.res * 1000);
         }
         sendNotification("CROSS_SERVER_SHIP_FIGHT_ATTACK_DEFENCE_HP");
         if(_loc4_ != "")
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":_loc4_,
               "textFormat":TextFormatLib.format_0x00FF00_14px,
               "runTime":0.5,
               "delay":3,
               "queue":true,
               "offsetY":-5
            });
         }
      }
      
      public function removeJoinCDCS() : void
      {
         this._socket.sendMessage("worldboat.removeJoinCd",new SocketCallback("re.worldboat.removeJoinCd"));
      }
      
      private function _removeJoinCDRE(param1:SocketDataEvent) : void
      {
         if(param1.data.ret == "ok")
         {
            if(param1.data.res)
            {
               sendNotification("CROSS_SERVER_SHIP_FIGHT_ATTR",[{"moduleGold":-param1.data.res},false]);
               this._dataProxy.fightVO.removeJoinCDTimes++;
               sendNotification("CROSS_SERVER_SHIP_FIGHT_REMOVE_JOIN_CD_OK");
            }
            else
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":GL.CD_END_TIP,
                  "textFormat":TextFormatLib.format_0x00FF00_14px,
                  "runTime":0.5,
                  "delay":3,
                  "queue":true,
                  "offsetY":-5
               });
            }
         }
      }
      
      public function getBridInfoCS(param1:int) : void
      {
         this._socket.sendMessage("worldboat.getBridInfo",new SocketCallback("re.worldboat.getBridInfo"),param1);
      }
      
      private function _getBridInfoRE(param1:SocketDataEvent) : void
      {
         var _loc2_:Object = param1.data;
         _loc2_.myID = this._dataProxy.fightVO.myID;
         sendNotification("CROSS_SERVER_SHIP_FIGHT_DETAIL_REPORT",_loc2_);
      }
      
      public function peacockCS() : void
      {
         BabelTimeSocket.getInstance().sendMessage("worldboat.peacock",new SocketCallback("re.worldboat.peacock"));
      }
      
      private function _peacockRE(param1:SocketDataEvent) : void
      {
      }
      
      private function _refreshSC(param1:SocketDataEvent) : void
      {
         var _loc2_:Object = param1.data.field;
         if(_loc2_.touchdown && _loc2_.touchdown.length)
         {
            sendNotification("CROSS_SERVER_SHIP_FIGHT_ROLE_DELETE",[0,_loc2_.touchdown]);
         }
         if(_loc2_.leave && _loc2_.leave.length)
         {
            sendNotification("CROSS_SERVER_SHIP_FIGHT_ROLE_DELETE",[1,_loc2_.leave]);
         }
         sendNotification("CROSS_SERVER_SHIP_FIGHT_ROLE_REFRESH",_loc2_);
      }
      
      private function _fightResultSC(param1:SocketDataEvent) : void
      {
         sendNotification("CROSS_SERVER_SHIP_FIGHT_REPORT",param1.data);
         if(param1.data.nep_uuid == 0)
         {
            sendNotification("CROSS_SERVER_SHIP_FIGHT_ROLE_DELETE",[2,[param1.data.loserId],[param1.data.winnerId,param1.data.winStreak]]);
         }
         else if(param1.data.winnerId == param1.data.nep_uuid)
         {
            sendNotification("CROSS_SERVER_SHIP_FIGHT_ROLE_DELETE",[2,[param1.data.loserId],[param1.data.winnerId,param1.data.winStreak]]);
         }
      }
      
      private function _fightResultDataSC(param1:SocketDataEvent) : void
      {
         sendNotification("WORLD_BOAT_DATA_UPDATA",param1.data);
      }
      
      private function _topSC(param1:SocketDataEvent) : void
      {
         sendNotification("CROSS_SERVER_SHIP_FIGHT_TOP",param1.data);
      }
      
      private function _fightWinSC(param1:SocketDataEvent) : void
      {
         sendNotification("CROSS_SERVER_SHIP_FIGHT_TOP",param1.data.topN);
         sendNotification("CROSS_SERVER_SHIP_FIGHT_ATTR",[param1.data.reward,true]);
         if(param1.data.reward.plunderScore)
         {
            this._plunderScore(true,param1.data.extra.loserName,param1.data.reward.plunderScore);
         }
      }
      
      private function _fightLoseSC(param1:SocketDataEvent) : void
      {
         sendNotification("CROSS_SERVER_SHIP_FIGHT_TOP",param1.data.topN);
         sendNotification("CROSS_SERVER_SHIP_FIGHT_ATTR",[param1.data.reward,true]);
         if(param1.data.reward.plunderScore)
         {
            this._plunderScore(false,param1.data.extra.winnerName,param1.data.reward.plunderScore);
         }
         var _loc2_:Object = {};
         if(param1.data.reward.reward_score)
         {
            _loc2_.score = param1.data.reward.reward_score;
         }
         if(param1.data.reward.reward_honour)
         {
            _loc2_.honour = param1.data.reward.reward_honour;
         }
         sendNotification("CROSS_SERVER_SHIP_FIGHT_ATTR",[_loc2_,true]);
      }
      
      private function _touchDownSC(param1:SocketDataEvent) : void
      {
         sendNotification("CROSS_SERVER_SHIP_FIGHT_TOP",param1.data.topN);
         sendNotification("CROSS_SERVER_SHIP_FIGHT_ATTR",[param1.data.reward,true]);
      }
      
      private function _battleEndSC(param1:SocketDataEvent) : void
      {
         this._dataProxy.setNowPortalsState(null);
         sendNotification("CROSS_SERVER_SHIP_FIGHT_END");
      }
      
      private function _reckonSC(param1:SocketDataEvent) : void
      {
         sendNotification("CROSS_SERVER_SHIP_FIGHT_TOP",param1.data.topN);
         sendNotification("CROSS_SERVER_SHIP_FIGHT_CLEARING",param1.data);
      }
      
      private function _sendmsgSC(param1:SocketDataEvent) : void
      {
         sendNotification("SC_WORLD_BOAT_SENDMSG_MAIN");
      }
      
      private function _plunderScore(param1:Boolean, param2:String, param3:String) : void
      {
         var _loc5_:* = null;
         var _loc4_:TextFormat = TextFormatLib.format_0x00FF00_14px;
         if(param1)
         {
            _loc5_ = StringUtil.substitute(GL.MY_GET_POINTS,param2,param3);
         }
         else
         {
            _loc5_ = "<font color=\'#F13F53\'>" + StringUtil.substitute(GL.MY_LOST_POINTS,param2,param3) + "</font>";
         }
         sendNotification("POP_TEXT_TIPS",{
            "text":_loc5_,
            "textFormat":_loc4_,
            "runTime":0.5,
            "delay":3,
            "queue":true,
            "offsetY":-5
         });
      }
      
      public function inGold(param1:int) : void
      {
         BabelTimeSocket.getInstance().regCallback("re.worldboat.inGold",this.getInGoldHandler);
         BabelTimeSocket.getInstance().sendMessage("worldboat.inGold",new SocketCallback("re.worldboat.inGold",[param1]),param1);
      }
      
      private function getInGoldHandler(param1:SocketDataEvent) : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.worldboat.inGold",this.getInGoldHandler);
         var _loc2_:int = int((param1.callbackParames as Array)[0]);
         if(param1.data != null)
         {
            if(param1.data.success)
            {
               MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - _loc2_;
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("worldBoatStr.17"),
                  "textFormat":TextFormatLib.format_0x00FF00_12px,
                  "runTime":2
               });
               ModuleData.crossServerShipFightGold += _loc2_;
               sendNotification("WORLD_BOAT_GOLD_CHANGE");
            }
         }
      }
      
      public function petJoinFight(param1:int, param2:int, param3:int, param4:int) : void
      {
         this._socket.sendMessage("worldboat.nepAttack",new SocketCallback("re.worldboat.nepAttack",[param4]),param1,param2,param3);
      }
      
      private function _petFightRE(param1:SocketDataEvent) : void
      {
         var _loc2_:int = int((param1.callbackParames as Array)[0]);
         if(param1.data != null)
         {
            if(param1.data.ret == "ok")
            {
               ModuleData.crossServerShipFightGold = param1.data.cur_gold;
               sendNotification("WORLD_BOAT_GOLD_CHANGE");
               this._dataProxy.fightVO.petFreeCount = param1.data.nepFree_count;
               this._dataProxy.fightVO.petFightCD = param1.data.nep_fight_cd * 1000;
            }
            else if(param1.data.err == "target_not_exist")
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("worldBoatPetFight.4"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
            }
            else if(param1.data.err == "battling")
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("worldBoatPetFight.5"),
                  "textFormat":TextFormatLib.format_0xFF0000_14px
               });
            }
         }
      }
   }
}

