package crossservershipfightmodule.mvc.model.vo
{
   import flash.utils.Dictionary;
   
   public class FightVO
   {
      public var paths:Dictionary = new Dictionary();
      
      public var roadsNum:int;
      
      public var roadState:int;
      
      public var roadLength:Number;
      
      public var stagePathDis:Number;
      
      public var speed:Number;
      
      public var refreshMs:Number;
      
      public var refreshStageFrames:int;
      
      public var correct:Boolean;
      
      public var transfer:Array;
      
      public var transferWalk:Vector.<Boolean>;
      
      public var roleAll:Dictionary = new Dictionary();
      
      public var hits:Dictionary = new Dictionary();
      
      public var roleGoOnCD:Number;
      
      public var roleGoOnState:Boolean;
      
      public var auto:Boolean;
      
      public var nowPortalsState:String;
      
      public var oldPortalsState:String;
      
      public var serverRespondTime:Number = 0;
      
      public var firstFight:Boolean;
      
      public var fightState:Boolean;
      
      public var cdState:int;
      
      public var integral:uint;
      
      public var reports:Vector.<ReportVO>;
      
      public var reportMax:int;
      
      public var eventTime:Vector.<Number>;
      
      public var durationTime:Number;
      
      public var readyStartTime:Number;
      
      public var startTime:Number;
      
      public var endTime:Number;
      
      public var myID:int;
      
      public var encourageFreeTimes:int;
      
      public var encourageNeedGold:int;
      
      public var encourageLevelMax:Vector.<int>;
      
      public var encourageCDTime:Number;
      
      public var encourageCD:Number;
      
      public var joinCD:Number;
      
      public var readyJoinCD:Number;
      
      public var startCD:Number;
      
      public var endCD:Number;
      
      public var autoCD:int;
      
      public var autoNeedVip:int;
      
      public var autoType:int;
      
      public var aotuCDTime:int;
      
      public var joinCDTime:Number;
      
      public var joinCDGold:int;
      
      public var joinCDGoldIncrease:int;
      
      public var removeJoinCDTimes:int;
      
      public var myState:int;
      
      public var winStreakConfig:Vector.<Vector.<String>>;
      
      public var winStreakNameColorTransform:Vector.<int>;
      
      public var winBelly:int;
      
      public var winHonor:int;
      
      public var rankRewardConfig:Vector.<Vector.<int>>;
      
      public var top:Array;
      
      public var honour:int;
      
      public var score:int;
      
      public var isAttack:Boolean;
      
      public var winStreakTimes:int;
      
      public var myBoatLevel:int;
      
      public var myBoatSkin:String;
      
      public var attackLevel:int;
      
      public var defendLevel:int;
      
      public var nepAttackLevel:int;
      
      public var boatValue:String;
      
      public var boatValueNum:String;
      
      public var attackValue:int;
      
      public var defendValue:int;
      
      public var hpValue:int;
      
      public var encourageAdd:Vector.<int>;
      
      public var boatConfig:Dictionary;
      
      public var nepSkin:Dictionary;
      
      public var canflauntTop:int;
      
      public var curNepetId:int;
      
      public var petFightNode:Boolean;
      
      public var petFreeCount:int;
      
      public var petFightCD:Number;
      
      public var fightTakeTime:Number;
      
      public function FightVO()
      {
         super();
      }
   }
}

