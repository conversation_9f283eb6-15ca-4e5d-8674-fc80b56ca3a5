package crossservershipfightmodule.mvc.model.vo
{
   import flash.geom.Point;
   
   public class RoleVO
   {
      public var id:int;
      
      public var name:String;
      
      public var keyName:String;
      
      public var tid:int;
      
      public var headTitle:String;
      
      public var type:int;
      
      public var curHp:int;
      
      public var maxHp:int;
      
      public var transferId:int;
      
      public var roadIds:Array;
      
      public var roadX:Number;
      
      public var stopX:Number;
      
      public var winStreak:int;
      
      public var speed:Number;
      
      public var stopA:Point;
      
      public var stopB:Point;
      
      public var stageSpeed:Number;
      
      public var refreshDis:Number;
      
      public var isAttackOffsetX:Number;
      
      public var isAttackOffsetY:Number;
      
      public var isAttackVX:Number;
      
      public var isAttackVY:Number;
      
      public var vx:Number;
      
      public var vy:Number;
      
      public var realX:Number;
      
      public var realY:Number;
      
      public var stageStop:Point;
      
      public var textureName:String;
      
      public var roadID:int;
      
      public var nameColor:Object;
      
      public var isAttack:Boolean;
      
      public var boatid:int;
      
      public var boatlv:int;
      
      public var boat_attack:int;
      
      public var boat_defence:int;
      
      public var boat_hp:int;
      
      public var tipInfo:String;
      
      public var boat_name:String;
      
      public var serverid:String;
      
      public var servername:String;
      
      public var extra:Object;
      
      public function RoleVO()
      {
         super();
      }
   }
}

