package crossservershipfightmodule.mvc.view
{
   import crossservershipfightmodule.mvc.model.DataProxy;
   import crossservershipfightmodule.mvc.model.ServiceProxy;
   import crossservershipfightmodule.mvc.model.vo.ReportVO;
   import crossservershipfightmodule.mvc.model.vo.RoleVO;
   import crossservershipfightmodule.mvc.view.components.BoatResultWin;
   import crossservershipfightmodule.mvc.view.components.ClearingComp;
   import crossservershipfightmodule.mvc.view.components.CrossServerShipFightComp;
   import crossservershipfightmodule.mvc.view.components.GoldInWindow;
   import crossservershipfightmodule.mvc.view.components.ReportComp;
   import crossservershipfightmodule.mvc.view.utils.GL;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import flash.events.TimerEvent;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import flash.text.TextFormat;
   import flash.utils.Timer;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.ModuleData;
   import game.data.PirateMediator;
   import game.manager.XmlManager;
   import game.modules.MenuInfo;
   import game.modules.activity.view.win.help.BoatHelpWindow;
   import game.mvc.module.ModuleManager;
   import game.mvc.module.ModuleParams;
   import mmo.Core;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.SwitchButton;
   import mmo.ui.control.window.ConfirmSwitchShowWindow;
   import mmo.ui.event.ButtonEvent;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.IMediator;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   import util.openModule;
   import util.time.TimeManager;
   
   public class CrossServerShipFightMediator extends PirateMediator implements IMediator
   {
      public static const NAME:String = "crossservershipfightmodule.mvc.view.CrossServerShipFightMediator";
      
      private var _noteName:String;
      
      private var _noteBody:Object;
      
      private var _dataProxy:DataProxy;
      
      private var _serviceProxy:ServiceProxy;
      
      private var _selectMenu:Array;
      
      private var _timer:Timer;
      
      private var _report:ReportComp;
      
      private var reportResultArr:Array = [];
      
      private var tCount:int;
      
      private var tCDCount:int;
      
      private var petShowTime:int;
      
      private var petShowCD:int;
      
      private var alertPetReportId:int;
      
      public function CrossServerShipFightMediator(param1:CrossServerShipFightComp)
      {
         super("crossservershipfightmodule.mvc.view.CrossServerShipFightMediator",param1);
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().userData,MainData.getInstance().groupData,MainData.getInstance().bagData];
      }
      
      override public function onRegister() : void
      {
         this._dataProxy = facade.retrieveProxy("crossservershipfightmodule.mvc.model.DataProxy") as DataProxy;
         this._serviceProxy = facade.retrieveProxy("crossservershipfightmodule.mvc.model.ServiceProxy") as ServiceProxy;
         this._dataProxy.xmlConfig();
         checkDataAvialable(function():void
         {
            _serviceProxy.enteredCS();
         });
         super.onRegister();
      }
      
      override public function onRemove() : void
      {
         this._killEventListener();
         super.onRemove();
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["STAGE_RESIZE","CROSS_SERVER_SHIP_FIGHT_ENTER","WORLD_BOAT_VALUE_STRENGTHEN","WORLD_BOAT_MAP_REFRESH_BOATFACE","CROSS_SERVER_SHIP_FIGHT_ATTACK_DEFENCE_HP","WORLD_BOAT_GOLD_CHANGE","CROSS_SERVER_SHIP_FIGHT_CLEARING","CROSS_SERVER_SHIP_FIGHT_REPORT","CROSS_SERVER_SHIP_FIGHT_DETAIL_REPORT","CROSS_SERVER_SHIP_FIGHT_FLAUNT","CROSS_SERVER_SHIP_FIGHT_ENCOURAGE_CD","CROSS_SERVER_SHIP_FIGHT_MY_STATE","CROSS_SERVER_SHIP_FIGHT_REMOVE_JOIN_CD_OK","CROSS_SERVER_SHIP_FIGHT_ATTR","CROSS_SERVER_SHIP_FIGHT_TOP","CROSS_SERVER_SHIP_FIGHT_QUIT","CROSS_SERVER_SHIP_FIGHT_END","CROSS_SERVER_SHIP_FIGHT_GO_ON_CD","SC_WORLD_BOAT_SENDMSG_MAIN","CROSS_SERVER_SHIP_FIGHT_AOTU_PORTAL","CROSS_SERVER_SHIP_FIGHT_AOTU","WORLD_BOAT_OPEN_GOLD","WORLD_BOAT_DATA_UPDATA","CROSS_BOAT_PET_FIGHT","WORLDBOAT_SETNEPID","ALERT_BOAT_PET_RESULT"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _confirmPOP:Function;
         var nowTime:Number = NaN;
         var bool:Boolean = false;
         var reportVO:ReportVO = null;
         var keyName:String = null;
         var transferData:Array = null;
         var bridId:int = 0;
         var battinfo:Object = null;
         var pop:ConfirmSwitchShowWindow = null;
         var petId:int = 0;
         var targetUid:int = 0;
         var roadId:int = 0;
         var needGold:int = 0;
         var resultD:Object = null;
         var note:INotification = param1;
         this._noteName = note.getName();
         this._noteBody = note.getBody();
         switch(this._noteName)
         {
            case "STAGE_RESIZE":
               this.comp.layout();
               break;
            case "CROSS_SERVER_SHIP_FIGHT_ENTER":
               nowTime = TimeManager.getInstance().getTime();
               this.comp.infoLayer.top.updateTop(this._dataProxy.fightVO.top);
               this._setAttr();
               this.comp.fightLayer.showPortalsAB(this._dataProxy.fightVO.isAttack);
               this.comp.fightLayer.changePortals(this._dataProxy.fightVO.roadState,this._dataProxy.fightVO.isAttack);
               this._setInspireTip();
               if(this._dataProxy.fightVO.joinCD)
               {
                  this._changeMyState(2,GL.JOIN_CD_TIP);
               }
               else if(this._dataProxy.fightVO.readyJoinCD)
               {
                  this._changeMyState(3,GL.READY_JOIN_CD_TIP);
               }
               else
               {
                  this._changeMyState(0,"");
               }
               if(nowTime < this._dataProxy.fightVO.startTime)
               {
                  this._dataProxy.fightVO.startCD = this._dataProxy.fightVO.startTime;
                  this.comp.infoLayer.addStartCD();
               }
               else
               {
                  this._dataProxy.fightVO.endCD = this._dataProxy.fightVO.endTime;
               }
               if(nowTime < this._dataProxy.fightVO.startTime + 10000)
               {
                  this.comp.infoLayer.playOnce("crossservershipfightmodule.FightStart");
               }
               this._dataProxy.fightVO.endCD = this._dataProxy.fightVO.endTime;
               if(ModuleData.crossServerShipFightReports)
               {
                  this.comp.infoLayer.report.setOldALLReport(ModuleData.crossServerShipFightReports,this._dataProxy.fightVO.myID);
               }
               this.comp.infoLayer.setJoinCDBTNTip(StringUtil.substitute(GL.REMOVE_JOIN_CD_TIP,this._dataProxy.fightVO.joinCDGold,this._dataProxy.fightVO.joinCDGoldIncrease));
               this.comp.infoLayer.setAutoTip(MainData.getInstance().userData.vip >= this._dataProxy.fightVO.autoNeedVip,this._dataProxy.fightVO.autoType);
               this.comp.infoLayer.addMyPetMC(String(this._dataProxy.fightVO.curNepetId));
               this.petShowCD = XmlManager.worldBoatWar.children()[0].attribute("popointerval");
               this.petShowTime = XmlManager.worldBoatWar.children()[0].attribute("popotimes");
               this._addEventListener();
               break;
            case "WORLD_BOAT_VALUE_STRENGTHEN":
               this.comp.infoLayer.updateShipValue(this._dataProxy.fightVO.boatValue = String(this._noteBody));
               break;
            case "WORLD_BOAT_MAP_REFRESH_BOATFACE":
               break;
            case "CROSS_SERVER_SHIP_FIGHT_TOP":
               this._dataProxy.fightVO.top = this._noteBody as Array;
               this.comp.infoLayer.top.updateTop(this._dataProxy.fightVO.top);
               break;
            case "CROSS_SERVER_SHIP_FIGHT_ATTR":
               bool = Boolean(this._noteBody[1]);
               if(bool)
               {
                  bool = Boolean(this.comp.stage);
               }
               this._dataProxy.updatetAttr(this._noteBody[0],bool);
               this._setAttr();
               this._setInspireTip();
               break;
            case "CROSS_SERVER_SHIP_FIGHT_REMOVE_JOIN_CD_OK":
               this._dataProxy.fightVO.joinCD = 0;
               this.comp.infoLayer.countdownLabel[1].text = "";
               this._changeMyState(0,"");
               break;
            case "CROSS_SERVER_SHIP_FIGHT_MY_STATE":
               this._changeMyState(int(this._noteBody[0]),this._noteBody[1]);
               break;
            case "CROSS_SERVER_SHIP_FIGHT_ATTACK_DEFENCE_HP":
               this.comp.infoLayer.inspireBTN.enabled = true;
               break;
            case "CROSS_SERVER_SHIP_FIGHT_ENCOURAGE_CD":
               break;
            case "CROSS_SERVER_SHIP_FIGHT_GO_ON_CD":
               this._dataProxy.fightVO.roleGoOnCD = this._noteBody[2];
               if(this._dataProxy.fightVO.roleGoOnCD - TimeManager.getInstance().getTime() > 1500)
               {
                  this.comp.fightLayer.addArrowAndCD(this.comp.fightLayer.topLayer,this._noteBody[0],this._noteBody[1]);
               }
               break;
            case "CROSS_SERVER_SHIP_FIGHT_END":
               this.comp.fightLayer.removeAllRole();
               this._dataProxy.fightVO.endCD = 0;
               break;
            case "CROSS_SERVER_SHIP_FIGHT_CLEARING":
               PopUpCenter.addPopUp("crossservershipfightmodule.mvc.view.components.ClearingComp",new ClearingComp(this._noteBody.topN,this._noteBody.myTop,this._noteBody.myKill,this._dataProxy.fightVO.canflauntTop,this._dataProxy.fightVO.rankRewardConfig),true,true);
               break;
            case "CROSS_SERVER_SHIP_FIGHT_FLAUNT":
               this._serviceProxy.peacockCS();
               break;
            case "CROSS_SERVER_SHIP_FIGHT_REPORT":
               reportVO = new ReportVO();
               for(keyName in this._noteBody)
               {
                  reportVO[keyName] = this._noteBody[keyName];
               }
               this._reportAndWinStreak(reportVO,Boolean(this.comp.stage));
               break;
            case "CROSS_SERVER_SHIP_FIGHT_DETAIL_REPORT":
               PopUpCenter.addPopUp("crossservershipfightmodule.mvc.view.components.BoatResultWin",new BoatResultWin(this._noteBody),true,true);
               break;
            case "CROSS_SERVER_SHIP_FIGHT_QUIT":
               this._quit();
               break;
            case "WORLD_BOAT_GOLD_CHANGE":
               this.comp.infoLayer.goldAttr.setAttrValue(ModuleData.crossServerShipFightGold);
               break;
            case "SC_WORLD_BOAT_SENDMSG_MAIN":
               if(!ModuleManager.instance.chkModuleIsOpen("WorldBoatWarChatModule"))
               {
                  this.comp.infoLayer.sceneBTN.sayGlow(true);
               }
               break;
            case "CROSS_SERVER_SHIP_FIGHT_AOTU":
               this._dataProxy.fightVO.auto = false;
               if(this._noteBody)
               {
                  this._dataProxy.fightVO.oldPortalsState = this._dataProxy.fightVO.nowPortalsState;
                  this._dataProxy.setNowPortalsState(GL.AUTO_STATE);
                  if(ModuleManager.instance.chkModuleIsOpen("FormationWindow"))
                  {
                     sendNotification("HANDLE_MODULE",new ModuleParams("FormationWindow",ModuleParams.act_Close));
                  }
               }
               else
               {
                  this._dataProxy.setNowPortalsState(this._dataProxy.fightVO.oldPortalsState);
               }
               this._dataProxy.fightVO.auto = Boolean(this._noteBody);
               break;
            case "CROSS_SERVER_SHIP_FIGHT_AOTU_PORTAL":
               transferData = this._getTransferData(int(this._noteBody));
               this._serviceProxy.joinCS(transferData[0],transferData[1],transferData[2]);
               break;
            case "WORLD_BOAT_OPEN_GOLD":
               this._serviceProxy.inGold(int(this._noteBody.goldNum));
               break;
            case "WORLD_BOAT_DATA_UPDATA":
               bridId = int(this._noteBody.brid);
               battinfo = this._noteBody.battleinfo;
               this.reportResultArr[bridId] = battinfo;
               if(this.alertPetReportId != 0)
               {
                  PopUpCenter.removePopUp("crossservershipfightmodule.mvc.view.components.BoatResultWin");
                  resultD = this.reportResultArr[this.alertPetReportId];
                  resultD.myID = this._dataProxy.fightVO.myID;
                  if(resultD.myID == resultD.attacker_info.attacker_id)
                  {
                     PopUpCenter.addPopUp("crossservershipfightmodule.mvc.view.components.BoatResultWin",new BoatResultWin(resultD),true,true);
                  }
                  this.alertPetReportId = 0;
               }
               break;
            case "CROSS_BOAT_PET_FIGHT":
               nowTime = TimeManager.getInstance().getTime();
               if(this._dataProxy.fightVO.petFightCD >= nowTime)
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("worldBoatPetFight.3"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               petId = this._dataProxy.fightVO.curNepetId;
               targetUid = int(this._noteBody.id);
               roadId = int(this._noteBody.transferId);
               needGold = int(XmlManager.worldBoatWar.children().attribute("nepUsegold"));
               if(this._dataProxy.fightVO.petFreeCount != 0)
               {
                  this._serviceProxy.petJoinFight(petId,targetUid,roadId,0);
                  return;
               }
               if(ModuleData.crossServerPetIsFree)
               {
                  this._serviceProxy.petJoinFight(petId,targetUid,roadId,0);
                  return;
               }
               if(ModuleData.crossServerShipFightGold < needGold)
               {
                  sendNotification("POP_TEXT_TIPS",{
                     "text":Globalization.getString("Gl.25"),
                     "textFormat":TextFormatLib.format_0xFF0000_14px
                  });
                  return;
               }
               if(!this._dataProxy.fightVO.petFightNode)
               {
                  _confirmPOP = function(param1:String, param2:String = ""):void
                  {
                     var pop:ConfirmSwitchShowWindow = null;
                     var _check:Function = null;
                     var _confirmCallback:Function = null;
                     var popTXT:String = param1;
                     var onlyName:String = param2;
                     _check = function():void
                     {
                        _dataProxy.fightVO.petFightNode = (pop.cbAlertAgain as SwitchButton).isCheck;
                     };
                     _confirmCallback = function():void
                     {
                        _serviceProxy.petJoinFight(petId,targetUid,roadId,needGold);
                     };
                     pop = PopUpCenter.confirmSwitchShowWin(getViewComponent() as UISprite,onlyName,popTXT + "\r\n",_confirmCallback,null,0,true);
                     (pop.cbAlertAgain as SwitchButton).addEventListener(ButtonEvent.Button_Update,_check);
                  };
                  _confirmPOP(StringUtil.substitute(Globalization.getString("worldBoatPetFight.1"),needGold),"CrossShipPetFightConfirmView");
               }
               else
               {
                  this._serviceProxy.petJoinFight(petId,targetUid,roadId,needGold);
               }
               break;
            case "WORLDBOAT_SETNEPID":
               this._dataProxy.fightVO.curNepetId = int(this._noteBody);
               this.comp.infoLayer.addMyPetMC(String(this._noteBody));
               break;
            case "ALERT_BOAT_PET_RESULT":
               PopUpCenter.removePopUp("crossservershipfightmodule.mvc.view.components.BoatResultWin");
               this.alertPetReportId = int(this._noteBody);
         }
      }
      
      private function _compMouseEvent(param1:MouseEvent) : void
      {
         var sp:Sprite = null;
         var w:Number = NaN;
         var h:Number = NaN;
         var gold:int = 0;
         var roleVO:RoleVO = null;
         var p:Point = null;
         var event:MouseEvent = param1;
         var display:DisplayObject = event.target as DisplayObject;
         if(this._selectMenu)
         {
            MenuInfo.show(this.comp.mouseX,this.comp.mouseY,this._selectMenu[0],[1,2,3,4],this._selectMenu[1]);
            this._selectMenu = null;
         }
         else
         {
            switch(event.type)
            {
               case "click":
                  if(event.target is Sprite)
                  {
                     sp = event.target as Sprite;
                     if(sp.parent.name.indexOf("portals_") != -1 && this._dataProxy.fightVO.nowPortalsState != null)
                     {
                        if(this._dataProxy.fightVO.nowPortalsState == "")
                        {
                           this._dataProxy.setNowPortalsState(null);
                           p = sp.localToGlobal(new Point(0,0));
                           this._serviceProxy.joinCS(this._dataProxy.getTransferID(sp.parent.getChildIndex(sp)),p.x,p.y);
                           this._dataProxy.fightVO.serverRespondTime = TimeManager.getInstance().getTime() + 5000;
                        }
                        else
                        {
                           sendNotification("POP_TEXT_TIPS",{
                              "text":this._dataProxy.fightVO.nowPortalsState,
                              "textFormat":TextFormatLib.format_0x00FF00_14px,
                              "runTime":0.5,
                              "delay":3,
                              "queue":true,
                              "offsetY":-5
                           });
                        }
                     }
                     else if(sp.parent == this.comp.fightLayer.roleLayer)
                     {
                        roleVO = this._dataProxy.fightVO.roleAll[sp.name];
                        if(roleVO)
                        {
                        }
                     }
                     else
                     {
                        switch(sp.name)
                        {
                           case "BTN_REMOVE_JOIN_CD":
                              gold = this._dataProxy.fightVO.joinCDGold + this._dataProxy.fightVO.joinCDGoldIncrease * this._dataProxy.fightVO.removeJoinCDTimes;
                              if(ModuleData.crossServerShipFightGold < gold)
                              {
                                 sendNotification("POP_TEXT_TIPS",{
                                    "text":GL.GOLD_NO,
                                    "textFormat":TextFormatLib.format_0x00FF00_14px,
                                    "runTime":0.5,
                                    "delay":3,
                                    "queue":true,
                                    "offsetY":-5
                                 });
                              }
                              else
                              {
                                 this._confirmSwitchPOP(function():void
                                 {
                                    if(_dataProxy.fightVO.joinCD)
                                    {
                                       _serviceProxy.removeJoinCDCS();
                                    }
                                    else
                                    {
                                       sendNotification("POP_TEXT_TIPS",{
                                          "text":GL.CD_END_TIP,
                                          "textFormat":TextFormatLib.format_0x00FF00_14px,
                                          "runTime":0.5,
                                          "delay":3,
                                          "queue":true,
                                          "offsetY":-5
                                       });
                                    }
                                 },this.comp,"CROSS_SERVER_SHIP_FIGHT_REMOVE_JOIN_CD_OK",StringUtil.substitute(GL.REMOVE_JOIN_CD,gold));
                              }
                              break;
                           case "CrossServerShipFightModuleBTNGoldInspire":
                              if(this._dataProxy.fightVO.attackLevel >= this._dataProxy.fightVO.encourageLevelMax[0] && this._dataProxy.fightVO.defendLevel >= this._dataProxy.fightVO.encourageLevelMax[0] && this._dataProxy.fightVO.hpValue >= this._dataProxy.fightVO.encourageLevelMax[1])
                              {
                                 sendNotification("POP_TEXT_TIPS",{
                                    "text":GL.ENCOURAGE_FAIL_LIMIT,
                                    "textFormat":TextFormatLib.format_0x00FF00_14px,
                                    "runTime":0.5,
                                    "delay":3,
                                    "queue":true,
                                    "offsetY":-5
                                 });
                              }
                              else if(this._dataProxy.fightVO.encourageFreeTimes < 1 && ModuleData.crossServerShipFightGold < this._dataProxy.fightVO.encourageNeedGold)
                              {
                                 sendNotification("POP_TEXT_TIPS",{
                                    "text":GL.ENCOURAGE_FAIL_GOLD_NO,
                                    "textFormat":TextFormatLib.format_0x00FF00_14px,
                                    "runTime":0.5,
                                    "delay":3,
                                    "queue":true,
                                    "offsetY":-5
                                 });
                              }
                              else
                              {
                                 this.comp.infoLayer.inspireBTN.enabled = false;
                                 this._serviceProxy.inspireCS();
                              }
                              break;
                           case "AUTO_CHECK_BOX":
                              if(!this.comp.infoLayer.auto.mouseChildren)
                              {
                                 sendNotification("POP_TEXT_TIPS",{
                                    "text":StringUtil.substitute(GL.AUTO_VIP,this._dataProxy.fightVO.autoNeedVip),
                                    "textFormat":TextFormatLib.format_0x00FF00_14px,
                                    "runTime":0.5,
                                    "delay":3,
                                    "queue":true,
                                    "offsetY":-5
                                 });
                              }
                              break;
                           case "BTNTRANSFERSceneBTNComp":
                              PopUpCenter.addPopUp("GoldInWindow",new GoldInWindow(),true,true);
                              break;
                           case "BTNSAYSceneBTNComp":
                              openModule("WorldBoatWarChatModule",false);
                              this.comp.infoLayer.sceneBTN.sayGlow(false);
                              PopUpCenter.removePopUp("BoatHelpWindow");
                              break;
                           case "BTNSHIPSceneBTNComp":
                              openModule("WorldBoatStrengthenModule",true,true,true,true);
                              break;
                           case "BTNHELPSceneBTNComp":
                              PopUpCenter.addPopUp("BoatHelpWindow",new BoatHelpWindow(),true,true);
                              break;
                           case "BTNQUITSceneBTNComp":
                              PopUpCenter.confirmWin(GL.QUIT,function():void
                              {
                                 sendNotification("CROSS_SERVER_SHIP_FIGHT_QUIT");
                              },null,0,true);
                              PopUpCenter.removePopUp("BoatHelpWindow");
                        }
                     }
                  }
                  break;
               case "mouseDown":
                  w = 1260 - Core.stgW;
                  h = 660 - Core.stgH;
                  if(w != 0 && h != 0)
                  {
                     this.comp.fightLayer.startDrag(false,new Rectangle(-w,-h,w,h));
                  }
                  break;
               case "mouseUp":
                  this.comp.fightLayer.stopDrag();
                  break;
               case "mouseOver":
                  if(event.target is Sprite)
                  {
                     sp = event.target as Sprite;
                     if(sp.parent == this.comp.fightLayer.roleLayer)
                     {
                        roleVO = this._dataProxy.fightVO.roleAll[sp.name];
                        if(roleVO)
                        {
                           this.comp.fightLayer.updateRoleTip(roleVO.tipInfo,sp.x - 80,sp.y);
                        }
                     }
                     else if(sp.parent.name.indexOf("portals_") != -1)
                     {
                        this.comp.fightLayer.updatePortalsTip(StringUtil.substitute(GL.PORTALS_WAIT_NUM,this._dataProxy.fightVO.transfer[this._dataProxy.getTransferID(sp.parent.getChildIndex(sp))]),sp);
                     }
                  }
            }
         }
         if(this.comp && event.type != "mouseOver")
         {
            if(event.type == "mouseOut" && display.parent == this.comp.fightLayer.roleLayer || display.parent != this.comp.fightLayer.roleLayer)
            {
               this.comp.fightLayer.updateRoleTip();
            }
            if(event.type == "mouseOut" && display.parent.name.indexOf("portals_") != -1 || display.parent.name.indexOf("portals_") == -1)
            {
               this.comp.fightLayer.updatePortalsTip();
            }
         }
      }
      
      private function _quit() : void
      {
         PopUpCenter.removePopUp("GoldInWindow");
         PopUpCenter.removePopUp("crossservershipfightmodule.mvc.view.components.BoatResultWin");
         ModuleData.crossServerShipFightReports = [];
         this.reportResultArr = [];
         sendNotification("HANDLE_MODULE",new ModuleParams("WorldBoatWarChatModule",ModuleParams.act_Close));
         GameScene.closeScene(45);
         if(PopUpCenter.containsWin(Core.popContainer.name + "confirmSwitchShowWindow" + "CrossShipPetFightConfirmView"))
         {
            PopUpCenter.removePopUp(Core.popContainer.name + "confirmSwitchShowWindow" + "CrossShipPetFightConfirmView");
         }
      }
      
      private function _reportAndWinStreak(param1:ReportVO, param2:Boolean) : void
      {
         var _loc10_:Boolean = false;
         var _loc3_:int = 0;
         var _loc8_:Array = null;
         var _loc7_:* = null;
         var _loc5_:* = null;
         var _loc11_:TextFormat = TextFormatLib.RED_24;
         var _loc4_:* = "";
         var _loc9_:* = "";
         var _loc6_:int = this._dataProxy.fightVO.myID;
         if(param1.winnerId == _loc6_)
         {
            this._dataProxy.fightVO.winStreakTimes = param1.winStreak;
            _loc10_ = true;
         }
         if(param1.loserId == _loc6_)
         {
            this._dataProxy.fightVO.winStreakTimes = 0;
            _loc10_ = true;
         }
         this.comp.infoLayer.top.myWinStreak.text = String(this._dataProxy.fightVO.winStreakTimes);
         if(param1.winStreak && param1.winStreak < 5 && param1.winnerId == _loc6_)
         {
            if(param2)
            {
               sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("groupwar." + (String(21 + param1.winStreak))),
                  "textFormat":TextFormatLib.RED_22,
                  "runTime":0.5,
                  "delay":3,
                  "queue":true,
                  "offsetY":-5
               });
            }
         }
         if(param1.winStreak > 4 && param1.winnerType == 0)
         {
            _loc10_ = true;
            if(param2)
            {
               _loc3_ = this._dataProxy.fightVO.winStreakConfig.length - 1;
               while(_loc3_ > -1)
               {
                  if(param1.winStreak == int(this._dataProxy.fightVO.winStreakConfig[_loc3_][0]))
                  {
                     _loc8_ = this._dataProxy.fightVO.winStreakConfig[_loc3_][3].split(",");
                     _loc7_ = "<font color=\'" + _loc8_[1] + "\'>" + param1.winnerName + "</font>";
                     _loc4_ = "<font color=\'" + _loc8_[0] + "\'>" + this._dataProxy.fightVO.winStreakConfig[_loc3_][1].replace(/\{0\}/g,_loc7_) + "</font>";
                     sendNotification("POP_TEXT_TIPS",{
                        "text":_loc4_,
                        "textFormat":_loc11_,
                        "runTime":0.5,
                        "delay":3,
                        "queue":true,
                        "offsetY":-5
                     });
                     break;
                  }
                  _loc3_--;
               }
            }
         }
         if(param1.terminalStreak > 4 && param1.loserType == 0)
         {
            _loc10_ = true;
            if(param2 && param1.nep_uuid == param1.winnerId)
            {
               _loc3_ = this._dataProxy.fightVO.winStreakConfig.length - 1;
               while(_loc3_ > -1)
               {
                  if(param1.terminalStreak >= int(this._dataProxy.fightVO.winStreakConfig[_loc3_][0]))
                  {
                     _loc8_ = this._dataProxy.fightVO.winStreakConfig[_loc3_][3].split(",");
                     _loc7_ = "<font color=\'" + _loc8_[1] + "\'>" + param1.winnerName + "</font>";
                     _loc5_ = "<font color=\'" + _loc8_[1] + "\'>" + param1.loserName + "</font>";
                     _loc9_ = "<font color=\'" + _loc8_[0] + "\'>" + this._dataProxy.fightVO.winStreakConfig[_loc3_][2].replace(/\{0\}/g,_loc7_).replace(/\{1\}/g,_loc5_).replace(/\{2\}/g,param1.terminalStreak) + "</font>";
                     sendNotification("POP_TEXT_TIPS",{
                        "text":_loc9_,
                        "textFormat":_loc11_,
                        "runTime":0.5,
                        "delay":3,
                        "queue":true,
                        "offsetY":-5
                     });
                     break;
                  }
                  _loc3_--;
               }
            }
         }
         if(_loc10_)
         {
            if(ModuleData.crossServerShipFightReports == null)
            {
               ModuleData.crossServerShipFightReports = [];
            }
            ModuleData.crossServerShipFightReports[ModuleData.crossServerShipFightReports.length] = param1;
            if(ModuleData.crossServerShipFightReports.length > this._dataProxy.fightVO.reportMax * 2)
            {
               ModuleData.crossServerShipFightReports.splice(0,this._dataProxy.fightVO.reportMax);
            }
            this.comp.infoLayer.report.setReport(param1,_loc6_);
         }
      }
      
      private function _getTransferData(param1:int) : Array
      {
         var _loc9_:MovieClip = null;
         var _loc7_:Array = null;
         var _loc8_:int = 0;
         var _loc2_:int = 0;
         var _loc5_:Array = null;
         if(this._dataProxy.fightVO.isAttack)
         {
            _loc9_ = this.comp.fightLayer.portalsA;
            _loc7_ = this._dataProxy.fightVO.transfer.slice(0,this._dataProxy.fightVO.roadsNum);
         }
         else
         {
            _loc9_ = this.comp.fightLayer.portalsB;
            _loc7_ = this._dataProxy.fightVO.transfer.slice(this._dataProxy.fightVO.roadsNum - 1,this._dataProxy.fightVO.transfer.length);
            _loc8_ = this._dataProxy.fightVO.roadsNum;
         }
         var _loc3_:Array = [];
         var _loc6_:int = this._dataProxy.fightVO.roadsNum - 1;
         while(_loc6_ > -1)
         {
            if(_loc9_.getChildAt(_loc6_).visible)
            {
               _loc3_[_loc3_.length] = _loc6_;
            }
            else
            {
               _loc7_.splice(_loc6_,1);
            }
            _loc6_--;
         }
         switch(param1 - 1)
         {
            case 0:
               _loc2_ = int(_loc3_[Math.floor(Math.random() * _loc3_.length)]);
               break;
            case 1:
               _loc5_ = _loc7_.concat();
               _loc5_.sort(16);
               _loc2_ = int(_loc7_.indexOf(int(_loc5_.shift())));
         }
         _loc8_ += _loc2_;
         var _loc4_:Point = _loc9_.getChildAt(_loc2_).localToGlobal(new Point(0,0));
         return [_loc8_,_loc4_.x,_loc4_.y];
      }
      
      private function _setAttr() : void
      {
         this.comp.infoLayer.goldAttr.setAttrValue(ModuleData.crossServerShipFightGold);
         this.comp.infoLayer.updateShipValue(this._dataProxy.fightVO.boatValue);
         this.comp.infoLayer.attackLevel.text = "+" + String(this._dataProxy.fightVO.attackValue);
         this.comp.infoLayer.defendLevel.text = "+" + String(this._dataProxy.fightVO.defendValue);
         this.comp.infoLayer.hpLevel.text = "+" + String(this._dataProxy.fightVO.hpValue);
         this.comp.infoLayer.top.myScore.text = String(this._dataProxy.fightVO.score);
         this.comp.infoLayer.top.myHonor.text = String(this._dataProxy.fightVO.honour);
         this.comp.infoLayer.top.myWinStreak.text = String(this._dataProxy.fightVO.winStreakTimes);
      }
      
      private function _setInspireTip() : void
      {
         var _loc1_:String = StringUtil.substitute(GL.ENCOURAGE_GOLD_TIP,this._dataProxy.fightVO.encourageNeedGold);
         this.comp.infoLayer.inspireGoldAttr.setAttrValue(this._dataProxy.fightVO.encourageNeedGold);
         this.comp.infoLayer.inspireGoldAttr.parent.visible = true;
         this.comp.infoLayer.inspireFree.htmlText = "";
         if(this._dataProxy.fightVO.encourageFreeTimes > 0)
         {
            this.comp.infoLayer.inspireGoldAttr.parent.visible = false;
            _loc1_ += "， <font color=\'#FF0000\'>" + GL.ENCOURAGE_FREE + "</font>";
            this.comp.infoLayer.inspireFree.htmlText = StringUtil.substitute(GL.ENCOURAGE_FREE_TIMES,this._dataProxy.fightVO.encourageFreeTimes);
         }
         this.comp.infoLayer.inspireBTN.setToolTip(_loc1_);
      }
      
      private function _changeMyState(param1:int, param2:String = null) : void
      {
         this._dataProxy.fightVO.myState = param1;
         this.comp.infoLayer.setMyState(this._dataProxy.fightVO.myState);
         if(param2 != null)
         {
            this._dataProxy.setNowPortalsState(param2);
         }
      }
      
      private function _confirmSwitchPOP(param1:Function, param2:Sprite, param3:String, param4:String, param5:int = 0, param6:Function = null) : void
      {
         var pop:ConfirmSwitchShowWindow = null;
         var dt:Array = null;
         var callback:Function = param1;
         var par:Sprite = param2;
         var unique:String = param3;
         var content:String = param4;
         var node:int = param5;
         var noCallback:Function = param6;
         if(node && MainData.getInstance().closeGoldNoticeData.isCloseGoldNotice(node) || ModuleData.getNotConfirm(unique))
         {
            callback();
         }
         else
         {
            pop = PopUpCenter.getWinByName(par.name + "confirmSwitchShowWindow" + unique) as ConfirmSwitchShowWindow;
            if(node && pop)
            {
               pop.cbAlertAgain.isCheck = MainData.getInstance().closeGoldNoticeData.isCloseGoldNotice(node);
            }
            pop = PopUpCenter.confirmSwitchShowWin(par,unique,content + "\r\n",callback,noCallback,0,true);
            if(node)
            {
               (pop.cbAlertAgain as SwitchButton).addEventListener(ButtonEvent.Button_Update,function():void
               {
                  dt = MainData.getInstance().closeGoldNoticeData.setCloseGoldNotic(node,(pop.cbAlertAgain as SwitchButton).isCheck);
                  sendNotification("CS_SET_COSTNOTICE",dt);
               });
            }
            else
            {
               (pop.cbAlertAgain as SwitchButton).addEventListener(ButtonEvent.Button_Update,function():void
               {
                  if(!pop.isAlertAgain)
                  {
                     ModuleData.notConfirm[ModuleData.notConfirm.length] = unique;
                  }
               });
               if(!pop.isAlertAgain)
               {
                  callback();
               }
            }
         }
      }
      
      private function _linkHandler(param1:TextEvent) : void
      {
         var _loc2_:Object = null;
         var _loc3_:Array = param1.text.split("_");
         if(_loc3_[0] == "playback")
         {
            _loc2_ = this.reportResultArr[int(_loc3_[1])];
            _loc2_.myID = this._dataProxy.fightVO.myID;
            PopUpCenter.addPopUp("crossservershipfightmodule.mvc.view.components.BoatResultWin",new BoatResultWin(_loc2_),true,true);
         }
         else if(_loc3_[0] == "uid")
         {
            this._selectMenu = [_loc3_[1],_loc3_[2]];
         }
      }
      
      private function _autoUpdate(param1:Event) : void
      {
         sendNotification("CROSS_SERVER_SHIP_FIGHT_AOTU",this.comp.infoLayer.autoCheckBox.isCheck);
         this._dataProxy.fightVO.autoCD = this._dataProxy.fightVO.aotuCDTime;
      }
      
      private function _enterFrameHandler(param1:Event) : void
      {
         this.comp.fightLayer.roleWalk(this._dataProxy.fightVO.roleAll,this._dataProxy.fightVO.transferWalk);
      }
      
      private function _timerHandler(param1:TimerEvent) : void
      {
         this._timeRun();
      }
      
      private function _timeRun() : void
      {
         var _loc2_:Number = TimeManager.getInstance().getTime();
         if(this._dataProxy.fightVO.roleGoOnCD)
         {
            this.comp.fightLayer.freshenTime(_loc2_,0,this._dataProxy.fightVO.roleGoOnCD,true);
            if(this._dataProxy.fightVO.roleGoOnCD < _loc2_)
            {
               this._dataProxy.fightVO.roleGoOnCD = 0;
            }
         }
         if(this._dataProxy.fightVO.serverRespondTime && _loc2_ > this._dataProxy.fightVO.serverRespondTime)
         {
            this._dataProxy.fightVO.serverRespondTime = 0;
            this._dataProxy.fightVO.nowPortalsState = "";
         }
         if(this._dataProxy.fightVO.endCD)
         {
            this.comp.infoLayer.freshenTime(0,_loc2_,0,this._dataProxy.fightVO.endCD,true,"",false,false);
         }
         if(this._dataProxy.fightVO.joinCD)
         {
            this.comp.infoLayer.freshenTime(1,_loc2_,0,this._dataProxy.fightVO.joinCD,true);
            if(this._dataProxy.fightVO.joinCD < _loc2_)
            {
               sendNotification("CROSS_SERVER_SHIP_FIGHT_REMOVE_JOIN_CD_OK");
            }
         }
         if(this._dataProxy.fightVO.startCD)
         {
            this.comp.infoLayer.warCountdown.visible = false;
            this.comp.infoLayer.freshenTime(2,_loc2_,0,this._dataProxy.fightVO.startCD,true);
            if(this._dataProxy.fightVO.startCD < _loc2_)
            {
               this._dataProxy.fightVO.startCD = 0;
               this.comp.infoLayer.removeStartCD();
               this._dataProxy.fightVO.endCD = this._dataProxy.fightVO.endTime;
               this._dataProxy.setNowPortalsState("");
               this.comp.infoLayer.warCountdown.visible = true;
            }
         }
         if(this._dataProxy.fightVO.readyJoinCD)
         {
            this.comp.infoLayer.freshenTime(3,_loc2_,0,this._dataProxy.fightVO.readyJoinCD,true);
            if(this._dataProxy.fightVO.readyJoinCD < _loc2_)
            {
               this._dataProxy.fightVO.readyJoinCD = 0;
               this.comp.infoLayer.countdownLabel[3].text = "";
               this._changeMyState(0,"");
            }
         }
         var _loc1_:String = "";
         if(this._dataProxy.fightVO.petFightCD)
         {
            this.comp.infoLayer.freshenTime(5,_loc2_,0,this._dataProxy.fightVO.petFightCD,true);
            if(this._dataProxy.fightVO.petFightCD < _loc2_)
            {
               this._dataProxy.fightVO.petFightCD = 0;
               this.comp.infoLayer.boatPetCD.textColor = 65280;
               this.comp.infoLayer.boatPetCD.text = GL.CAN_JOIN;
               this.tCount++;
               this.tCDCount++;
               if(this.tCount == this.petShowTime && this.tCDCount != this.petShowCD)
               {
                  this.comp.infoLayer.petTalkSp.visible = false;
                  this.tCount = 0;
               }
               else if(this.tCDCount == this.petShowCD)
               {
                  _loc1_ = this.popTxtStr();
                  this.comp.infoLayer.talkLb.text = _loc1_;
                  this.comp.infoLayer.petTalkSp.visible = true;
                  this.comp.infoLayer.petTalkBg.height = this.comp.infoLayer.talkLb.height + 15;
                  this.tCDCount = 0;
                  this.tCount = 0;
               }
            }
            else
            {
               this.comp.infoLayer.boatPetCD.textColor = 16711680;
               this.comp.infoLayer.petTalkSp.visible = false;
            }
         }
         else
         {
            this.comp.infoLayer.boatPetCD.textColor = 65280;
            this.comp.infoLayer.boatPetCD.text = GL.CAN_JOIN;
            this.tCount++;
            this.tCDCount++;
            if(this.tCount == this.petShowTime && this.tCDCount != this.petShowCD)
            {
               this.comp.infoLayer.petTalkSp.visible = false;
               this.tCount = 0;
            }
            else if(this.tCDCount == this.petShowCD)
            {
               _loc1_ = this.popTxtStr();
               this.comp.infoLayer.talkLb.text = _loc1_;
               this.comp.infoLayer.petTalkSp.visible = true;
               this.comp.infoLayer.petTalkBg.height = this.comp.infoLayer.talkLb.height + 15;
               this.tCDCount = 0;
               this.tCount = 0;
            }
         }
         if(this.comp.infoLayer.autoCheckBox.isCheck && this._dataProxy.fightVO.myState == 0)
         {
            if(this._dataProxy.fightVO.endCD - _loc2_ > 0)
            {
               if(this._dataProxy.fightVO.autoCD < 0)
               {
                  this._dataProxy.fightVO.autoCD = this._dataProxy.fightVO.aotuCDTime;
                  sendNotification("CROSS_SERVER_SHIP_FIGHT_AOTU_PORTAL",this._dataProxy.fightVO.autoType);
               }
               else
               {
                  this._dataProxy.fightVO.autoCD--;
               }
            }
         }
      }
      
      private function popTxtStr() : String
      {
         var _loc4_:String = null;
         var _loc2_:String = null;
         var _loc5_:Array = null;
         var _loc3_:Array = null;
         var _loc1_:int = 0;
         var _loc8_:int = 0;
         var _loc6_:int = 0;
         var _loc7_:Array = null;
         _loc2_ = XmlManager.worldNeptune.children().(@ID == _dataProxy.fightVO.curNepetId).attribute("popotxt");
         _loc5_ = _loc2_.split(";");
         _loc3_ = [];
         _loc1_ = 0;
         while(_loc1_ < _loc5_.length)
         {
            _loc7_ = _loc5_[_loc1_].split("|");
            _loc3_.push(_loc7_);
            _loc1_++;
         }
         _loc8_ = Math.round(Math.random() * 100);
         _loc6_ = 0;
         while(_loc6_ < _loc3_.length)
         {
            if(_loc6_ == 0)
            {
               if(_loc8_ <= _loc3_[_loc6_][1])
               {
                  _loc4_ = _loc3_[_loc6_][0];
                  break;
               }
            }
            else if(_loc6_ == _loc3_.length - 1)
            {
               if(_loc8_ <= _loc3_[_loc3_.length - 1][1] && _loc8_ > _loc3_[_loc3_.length - 2][1])
               {
                  _loc4_ = _loc3_[_loc3_.length - 1][0];
                  break;
               }
            }
            else if(_loc8_ > _loc3_[_loc6_ - 1][1] && _loc8_ <= _loc3_[_loc6_][1])
            {
               _loc4_ = _loc3_[_loc6_][0];
               break;
            }
            _loc6_++;
         }
         return _loc4_;
      }
      
      private function _setTimer() : void
      {
         if(this._timer == null)
         {
            this._timer = new Timer(1000);
            this._timer.addEventListener("timer",this._timerHandler);
            this._timer.start();
            this._timeRun();
         }
      }
      
      private function _killTimer() : void
      {
         if(this._timer)
         {
            this._timer.reset();
            this._timer.removeEventListener("timer",this._timerHandler);
            this._timer = null;
         }
      }
      
      private function _swfGetFocus(param1:Event) : void
      {
         this._dataProxy.fightVO.correct = true;
      }
      
      private function _swfLosesFocus(param1:Event) : void
      {
         this._dataProxy.fightVO.correct = false;
      }
      
      private function _addEventListener() : void
      {
         this.comp.fightLayer.roleLayer.addEventListener("enterFrame",this._enterFrameHandler);
         this.comp.addEventListener("click",this._compMouseEvent);
         this.comp.addEventListener("mouseOver",this._compMouseEvent);
         this.comp.addEventListener("mouseOut",this._compMouseEvent);
         this.comp.addEventListener("mouseDown",this._compMouseEvent);
         this.comp.addEventListener("mouseUp",this._compMouseEvent);
         Core.stg.addEventListener("activate",this._swfGetFocus);
         Core.stg.addEventListener("deactivate",this._swfLosesFocus);
         this.comp.addEventListener("link",this._linkHandler);
         this.comp.infoLayer.autoCheckBox.addEventListener(ButtonEvent.Button_Update,this._autoUpdate);
         this._setTimer();
      }
      
      private function _killEventListener() : void
      {
         this.comp.fightLayer.roleLayer.removeEventListener("enterFrame",this._enterFrameHandler);
         this.comp.removeEventListener("click",this._compMouseEvent);
         this.comp.removeEventListener("mouseOver",this._compMouseEvent);
         this.comp.removeEventListener("mouseOut",this._compMouseEvent);
         this.comp.removeEventListener("mouseDown",this._compMouseEvent);
         this.comp.removeEventListener("mouseUp",this._compMouseEvent);
         Core.stg.removeEventListener("activate",this._swfGetFocus);
         Core.stg.removeEventListener("deactivate",this._swfLosesFocus);
         this.comp.removeEventListener("link",this._linkHandler);
         this.comp.infoLayer.autoCheckBox.removeEventListener(ButtonEvent.Button_Update,this._autoUpdate);
         this._killTimer();
      }
      
      public function get comp() : CrossServerShipFightComp
      {
         return viewComponent as CrossServerShipFightComp;
      }
   }
}

