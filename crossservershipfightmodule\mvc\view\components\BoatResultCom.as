package crossservershipfightmodule.mvc.view.components
{
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import util.Globalization;
   
   public class BoatResultCom extends UISprite
   {
      private var comBg:UISkin;
      
      public var boatName:Label;
      
      private var boatAttLb:Label;
      
      private var boatDefLb:Label;
      
      private var boatHpLb:Label;
      
      private var boatAttPreLb:Label;
      
      private var boatAttAfterLb:Label;
      
      public var attLb:Label;
      
      public var defLb:Label;
      
      public var hpLb:Label;
      
      public var attPreLb:Label;
      
      public var attAfterLb:Label;
      
      public var firstAttSkin:UISkin;
      
      public function BoatResultCom()
      {
         super();
         this.comBg = UIManager.getUISkin("worldBoatResultBlack");
         this.comBg.width = 165;
         this.comBg.height = 145;
         addChild(this.comBg);
         this.boatName = new Label("",TextFormatLib.format_0xFF0000_12px);
         this.boatName.y = 5;
         this.boatName.x = 10;
         addChild(this.boatName);
         this.boatAttLb = new Label(Globalization.getString("worldBoatResult.1"),TextFormatLib.format_0xFFF000_12px);
         this.boatAttLb.y = this.boatName.y + 30;
         this.boatAttLb.x = this.boatName.x;
         addChild(this.boatAttLb);
         this.attLb = new Label("",TextFormatLib.format_0x00FF00_12px);
         this.attLb.x = this.boatAttLb.x + 55;
         this.attLb.y = this.boatAttLb.y;
         addChild(this.attLb);
         this.boatDefLb = new Label(Globalization.getString("worldBoatResult.2"),TextFormatLib.format_0xFFF000_12px);
         this.boatDefLb.y = this.boatAttLb.y + 20;
         this.boatDefLb.x = this.boatName.x;
         this.defLb = new Label("",TextFormatLib.format_0x00FF00_12px);
         this.defLb.x = this.boatDefLb.x + 55;
         this.defLb.y = this.boatDefLb.y;
         this.boatHpLb = new Label(Globalization.getString("worldBoatResult.3"),TextFormatLib.format_0xFFF000_12px);
         this.boatHpLb.y = this.boatDefLb.y + 25;
         this.boatHpLb.x = this.boatName.x;
         this.hpLb = new Label("",TextFormatLib.format_0x00FF00_12px);
         this.hpLb.x = this.boatHpLb.x + 65;
         this.hpLb.y = this.boatHpLb.y;
         this.boatAttPreLb = new Label(Globalization.getString("worldBoatResult.4"),TextFormatLib.format_0xFFF000_12px);
         this.boatAttPreLb.y = this.boatAttLb.y + 30;
         this.boatAttPreLb.x = this.boatName.x;
         addChild(this.boatAttPreLb);
         this.attPreLb = new Label("",TextFormatLib.format_0x00FF00_12px);
         this.attPreLb.x = this.boatAttPreLb.x + 75;
         this.attPreLb.y = this.boatAttPreLb.y;
         addChild(this.attPreLb);
         this.boatAttAfterLb = new Label(Globalization.getString("worldBoatResult.5"),TextFormatLib.format_0xFFF000_12px);
         this.boatAttAfterLb.y = this.boatAttPreLb.y + 25;
         this.boatAttAfterLb.x = this.boatName.x;
         addChild(this.boatAttAfterLb);
         this.attAfterLb = new Label("",TextFormatLib.format_0x00FF00_12px);
         this.attAfterLb.x = this.boatAttAfterLb.x + 100;
         this.attAfterLb.y = this.boatAttAfterLb.y;
         addChild(this.attAfterLb);
         this.firstAttSkin = UIManager.getUISkin("worldBoatFirstAtt");
         this.firstAttSkin.x = 120;
         this.firstAttSkin.y = -10;
         addChild(this.firstAttSkin);
      }
      
      public function updateComp(param1:Object, param2:Boolean, param3:Boolean) : void
      {
         var _loc4_:int = 0;
         if(param3)
         {
            if(param1.attacker_name.indexOf(".s") != -1)
            {
               this.boatName.text = "" + param1.attacker_name;
            }
            else
            {
               this.boatName.text = param1.attacker_name + ".S" + param1.attacker_serverid;
            }
            this.attLb.text = "" + param1.attacker_nepfightval;
            this.attPreLb.text = "" + (param1.attacker_attack - 2000);
            this.attAfterLb.text = "" + (param1.attacker_phy - 16000);
         }
         else
         {
            _loc4_ = param1.defender_attack + param1.defender_defence + param1.defender_maxhp / 10;
            if(param1.defender_name.indexOf(".s") != -1)
            {
               this.boatName.text = param1.defender_name + "";
            }
            else
            {
               this.boatName.text = param1.defender_name + ".S" + param1.defender_serverid;
            }
            this.attLb.text = "" + _loc4_;
            this.attPreLb.text = param1.defender_befor_hp;
            this.attAfterLb.text = param1.defender_after_hp;
         }
         if(param3)
         {
            this.boatAttLb.text = Globalization.getString("worldBoatPetFight.6");
            this.boatAttPreLb.text = Globalization.getString("worldBoatPetFight.7");
            this.boatAttAfterLb.text = Globalization.getString("worldBoatPetFight.8");
         }
         else
         {
            this.boatAttLb.text = Globalization.getString("worldBoatResult.1");
            this.boatAttPreLb.text = Globalization.getString("worldBoatResult.4");
            this.boatAttAfterLb.text = Globalization.getString("worldBoatResult.5");
         }
         this.attLb.x = this.boatAttLb.x + this.boatAttLb.width + 10;
         this.attPreLb.x = this.boatAttPreLb.x + this.boatAttPreLb.width + 10;
         this.attAfterLb.x = this.boatAttAfterLb.x + this.boatAttAfterLb.width + 10;
      }
   }
}

