package crossservershipfightmodule.mvc.view.components
{
   import flash.events.MouseEvent;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class BoatResultWin extends PopUpWindow
   {
      public static const NAME:String = "crossservershipfightmodule.mvc.view.components.BoatResultWin";
      
      private var winBg:UISkin;
      
      private var comBg:UISkin;
      
      private var leftCom:BoatResultCom;
      
      private var rightCom:BoatResultCom;
      
      public var conBtn:Button;
      
      public var titleS:String;
      
      public function BoatResultWin(param1:Object)
      {
         super(425,240,UIManager.getUISkin("win_blank"),false);
         if(int(param1.myID) == int(param1.winner_id))
         {
            this.titleS = "CrossServerShipFightModuleReport1";
         }
         else
         {
            this.titleS = "CrossServerShipFightModuleReport2";
         }
         setTitleImageData(AssetManager.getImgData(this.titleS),-25);
         this.winBg = UIManager.getUISkin("worldBoatResultBg");
         this.winBg.width = 410;
         this.winBg.height = 165;
         this.winBg.x = 8;
         this.winBg.y = 30;
         addChild(this.winBg);
         this.leftCom = new BoatResultCom();
         this.leftCom.x = 20;
         this.leftCom.y = 40;
         addChild(this.leftCom);
         var _loc2_:UISkin = UIManager.getUISkin("CrossServerShipFightModuleReportVS");
         _loc2_.x = 190;
         _loc2_.y = 95;
         addChild(_loc2_);
         this.rightCom = new BoatResultCom();
         this.rightCom.x = 240;
         this.rightCom.y = 40;
         addChild(this.rightCom);
         this.conBtn = new Button(Globalization.getString("Gl.161"),null,70);
         this.conBtn.x = 175;
         this.conBtn.y = 200;
         addChild(this.conBtn);
         if(param1.myID == param1.attacker_info.attacker_id)
         {
            this.leftCom.updateComp(param1.attacker_info,true,true);
            this.rightCom.updateComp(param1.defender_info,false,false);
            this.leftCom.firstAttSkin.visible = true;
            this.rightCom.firstAttSkin.visible = false;
         }
         else if(param1.myID == param1.defender_info.defender_id)
         {
            this.leftCom.updateComp(param1.defender_info,false,false);
            this.rightCom.updateComp(param1.attacker_info,true,true);
            this.leftCom.firstAttSkin.visible = false;
            this.rightCom.firstAttSkin.visible = true;
         }
         this.conBtn.addEventListener("click",this.closeHandler);
      }
      
      private function closeHandler(param1:MouseEvent) : void
      {
         this.conBtn.removeEventListener("click",this.closeHandler);
         this.close();
      }
      
      override public function close() : void
      {
         super.close();
      }
   }
}

