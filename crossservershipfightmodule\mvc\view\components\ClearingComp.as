package crossservershipfightmodule.mvc.view.components
{
   import crossservershipfightmodule.mvc.view.utils.GL;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   
   public class ClearingComp extends PopUpWindow
   {
      public static const NAME:String = "crossservershipfightmodule.mvc.view.components.ClearingComp";
      
      private var _tip:String;
      
      public function ClearingComp(param1:Array, param2:String, param3:String, param4:int, param5:Vector.<Vector.<int>>)
      {
         var _loc13_:UISprite = null;
         var _loc11_:UISkin = null;
         var _loc22_:UISkin = null;
         var _loc12_:Label = null;
         var _loc23_:UISkin = null;
         var _loc24_:UISkin = null;
         var _loc14_:UISkin = null;
         var _loc15_:Label = null;
         super(1260,660,UIManager.getUISkin("CrossServerShipFightModuleEndBg"),false);
         this.isLive = false;
         this.canEscClose = false;
         this.allowDrag = false;
         var _loc18_:UISkin = addChild(UIManager.getUISkin("CrossServerShipFightModuleEndTitle")) as UISkin;
         _loc18_.x = 570;
         _loc18_.y = 120;
         var _loc21_:int = int(param1.length);
         if(_loc21_ > 3)
         {
            _loc21_ = 3;
         }
         var _loc20_:int = 0;
         while(_loc20_ < _loc21_)
         {
            _loc13_ = addChild(new UISprite()) as UISprite;
            _loc13_.x = 385;
            _loc13_.y = 200 + _loc20_ * 50;
            _loc11_ = _loc13_.addChild(UIManager.getUISkin("CrossServerShipFightModuleEndTopBG" + (_loc20_ + 1))) as UISkin;
            _loc22_ = _loc13_.addChild(UIManager.getUISkin("CrossServerShipFightModuleEndGetTitle1")) as UISkin;
            _loc22_.x = 48 - _loc20_ * 22;
            _loc12_ = _loc13_.addChild(new Label(param1[_loc20_].uname,TextFormatLib.format_0xFFFFFF_18px_verdana)) as Label;
            _loc12_.x = _loc22_.x + _loc22_.width;
            _loc23_ = _loc13_.addChild(UIManager.getUISkin("CrossServerShipFightModuleEndGetTitle2")) as UISkin;
            _loc23_.x = _loc12_.x + _loc12_.textWidth + 6;
            _loc24_ = _loc13_.addChild(UIManager.getUISkin("CrossServerShipFightModuleEndTop" + (_loc20_ + 1))) as UISkin;
            _loc24_.x = _loc23_.x + _loc23_.width;
            _loc14_ = _loc13_.addChild(UIManager.getUISkin("CrossServerShipFightModuleEndGetTitle3")) as UISkin;
            _loc14_.x = _loc24_.x + _loc24_.width;
            _loc15_ = _loc13_.addChild(new Label(this._rankReward(_loc20_ + 1,param5,param1[_loc20_].userlevel),TextFormatLib.format_0xFFFFFF_18px_verdana)) as Label;
            _loc15_.x = _loc14_.x + _loc14_.width;
            _loc22_.y = _loc23_.y = _loc24_.y = _loc14_.y = 10;
            _loc12_.y = _loc15_.y = 8;
            _loc11_.setSize(_loc15_.x + _loc15_.width + 90,_loc11_.height);
            _loc20_++;
         }
         var _loc19_:UISkin = addChild(UIManager.getUISkin("CrossServerShipFightModuleEndMyTitle1")) as UISkin;
         _loc19_.x = 528;
         var _loc8_:Label = addChild(new Label(param2,TextFormatLib.format_0xFF0000_18px)) as Label;
         _loc8_.x = 744;
         _loc19_.y = _loc8_.y = 350;
         _loc8_.width = 60;
         _loc8_.autoSize = "center";
         var _loc9_:UISkin = addChild(UIManager.getUISkin("CrossServerShipFightModuleEndGetTitle3")) as UISkin;
         _loc9_.x = _loc19_.x;
         var _loc7_:Label = addChild(new Label(this._rankReward(int(param2),param5,MainData.getInstance().groupData.roleModle.level),TextFormatLib.format_0xFFFFFF_18px_verdana)) as Label;
         _loc7_.x = _loc9_.x + _loc9_.width;
         _loc9_.y = 380;
         _loc7_.y = 378;
         var _loc10_:UISkin = addChild(UIManager.getUISkin("CrossServerShipFightModuleEndMyTitle2")) as UISkin;
         _loc10_.x = 608;
         _loc10_.y = 386;
         var _loc6_:Label = addChild(new Label(param3,TextFormatLib.format_0x00FF24_18px)) as Label;
         _loc6_.x = 700;
         _loc10_.y = 406;
         _loc6_.y = 404;
         var _loc16_:Button = addChild(new Button(GL.CONFIRM,null,80)) as Button;
         _loc16_.x = 552;
         _loc16_.y = 440;
         _loc16_.addEventListener("click",this._confirmMouseEvent);
         var _loc17_:Button = addChild(new Button(GL.FLAUNT,null,80)) as Button;
         _loc17_.x = _loc16_.x + _loc16_.width + 30;
         _loc17_.y = _loc16_.y;
         _loc17_.addEventListener("click",this._flauntMouseEvent);
         if(int(param2) > param4)
         {
            this._tip = StringUtil.substitute(GL.CAN_FLAUNT_TIP,param4);
         }
      }
      
      private function _rankReward(param1:int, param2:Vector.<Vector.<int>>, param3:int) : String
      {
         var _loc4_:Vector.<int> = null;
         var _loc9_:* = 0;
         var _loc8_:int = 0;
         var _loc10_:* = "";
         var _loc6_:int = param2.length - 1;
         var _loc7_:Array = [];
         var _loc5_:Array = [];
         if(param1)
         {
            _loc9_ = _loc6_;
            while(_loc9_ > -1)
            {
               if(param2[_loc9_][0] == param1)
               {
                  _loc4_ = param2[_loc9_];
                  break;
               }
               _loc4_ = param2[_loc6_];
               _loc9_--;
            }
            if(_loc4_[1])
            {
               if(_loc10_ != "")
               {
                  _loc10_ += "，";
               }
               _loc10_ += _loc4_[1] * param3 + GL.BELLY;
            }
            if(_loc4_[2])
            {
               if(_loc10_ != "")
               {
                  _loc10_ += "，";
               }
               _loc10_ += _loc4_[2] + GL.EXPERIECE;
            }
            if(_loc4_[3])
            {
               if(_loc10_ != "")
               {
                  _loc10_ += "，";
               }
               _loc10_ += _loc4_[3] + GL.GOLD;
            }
            if(_loc4_[4])
            {
               if(_loc10_ != "")
               {
                  _loc10_ += "，";
               }
               _loc10_ += _loc4_[4] + GL.PRESTIGE;
            }
            if(_loc4_[5])
            {
               if(_loc10_ != "")
               {
                  _loc10_ += "，";
               }
               _loc10_ += _loc4_[5] + GL.EXECUTION;
            }
            if(_loc4_[7])
            {
               if(_loc10_ != "")
               {
                  _loc10_ += "，";
               }
               _loc10_ += _loc4_[7] + GL.HONOUR;
            }
         }
         return _loc10_;
      }
      
      private function _confirmMouseEvent(param1:MouseEvent) : void
      {
         close();
         AppFacade.instance.sendNotification("CROSS_SERVER_SHIP_FIGHT_QUIT");
      }
      
      private function _flauntMouseEvent(param1:MouseEvent) : void
      {
         if(this._tip)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":this._tip,
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
         }
         else
         {
            AppFacade.instance.sendNotification("CROSS_SERVER_SHIP_FIGHT_FLAUNT");
            this._tip = GL.FLAUNT_TIP;
         }
      }
   }
}

