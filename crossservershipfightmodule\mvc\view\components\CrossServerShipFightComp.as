package crossservershipfightmodule.mvc.view.components
{
   import crossservershipfightmodule.mvc.controller.ModelPrepPathsCommand;
   import crossservershipfightmodule.mvc.controller.ModelRoleDeleteCommand;
   import crossservershipfightmodule.mvc.controller.ModelRoleRefreshCommand;
   import crossservershipfightmodule.mvc.controller.StartupCommand;
   import flash.events.Event;
   import game.mvc.AppFacade;
   import game.mvc.module.ModulePart;
   
   public class CrossServerShipFightComp extends ModulePart
   {
      public static const NAME:String = "crossservershipfightmodule.mvc.view.components.CrossServerShipFightComp";
      
      private var _facade:AppFacade;
      
      private var _fightLayer:FightLayerComp;
      
      private var _infoLayer:InfoLayerComp;
      
      public function CrossServerShipFightComp()
      {
         super();
         isLive = false;
         this._setup();
      }
      
      override public function close() : void
      {
         if(!isLive)
         {
            this._removeMVC();
         }
         super.close();
      }
      
      override public function dispose() : void
      {
         this._removeMVC();
         this._disposeDisplayObject();
         super.dispose();
      }
      
      private function _disposeDisplayObject() : void
      {
         if(this._fightLayer)
         {
            this._fightLayer.kill();
            this._fightLayer = null;
         }
      }
      
      private function _setup() : void
      {
         if(stage)
         {
            this._initStage();
         }
         else
         {
            addEventListener("addedToStage",this._initStage);
         }
      }
      
      private function _removeMVC() : void
      {
         this._facade.removeMediator("crossservershipfightmodule.mvc.view.CrossServerShipFightMediator");
         this._facade.removeMultiCommand("CROSS_SERVER_SHIP_FIGHT_ROLE_REFRESH","CROSS_SERVER_SHIP_FIGHT_ROLE_DELETE");
         this._facade.removeProxy("crossservershipfightmodule.mvc.model.ServiceProxy");
         this._facade.removeProxy("crossservershipfightmodule.mvc.model.DataProxy");
      }
      
      private function _initStage(param1:Event = null) : void
      {
         if(param1)
         {
            removeEventListener("addedToStage",this._initStage);
         }
         this._fightLayer = addChild(new FightLayerComp()) as FightLayerComp;
         this._infoLayer = addChild(new InfoLayerComp()) as InfoLayerComp;
         this.layout();
         this._initMVC();
      }
      
      private function _initMVC() : void
      {
         this._facade = AppFacade.instance;
         this._initController();
         this._startup();
      }
      
      private function _initController() : void
      {
         this._facade.registerCommand("CROSS_SERVER_SHIP_FIGHT_STARTUP",StartupCommand);
         this._facade.registerCommand("CROSS_SERVER_SHIP_FIGHT_INIT_LANES",ModelPrepPathsCommand);
         this._facade.registerCommand("CROSS_SERVER_SHIP_FIGHT_ROLE_REFRESH",ModelRoleRefreshCommand);
         this._facade.registerCommand("CROSS_SERVER_SHIP_FIGHT_ROLE_DELETE",ModelRoleDeleteCommand);
      }
      
      private function _startup() : void
      {
         this._facade.sendNotification("HIDE_CHAT_PANEL");
         this._facade.sendNotification("CROSS_SERVER_SHIP_FIGHT_STARTUP",this);
         this._facade.removeCommand("CROSS_SERVER_SHIP_FIGHT_STARTUP");
         this._facade.sendNotification("CROSS_SERVER_SHIP_FIGHT_INIT_LANES");
         this._facade.removeCommand("CROSS_SERVER_SHIP_FIGHT_INIT_LANES");
      }
      
      public function layout() : void
      {
         this._fightLayer.layout();
         this._infoLayer.layout();
      }
      
      public function get fightLayer() : FightLayerComp
      {
         return this._fightLayer;
      }
      
      public function get infoLayer() : InfoLayerComp
      {
         return this._infoLayer;
      }
   }
}

