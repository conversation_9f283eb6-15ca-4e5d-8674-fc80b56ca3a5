package crossservershipfightmodule.mvc.view.components
{
   import com.greensock.TweenLite;
   import crossservershipfightmodule.mvc.model.vo.RoleVO;
   import flash.display.DisplayObject;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import flash.text.TextFormat;
   import flash.ui.Mouse;
   import flash.utils.Dictionary;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.Core;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.toolTip.ToolTip;
   import util.TextStyle;
   import util.ViewUtilities;
   
   public class FightLayerComp extends UISprite
   {
      public static const PORTALS_PREFIX:String = "portals_";
      
      private var _portalsA:MovieClip;
      
      private var _portalsB:MovieClip;
      
      private var _roleLayer:UISprite;
      
      private var _portalsTip:ToolTip;
      
      private var _roleTip:TipComp;
      
      private var _arrow:MovieClip;
      
      private var _cd:Label;
      
      private var _topLayer:UISprite;
      
      private var fightSK:UISkin;
      
      private var mSp:UISprite;
      
      public function FightLayerComp()
      {
         super();
         var _loc1_:MovieClip = addChild(AssetManager.getMc("crossservershipfightmodule.Map")) as MovieClip;
         this._portalsA = _loc1_.getChildByName("portals_0") as MovieClip;
         this._portalsB = _loc1_.getChildByName("portals_1") as MovieClip;
         this._initPortalsAB();
         this._roleLayer = addChild(new UISprite()) as UISprite;
         this._topLayer = addChild(new UISprite()) as UISprite;
         this._topLayer.mouseChildren = false;
         this._topLayer.mouseEnabled = false;
         this.fightSK = UIManager.getUISkin("copy_battle");
         this.mSp = new UISprite();
         this.mSp.addChild(this.fightSK);
         this.mSp.mouseChildren = false;
         this.mSp.mouseEnabled = false;
         this._topLayer.addChild(this.mSp);
         this.mSp.visible = false;
         this.addEventListener("enterFrame",this.pHandler);
      }
      
      private function pHandler(param1:Event) : void
      {
         this.mSp.x = stage.mouseX - 15;
         this.mSp.y = stage.mouseY - 15;
      }
      
      public function layout() : void
      {
         this.x = 0;
         this.y = 0;
      }
      
      private function _initPortalsAB() : void
      {
         var _loc4_:MovieClip = null;
         var _loc3_:MovieClip = null;
         var _loc1_:int = this._portalsA.numChildren;
         var _loc2_:int = _loc1_ - 1;
         while(_loc2_ > -1)
         {
            _loc4_ = this._portalsA.getChildAt(_loc2_) as MovieClip;
            _loc3_ = this._portalsB.getChildAt(_loc2_) as MovieClip;
            ViewUtilities.convertButtonMode(_loc4_);
            ViewUtilities.convertButtonMode(_loc3_);
            _loc4_.gotoAndStop(1);
            _loc3_.gotoAndStop(1);
            _loc2_--;
         }
         this._portalsA.visible = false;
         this._portalsB.visible = false;
      }
      
      public function changePortals(param1:int, param2:Boolean) : void
      {
         var _loc6_:MovieClip = null;
         var _loc8_:int = 3;
         if(param1 == 2)
         {
            _loc8_ = 5;
         }
         var _loc9_:int = this._portalsA.numChildren;
         var _loc3_:Number = (_loc9_ - _loc8_) / 2;
         var _loc4_:int = Math.floor(_loc3_);
         var _loc7_:int = _loc9_ - Math.ceil(_loc3_);
         var _loc5_:int = _loc9_ - 1;
         while(_loc5_ > -1)
         {
            if(param2)
            {
               _loc6_ = this.portalsA.getChildAt(_loc5_) as MovieClip;
            }
            else
            {
               _loc6_ = this.portalsB.getChildAt(_loc5_) as MovieClip;
            }
            if(_loc5_ < _loc4_ || _loc5_ >= _loc7_)
            {
               _loc6_.visible = false;
               _loc6_.gotoAndStop(1);
            }
            else
            {
               _loc6_.visible = true;
               _loc6_.play();
            }
            _loc5_--;
         }
      }
      
      public function showPortalsAB(param1:Boolean) : void
      {
         this._portalsA.visible = param1;
         this._portalsB.visible = !param1;
      }
      
      public function createRole(param1:RoleVO, param2:Boolean = false, param3:Boolean = false) : void
      {
         var _loc9_:MovieClip = this._roleLayer.addChild(AssetManager.getMc(param1.textureName)) as MovieClip;
         _loc9_.x = param1.realX;
         _loc9_.y = param1.realY;
         _loc9_.mouseChildren = false;
         _loc9_.name = param1.keyName;
         _loc9_.obj = param1;
         var _loc4_:UISprite = _loc9_.addChildAt(new UISprite(),0) as UISprite;
         _loc4_.mouseChildren = false;
         _loc4_.mouseEnabled = false;
         var _loc5_:UISprite = _loc4_.addChild(new UISprite()) as UISprite;
         _loc5_.y = 22;
         var _loc8_:UISkin = _loc5_.addChild(UIManager.getUISkin("pro_bg_small")) as UISkin;
         _loc8_.setSize(52,6);
         var _loc7_:UISkin = _loc5_.addChild(UIManager.getUISkin("pro_red_tiny")) as UISkin;
         _loc7_.x = _loc7_.y = 1;
         _loc7_.setSize(50 * (param1.curHp / param1.maxHp),4);
         var _loc6_:Label = _loc4_.addChild(new Label(param1.headTitle,new TextFormat("Verdana",12,param1.nameColor))) as Label;
         _loc6_.x = -25;
         _loc6_.autoSize = "center";
         _loc6_.width = 100;
         _loc6_.height = 16;
         if(param1.isAttack)
         {
            _loc4_.x = -(_loc4_.width >> 1);
         }
         else
         {
            _loc4_.x = 0;
         }
         _loc4_.y = -(_loc9_.height + 8);
         if(param2)
         {
            this.addArrowAndCD(_loc4_,25);
         }
         else if(param1.isAttack)
         {
            if(!param3)
            {
               _loc9_.addEventListener("click",this.attPlayerHandler);
               _loc9_.addEventListener("mouseOver",this.roleOverHandler);
               _loc9_.addEventListener("mouseOut",this.roleOutHandler);
            }
         }
         else if(param3)
         {
            _loc9_.addEventListener("click",this.attPlayerHandler);
            _loc9_.addEventListener("mouseOver",this.roleOverHandler);
            _loc9_.addEventListener("mouseOut",this.roleOutHandler);
         }
      }
      
      private function roleOverHandler(param1:MouseEvent) : void
      {
         Mouse.hide();
         this.mSp.visible = true;
      }
      
      private function roleOutHandler(param1:MouseEvent) : void
      {
         Mouse.show();
         this.mSp.visible = false;
      }
      
      private function attPlayerHandler(param1:MouseEvent) : void
      {
         AppFacade.instance.sendNotification("CROSS_BOAT_PET_FIGHT",param1.target.obj);
      }
      
      public function removeRole(param1:RoleVO, param2:Boolean = false, param3:String = null) : void
      {
         var _loc5_:MovieClip = null;
         var _loc4_:MovieClip = null;
         _loc4_ = this._roleLayer.getChildByName(param1.keyName) as MovieClip;
         if(_loc4_)
         {
            if(param2 && this._arrow && this._arrow.parent)
            {
               this._arrow.parent.removeChild(this._arrow);
               this._arrow.gotoAndStop(1);
            }
            _loc4_.gotoAndStop(1);
            if(param3)
            {
               _loc5_ = addChild(AssetManager.getMc(param3)) as MovieClip;
               _loc5_.gotoAndStop(1);
               _loc5_.x = _loc4_.x;
               _loc5_.y = _loc4_.y - (_loc4_.height >> 1);
               TweenLite.to(_loc5_,_loc5_.totalFrames / Core.stg.frameRate,{
                  "frame":_loc5_.totalFrames,
                  "onComplete":this._removeTC,
                  "onCompleteParams":[_loc5_]
               });
            }
            this._roleLayer.removeChild(_loc4_);
         }
      }
      
      private function _removeTC(param1:MovieClip) : void
      {
         if(param1)
         {
            param1.gotoAndStop(1);
            if(param1.parent)
            {
               param1.parent.removeChild(param1);
            }
         }
      }
      
      public function removeAllRole() : void
      {
         var _loc1_:int = this._roleLayer.numChildren - 1;
         while(_loc1_ > -1)
         {
            this._roleLayer.removeChildAt(_loc1_);
            _loc1_--;
         }
      }
      
      public function roleWalk(param1:Dictionary, param2:Vector.<Boolean>) : void
      {
         var _loc6_:DisplayObject = null;
         var _loc7_:RoleVO = null;
         var _loc3_:Boolean = false;
         var _loc4_:String = null;
         var _loc5_:int = this._roleLayer.numChildren - 1;
         while(_loc5_ > -1)
         {
            _loc6_ = this._roleLayer.getChildAt(_loc5_);
            _loc4_ = _loc6_.name;
            if(param1[_loc4_])
            {
               _loc3_ = true;
               _loc7_ = param1[_loc4_];
               if(_loc7_.isAttack)
               {
                  if(_loc7_.realX > _loc7_.stageStop.x + _loc7_.isAttackOffsetX)
                  {
                     if(_loc7_.realX < _loc7_.stopB.x)
                     {
                        param2[_loc7_.transferId] = false;
                     }
                     _loc3_ = false;
                  }
               }
               else if(_loc7_.realX < _loc7_.stageStop.x - _loc7_.isAttackOffsetX)
               {
                  if(_loc7_.realX > _loc7_.stopA.x)
                  {
                     param2[_loc7_.transferId] = false;
                  }
                  _loc3_ = false;
               }
               if(_loc3_ && param2[_loc7_.transferId])
               {
                  _loc6_.x = _loc7_.realX = _loc7_.realX + _loc7_.vx;
                  _loc6_.y = _loc7_.realY = _loc7_.realY + _loc7_.vy;
               }
            }
            else
            {
               this._roleLayer.removeChildAt(_loc5_);
            }
            _loc5_--;
         }
      }
      
      public function sortRole() : void
      {
         var _loc3_:* = 0;
         var _loc2_:int = this._roleLayer.numChildren - 1;
         var _loc1_:Array = [];
         _loc3_ = _loc2_;
         while(_loc3_ > -1)
         {
            _loc1_[_loc3_] = this._roleLayer.getChildAt(_loc3_);
            _loc3_--;
         }
         _loc1_.sortOn("y",16);
         _loc3_ = _loc2_;
         while(_loc3_ > -1)
         {
            if(this._roleLayer.getChildAt(_loc3_) != _loc1_[_loc3_])
            {
               this._roleLayer.setChildIndex(_loc1_[_loc3_],_loc3_);
            }
            _loc3_--;
         }
      }
      
      public function changeHP(param1:String, param2:int, param3:int) : void
      {
         var _loc4_:DisplayObject = this._roleLayer.getChildByName(param1);
         if(_loc4_)
         {
            ((((_loc4_ as Sprite).getChildAt(0) as Sprite).getChildAt(0) as Sprite).getChildAt(1) as UISkin).setSize(50 * (param2 / param3),4);
         }
      }
      
      public function changeNameColor(param1:String, param2:Object) : void
      {
         var _loc5_:Label = null;
         var _loc3_:TextFormat = null;
         var _loc4_:DisplayObject = this._roleLayer.getChildByName(param1);
         if(_loc4_)
         {
            _loc5_ = ((_loc4_ as Sprite).getChildAt(0) as Sprite).getChildAt(1) as Label;
            _loc3_ = _loc5_.getTextFormat();
            _loc3_.color = param2;
            _loc5_.setTextFormat(_loc3_);
         }
      }
      
      public function freshenTime(param1:Number, param2:Number = NaN, param3:Number = NaN, param4:Boolean = false, param5:String = "") : void
      {
         if(this._cd)
         {
            this._cd.text = TextStyle.formatTime(param1,param2,param3,param4,param5);
         }
      }
      
      public function addArrowAndCD(param1:Sprite, param2:Number, param3:Number = 0) : void
      {
         var _loc5_:UISprite = null;
         var _loc4_:UISkin = null;
         if(this._arrow == null)
         {
            this._arrow = AssetManager.getMc("crossservershipfightmodule.Arrow");
         }
         param1.addChild(this._arrow);
         this._arrow.x = param2;
         this._arrow.y = param3;
         this._arrow.play();
         if(this._cd == null)
         {
            _loc5_ = this._topLayer.addChild(new UISprite()) as UISprite;
            _loc4_ = _loc5_.addChild(UIManager.getUISkin("text_bg_2")) as UISkin;
            _loc4_.setSize(60,20);
            this._cd = _loc5_.addChild(new Label("",TextFormatLib.format_0x00A8FF_12px)) as Label;
            this._cd.x = 0;
            this._cd.y = 0;
            this._cd.autoSize = "center";
            this._cd.width = 60;
         }
         this._cd.text = "";
         if(param3)
         {
            this._arrow.y -= 20;
            this._cd.parent.x = this._arrow.x - 30;
            this._cd.parent.y = this._arrow.y + 6;
            this._cd.parent.visible = true;
         }
         else
         {
            this._cd.parent.visible = false;
         }
      }
      
      public function updateRoleTip(param1:String = null, param2:Number = 0, param3:Number = 0) : void
      {
         if(this._roleTip)
         {
            if(this._roleTip.parent)
            {
               this._roleTip.parent.removeChild(this._roleTip);
            }
            this._roleTip.dispose();
            this._roleTip = null;
         }
         if(param1)
         {
            this._roleTip = addChild(new TipComp(param1)) as TipComp;
            this._roleTip.x = param2;
            this._roleTip.y = param3 - this._roleTip.height - (this._roleTip.height >> 1);
         }
      }
      
      public function updatePortalsTip(param1:String = null, param2:Sprite = null) : void
      {
         var _loc3_:Point = null;
         if(this._portalsTip)
         {
            if(this._portalsTip.parent)
            {
               this._portalsTip.parent.removeChild(this._portalsTip);
            }
            this._portalsTip.dispose();
            this._portalsTip = null;
         }
         if(param1)
         {
            this._portalsTip = addChild(new ToolTip("")) as ToolTip;
            this._portalsTip.setText(param1);
            _loc3_ = param2.localToGlobal(new Point(0,0));
            _loc3_ = this._tipPosition(new Point(_loc3_.x,_loc3_.y));
            this._portalsTip.x = _loc3_.x;
            this._portalsTip.y = _loc3_.y;
         }
      }
      
      private function _tipPosition(param1:Point) : Point
      {
         param1.x > Core.stgW - this._portalsTip.width - 3 && (param1.x = Core.stgW - this._portalsTip.width - 3);
         param1.x < 0 && (param1.x = 0);
         param1.y > Core.stgH - this._portalsTip.height && (param1.y = param1.y - (this.height + this._portalsTip.height));
         return param1;
      }
      
      public function kill() : void
      {
         this.removeEventListener("enterFrame",this.pHandler);
         this.updatePortalsTip();
         this.updateRoleTip();
         if(this._arrow)
         {
            if(this._arrow.parent)
            {
               this._arrow.parent.removeChild(this._arrow);
            }
            this._arrow.gotoAndStop(1);
            this._arrow = null;
         }
         dispose();
      }
      
      public function get portalsA() : MovieClip
      {
         return this._portalsA;
      }
      
      public function get portalsB() : MovieClip
      {
         return this._portalsB;
      }
      
      public function get roleLayer() : UISprite
      {
         return this._roleLayer;
      }
      
      public function get topLayer() : UISprite
      {
         return this._topLayer;
      }
   }
}

