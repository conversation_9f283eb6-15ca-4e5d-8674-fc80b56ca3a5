package crossservershipfightmodule.mvc.view.components
{
   import flash.events.Event;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.data.ModuleData;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.TextButton;
   import mmo.ui.control.input.TextInput;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import util.Globalization;
   
   public class GoldInWindow extends PopUpWindow
   {
      public static const NAME:String = "GoldInWindow";
      
      public var goldTxt:TextInput;
      
      public var chipTxt:TextInput;
      
      public var goldEmptyBtn:TextButton;
      
      public var chipEmptyBtn:TextButton;
      
      public var submitBtn:Button;
      
      public var cancelBtn:Button;
      
      public var cardGoldTF:Label;
      
      private var cardGoldTF2:Label;
      
      private var cardChipTF:Label;
      
      private var cardChipTF2:Label;
      
      private var inLbTip:Label;
      
      private var outLbTip:Label;
      
      private var line:UISkin;
      
      private var offY:int = 75;
      
      private var offX:int = 12;
      
      public var addGoldBtn1:Button;
      
      public var addGoldBtn2:Button;
      
      public var addGoldBtn3:Button;
      
      public var addGoldBtn4:Button;
      
      public function GoldInWindow()
      {
         super(270,405);
         this.isLive = false;
         this.setTitleImageData(UIManager.getUISkin("worldGoldInTitle").bitmapData);
         var _loc8_:UISkin = UIManager.getUISkin("group_bg");
         _loc8_.x = 4;
         _loc8_.y = 2;
         _loc8_.width = 250;
         _loc8_.height = 325;
         pane.addChild(_loc8_);
         var _loc7_:TextFormat = TextFormatLib.format_0xfff5ce_12px;
         _loc7_.leading = 5;
         var _loc5_:Label = new Label("",_loc7_,[FilterLib.glow_0x272727],false);
         _loc5_.htmlText = Globalization.getString("worldBoatBank.1");
         _loc5_.x = 20;
         _loc5_.y = 9;
         _loc5_.width = 230;
         _loc5_.wordWrap = true;
         pane.addChild(_loc5_);
         _loc7_.leading = 0;
         var _loc6_:UISkin = UIManager.getUISkin("black_bg3");
         _loc6_.x = 12 + this.offX;
         _loc6_.y = 96;
         _loc6_.width = 208;
         _loc6_.height = 170;
         pane.addChild(_loc6_);
         var _loc9_:Label = new Label(Globalization.getString("worldBoatBank.2"),TextFormatLib.format_0xffb932_12px_center,[FilterLib.glow_0x272727]);
         _loc9_.x = 20 + this.offX;
         _loc9_.y = 119 + this.offY;
         pane.addChild(_loc9_);
         var _loc10_:UISkin = UIManager.getUISkin("gold_card");
         _loc10_.x = 25 + this.offX;
         _loc10_.y = 150 + this.offY;
         pane.addChild(_loc10_);
         this.goldTxt = new TextInput("0",100,TextFormatLib.format_0xfffb00_18px,32);
         this.goldTxt.x = 60 + this.offX;
         this.goldTxt.y = 145 + this.offY;
         this.goldTxt.restrict = "0-9";
         this.goldTxt.addEventListener("change",this.onGoldChangeHandler);
         pane.addChild(this.goldTxt);
         var _loc13_:TextFormat = new TextFormat("Verdana",14,65280);
         _loc13_.underline = true;
         this.goldEmptyBtn = new TextButton(Globalization.getString("buyKongdaobei1"),50,_loc13_,[FilterLib.glow_0x272727],65280,********,95489);
         this.goldEmptyBtn.x = 168 + this.offX;
         this.goldEmptyBtn.y = 150 + this.offY;
         pane.addChild(this.goldEmptyBtn);
         this.submitBtn = new Button(Globalization.getString("Gl.161"),TextFormatLib.format_0xFFED89_12px,85);
         this.submitBtn.x = 30;
         this.submitBtn.y = 330;
         pane.addChild(this.submitBtn);
         this.cancelBtn = new Button(Globalization.getString("Globalization.38"),TextFormatLib.format_0xFFED89_12px,85);
         this.cancelBtn.x = 145;
         this.cancelBtn.y = this.submitBtn.y;
         pane.addChild(this.cancelBtn);
         var _loc11_:UISkin = UIManager.getUISkin("black_bg3");
         _loc11_.x = 28 + this.offX;
         _loc11_.y = 57 + this.offY;
         _loc11_.width = 174;
         _loc11_.height = 26;
         pane.addChild(_loc11_);
         var _loc12_:UISkin = UIManager.getUISkin("black_bg3");
         _loc12_.x = 28 + this.offX;
         _loc12_.y = 89 + this.offY;
         _loc12_.width = 174;
         _loc12_.height = 26;
         pane.addChild(_loc12_);
         var _loc1_:UISkin = UIManager.getUISkin("gold");
         _loc1_.x = 42 + this.offX;
         _loc1_.y = 98 + this.offY;
         this.addChild(_loc1_);
         var _loc3_:UISkin = UIManager.getUISkin("gold");
         _loc3_.x = 42 + this.offX;
         _loc3_.y = 130 + this.offY;
         this.addChild(_loc3_);
         var _loc2_:Label = new Label(Globalization.getString("worldBoatBank.4"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc2_.x = 60 + this.offX;
         _loc2_.y = 128 + this.offY;
         this.addChild(_loc2_);
         var _loc4_:Label = new Label(Globalization.getString("card.39"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         _loc4_.x = 60 + this.offX;
         _loc4_.y = 96 + this.offY;
         this.addChild(_loc4_);
         this.cardGoldTF = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.cardGoldTF.x = 120 + this.offX;
         this.cardGoldTF.y = 126 + this.offY;
         this.addChild(this.cardGoldTF);
         this.cardGoldTF2 = new Label("",TextFormatLib.format_0x00FF00_14px,[FilterLib.glow_0x272727]);
         this.cardGoldTF2.x = 97 + this.offX;
         this.cardGoldTF2.y = 94 + this.offY;
         this.addChild(this.cardGoldTF2);
         this.addGoldBtn1 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipblue2"));
         this.addGoldBtn1.x = 30;
         this.addGoldBtn1.y = 270;
         this.addGoldBtn1.text = this.formatTxt(100);
         this.addGoldBtn1.name = "100";
         pane.addChild(this.addGoldBtn1);
         this.addGoldBtn2 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipyellow2"));
         this.addGoldBtn2.x = this.addGoldBtn1.x + 50;
         this.addGoldBtn2.y = 270;
         this.addGoldBtn2.text = this.formatTxt(500);
         this.addGoldBtn2.name = "500";
         pane.addChild(this.addGoldBtn2);
         this.addGoldBtn3 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChipred2"));
         this.addGoldBtn3.x = this.addGoldBtn2.x + 50;
         this.addGoldBtn3.y = 270;
         this.addGoldBtn3.text = this.formatTxt(1000);
         this.addGoldBtn3.name = "1000";
         pane.addChild(this.addGoldBtn3);
         this.addGoldBtn4 = new Button("",TextFormatLib.format_0xFFFFFF_12px,52,UIManager.getMultiUISkin("addChippurple2"));
         this.addGoldBtn4.x = this.addGoldBtn3.x + 50;
         this.addGoldBtn4.y = 270;
         this.addGoldBtn4.text = this.formatTxt(2000);
         this.addGoldBtn4.name = "2000";
         pane.addChild(this.addGoldBtn4);
         this.addEventListener("click",this.onMouseClickHandler);
         this.initUI();
      }
      
      public function initUI() : void
      {
         this.cardGoldTF2.text = "" + MainData.getInstance().userData.gold_num;
         this.cardGoldTF.text = "" + ModuleData.crossServerShipFightGold;
      }
      
      private function onMouseClickHandler(param1:MouseEvent) : void
      {
         if(param1.target == this.goldEmptyBtn)
         {
            this.goldTxt.text = "0";
         }
         else if(param1.target == this.chipEmptyBtn)
         {
            this.chipTxt.text = "0";
         }
         else if(param1.target == this.addGoldBtn1)
         {
            this.goldTxt.text = "" + (int(this.goldTxt.text) + int(this.addGoldBtn1.name));
            this.setInputGold();
         }
         else if(param1.target == this.addGoldBtn2)
         {
            this.goldTxt.text = "" + (int(this.goldTxt.text) + int(this.addGoldBtn2.name));
            this.setInputGold();
         }
         else if(param1.target == this.addGoldBtn3)
         {
            this.goldTxt.text = "" + (int(this.goldTxt.text) + int(this.addGoldBtn3.name));
            this.setInputGold();
         }
         else if(param1.target == this.addGoldBtn4)
         {
            this.goldTxt.text = "" + (int(this.goldTxt.text) + int(this.addGoldBtn4.name));
            this.setInputGold();
         }
         else if(param1.target == this.submitBtn)
         {
            if(int(this.goldTxt.text) <= 0)
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("worldBoatBank.5"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px,
                  "runTime":2
               });
               return;
            }
            AppFacade.instance.sendNotification("WORLD_BOAT_OPEN_GOLD",{"goldNum":int(this.goldTxt.text)});
            this.close();
         }
         else if(param1.target == this.cancelBtn)
         {
            this.close();
         }
      }
      
      private function setInputGold() : void
      {
         if(MainData.getInstance().userData.gold_num < int(this.goldTxt.text))
         {
            this.goldTxt.text = "" + MainData.getInstance().userData.gold_num;
         }
      }
      
      private function onGoldChangeHandler(param1:Event) : void
      {
         TextInput(param1.currentTarget).text = "" + int(TextInput(param1.currentTarget).text);
         this.setInputGold();
      }
      
      private function formatTxt(param1:int) : String
      {
         var _loc2_:String = "";
         if(param1 >= 1000 && param1 < 10000)
         {
            _loc2_ = "+" + int(param1 / 1000) + Globalization.getString("card.34");
         }
         else if(param1 >= 10000)
         {
            _loc2_ = "+" + int(param1 / 10000) + Globalization.getString("card.35");
         }
         else
         {
            _loc2_ = "+" + param1;
         }
         return _loc2_;
      }
   }
}

