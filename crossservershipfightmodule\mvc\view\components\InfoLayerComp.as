package crossservershipfightmodule.mvc.view.components
{
   import com.greensock.TweenLite;
   import crossservershipfightmodule.mvc.view.utils.GL;
   import flash.display.MovieClip;
   import flash.filters.GlowFilter;
   import flash.text.TextFormat;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import mmo.Core;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.CheckBox;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.label.Label;
   import util.Globalization;
   import util.TextStyle;
   import util.VectorUtilities;
   
   public class InfoLayerComp extends UISprite
   {
      public static const AUTO_CHECK_BOX:String = "AUTO_CHECK_BOX";
      
      public static const BTN_REMOVE_JOIN_CD:String = "BTN_REMOVE_JOIN_CD";
      
      private var _report:ReportComp;
      
      private var _myState:Vector.<UISprite>;
      
      private var _countdownLabel:Vector.<Label>;
      
      private var _inspireBTN:ImgButton;
      
      private var _goldAttr:UserAttrComp;
      
      private var _inspireGoldAttr:UserAttrComp;
      
      private var _inspireFree:Label;
      
      private var _auto:UISprite;
      
      private var _autoCheckBox:CheckBox;
      
      private var _top:TopComp;
      
      private var _sceneBTN:SceneBTNComp;
      
      private var _boatValue:UISprite;
      
      private var _attackLevel:Label;
      
      private var _defendLevel:Label;
      
      private var _hpLevel:Label;
      
      private var _endCD:UISprite;
      
      private var _myBoat:MovieClip;
      
      private var _petCDLb:Label;
      
      private var _boatPetCD:Label;
      
      private var _myPetMC:MovieClip;
      
      private var _petTalkSp:UISprite;
      
      private var _talkLb:Label;
      
      private var _petTalkBg:UISkin;
      
      public var warCountdown:UISprite;
      
      public function InfoLayerComp()
      {
         var _loc13_:int = 0;
         var _loc1_:Label = null;
         var _loc10_:ImgButton = null;
         var _loc12_:Label = null;
         var _loc6_:Label = null;
         super();
         this._countdownLabel = new Vector.<Label>(4);
         this.warCountdown = addChild(new UISprite()) as UISprite;
         this.warCountdown.x = 330;
         this.warCountdown.y = 4;
         this.warCountdown.mouseChildren = false;
         this.warCountdown.mouseEnabled = false;
         var _loc11_:UISkin = this.warCountdown.addChild(UIManager.getUISkin("CrossServerShipFightModuleEndTimeTitle")) as UISkin;
         this._endCD = this.warCountdown.addChild(new UISprite()) as UISprite;
         this._endCD.x = (_loc11_.width >> 1) - 40;
         this._endCD.y = _loc11_.height;
         var _loc9_:UISprite = addChild(new UISprite()) as UISprite;
         var _loc7_:UISkin = _loc9_.addChild(UIManager.getUISkin("bg_v5")) as UISkin;
         _loc7_.setSize(212,140);
         var _loc8_:UISkin = _loc9_.addChild(UIManager.getUISkin("CrossServerShipFightModuleShipTitle1")) as UISkin;
         _loc8_.x = 10;
         this._boatValue = _loc9_.addChild(new UISprite()) as UISprite;
         this._boatValue.x = _loc8_.x + _loc8_.width + 2;
         _loc8_.y = 2;
         this._boatValue.y = 11;
         this._inspireBTN = _loc9_.addChild(new ImgButton(UIManager.getMultiUISkin("CrossServerShipFightModuleBTNGoldInspire"))) as ImgButton;
         this._inspireBTN.x = 90;
         this._inspireBTN.y = 35;
         this._inspireBTN.name = "CrossServerShipFightModuleBTNGoldInspire";
         var _loc14_:Label = _loc9_.addChild(new Label(GL.GOLD_TEMP,TextFormatLib.format_0xFFF600_12px)) as Label;
         _loc14_.x = 18;
         _loc14_.y = 100;
         this._goldAttr = _loc9_.addChild(new UserAttrComp("gold",GL.GOLD)) as UserAttrComp;
         this._goldAttr.x = _loc14_.x + _loc14_.textWidth + 6;
         this._goldAttr.y = 102;
         this._inspireFree = _loc9_.addChild(new Label("",TextFormatLib.format_0xFFF600_12px)) as Label;
         this._inspireFree.x = 18;
         this._inspireFree.y = 116;
         var _loc17_:UISprite = _loc9_.addChild(new UISprite()) as UISprite;
         _loc17_.x = 18;
         _loc17_.y = 118;
         var _loc15_:Label = _loc17_.addChild(new Label(GL.ENCOURAGE_NEED_GOLD,TextFormatLib.format_0xFFF600_12px)) as Label;
         this._inspireGoldAttr = _loc17_.addChild(new UserAttrComp("gold",GL.GOLD)) as UserAttrComp;
         this._inspireGoldAttr.x = _loc15_.x + _loc15_.textWidth + 6;
         this._inspireGoldAttr.y = 2;
         var _loc16_:Label = _loc9_.addChild(new Label(GL.ATTACK_LEVEL,TextFormatLib.format_0xFFF600_12px)) as Label;
         _loc16_.x = 18;
         this._attackLevel = _loc9_.addChild(new Label("",TextFormatLib.format_0x00FF00_12px)) as Label;
         this._attackLevel.x = _loc16_.x + _loc16_.textWidth + 6;
         _loc16_.y = this._attackLevel.y = 44;
         var _loc3_:Label = _loc9_.addChild(new Label(GL.DEFEND_LEVEL,TextFormatLib.format_0xFFF600_12px)) as Label;
         _loc3_.x = 18;
         this._defendLevel = _loc9_.addChild(new Label("",TextFormatLib.format_0x00FF00_12px)) as Label;
         this._defendLevel.x = _loc3_.x + _loc3_.textWidth + 6;
         _loc3_.y = this._defendLevel.y = 61;
         var _loc4_:Label = _loc9_.addChild(new Label(GL.HP_LEVEL,TextFormatLib.format_0xFFF600_12px)) as Label;
         _loc4_.x = 18;
         this._hpLevel = _loc9_.addChild(new Label("",TextFormatLib.format_0x00FF00_12px)) as Label;
         this._hpLevel.x = _loc4_.x + _loc4_.textWidth + 6;
         _loc4_.y = this._hpLevel.y = 78;
         this._petCDLb = _loc9_.addChild(new Label(Globalization.getString("worldBoatPetFight.2"),TextFormatLib.format_0xFFB932_12px)) as Label;
         this._petCDLb.x = 18;
         this._petCDLb.y = 150;
         this._boatPetCD = _loc9_.addChild(new Label("",TextFormatLib.format_0x00FF00_12px)) as Label;
         this._boatPetCD.x = 88;
         this._boatPetCD.y = 150;
         var _loc2_:int = 4;
         this._myState = new Vector.<UISprite>(_loc2_,true);
         var _loc5_:Vector.<String> = VectorUtilities.getFixedString([GL.CAN_JOIN,GL.FIGHTING,GL.JOIN_CD,GL.READY_JOIN_CD]);
         _loc13_ = 0;
         while(_loc13_ < _loc2_)
         {
            this._myState[_loc13_] = _loc9_.addChild(new UISprite()) as UISprite;
            this._myState[_loc13_].x = 18;
            this._myState[_loc13_].y = 135;
            this._myState[_loc13_].visible = false;
            switch(_loc13_)
            {
               case 0:
                  _loc12_ = this._myState[_loc13_].addChild(new Label(Globalization.getString("worldBoatPetFight.9") + ":",TextFormatLib.format_0xFFB932_12px)) as Label;
                  _loc6_ = this._myState[_loc13_].addChild(new Label(_loc5_[_loc13_],TextFormatLib.format_0x00FF00_12px)) as Label;
                  _loc6_.x = _loc12_.x + _loc12_.width + 6;
                  break;
               case 1:
                  this._myState[_loc13_].addChild(new Label(_loc5_[_loc13_],TextFormatLib.format_0xFFB932_12px));
                  break;
               case 2:
                  _loc1_ = this._myState[_loc13_].addChild(new Label(_loc5_[_loc13_] + ":",TextFormatLib.format_0xFFB932_12px)) as Label;
                  this._countdownLabel[1] = this._myState[_loc13_].addChild(new Label("",TextFormatLib.format_0xFF0000_12px)) as Label;
                  this._countdownLabel[1].x = _loc1_.textWidth + 6;
                  _loc10_ = this._myState[_loc13_].addChild(new ImgButton(UIManager.getMultiUISkin("btn_CD"))) as ImgButton;
                  _loc10_.x = 108;
                  _loc10_.y = 2;
                  _loc10_.name = "BTN_REMOVE_JOIN_CD";
                  break;
               case 3:
                  _loc1_ = this._myState[_loc13_].addChild(new Label(_loc5_[_loc13_] + ":",TextFormatLib.format_0xFFB932_12px)) as Label;
                  this._countdownLabel[3] = this._myState[_loc13_].addChild(new Label("",TextFormatLib.format_0x00A8FF_12px)) as Label;
                  this._countdownLabel[3].x = _loc1_.textWidth + 6;
                  break;
            }
            _loc13_++;
         }
         this._auto = _loc9_.addChild(new UISprite()) as UISprite;
         this._auto.x = 16;
         this._auto.y = 167;
         this._auto.name = "AUTO_CHECK_BOX";
         this._autoCheckBox = this._auto.addChild(new CheckBox(GL.AUTO,TextFormatLib.format_0xFFB932_12px,null,1)) as CheckBox;
         this._petTalkSp = _loc9_.addChild(new UISprite()) as UISprite;
         this._petTalkSp.x = 900;
         this._petTalkSp.y = 400;
         this._petTalkBg = UIManager.getUISkin("petSayWord_bg");
         this._petTalkSp.addChild(this._petTalkBg);
         this._petTalkBg.width = 130;
         this._talkLb = this._petTalkSp.addChild(new Label("",TextFormatLib.format_0xFFF600_12px)) as Label;
         this._talkLb.width = 120;
         this._talkLb.wordWrap = true;
         this._talkLb.x = 9;
         this._talkLb.y = 2;
         this._petTalkSp.visible = false;
         this._report = addChild(new ReportComp()) as ReportComp;
         this._top = addChild(new TopComp()) as TopComp;
         this._sceneBTN = addChild(new SceneBTNComp()) as SceneBTNComp;
         this.layout();
      }
      
      public function layout() : void
      {
         this._report.x = Core.stgW - this._report.width;
         this._sceneBTN.x = Core.stgW - this._sceneBTN.width - 10;
         this._sceneBTN.y = Core.stgH - this._sceneBTN.height - 10;
         this._top.x = Core.stgW - this._top.w;
         this._top.y = this._sceneBTN.y - this._top.height;
      }
      
      public function addMyPetMC(param1:String) : void
      {
         var _loc3_:String = null;
         var _loc2_:* = param1;
         _loc3_ = XmlManager.worldNeptune.children().(@ID == _loc2_).attribute("artID");
         if(this._myPetMC)
         {
            this._myPetMC.parent.removeChild(this._myPetMC);
            this._myPetMC.stop();
            this._myPetMC = null;
         }
         this._myPetMC = this._boatValue.parent.addChild(AssetManager.getMc("nepModel_" + _loc3_)) as MovieClip;
         this._myPetMC.x = 1000;
         this._myPetMC.y = 600;
      }
      
      public function addStartCD() : void
      {
         this._countdownLabel[2] = addChild(new Label("10",new TextFormat("Verdana",20,16773910,true))) as Label;
         this._countdownLabel[2].filters = [new GlowFilter(9440914,1,6,6,8)];
         this._countdownLabel[2].x = 330;
         this._countdownLabel[2].y = 20;
         this._countdownLabel[2].text = Globalization.getString("groupwar.29") + ":";
      }
      
      public function removeStartCD() : void
      {
         removeChild(this._countdownLabel[2]);
         this._countdownLabel[2] = null;
         this.playOnce("crossservershipfightmodule.FightStart");
      }
      
      public function playOnce(param1:String, param2:Number = 0) : void
      {
         var duration:Number;
         var mc:MovieClip = null;
         var textureName:String = param1;
         var offsetY:Number = param2;
         mc = addChild(AssetManager.getMc(textureName)) as MovieClip;
         mc.x = Core.stgW >> 1;
         mc.y = (Core.stgH >> 1) + offsetY;
         mc.gotoAndStop(1);
         duration = mc.totalFrames / Core.stg.frameRate;
         TweenLite.from(mc,0.2,{"alpha":0});
         TweenLite.to(mc,duration,{
            "delay":0.2,
            "frame":mc.totalFrames
         });
         TweenLite.to(mc,0.2,{
            "delay":duration,
            "alpha":0,
            "onComplete":function():void
            {
               removeChild(mc);
            }
         });
      }
      
      public function freshenTime(param1:int, param2:Number, param3:Number = NaN, param4:Number = NaN, param5:Boolean = false, param6:String = "", param7:Boolean = false, param8:Boolean = true) : void
      {
         if(param1 == 2)
         {
            this._countdownLabel[param1].text = Globalization.getString("groupwar.29") + ":" + String(int((param4 - param2) / 1000));
         }
         else if(param1 == 0)
         {
            this._graphNum(TextStyle.formatTime(param2,param3,param4,param5,param6,param7,param8),this._endCD);
         }
         else if(param1 == 5)
         {
            this._boatPetCD.text = TextStyle.formatTime(param2,param3,param4,param5,param6,param7,param8);
         }
         else
         {
            this._countdownLabel[param1].text = TextStyle.formatTime(param2,param3,param4,param5,param6,param7,param8);
         }
      }
      
      public function updateShipValue(param1:String) : void
      {
         this._graphNum(param1,this._boatValue);
      }
      
      private function _graphNum(param1:String, param2:UISprite) : void
      {
         var _loc7_:UISkin = null;
         var _loc8_:UISkin = null;
         var _loc3_:String = null;
         var _loc4_:String = null;
         var _loc5_:int = 0;
         param2.dispose();
         var _loc6_:int = param1.length;
         _loc5_ = 0;
         while(_loc5_ < _loc6_)
         {
            switch(_loc4_ = param1.charAt(_loc5_))
            {
               case ":":
                  _loc3_ = "CrossServerShipFightModuleSmallColon";
                  break;
               case "(":
                  _loc3_ = "CrossServerShipFightModuleSmallParenthesisLeft";
                  break;
               case ")":
                  _loc3_ = "CrossServerShipFightModuleSmallParenthesisRight";
                  break;
               case "+":
                  _loc3_ = "CrossServerShipFightModuleSmallPlus";
                  break;
               default:
                  _loc3_ = "CrossServerShipFightModuleSmallNum" + _loc4_;
            }
            _loc7_ = param2.addChild(UIManager.getUISkin(_loc3_)) as UISkin;
            if(_loc5_)
            {
               _loc8_ = param2.getChildAt(_loc5_ - 1) as UISkin;
               _loc7_.x = _loc8_.x + 15;
            }
            _loc5_++;
         }
      }
      
      public function setMyState(param1:int) : void
      {
         var _loc2_:int = 3;
         while(_loc2_ > -1)
         {
            if(_loc2_ == param1)
            {
               this._myState[_loc2_].visible = true;
            }
            else
            {
               this._myState[_loc2_].visible = false;
            }
            _loc2_--;
         }
      }
      
      public function setJoinCDBTNTip(param1:String) : void
      {
         (this._myState[2].getChildAt(2) as ImgButton).setToolTip(param1);
      }
      
      public function setAutoTip(param1:Boolean, param2:int) : void
      {
         if(!param1)
         {
            this._auto.mouseChildren = false;
         }
         if(param2 == 1)
         {
            this._auto.setToolTip(GL.AUTO_TYPE_1);
         }
         else if(param2 == 2)
         {
            this._auto.setToolTip(GL.AUTO_TYPE_2);
         }
      }
      
      public function get report() : ReportComp
      {
         return this._report;
      }
      
      public function get countdownLabel() : Vector.<Label>
      {
         return this._countdownLabel;
      }
      
      public function get top() : TopComp
      {
         return this._top;
      }
      
      public function get auto() : UISprite
      {
         return this._auto;
      }
      
      public function get autoCheckBox() : CheckBox
      {
         return this._autoCheckBox;
      }
      
      public function get sceneBTN() : SceneBTNComp
      {
         return this._sceneBTN;
      }
      
      public function get attackLevel() : Label
      {
         return this._attackLevel;
      }
      
      public function get defendLevel() : Label
      {
         return this._defendLevel;
      }
      
      public function get hpLevel() : Label
      {
         return this._hpLevel;
      }
      
      public function get inspireBTN() : ImgButton
      {
         return this._inspireBTN;
      }
      
      public function get goldAttr() : UserAttrComp
      {
         return this._goldAttr;
      }
      
      public function get inspireFree() : Label
      {
         return this._inspireFree;
      }
      
      public function get inspireGoldAttr() : UserAttrComp
      {
         return this._inspireGoldAttr;
      }
      
      public function get boatPetCD() : Label
      {
         return this._boatPetCD;
      }
      
      public function get petCDLb() : Label
      {
         return this._petCDLb;
      }
      
      public function get petTalkSp() : UISprite
      {
         return this._petTalkSp;
      }
      
      public function get talkLb() : Label
      {
         return this._talkLb;
      }
      
      public function get petTalkBg() : UISkin
      {
         return this._petTalkBg;
      }
   }
}

