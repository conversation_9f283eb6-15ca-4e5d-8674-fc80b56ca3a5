package crossservershipfightmodule.mvc.view.components
{
   import crossservershipfightmodule.mvc.model.vo.ReportVO;
   import crossservershipfightmodule.mvc.view.utils.GL;
   import flash.display.DisplayObject;
   import flash.display.Sprite;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.scrollPane.ScrollPane;
   import util.TextStyle;
   
   public class ReportComp extends UISprite
   {
      public static const PLAYBACK:String = "playback";
      
      public static const UID:String = "uid";
      
      private var _content:UISprite;
      
      private var _sc:ScrollPane;
      
      private var _max:int = 100;
      
      public var reportDataArr:Array = [];
      
      public function ReportComp()
      {
         super();
         var _loc1_:UISkin = addChild(UIManager.getUISkin("bg_v5")) as UISkin;
         _loc1_.setSize(220,145);
         this._content = new UISprite();
         this._sc = addChild(new ScrollPane(217,130)) as ScrollPane;
         this._sc.x = 4;
         this._sc.y = 4;
         this._sc.addToPane(this._content);
      }
      
      public function setOldALLReport(param1:Array, param2:int) : void
      {
         var _loc3_:int = 0;
         var _loc4_:int = 0;
         if(param1.length)
         {
            _loc3_ = int(param1.length);
            _loc4_ = 0;
            while(_loc4_ < _loc3_)
            {
               this.setReport(param1[_loc4_],param2);
               _loc4_++;
            }
         }
      }
      
      public function setReport(param1:ReportVO, param2:int) : void
      {
         var _loc4_:DisplayObject = null;
         var _loc5_:int = 0;
         var _loc3_:int = 0;
         _loc5_ = this._content.numChildren;
         if(_loc5_ > this.max * 2)
         {
            _loc3_ = _loc5_ - 1;
            while(_loc3_ > this.max)
            {
               this._content.removeChildAt(0);
               _loc3_--;
            }
            _loc5_ = this._content.numChildren;
            this._content.getChildAt(0).y = 0;
            _loc3_ = 1;
            while(_loc3_ < _loc5_)
            {
               _loc4_ = this._content.getChildAt(_loc3_ - 1);
               this._content.getChildAt(_loc3_).y = _loc4_.y + _loc4_.height;
               _loc3_++;
            }
         }
         this._addReport(param1,param2);
      }
      
      private function _addReport(param1:ReportVO, param2:int) : void
      {
         var _loc12_:* = null;
         var _loc6_:* = null;
         var _loc7_:Sprite = null;
         var _loc5_:String = null;
         var _loc11_:String = "00FF00";
         _loc12_ = "<font color=\'#FFFFFF\'>" + param1.winnerName + "</font>";
         var _loc3_:* = param1.winnerId == param2;
         _loc6_ = "<font color=\'#FFFFFF\'>" + param1.loserName + "</font>";
         var _loc10_:* = param1.loserId == param2;
         if(_loc3_)
         {
            _loc12_ = "<font color=\'#FFED89\'>" + GL.YOU + "</font>";
         }
         if(_loc10_)
         {
            _loc6_ = "<font color=\'#FFED89\'>" + GL.YOU + "</font>";
         }
         var _loc9_:String = "<p>" + _loc12_ + "<font color=\'#" + _loc11_ + "\'>" + GL.DEFEAT + "</font>" + _loc6_ + "</p>";
         var _loc8_:Sprite = this._content.addChild(new UISprite()) as UISprite;
         _loc8_.addChild(TextStyle.createFieldHTMLText(new <String>[_loc9_,"a:hover{color:#FF0000;} a:visited{color:#990000;} a:active{color:#000000;} .hide{display:none;}"],160,0,0,0,"p{leading:2;}"));
         if(param1.brid)
         {
            if(param1.nep_uuid != 0 && (param1.loserId == param2 || param1.winnerId == param2))
            {
               _loc5_ = "<a href=\'event:{_DATA_}\'><font color=\'#{_COLOR_}\'>{_NAME_}</font></a>".replace(TextStyle.R_DATA,"playback" + "_" + param1.brid).replace(TextStyle.R_COLOR,"00FF00").replace(TextStyle.R_NAME,GL.PLAYBACK);
               _loc8_.addChild(TextStyle.createFieldHTMLText(new <String>[_loc5_,"a:hover{color:#FF0000;} a:visited{color:#990000;} a:active{color:#000000;} .hide{display:none;}"],40,20,170));
               AppFacade.instance.sendNotification("ALERT_BOAT_PET_RESULT",param1.brid);
            }
         }
         var _loc4_:int = this._content.numChildren;
         if(_loc4_ > 1)
         {
            _loc7_ = this._content.getChildAt(_loc4_ - 2) as Sprite;
            _loc8_.y = _loc7_.y + _loc7_.height;
         }
         this._sc.nowUpdateUI();
         this._sc.scrollTo(1);
      }
      
      public function get max() : int
      {
         return this._max;
      }
      
      public function set max(param1:int) : void
      {
         this._max = param1;
      }
   }
}

