package crossservershipfightmodule.mvc.view.components
{
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import mmo.ui.control.button.ImgButton;
   import util.VectorUtilities;
   
   public class SceneBTNComp extends Sprite
   {
      public static const BTN_TRANSFER:String = "BTNTRANSFERSceneBTNComp";
      
      public static const BTN_SAY:String = "BTNSAYSceneBTNComp";
      
      public static const BTN_SHIP:String = "BTNSHIPSceneBTNComp";
      
      public static const BTN_HELP:String = "BTNHELPSceneBTNComp";
      
      public static const BTN_QUIT:String = "BTNQUITSceneBTNComp";
      
      private var _imgBTN:Vector.<ImgButton>;
      
      private var _sayGlow:MovieClip;
      
      public function SceneBTNComp(param1:Number = 10, param2:int = 1)
      {
         super();
         var _loc5_:Vector.<String> = VectorUtilities.getFixedString(["CrossServerShipFightModuleBTNTransfer","CrossServerShipFightModuleBTNSay","boatStrengthBtn","soul_Help_button","exitArenaBtn"]);
         var _loc6_:Vector.<String> = VectorUtilities.getFixedString(["BTNTRANSFERSceneBTNComp","BTNSAYSceneBTNComp","BTNSHIPSceneBTNComp","BTNHELPSceneBTNComp","BTNQUITSceneBTNComp"]);
         var _loc3_:int = int(_loc5_.length);
         this._imgBTN = new Vector.<ImgButton>(_loc3_,true);
         var _loc4_:int = 0;
         while(_loc4_ < _loc3_)
         {
            this._imgBTN[_loc4_] = addChild(new ImgButton(UIManager.getMultiUISkin(_loc5_[_loc4_]))) as ImgButton;
            if(_loc4_)
            {
               if(param2)
               {
                  this._imgBTN[_loc4_].x = this._imgBTN[_loc4_ - 1].x + this._imgBTN[_loc4_ - 1].width + param1;
               }
               else
               {
                  this._imgBTN[_loc4_].y = this._imgBTN[_loc4_ - 1].y + this._imgBTN[_loc4_ - 1].height + param1;
               }
            }
            this._imgBTN[_loc4_].name = _loc6_[_loc4_];
            if(_loc4_ != 3)
            {
               this._imgBTN[_loc4_].y -= 2;
            }
            else
            {
               this._imgBTN[_loc4_].y += 1;
            }
            if(_loc4_ == 4)
            {
               this._imgBTN[_loc4_].y -= 7;
            }
            _loc4_++;
         }
      }
      
      public function sayGlow(param1:Boolean) : void
      {
         if(param1 && this._sayGlow == null)
         {
            this._sayGlow = addChildAt(AssetManager.getMc("crossservershipfightmodule.SayGlow"),0) as MovieClip;
            this._sayGlow.x = this._imgBTN[1].x + 40;
            this._sayGlow.y = this._imgBTN[1].y + 35;
         }
         else if(!param1 && this._sayGlow)
         {
            if(this._sayGlow.parent)
            {
               this._sayGlow.parent.removeChild(this._sayGlow);
            }
            this._sayGlow.stop();
            this._sayGlow = null;
         }
      }
   }
}

