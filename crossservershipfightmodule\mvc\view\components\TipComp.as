package crossservershipfightmodule.mvc.view.components
{
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   
   public class TipComp extends UISprite
   {
      public function TipComp(param1:String)
      {
         super();
         this.mouseChildren = false;
         this.mouseEnabled = false;
         var _loc3_:UISkin = addChild(UIManager.getUISkin("info_bg")) as UISkin;
         _loc3_.setSize(100,80);
         var _loc2_:Label = addChild(new Label("",TextFormatLib.format_0xFFB932_12px)) as Label;
         _loc2_.x = 10;
         _loc2_.y = 10;
         _loc2_.width = 90;
         _loc2_.wordWrap = true;
         _loc2_.htmlText = param1;
      }
   }
}

