package crossservershipfightmodule.mvc.view.components
{
   import com.greensock.TweenLite;
   import crossservershipfightmodule.mvc.view.utils.GL;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TextEvent;
   import game.manager.UIManager;
   import mmo.Core;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import util.Globalization;
   
   public class TopComp extends UISprite
   {
      private var _leftFlexBtn:ImgButton;
      
      private var _rightFlexBtn:ImgButton;
      
      private var _textEvent:TextEvent;
      
      private var _infoContainer:Sprite;
      
      public var isOpen:Boolean = false;
      
      public var w:Number = 202;
      
      private var _topList:Vector.<TopItemComp>;
      
      private var _myScore:Label;
      
      private var _myWinStreak:Label;
      
      private var _myHonor:Label;
      
      private var _sc:ScrollPane;
      
      private var curTopArr:Array;
      
      public function TopComp()
      {
         var _loc1_:TopItemComp = null;
         this._topList = new Vector.<TopItemComp>(10,true);
         this.curTopArr = [];
         super();
         this._leftFlexBtn = new ImgButton(UIManager.getMultiUISkin("btn_openUp"));
         this._leftFlexBtn.scaleX = -1;
         this._leftFlexBtn.x = 20;
         this.addChild(this._leftFlexBtn);
         this._leftFlexBtn.visible = false;
         this._leftFlexBtn.addEventListener("click",this.onClickFlexBtn);
         this._infoContainer = this.addChild(new Sprite()) as Sprite;
         this._infoContainer.x = 20;
         var _loc10_:UISkin = this._infoContainer.addChild(UIManager.getUISkin("worldBoatResultBg")) as UISkin;
         _loc10_.setSize(210,204);
         var _loc9_:Label = new Label(GL.POINTS_TOP,TextFormatLib.format_0xffb932_12px);
         _loc9_.y = 6;
         _loc9_.width = this.w;
         _loc9_.autoSize = "center";
         this._infoContainer.addChild(_loc9_);
         var _loc7_:UISkin = UIManager.getUISkin("split_line2");
         _loc7_.width = this.w;
         _loc7_.y = 30;
         this._infoContainer.addChild(_loc7_);
         var _loc8_:UISprite = new UISprite();
         this._sc = this._infoContainer.addChild(new ScrollPane(174,100)) as ScrollPane;
         this._sc.x = 6;
         this._sc.y = 34;
         this._sc.addToPane(_loc8_);
         var _loc3_:int = int(this._topList.length);
         var _loc6_:int = 0;
         while(_loc6_ < _loc3_)
         {
            if(_loc6_ == 0)
            {
               _loc1_ = new TopItemComp(TextFormatLib.format_verdana_0xff0000_12px);
            }
            else if(_loc6_ == 1)
            {
               _loc1_ = new TopItemComp(TextFormatLib.format_0x00A8FF_12px);
            }
            else if(_loc6_ == 2)
            {
               _loc1_ = new TopItemComp(TextFormatLib.format_Verdana_0xFFB932_12px);
            }
            else
            {
               _loc1_ = new TopItemComp(TextFormatLib.format_Verdana_0xFFFFFF_12px);
            }
            _loc1_.x = 0;
            _loc1_.y = _loc6_ * 20;
            _loc8_.addChild(_loc1_);
            this._topList[_loc6_] = _loc1_;
            _loc6_++;
         }
         _loc7_ = UIManager.getUISkin("split_line2");
         _loc7_.width = this.w;
         _loc7_.y = 134;
         this._infoContainer.addChild(_loc7_);
         var _loc5_:Label = this._infoContainer.addChild(new Label(GL.MY_POINTS + ":",TextFormatLib.format_0xFFB932_12px)) as Label;
         _loc5_.x = 8;
         this._myScore = this._infoContainer.addChild(new Label("",TextFormatLib.format_0x00a8ff_12px)) as Label;
         this._myScore.x = _loc5_.x + _loc5_.textWidth + 6;
         _loc5_.y = this._myScore.y = 138;
         var _loc4_:Label = this._infoContainer.addChild(new Label(GL.MY_WIN_STREAK + ":",TextFormatLib.format_0xFFB932_12px)) as Label;
         _loc4_.x = 8;
         this._myWinStreak = this._infoContainer.addChild(new Label("",TextFormatLib.format_0x00a8ff_12px)) as Label;
         this._myWinStreak.x = _loc4_.x + _loc4_.textWidth + 6;
         _loc4_.y = this._myWinStreak.y = 157;
         var _loc2_:Label = this._infoContainer.addChild(new Label(GL.MY_HONOR + ":",TextFormatLib.format_0xFFB932_12px)) as Label;
         _loc2_.x = 8;
         this._myHonor = this._infoContainer.addChild(new Label("",TextFormatLib.format_0x00a8ff_12px)) as Label;
         this._myHonor.x = _loc2_.x + _loc2_.textWidth + 6;
         _loc2_.y = this._myHonor.y = 176;
         this._rightFlexBtn = new ImgButton(UIManager.getMultiUISkin("btn_packUp"));
         this._rightFlexBtn.scaleX = -1;
         this._rightFlexBtn.x = this.w;
         this.addChild(this._rightFlexBtn);
         this._rightFlexBtn.addEventListener("click",this.onClickFlexBtn);
         this._rightFlexBtn.y = this._leftFlexBtn.y = 6;
      }
      
      private function onClickFlexBtn(param1:MouseEvent) : void
      {
         var _loc2_:ImgButton = param1.target as ImgButton;
         switch(_loc2_)
         {
            case this._rightFlexBtn:
               this.fold();
               break;
            case this._leftFlexBtn:
               TweenLite.to(this,0.2,{"x":Core.stgW - this.w});
               this._leftFlexBtn.visible = false;
               this._rightFlexBtn.visible = true;
               this.isOpen = true;
         }
      }
      
      public function fold() : void
      {
         TweenLite.to(this,0.2,{"x":Core.stgW - 20});
         this._rightFlexBtn.visible = false;
         this._leftFlexBtn.visible = true;
         this.isOpen = false;
      }
      
      public function updateTop(param1:Array) : void
      {
         var _loc4_:int = 0;
         var _loc2_:* = 0;
         var _loc3_:* = 0;
         if(param1 != null)
         {
            _loc4_ = param1.length - 1;
            if(_loc4_ > 9)
            {
               _loc4_ = 9;
            }
            if(this.curTopArr.length - 1 == _loc4_)
            {
               _loc3_ = _loc4_;
               while(_loc3_ > -1)
               {
                  if(this.curTopArr[_loc2_].score > param1[_loc2_].score)
                  {
                     return;
                  }
                  _loc3_--;
               }
            }
            _loc2_ = _loc4_;
            while(_loc2_ > -1)
            {
               if(String(param1[_loc2_].uname).indexOf(".s") == -1)
               {
                  this._topList[_loc2_].uName.htmlText = _loc2_ + 1 + ". " + param1[_loc2_].uname + ".s" + param1[_loc2_].server_id;
               }
               else
               {
                  this._topList[_loc2_].uName.htmlText = _loc2_ + 1 + ". " + param1[_loc2_].uname;
               }
               this._topList[_loc2_].points.text = Globalization.getString(GL.POINTS) + param1[_loc2_].score;
               this._topList[_loc2_].setToolTip(param1[_loc2_].server_name);
               _loc2_--;
            }
            this.curTopArr = param1;
            this._sc.nowUpdateUI();
         }
      }
      
      public function get myScore() : Label
      {
         return this._myScore;
      }
      
      public function get myWinStreak() : Label
      {
         return this._myWinStreak;
      }
      
      public function get myHonor() : Label
      {
         return this._myHonor;
      }
   }
}

