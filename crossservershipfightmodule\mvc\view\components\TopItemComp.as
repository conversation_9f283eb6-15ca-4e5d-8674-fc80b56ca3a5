package crossservershipfightmodule.mvc.view.components
{
   import flash.text.TextFormat;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   
   public class TopItemComp extends UISprite
   {
      private var _uName:Label;
      
      private var _points:Label;
      
      public function TopItemComp(param1:TextFormat, param2:Number = 120)
      {
         super();
         this._uName = new Label("",param1);
         this._points = new Label("",param1);
         this._points.x = param2;
         this.addChild(this._uName);
         this.addChild(this._points);
      }
      
      public function get uName() : Label
      {
         return this._uName;
      }
      
      public function get points() : Label
      {
         return this._points;
      }
   }
}

