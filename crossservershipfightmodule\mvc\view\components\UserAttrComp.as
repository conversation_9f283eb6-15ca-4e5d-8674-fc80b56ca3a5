package crossservershipfightmodule.mvc.view.components
{
   import game.manager.UIManager;
   import game.modules.user.view.HeroInfoBoard.heroInfoPanel.roleHeroTransfor.NumberHelper;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import util.ChineseString;
   
   public class UserAttrComp extends UISprite
   {
      private var _attrValue:Label;
      
      private var _attrName:String;
      
      public function UserAttrComp(param1:String, param2:String)
      {
         super();
         var _loc3_:UISkin = addChild(UIManager.getUISkin(param1)) as UISkin;
         this._attrValue = addChild(new Label("",TextFormatLib.format_0xFFFFFF_12px,[FilterLib.dropShadow_0x000000])) as Label;
         this._attrValue.x = _loc3_.width;
         this._attrName = param2;
      }
      
      public function setAttrValue(param1:Number) : void
      {
         this._attrValue.text = ChineseString.desNumbyScale(param1);
         setToolTip(this._attrName + ":" + NumberHelper.getNumberString(param1,0));
      }
   }
}

