package crossservershipfightmodule.mvc.view.utils
{
   import util.Globalization;
   
   public class GL
   {
      public static const GROUPWAR:String = "groupwar.";
      
      public static const ATTACK_LEVEL:String = Globalization.getString("crossservershipfightmodule.2");
      
      public static const DEFEND_LEVEL:String = Globalization.getString("crossservershipfightmodule.3");
      
      public static const HP_LEVEL:String = Globalization.getString("crossservershipfightmodule.4");
      
      public static const JOIN_CD_TIP:String = Globalization.getString("groupwar.1");
      
      public static const PORTALS_ENTER_OK:String = Globalization.getString("groupwar.2");
      
      public static const PORTALS_FULL:String = Globalization.getString("groupwar.3");
      
      public static const PORTALS_LACK_HP:String = Globalization.getString("groupwar.4");
      
      public static const MY_HONOR:String = Globalization.getString("crossservershipfightmodule.19");
      
      public static const CAN_JOIN:String = Globalization.getString("groupwar.6");
      
      public static const FIGHTING:String = Globalization.getString("groupwar.7");
      
      public static const END_CD:String = Globalization.getString("groupwar.8");
      
      public static const JOIN_CD:String = Globalization.getString("worldBoatPetFight.9");
      
      public static const ATTACK_LV_ADD:String = Globalization.getString("groupwar.10");
      
      public static const DEFENCE_LV_ADD:String = Globalization.getString("groupwar.11");
      
      public static const HP_LV_ADD:String = Globalization.getString("crossservershipfightmodule.11");
      
      public static const ENCOURAGE_EXP_TIP:String = Globalization.getString("groupwar.12");
      
      public static const ENCOURAGE_GOLD_TIP:String = Globalization.getString("crossservershipfightmodule.12");
      
      public static const ENCOURAGE_FREE_TIMES:String = Globalization.getString("crossservershipfightmodule.13");
      
      public static const ENCOURAGE_FAIL_EXP_NO:String = Globalization.getString("groupwar.14");
      
      public static const ENCOURAGE_FAIL_GOLD_NO:String = Globalization.getString("crossservershipfightmodule.21");
      
      public static const ENCOURAGE_FAIL_LIMIT:String = Globalization.getString("groupwar.16");
      
      public static const ENCOURAGE_OK_EXP:String = Globalization.getString("groupwar.17");
      
      public static const ENCOURAGE_OK_GOLD:String = Globalization.getString("groupwar.18");
      
      public static const ENCOURAGE_FAIL_EXP:String = Globalization.getString("groupwar.19");
      
      public static const QUIT:String = Globalization.getString("groupwar.20");
      
      public static const MY_POINTS:String = Globalization.getString("groupwar.21");
      
      public static const HONOUR:String = Globalization.getString("crossservershipfightmodule.18");
      
      public static const WAIT:String = Globalization.getString("groupwar.27");
      
      public static const END:String = Globalization.getString("groupwar.28");
      
      public static const READY_CD:String = Globalization.getString("groupwar.29");
      
      public static const PLUNDERING_RESOURCES:String = Globalization.getString("groupwar.30");
      
      public static const PORTALS_WAIT_NUM:String = Globalization.getString("groupwar.31");
      
      public static const CLEARING_HALF:String = Globalization.getString("groupwar.32");
      
      public static const DEFEAT:String = Globalization.getString("groupwar.33");
      
      public static const MY_GET_POINTS:String = Globalization.getString("groupwar.34");
      
      public static const MY_LOST_POINTS:String = Globalization.getString("groupwar.35");
      
      public static const READY_JOIN_CD:String = Globalization.getString("groupwar.36");
      
      public static const READY_JOIN_CD_TIP:String = Globalization.getString("groupwar.37");
      
      public static const POINTS_TOP:String = Globalization.getString("groupwar.38");
      
      public static const PLUNDER_SCORE:String = Globalization.getString("groupwar.39");
      
      public static const REMOVE_JOIN_CD:String = Globalization.getString("groupwar.40");
      
      public static const REMOVE_JOIN_CD_TIP:String = Globalization.getString("groupwar.41");
      
      public static const CD_END_TIP:String = Globalization.getString("groupwar.42");
      
      public static const PORTALS_ENTERED:String = Globalization.getString("groupwar.43");
      
      public static const MY_WIN_STREAK:String = Globalization.getString("groupwar.44");
      
      public static const AUTO:String = Globalization.getString("groupwar.45");
      
      public static const AUTO_STATE:String = Globalization.getString("groupwar.46");
      
      public static const AUTO_VIP:String = Globalization.getString("groupwar.47");
      
      public static const AUTO_TYPE_1:String = Globalization.getString("groupwar.48");
      
      public static const AUTO_TYPE_2:String = Globalization.getString("groupwar.49");
      
      public static const GOLD_NO:String = Globalization.getString("crossservershipfightmodule.21");
      
      public static const RESOURCE:String = Globalization.getString("Globalization.81");
      
      public static const REPORT:String = Globalization.getString("teamwar.5");
      
      public static const REPORT_ALL:String = Globalization.getString("teamwar.9");
      
      public static const REPORT_MY:String = Globalization.getString("teamwar.10");
      
      public static const YOU:String = Globalization.getString("arena.19");
      
      public static const PLAYBACK:String = Globalization.getString("arena.26");
      
      public static const POINTS:String = Globalization.getString("Gl.70");
      
      public static const GOLD:String = Globalization.getString("Gl.43");
      
      public static const BELLY:String = Globalization.getString("Gl.44");
      
      public static const EXPERIECE:String = Globalization.getString("Gl.49");
      
      public static const EXECUTION:String = Globalization.getString("Globalization.xingdongli");
      
      public static const PRESTIGE:String = Globalization.getString("Globalization.shengwang");
      
      public static const HUNDRED_MILLION:String = Globalization.getString("user.35");
      
      public static const TEN_THOUSAND:String = Globalization.getString("user.36");
      
      public static const CONFIRM:String = Globalization.getString("Globalization.37");
      
      public static const FLAUNT:String = Globalization.getString("crossservershipfightmodule.5");
      
      public static const FLAUNT_TIP:String = Globalization.getString("crossservershipfightmodule.6");
      
      public static const HEAD_TITLE:String = Globalization.getString("crossservershipfightmodule.7");
      
      public static const ENCOURAGE_FREE:String = Globalization.getString("crossservershipfightmodule.8");
      
      public static const ENCOURAGE_OK:String = Globalization.getString("crossservershipfightmodule.9");
      
      public static const ENCOURAGE_FAIL:String = Globalization.getString("crossservershipfightmodule.10");
      
      public static const BOAT:String = Globalization.getString("crossservershipfightmodule.14");
      
      public static const CAN_FLAUNT_TIP:String = Globalization.getString("crossservershipfightmodule.15");
      
      public static const GOLD_TEMP:String = Globalization.getString("crossservershipfightmodule.16");
      
      public static const ENCOURAGE_NEED_GOLD:String = Globalization.getString("crossservershipfightmodule.17");
      
      public static const LV:String = Globalization.getString("Globalization.191");
      
      public function GL()
      {
         super();
      }
   }
}

