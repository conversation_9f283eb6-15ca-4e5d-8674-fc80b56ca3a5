package crossservershipfightmodule.mvc.view.utils
{
   public class Texture
   {
      public static const SAY_GLOW:String = "crossservershipfightmodule.SayGlow";
      
      public static const MAP:String = "crossservershipfightmodule.Map";
      
      public static const ARROW:String = "crossservershipfightmodule.Arrow";
      
      public static const FIGHT_START:String = "crossservershipfightmodule.FightStart";
      
      public static const ROLE_PREFIX_BACK:String = "crossservershipfightmodule.Back_";
      
      public static const ROLE_PREFIX_FRONT:String = "crossservershipfightmodule.Front_";
      
      public static const NUM:String = "CrossServerShipFightModuleNum";
      
      public static const SMAILL_NUM:String = "CrossServerShipFightModuleSmallNum";
      
      public static const SMAILL_COLON:String = "CrossServerShipFightModuleSmallColon";
      
      public static const SMAILL_PARENTHESIS_LEFT:String = "CrossServerShipFightModuleSmallParenthesisLeft";
      
      public static const SMAILL_PARENTHESIS_RIGHT:String = "CrossServerShipFightModuleSmallParenthesisRight";
      
      public static const SMAILL_PLUS:String = "CrossServerShipFightModuleSmallPlus";
      
      public static const END_TIME_TITLE:String = "CrossServerShipFightModuleEndTimeTitle";
      
      public static const SHIP_TITLE_1:String = "CrossServerShipFightModuleShipTitle1";
      
      public static const SHIP_TITLE_2:String = "CrossServerShipFightModuleShipTitle2";
      
      public static const TOP_BG:String = "worldBoatResultBg";
      
      public static const END_BG:String = "CrossServerShipFightModuleEndBg";
      
      public static const END_TITLE:String = "CrossServerShipFightModuleEndTitle";
      
      public static const END_GET_TITLE_1:String = "CrossServerShipFightModuleEndGetTitle1";
      
      public static const END_GET_TITLE_2:String = "CrossServerShipFightModuleEndGetTitle2";
      
      public static const END_GET_TITLE_3:String = "CrossServerShipFightModuleEndGetTitle3";
      
      public static const END_GET_TITLE_UNIT:String = "CrossServerShipFightModuleEndUnit";
      
      public static const END_TOP:String = "CrossServerShipFightModuleEndTop";
      
      public static const END_TOP_BG:String = "CrossServerShipFightModuleEndTopBG";
      
      public static const END_MY_TITLE_1:String = "CrossServerShipFightModuleEndMyTitle1";
      
      public static const END_MY_TITLE_2:String = "CrossServerShipFightModuleEndMyTitle2";
      
      public static const BTN_TRANSFER:String = "CrossServerShipFightModuleBTNTransfer";
      
      public static const BTN_SAY:String = "CrossServerShipFightModuleBTNSay";
      
      public static const BTN_SHIP:String = "boatStrengthBtn";
      
      public static const BTN_GOLD_INSPIRE:String = "CrossServerShipFightModuleBTNGoldInspire";
      
      public static const BTN_HELP:String = "soul_Help_button";
      
      public static const BTN_QUIT:String = "exitArenaBtn";
      
      public static const ICON_BG:String = "icon_bg";
      
      public static const EXPLOSION:String = "meffect_24";
      
      public static const PRO_BG_SMALL:String = "pro_bg_small";
      
      public static const PRO_RED_TINY:String = "pro_red_tiny";
      
      public static const SPLIT_HORIZONTAL:String = "line2";
      
      public static const TEXT_BG_2:String = "text_bg_2";
      
      public static const TEXT_BG_BIG:String = "text_bg_17";
      
      public static const INFO_BG:String = "info_bg";
      
      public static const BLACK_BG:String = "black_bg";
      
      public static const BG_V5:String = "bg_v5";
      
      public static const GOLD_ICON:String = "gold";
      
      public static const EXP_ICON:String = "experence";
      
      public static const BTN_CD:String = "btn_CD";
      
      public function Texture()
      {
         super();
      }
   }
}

