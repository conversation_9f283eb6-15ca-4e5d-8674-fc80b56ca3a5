package customEvent
{
   import flash.events.Event;
   
   public class BuffDebugEvent extends Event
   {
      private var _info:String;
      
      public function BuffDebugEvent()
      {
         super("event_for_buff_debug");
      }
      
      public function get info() : String
      {
         return this._info;
      }
      
      public function set info(param1:String) : void
      {
         this._info = param1;
      }
   }
}

