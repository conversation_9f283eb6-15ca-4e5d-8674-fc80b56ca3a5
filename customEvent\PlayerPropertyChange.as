package customEvent
{
   import flash.events.Event;
   
   public final class PlayerProperty<PERSON>hange extends Event
   {
      public static const EVE_PLAYER_PROPERTY_CHANGE:String = "EVE_PLAYER_PROPERTY_CHANGE";
      
      private var _uid:uint;
      
      private var _from:Object;
      
      private var _to:Object;
      
      private var _changeValue:Object;
      
      private var _changePropertyType:uint;
      
      public function PlayerPropertyChange()
      {
         super("EVE_PLAYER_PROPERTY_CHANGE");
      }
      
      public function get uid() : uint
      {
         return this._uid;
      }
      
      public function set uid(param1:uint) : void
      {
         this._uid = param1;
      }
      
      public function get from() : Object
      {
         return this._from;
      }
      
      public function set from(param1:Object) : void
      {
         this._from = param1;
      }
      
      public function get to() : Object
      {
         return this._to;
      }
      
      public function set to(param1:Object) : void
      {
         this._to = param1;
      }
      
      public function get changePropertyType() : uint
      {
         return this._changePropertyType;
      }
      
      public function set changePropertyType(param1:uint) : void
      {
         this._changePropertyType = param1;
      }
      
      public function get changeValue() : Object
      {
         return this._changeValue;
      }
      
      public function set changeValue(param1:Object) : void
      {
         this._changeValue = param1;
      }
      
      override public function clone() : Event
      {
         var _loc1_:PlayerPropertyChange = new PlayerPropertyChange();
         _loc1_.uid = this._uid;
         _loc1_._from = this._from;
         _loc1_.to = this._to;
         _loc1_.changeValue = this._changeValue;
         _loc1_._changePropertyType = this._changePropertyType;
         return _loc1_;
      }
      
      override public function toString() : String
      {
         return this.formatToString("EVE_PLAYER_PROPERTY_CHANGE","type","bubbles","cancelabled","eventPhase");
      }
   }
}

