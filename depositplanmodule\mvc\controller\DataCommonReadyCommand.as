package depositplanmodule.mvc.controller
{
   import depositplanmodule.mvc.model.DataProxy;
   import org.puremvc.as3.interfaces.ICommand;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class DataCommonReadyCommand extends SimpleCommand implements ICommand
   {
      public function DataCommonReadyCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         this._dataProxy.dataCommonReady();
      }
      
      private function get _dataProxy() : DataProxy
      {
         return facade.retrieveProxy("depositplanmodule.mvc.model.DataProxy") as DataProxy;
      }
   }
}

