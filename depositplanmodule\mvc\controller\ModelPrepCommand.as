package depositplanmodule.mvc.controller
{
   import depositplanmodule.mvc.model.DataProxy;
   import depositplanmodule.mvc.model.ServiceProxy;
   import org.puremvc.as3.interfaces.ICommand;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class ModelPrepCommand extends SimpleCommand implements ICommand
   {
      public function ModelPrepCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         if(!facade.retrieveProxy("depositplanmodule.mvc.model.DataProxy"))
         {
            facade.registerProxy(new DataProxy());
         }
         if(!facade.retrieveProxy("depositplanmodule.mvc.model.ServiceProxy"))
         {
            facade.registerProxy(new ServiceProxy());
         }
      }
   }
}

