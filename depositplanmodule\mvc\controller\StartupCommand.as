package depositplanmodule.mvc.controller
{
   import org.puremvc.as3.interfaces.ICommand;
   import org.puremvc.as3.patterns.command.MacroCommand;
   
   public class StartupCommand extends MacroCommand implements ICommand
   {
      public function StartupCommand()
      {
         super();
      }
      
      override protected function initializeMacroCommand() : void
      {
         addSubCommand(ModelPrepCommand);
         addSubCommand(ViewPrepCommand);
      }
   }
}

