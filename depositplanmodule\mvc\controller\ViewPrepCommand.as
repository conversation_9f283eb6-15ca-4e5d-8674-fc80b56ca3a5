package depositplanmodule.mvc.controller
{
   import depositplanmodule.mvc.view.DepositPlanMediator;
   import depositplanmodule.mvc.view.DepositPlanReturnMediator;
   import depositplanmodule.mvc.view.components.DepositPlanComp;
   import depositplanmodule.mvc.view.components.DepositPlanReturnComp;
   import org.puremvc.as3.interfaces.ICommand;
   import org.puremvc.as3.interfaces.INotification;
   import org.puremvc.as3.patterns.command.SimpleCommand;
   
   public class ViewPrepCommand extends SimpleCommand implements ICommand
   {
      public function ViewPrepCommand()
      {
         super();
      }
      
      override public function execute(param1:INotification) : void
      {
         if(param1.getBody() is DepositPlanComp)
         {
            facade.registerMediator(new DepositPlanMediator(param1.getBody() as DepositPlanComp));
         }
         if(param1.getBody() is DepositPlanReturnComp)
         {
            facade.registerMediator(new DepositPlanReturnMediator(param1.getBody() as DepositPlanReturnComp));
         }
      }
   }
}

