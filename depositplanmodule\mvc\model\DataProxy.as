package depositplanmodule.mvc.model
{
   import depositplanmodule.mvc.model.vo.DataVO;
   import depositplanmodule.mvc.model.vo.FundVO;
   import depositplanmodule.mvc.model.vo.HadFundVO;
   import depositplanmodule.mvc.view.utils.GL;
   import flash.events.TimerEvent;
   import flash.utils.Dictionary;
   import flash.utils.Timer;
   import game.Environment;
   import game.data.MainData;
   import game.manager.XmlManager;
   import mmo.Core;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.IProxy;
   import org.puremvc.as3.patterns.proxy.Proxy;
   import util.ModelUtilities;
   import util.time.TimeManager;
   
   public class DataProxy extends Proxy implements IProxy
   {
      public static const NAME:String = "depositplanmodule.mvc.model.DataProxy";
      
      private var _dataVO:DataVO;
      
      private var _timer:Timer;
      
      public function DataProxy()
      {
         super("depositplanmodule.mvc.model.DataProxy");
      }
      
      override public function onRegister() : void
      {
         this._dataVO = new DataVO();
      }
      
      override public function onRemove() : void
      {
         this._killTimer();
         super.onRemove();
      }
      
      public function dataCommonReady() : void
      {
         this._xmlConfig();
      }
      
      private function _xmlConfig() : void
      {
         var _loc11_:int = 0;
         var _loc14_:Array = Core.autoactive.data;
         var _loc5_:String = "0";
         var _loc17_:String = "0";
         _loc11_ = 0;
         while(_loc11_ < _loc14_.length)
         {
            if(_loc14_[_loc11_].hasOwnProperty("a_name") && _loc14_[_loc11_].a_name == "jijin")
            {
               _loc5_ = _loc14_[_loc11_].stime;
               _loc17_ = _loc14_[_loc11_].etime;
               break;
            }
            _loc11_++;
         }
         var _loc9_:XML = null;
         var _loc8_:Number = NaN;
         var _loc12_:Number = NaN;
         var _loc13_:Number = NaN;
         var _loc15_:int = 0;
         var _loc16_:int = 0;
         var _loc2_:int = 0;
         var _loc4_:Array = null;
         var _loc3_:Array = null;
         var _loc6_:FundVO = null;
         var _loc1_:Array = null;
         var _loc10_:XMLList = XmlManager.layIn.children();
         var _loc7_:Number = ModelUtilities.timeArrayToNumber(String(Environment.loadingParams.openDateTime).split("-"));
         var _loc18_:Number = TimeManager.getInstance().getTime();
         _loc15_ = _loc10_.length() - 1;
         if(_loc5_ != "0")
         {
            _loc10_.@startTime = _loc5_;
            _loc10_.@closeTime = _loc17_;
            _loc10_.@needOpenTime = _loc17_;
         }
         while(_loc15_ > -1)
         {
            _loc9_ = _loc10_[_loc15_];
            _loc8_ = ModelUtilities.timeStringToNumber(String(_loc9_.@needOpenTime));
            if(_loc7_ <= _loc8_)
            {
               _loc12_ = ModelUtilities.timeStringToNumber(String(_loc9_.@startTime));
               _loc13_ = ModelUtilities.timeStringToNumber(String(_loc9_.@closeTime));
               if(_loc18_ > _loc12_ && _loc18_ < _loc13_)
               {
                  this._dataVO.inEventTime = true;
                  break;
               }
            }
            _loc15_--;
         }
         if(this._dataVO.inEventTime)
         {
            _loc4_ = ModelUtilities.timeStringToArray(String(_loc9_.@startTime));
            _loc3_ = ModelUtilities.timeStringToArray(String(_loc9_.@closeTime));
            this._dataVO.eventTime = StringUtil.substitute(GL.TIME,_loc4_[0],_loc4_[1],_loc4_[2],_loc4_[3],_loc4_[4],_loc4_[5]) + "-" + StringUtil.substitute(GL.TIME,_loc3_[0],_loc3_[1],_loc3_[2],_loc3_[3],_loc3_[4],_loc3_[5]);
         }
         else
         {
            _loc15_ = _loc10_.length() - 1;
            while(_loc15_ > -1)
            {
               _loc9_ = _loc10_[_loc15_];
               _loc12_ = ModelUtilities.timeStringToNumber(String(_loc9_.@startTime));
               if(_loc12_ < _loc18_)
               {
                  break;
               }
               _loc15_--;
            }
            this._dataVO.eventTime = GL.EVENT_END;
         }
         this._dataVO.endTime = _loc13_;
         this._dataVO.eventFunds = Vector.<String>(String(_loc9_.@foundationId).split("|"));
         this._dataVO.max = int(_loc9_.@maxNum);
         this._dataVO.purchasedTip = StringUtil.substitute(GL.HAD_FUND,"<font color=\'#FFFFFF\'>0/" + this._dataVO.max + "</font>");
         if(this._dataVO.funds == null)
         {
            this._dataVO.funds = new Dictionary();
            _loc10_ = XmlManager.foundation.children();
            _loc15_ = _loc10_.length() - 1;
            while(_loc15_ > -1)
            {
               _loc9_ = _loc10_[_loc15_];
               _loc6_ = new FundVO();
               _loc6_.id = int(_loc9_.@id);
               _loc6_.gold = Number(_loc9_.@gold);
               _loc6_.vip = int(_loc9_.@vip);
               if(MainData.getInstance().userData.vip < _loc6_.vip)
               {
                  _loc6_.vipNeedGoldTip = StringUtil.substitute(GL.TIP_BUY_VIP,_loc6_.vip);
               }
               _loc6_.vipInfo = "VIP" + _loc6_.vip + GL.CAN_BUY;
               _loc6_.name = String(_loc9_.@name);
               _loc6_.info = String(_loc9_.@info);
               _loc6_.returnAllGold = 0;
               _loc1_ = String(_loc9_.attribute("return")).split(",");
               _loc2_ = int(_loc1_.length);
               _loc6_.returnGold = new Vector.<Vector.<Number>>(_loc2_,true);
               _loc16_ = 0;
               while(_loc16_ < _loc2_)
               {
                  _loc6_.returnGold[_loc16_] = Vector.<Number>(String(_loc1_[_loc16_]).split("|"));
                  _loc6_.returnAllGold += _loc6_.returnGold[_loc16_][1];
                  _loc16_++;
               }
               _loc6_.income = String(int(_loc6_.returnAllGold / _loc6_.gold * 100));
               this._dataVO.funds[String(_loc9_.@id)] = _loc6_;
               _loc15_--;
            }
         }
         sendNotification("MutexIsLiveFalseWindowDataReady");
         (facade.retrieveProxy("depositplanmodule.mvc.model.ServiceProxy") as ServiceProxy).hadFunds();
         this._setTimer();
      }
      
      public function hadFunds(param1:Object) : void
      {
         var hadFundVO:HadFundVO = null;
         var order:Object = null;
         var key:String = null;
         var data:Object = param1;
         this._dataVO.purchased = 0;
         this._dataVO.hadFunds = new Vector.<HadFundVO>();
         for(key in data)
         {
            hadFundVO = new HadFundVO();
            order = data[key];
            hadFundVO.orderID = key;
            hadFundVO.fund = this._dataVO.funds[order.id];
            hadFundVO.num = order.num;
            this._dataVO.purchased += hadFundVO.num;
            hadFundVO.returned = order.reward - 1;
            hadFundVO.startTime = order.time * 1000;
            this._dataVO.hadFunds[this._dataVO.hadFunds.length] = hadFundVO;
            this._updateHadFund(this._dataVO.hadFunds.length - 1);
         }
         this._dataVO.hadFunds = this._dataVO.hadFunds.sort(function(param1:HadFundVO, param2:HadFundVO):Number
         {
            if(param1.fund.id < param2.fund.id)
            {
               return -1;
            }
            if(param1.fund.id > param2.fund.id)
            {
               return 1;
            }
            return 0;
         });
         this._dataVO.hadFunds = this._dataVO.hadFunds.sort(function(param1:HadFundVO, param2:HadFundVO):Number
         {
            if(param1.startTime < param2.startTime)
            {
               return -1;
            }
            if(param1.startTime > param2.startTime)
            {
               return 1;
            }
            return 0;
         });
         this._dataVO.hadFunds.fixed = true;
         this._dataVO.purchasedTip = StringUtil.substitute(GL.HAD_FUND,"<font color=\'#FFFFFF\'>" + this._dataVO.purchased + "/" + this._dataVO.max + "</font>");
         sendNotification("DepositPlanHadFunds");
      }
      
      private function _updateHadFund(param1:int) : void
      {
         var _loc6_:int = 0;
         var _loc2_:int = 0;
         var _loc3_:Number = NaN;
         var _loc4_:int = 0;
         var _loc7_:HadFundVO = this._dataVO.hadFunds[param1];
         var _loc5_:Number = TimeManager.getInstance().getTime();
         _loc7_.nextCanReturn = 0;
         _loc7_.nextCanReturnInfo = "0";
         _loc7_.nextReturnTime = 0;
         _loc7_.canReturns = new Vector.<int>();
         _loc7_.nowCanReturn = 0;
         _loc7_.returnedGold = 0;
         _loc2_ = int(_loc7_.fund.returnGold.length);
         _loc6_ = 0;
         while(_loc6_ < _loc2_)
         {
            _loc3_ = _loc7_.startTime + _loc7_.fund.returnGold[_loc6_][0] * 86400000;
            if(_loc5_ < _loc3_)
            {
               _loc7_.nextCanReturn = _loc7_.fund.returnGold[_loc6_][1] * _loc7_.num;
               if(_loc5_ > _loc7_.startTime)
               {
                  _loc4_ = int((_loc3_ - _loc5_) / 86400000);
                  if(_loc4_)
                  {
                     _loc7_.nextCanReturnInfo = _loc7_.nextCanReturn + "<font color=\'#FFB932\'>(" + StringUtil.substitute(GL.DAY_AFTER,_loc4_) + ")</font>";
                  }
                  else
                  {
                     _loc7_.nextCanReturnInfo = _loc7_.nextCanReturn + "<font color=\'#FFB932\'>(" + GL.TODAY + ")</font>";
                  }
               }
               _loc7_.nextReturnTime = _loc3_;
               break;
            }
            if(_loc6_ > _loc7_.returned)
            {
               _loc7_.canReturns[_loc7_.canReturns.length] = _loc6_;
               _loc7_.nowCanReturn += _loc7_.fund.returnGold[_loc6_][1] * _loc7_.num;
            }
            else
            {
               _loc7_.returnedGold += _loc7_.fund.returnGold[_loc6_][1] * _loc7_.num;
            }
            _loc6_++;
         }
      }
      
      public function openSendCheck(param1:FundVO) : String
      {
         var _loc2_:String = null;
         if(this._dataVO.purchased >= this._dataVO.max)
         {
            _loc2_ = GL.NUM_NOT_ENOUGH;
         }
         else if(param1.vipNeedGoldTip)
         {
            _loc2_ = param1.vipNeedGoldTip;
         }
         return _loc2_;
      }
      
      public function sendCheck(param1:Number) : String
      {
         var _loc2_:String = null;
         if(param1 > MainData.getInstance().userData.gold_num)
         {
            _loc2_ = GL.GOLD_NOT_ENOUGH;
         }
         return _loc2_;
      }
      
      private function _timerHandler(param1:TimerEvent) : void
      {
         this._timeRun();
      }
      
      private function _timeRun() : void
      {
         var _loc4_:Boolean = false;
         var _loc3_:Number = NaN;
         var _loc1_:HadFundVO = null;
         var _loc2_:int = 0;
         if(this._dataVO.hadFunds && this._dataVO.hadFunds.length)
         {
            _loc3_ = TimeManager.getInstance().getTime();
            _loc2_ = this._dataVO.hadFunds.length - 1;
            while(_loc2_ > -1)
            {
               _loc1_ = this._dataVO.hadFunds[_loc2_];
               if(_loc3_ > _loc1_.startTime && _loc1_.nextReturnTime && _loc3_ > _loc1_.nextReturnTime)
               {
                  this._updateHadFund(_loc2_);
                  _loc4_ = true;
               }
               _loc2_--;
            }
            if(_loc4_)
            {
               if(this._dataVO.inEventTime)
               {
                  this._dataVO.inEventTime = false;
                  this._xmlConfig();
               }
               else
               {
                  sendNotification("DepositPlanHadFunds");
               }
               sendNotification("DepositPlanEnterBtnEffect",true);
            }
         }
      }
      
      private function _setTimer() : void
      {
         if(this._timer == null)
         {
            this._timer = new Timer(1000);
            this._timer.addEventListener("timer",this._timerHandler);
            this._timer.start();
         }
      }
      
      private function _killTimer() : void
      {
         if(this._timer)
         {
            this._timer.reset();
            this._timer.removeEventListener("timer",this._timerHandler);
            this._timer = null;
         }
      }
      
      public function get dataVO() : DataVO
      {
         return this._dataVO;
      }
   }
}

