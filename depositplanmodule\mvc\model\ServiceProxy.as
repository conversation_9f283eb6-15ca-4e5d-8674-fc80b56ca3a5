package depositplanmodule.mvc.model
{
   import depositplanmodule.mvc.view.utils.GL;
   import game.data.MainData;
   import game.net.BabelTimeSocket;
   import game.net.SocketCallback;
   import game.net.SocketDataEvent;
   import mx.utils.StringUtil;
   import org.puremvc.as3.interfaces.IProxy;
   import org.puremvc.as3.patterns.proxy.Proxy;
   
   public class ServiceProxy extends Proxy implements IProxy
   {
      public static const NAME:String = "depositplanmodule.mvc.model.ServiceProxy";
      
      private var _dataProxy:DataProxy;
      
      public function ServiceProxy()
      {
         super("depositplanmodule.mvc.model.ServiceProxy");
      }
      
      override public function onRegister() : void
      {
         this._dataProxy = facade.retrieveProxy("depositplanmodule.mvc.model.DataProxy") as DataProxy;
         this._regSocketCallback();
      }
      
      override public function onRemove() : void
      {
         this._removeSocketCallback();
         super.onRemove();
      }
      
      private function _regSocketCallback() : void
      {
         BabelTimeSocket.getInstance().regCallback("re.depositPlan.getDepositPlanInfo",this._reHadFunds);
         BabelTimeSocket.getInstance().regCallback("re.depositPlan.buyDepositPlan",this._reBuy);
         BabelTimeSocket.getInstance().regCallback("re.depositPlan.receivePrize",this._reReturnGold);
      }
      
      private function _removeSocketCallback() : void
      {
         BabelTimeSocket.getInstance().removeCallback("re.depositPlan.getDepositPlanInfo",this._reHadFunds);
         BabelTimeSocket.getInstance().removeCallback("re.depositPlan.buyDepositPlan",this._reBuy);
         BabelTimeSocket.getInstance().removeCallback("re.depositPlan.receivePrize",this._reReturnGold);
      }
      
      public function hadFunds() : void
      {
         BabelTimeSocket.getInstance().sendMessage("depositPlan.getDepositPlanInfo",new SocketCallback("re.depositPlan.getDepositPlanInfo"));
      }
      
      private function _reHadFunds(param1:SocketDataEvent) : void
      {
         this._dataProxy.dataVO.hadFunds = null;
         if(param1.data)
         {
            this._dataProxy.hadFunds(param1.data);
            if(!this._dataProxy.dataVO.inEventTime)
            {
               sendNotification("DepositPlanReturnEnterBtn",true);
            }
         }
         else
         {
            this._dataProxy.dataVO.purchased = 0;
            this._dataProxy.dataVO.purchasedTip = StringUtil.substitute(GL.HAD_FUND,"<font color=\'#FFFFFF\'>" + this._dataProxy.dataVO.purchased + "/" + this._dataProxy.dataVO.max + "</font>");
            sendNotification("DepositPlanHadFunds");
            sendNotification("DepositPlanReturnEnterBtn",false);
         }
      }
      
      public function buy(param1:int, param2:int, param3:Number) : void
      {
         BabelTimeSocket.getInstance().sendMessage("depositPlan.buyDepositPlan",new SocketCallback("re.depositPlan.buyDepositPlan",[param3]),param1,param2);
      }
      
      private function _reBuy(param1:SocketDataEvent) : void
      {
         if(param1.error == "ok")
         {
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num - param1.callbackParames[0];
            this.hadFunds();
         }
      }
      
      public function returnGold(param1:String, param2:Number) : void
      {
         BabelTimeSocket.getInstance().sendMessage("depositPlan.receivePrize",new SocketCallback("re.depositPlan.receivePrize",[param2]),param1);
      }
      
      private function _reReturnGold(param1:SocketDataEvent) : void
      {
         if(param1.error == "ok")
         {
            MainData.getInstance().userData.gold_num = MainData.getInstance().userData.gold_num + param1.callbackParames[0];
            this.hadFunds();
         }
      }
   }
}

