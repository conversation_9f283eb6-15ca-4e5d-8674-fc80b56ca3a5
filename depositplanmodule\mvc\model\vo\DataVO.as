package depositplanmodule.mvc.model.vo
{
   import flash.utils.Dictionary;
   
   public class DataVO
   {
      public var inEventTime:Boolean;
      
      public var eventTime:String;
      
      public var endTime:Number;
      
      public var max:int;
      
      public var eventFunds:Vector.<String>;
      
      public var funds:Dictionary;
      
      public var hadFunds:Vector.<HadFundVO>;
      
      public var purchased:int;
      
      public var purchasedTip:String;
      
      public function DataVO()
      {
         super();
      }
   }
}

