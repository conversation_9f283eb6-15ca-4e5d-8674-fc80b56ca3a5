package depositplanmodule.mvc.view
{
   import depositplanmodule.mvc.model.DataProxy;
   import depositplanmodule.mvc.model.ServiceProxy;
   import depositplanmodule.mvc.model.vo.FundVO;
   import depositplanmodule.mvc.view.components.DepositPlanComp;
   import depositplanmodule.mvc.view.components.DetailComp;
   import depositplanmodule.mvc.view.components.FundComp;
   import depositplanmodule.mvc.view.components.SendComp;
   import depositplanmodule.mvc.view.utils.GL;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.data.PirateMediator;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import org.puremvc.as3.interfaces.IMediator;
   import org.puremvc.as3.interfaces.INotification;
   import util.ChineseString;
   import util.openModule;
   import util.time.TimeManager;
   
   public class DepositPlanMediator extends PirateMediator implements IMediator
   {
      public static const NAME:String = "depositplanmodule.mvc.view.DepositPlanMediator";
      
      private var _noteName:String;
      
      private var _noteBody:Object;
      
      private var _dataProxy:DataProxy;
      
      private var _serviceProxy:ServiceProxy;
      
      public function DepositPlanMediator(param1:DepositPlanComp)
      {
         super("depositplanmodule.mvc.view.DepositPlanMediator",param1);
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().userData];
      }
      
      override public function onRegister() : void
      {
         this._dataProxy = facade.retrieveProxy("depositplanmodule.mvc.model.DataProxy") as DataProxy;
         this._serviceProxy = facade.retrieveProxy("depositplanmodule.mvc.model.ServiceProxy") as ServiceProxy;
         checkDataAvialable(function():void
         {
            sendNotification("MutexIsLiveFalseWindowDataCommonReady");
         });
      }
      
      override public function onRemove() : void
      {
         this.comp.removeEventListener("click",this._compMouseEvent);
         super.onRemove();
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["MutexIsLiveFalseWindowDataReady","DepositPlanHadFunds"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         this._noteName = param1.getName();
         this._noteBody = param1.getBody();
         switch(this._noteName)
         {
            case "MutexIsLiveFalseWindowDataReady":
               this.comp.eventTime(this._dataProxy.dataVO.eventTime);
               this.comp.updatePurchased(this._dataProxy.dataVO.purchasedTip);
               this.comp.funds(this._dataProxy.dataVO.eventFunds,this._dataProxy.dataVO.funds,this._dataProxy.dataVO.inEventTime);
               this.comp.addEventListener("click",this._compMouseEvent);
               break;
            case "DepositPlanHadFunds":
               this.comp.updatePurchased(this._dataProxy.dataVO.purchasedTip);
         }
      }
      
      private function _compMouseEvent(param1:MouseEvent) : void
      {
         var _loc5_:Sprite = null;
         var _loc3_:Array = null;
         var _loc4_:String = null;
         var _loc2_:FundVO = null;
         if(param1.target is Sprite && (param1.target as Sprite).buttonMode)
         {
            _loc5_ = param1.target as Sprite;
            _loc3_ = _loc5_.name.split("_");
            var _loc6_:* = param1.type;
            if("click" === _loc6_)
            {
               switch(_loc3_[0])
               {
                  case "btnRecharge":
                     sendNotification("chargeGold");
                     break;
                  case "btnMyFund":
                     openModule("DepositPlanReturnModule",true,null,true,true);
                     break;
                  case "btnBuy":
                     if(TimeManager.getInstance().getTime() > this._dataProxy.dataVO.endTime)
                     {
                        sendNotification("POP_TEXT_TIPS",{
                           "text":GL.EVENT_END,
                           "textFormat":TextFormatLib.format_0x00FF00_14px
                        });
                        this.comp.close();
                        break;
                     }
                     _loc2_ = (_loc5_.parent as FundComp).data;
                     _loc4_ = this._dataProxy.openSendCheck(_loc2_);
                     if(_loc4_)
                     {
                        sendNotification("POP_TEXT_TIPS",{
                           "text":_loc4_,
                           "textFormat":TextFormatLib.format_0x00FF00_14px
                        });
                        break;
                     }
                     PopUpCenter.addPopUp("depositplanmodule.mvc.view.components.SendComp",new SendComp(_loc2_,this._dataProxy.dataVO.max - this._dataProxy.dataVO.purchased,ChineseString.numFloorToString(MainData.getInstance().userData.gold_num,100000)),true,true);
                     break;
                  case "btnDetail":
                     _loc2_ = (_loc5_.parent as FundComp).data;
                     PopUpCenter.addPopUp("depositplanmodule.mvc.view.components.DetailComp",new DetailComp(_loc2_,this._dataProxy.dataVO.inEventTime),true,true);
               }
            }
         }
      }
      
      public function get comp() : DepositPlanComp
      {
         return viewComponent as DepositPlanComp;
      }
   }
}

