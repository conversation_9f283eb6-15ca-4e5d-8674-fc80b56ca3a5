package depositplanmodule.mvc.view
{
   import depositplanmodule.mvc.model.DataProxy;
   import depositplanmodule.mvc.model.ServiceProxy;
   import depositplanmodule.mvc.model.vo.FundVO;
   import depositplanmodule.mvc.view.components.DepositPlanReturnComp;
   import depositplanmodule.mvc.view.components.DetailComp;
   import depositplanmodule.mvc.view.components.HadFundComp;
   import depositplanmodule.mvc.view.components.ReturnGoldComp;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.data.PirateMediator;
   import mmo.ui.control.PopUpCenter;
   import org.puremvc.as3.interfaces.IMediator;
   import org.puremvc.as3.interfaces.INotification;
   import util.openModule;
   
   public class DepositPlanReturnMediator extends PirateMediator implements IMediator
   {
      public static const NAME:String = "depositplanmodule.mvc.view.DepositPlanReturnMediator";
      
      private var _noteName:String;
      
      private var _noteBody:Object;
      
      private var _dataProxy:DataProxy;
      
      private var _serviceProxy:ServiceProxy;
      
      public function DepositPlanReturnMediator(param1:DepositPlanReturnComp)
      {
         super("depositplanmodule.mvc.view.DepositPlanReturnMediator",param1);
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().userData];
      }
      
      override public function onRegister() : void
      {
         this._dataProxy = facade.retrieveProxy("depositplanmodule.mvc.model.DataProxy") as DataProxy;
         this._serviceProxy = facade.retrieveProxy("depositplanmodule.mvc.model.ServiceProxy") as ServiceProxy;
         checkDataAvialable(function():void
         {
            sendNotification("MutexIsLiveFalseWindowDataCommonReady");
         });
         this.comp.addEventListener("click",this._compMouseEvent);
      }
      
      override public function onRemove() : void
      {
         this.comp.removeEventListener("click",this._compMouseEvent);
         super.onRemove();
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["DepositPlanHadFunds"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         this._noteName = param1.getName();
         this._noteBody = param1.getBody();
         var _loc2_:* = this._noteName;
         if("DepositPlanHadFunds" === _loc2_)
         {
            this.comp.updateContent(this._dataProxy.dataVO.hadFunds);
         }
      }
      
      private function _compMouseEvent(param1:MouseEvent) : void
      {
         var _loc4_:Sprite = null;
         var _loc2_:Array = null;
         var _loc3_:FundVO = null;
         if(param1.target is Sprite && (param1.target as Sprite).buttonMode)
         {
            _loc4_ = param1.target as Sprite;
            _loc2_ = _loc4_.name.split("_");
            var _loc5_:* = param1.type;
            if("click" === _loc5_)
            {
               switch(_loc2_[0])
               {
                  case "btnReceive":
                     PopUpCenter.addPopUp("depositplanmodule.mvc.view.components.ReturnGoldComp",new ReturnGoldComp((_loc4_.parent.parent as HadFundComp).data),true,true);
                     break;
                  case "btnDetail":
                     _loc3_ = (_loc4_.parent.parent as HadFundComp).data.fund;
                     PopUpCenter.addPopUp("depositplanmodule.mvc.view.components.DetailComp",new DetailComp(_loc3_,this._dataProxy.dataVO.inEventTime),true,true);
                     break;
                  case "btnView":
                     this.comp.close();
                     if(!PopUpCenter.containsWin("DepositPlanModule"))
                     {
                        openModule("DepositPlanModule");
                        break;
                     }
               }
            }
         }
      }
      
      public function get comp() : DepositPlanReturnComp
      {
         return viewComponent as DepositPlanReturnComp;
      }
   }
}

