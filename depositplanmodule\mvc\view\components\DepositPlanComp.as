package depositplanmodule.mvc.view.components
{
   import depositplanmodule.mvc.controller.DataCommonReadyCommand;
   import depositplanmodule.mvc.controller.StartupCommand;
   import depositplanmodule.mvc.view.utils.GL;
   import flash.events.Event;
   import flash.utils.Dictionary;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   
   public class DepositPlanComp extends PopUpWindow
   {
      public static const NAME:String = "depositplanmodule.mvc.view.components.DepositPlanComp";
      
      public static const BTN_RECHARGE:String = "btnRecharge";
      
      public static const BTN_MY_FUND:String = "btnMyFund";
      
      public static const BTN_BUY:String = "btnBuy";
      
      public static const BTN_DETAIL:String = "btnDetail";
      
      private var _facade:AppFacade;
      
      private var _purchased:Label;
      
      private var _eventTime:Label;
      
      private var _funds:UISprite;
      
      public function DepositPlanComp()
      {
         super(592,567);
         isLive = false;
      }
      
      override public function show(param1:Object) : void
      {
         this._setup();
      }
      
      override public function dispose() : void
      {
         this.kill();
         super.dispose();
      }
      
      private function _setup() : void
      {
         if(stage)
         {
            this._initStage();
         }
         else
         {
            addEventListener("addedToStage",this._initStage);
         }
      }
      
      private function _initMVC() : void
      {
         this._facade = AppFacade.instance;
         this._initController();
         this._startup();
      }
      
      private function _initController() : void
      {
         this._facade.registerCommand("MutexIsLiveFalseWindowStartUp",StartupCommand);
         this._facade.registerCommand("MutexIsLiveFalseWindowDataCommonReady",DataCommonReadyCommand);
      }
      
      private function _startup() : void
      {
         this._facade.sendNotification("MutexIsLiveFalseWindowStartUp",this);
      }
      
      private function _initStage(param1:Event = null) : void
      {
         var _loc7_:Label = null;
         var _loc2_:Label = null;
         var _loc3_:ImgButton = null;
         var _loc5_:ImgButton = null;
         var _loc4_:UISkin = null;
         if(param1)
         {
            removeEventListener("addedToStage",this._initStage);
         }
         setTitleImageData(UIManager.getUISkin("DepositPlanModuleTitle").bitmapData,-20);
         var _loc8_:UISkin = addChild(UIManager.getUISkin("pane_bg_light")) as UISkin;
         _loc8_.x = 7;
         _loc8_.y = 30;
         _loc8_.setSize(579,209);
         var _loc6_:UISkin = addChild(UIManager.getUISkin("DepositPlanModuleInfo")) as UISkin;
         _loc6_.x = 27;
         _loc6_.y = 47;
         _loc7_ = addChild(new Label(GL.BUY_INFO_TITLE + ":",TextFormatLib.format_0xffed89_12px)) as Label;
         _loc7_.x = 30;
         _loc7_.y = 132;
         _loc2_ = addChild(new Label(GL.INFO,TextFormatLib.format_verdana_0xffed89_12px)) as Label;
         _loc2_.x = 97;
         _loc2_.y = 132;
         _loc2_.width = 320;
         _loc2_.wordWrap = true;
         _loc3_ = addChild(new ImgButton(UIManager.getMultiUISkin("DepositPlanModuleRechargeBTN"))) as ImgButton;
         _loc3_.x = 473;
         _loc3_.y = 74;
         _loc3_.name = "btnRecharge";
         _loc5_ = addChild(new ImgButton(UIManager.getMultiUISkin("DepositPlanModuleMyBTN"))) as ImgButton;
         _loc5_.x = 490;
         _loc5_.y = 134;
         _loc5_.name = "btnMyFund";
         this._purchased = addChild(new Label("",TextFormatLib.format_0x00FF00_12px)) as Label;
         this._purchased.x = 484;
         this._purchased.y = 204;
         _loc4_ = addChild(UIManager.getUISkin("pane_bg_light")) as UISkin;
         _loc4_.x = 7;
         _loc4_.y = 244;
         _loc4_.setSize(579,300);
         this._initMVC();
      }
      
      public function eventTime(param1:String) : void
      {
         var _loc2_:Label = null;
         if(this._eventTime == null)
         {
            _loc2_ = addChild(new Label(GL.EVENT_TIME_TITLE + ":",TextFormatLib.format_0x00A8FF_12px)) as Label;
            this._eventTime = addChild(new Label("",TextFormatLib.format_0x00A8FF_12px)) as Label;
            _loc2_.x = 30;
            this._eventTime.x = 97;
            this._eventTime.y = 204;
            _loc2_.y = 204;
         }
         this._eventTime.text = param1;
      }
      
      public function updatePurchased(param1:String) : void
      {
         this._purchased.htmlText = param1;
      }
      
      public function funds(param1:Vector.<String>, param2:Dictionary, param3:Boolean) : void
      {
         var _loc4_:FundComp = null;
         if(this._funds)
         {
            this._funds.dispose();
         }
         else
         {
            this._funds = addChild(new UISprite()) as UISprite;
         }
         var _loc6_:int = int(param1.length);
         var _loc5_:int = 0;
         while(_loc5_ < _loc6_)
         {
            _loc4_ = this._funds.addChild(new FundComp(param2[param1[_loc5_]],param3)) as FundComp;
            _loc4_.x = 18 + _loc5_ * (_loc4_.width + 10);
            _loc4_.y = 252;
            _loc5_++;
         }
      }
      
      public function kill() : void
      {
         this._facade.removeMultiCommand("MutexIsLiveFalseWindowStartUp","MutexIsLiveFalseWindowDataCommonReady");
         this._facade.removeMediator("depositplanmodule.mvc.view.DepositPlanMediator");
         this._facade.removeProxy("depositplanmodule.mvc.model.ServiceProxy");
         this._facade.removeProxy("depositplanmodule.mvc.model.DataProxy");
      }
   }
}

