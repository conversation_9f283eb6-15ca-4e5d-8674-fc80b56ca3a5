package depositplanmodule.mvc.view.components
{
   import depositplanmodule.mvc.controller.DataCommonReadyCommand;
   import depositplanmodule.mvc.controller.StartupCommand;
   import depositplanmodule.mvc.model.vo.HadFundVO;
   import depositplanmodule.mvc.view.utils.GL;
   import flash.events.Event;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.scrollPane.ScrollPane;
   import mmo.ui.control.window.PopUpWindow;
   
   public class DepositPlanReturnComp extends PopUpWindow
   {
      public static const NAME:String = "depositplanmodule.mvc.view.components.DepositPlanReturnComp";
      
      public static const BTN_RECEIVE:String = "btnReceive";
      
      public static const BTN_DETAIL:String = "btnDetail";
      
      public static const BTN_VIEW:String = "btnView";
      
      private var _facade:AppFacade;
      
      private var _sc:ScrollPane;
      
      private var _scContent:UISprite;
      
      public function DepositPlanReturnComp()
      {
         super(668,328);
         isLive = false;
      }
      
      override public function show(param1:Object) : void
      {
         this._setup();
      }
      
      override public function dispose() : void
      {
         this.kill();
         super.dispose();
      }
      
      private function _setup() : void
      {
         if(stage)
         {
            this._initStage();
         }
         else
         {
            addEventListener("addedToStage",this._initStage);
         }
      }
      
      private function _initMVC() : void
      {
         this._facade = AppFacade.instance;
         this._initController();
         this._startup();
      }
      
      private function _initController() : void
      {
         if(!this._facade.retrieveMediator("depositplanmodule.mvc.view.DepositPlanMediator"))
         {
            this._facade.registerCommand("MutexIsLiveFalseWindowStartUp",StartupCommand);
            this._facade.registerCommand("MutexIsLiveFalseWindowDataCommonReady",DataCommonReadyCommand);
         }
      }
      
      private function _startup() : void
      {
         this._facade.sendNotification("MutexIsLiveFalseWindowStartUp",this);
      }
      
      private function _initStage(param1:Event = null) : void
      {
         var _loc11_:UISprite = null;
         var _loc2_:Label = null;
         var _loc5_:Label = null;
         var _loc9_:Label = null;
         var _loc8_:Label = null;
         var _loc7_:Label = null;
         var _loc4_:Label = null;
         var _loc6_:Label = null;
         var _loc3_:Button = null;
         if(param1)
         {
            removeEventListener("addedToStage",this._initStage);
         }
         setTitleImageData(UIManager.getUISkin("DepositPlanModuleMyTitle").bitmapData,-20);
         var _loc12_:UISkin = addChild(UIManager.getUISkin("intro_small_bg")) as UISkin;
         _loc12_.x = 8;
         _loc12_.y = 37;
         _loc12_.setSize(646,259);
         var _loc10_:UISkin = addChild(UIManager.getUISkin("DepositPlanModuleTitleLine")) as UISkin;
         _loc10_.x = 13;
         _loc10_.y = 43;
         _loc11_ = addChild(new UISprite()) as UISprite;
         _loc11_.x = 13;
         _loc11_.y = 44;
         _loc2_ = _loc11_.addChild(new Label(GL.FUND_TYPE,TextFormatLib.format_0xebce82_12px)) as Label;
         _loc2_.x = 0;
         _loc2_.y = 0;
         _loc2_.width = 82;
         _loc2_.autoSize = "center";
         _loc5_ = _loc11_.addChild(new Label(GL.FUND_BUY_NUM,TextFormatLib.format_0xebce82_12px)) as Label;
         _loc5_.x = 85;
         _loc5_.y = 0;
         _loc5_.width = 70;
         _loc5_.autoSize = "center";
         _loc9_ = _loc11_.addChild(new Label(GL.FUND_RETURN_TOTAL,TextFormatLib.format_0xebce82_12px)) as Label;
         _loc9_.x = 158;
         _loc9_.y = 0;
         _loc9_.width = 75;
         _loc9_.autoSize = "center";
         _loc8_ = _loc11_.addChild(new Label(GL.FUND_RETURNED,TextFormatLib.format_0xebce82_12px)) as Label;
         _loc8_.x = 236;
         _loc8_.y = 0;
         _loc8_.width = 71;
         _loc8_.autoSize = "center";
         _loc7_ = _loc11_.addChild(new Label(GL.FUND_NEXT_RETURN,TextFormatLib.format_0xebce82_12px)) as Label;
         _loc7_.x = 310;
         _loc7_.y = 0;
         _loc7_.width = 113;
         _loc7_.autoSize = "center";
         _loc4_ = _loc11_.addChild(new Label(GL.FUND_NOW_RETURN,TextFormatLib.format_0xebce82_12px)) as Label;
         _loc4_.x = 426;
         _loc4_.y = 0;
         _loc4_.width = 74;
         _loc4_.autoSize = "center";
         _loc6_ = _loc11_.addChild(new Label(GL.FUND_ACTION,TextFormatLib.format_0xebce82_12px)) as Label;
         _loc6_.x = 504;
         _loc6_.y = 0;
         _loc6_.width = 112;
         _loc6_.autoSize = "center";
         this._sc = addChild(new ScrollPane(632,224)) as ScrollPane;
         this._sc.x = 13;
         this._sc.y = 67;
         _loc3_ = addChild(new Button(GL.BTN_VIEW,null)) as Button;
         _loc3_.x = width - _loc3_.width >> 1;
         _loc3_.y = 294;
         _loc3_.name = "btnView";
         this._initMVC();
      }
      
      public function updateContent(param1:Vector.<HadFundVO>) : void
      {
         var _loc4_:HadFundComp = null;
         var _loc2_:int = 0;
         var _loc3_:int = 0;
         if(this._scContent)
         {
            this._sc.delFromPane(this._scContent);
            this._scContent.dispose();
            this._scContent = null;
         }
         if(param1)
         {
            this._scContent = new UISprite();
            _loc2_ = int(param1.length);
            _loc3_ = 0;
            while(_loc3_ < _loc2_)
            {
               _loc4_ = new HadFundComp(param1[_loc3_]);
               _loc4_.y = _loc3_ * _loc4_.height;
               this._scContent.addChild(_loc4_);
               _loc3_++;
            }
            this._sc.addToPane(this._scContent);
         }
      }
      
      public function kill() : void
      {
         if(!PopUpCenter.containsWin("DepositPlanModule"))
         {
            this._facade.removeMultiCommand("MutexIsLiveFalseWindowStartUp","MutexIsLiveFalseWindowDataCommonReady");
            this._facade.removeProxy("depositplanmodule.mvc.model.ServiceProxy");
            this._facade.removeProxy("depositplanmodule.mvc.model.DataProxy");
         }
         this._facade.removeMediator("depositplanmodule.mvc.view.DepositPlanReturnMediator");
      }
   }
}

