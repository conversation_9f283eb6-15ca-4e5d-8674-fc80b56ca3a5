package depositplanmodule.mvc.view.components
{
   import depositplanmodule.mvc.model.DataProxy;
   import depositplanmodule.mvc.model.vo.FundVO;
   import depositplanmodule.mvc.view.utils.GL;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ButtonModel;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import util.ChineseString;
   
   public class DetailComp extends PopUpWindow
   {
      public static const NAME:String = "depositplanmodule.mvc.view.components.DetailComp";
      
      public static const BTN_RECHARGE:String = "btnRecharge";
      
      public static const BTN_BUY:String = "btnBuy";
      
      public static const BTN_CONFIRM:String = "btnConfirm";
      
      private var _fundName:Label;
      
      private var _price:Label;
      
      private var _priceGold:UISkin;
      
      private var _finallyReturn:Label;
      
      private var _finallyReturnGold:UISkin;
      
      private var _income:Label;
      
      private var _vipLevel:Label;
      
      private var _returnCycle:Label;
      
      private var _data:FundVO;
      
      public function DetailComp(param1:FundVO, param2:Boolean)
      {
         var _loc11_:Label = null;
         var _loc3_:Label = null;
         var _loc4_:Label = null;
         var _loc9_:Label = null;
         var _loc7_:Label = null;
         var _loc8_:Button = null;
         var _loc5_:Button = null;
         var _loc6_:Button = null;
         super(240,300);
         this._data = param1;
         isLive = false;
         title = GL.DETAIL;
         var _loc10_:UISkin = addChild(UIManager.getUISkin("pane_bg_light")) as UISkin;
         _loc10_.x = 7;
         _loc10_.y = 30;
         _loc10_.setSize(224,215);
         this._fundName = addChild(new Label(this._data.name,TextFormatLib.format_0xffed89_14px)) as Label;
         this._fundName.x = 24;
         this._fundName.y = 54;
         _loc11_ = addChild(new Label(GL.PRICE + ":",TextFormatLib.format_0xffb932_12px)) as Label;
         _loc11_.x = 24;
         this._price = addChild(new Label(String(this._data.gold),TextFormatLib.format_verdana_0xFFF600_12px)) as Label;
         this._price.x = _loc11_.x + _loc11_.textWidth + 6;
         this._priceGold = addChild(UIManager.getUISkin("gold")) as UISkin;
         this._priceGold.x = this._price.x + this._price.textWidth + 6;
         _loc11_.y = this._price.y = this._priceGold.y = 88;
         _loc3_ = addChild(new Label(GL.FINALLY_RETURN + ":",TextFormatLib.format_0xffb932_12px)) as Label;
         _loc3_.x = 24;
         this._finallyReturn = addChild(new Label(String(this._data.returnAllGold),TextFormatLib.format_verdana_0xFFF600_12px)) as Label;
         this._finallyReturn.x = _loc3_.x + _loc3_.textWidth + 6;
         this._finallyReturnGold = addChild(UIManager.getUISkin("gold")) as UISkin;
         this._finallyReturnGold.x = this._finallyReturn.x + this._finallyReturn.textWidth + 6;
         _loc3_.y = this._finallyReturn.y = this._finallyReturnGold.y = 112;
         _loc4_ = addChild(new Label(GL.INCOME + ":",TextFormatLib.format_0xffb932_12px)) as Label;
         _loc4_.x = 26;
         this._income = addChild(new Label(this._data.income + "%",TextFormatLib.format_verdana_0xffed89_12px)) as Label;
         this._income.x = _loc4_.x + _loc4_.textWidth + 6;
         _loc4_.y = this._income.y = 137;
         _loc9_ = addChild(new Label(GL.VIP_LEVEL + ":",TextFormatLib.format_0xffb932_12px)) as Label;
         _loc9_.x = 24;
         this._vipLevel = addChild(new Label(this._data.vipInfo,TextFormatLib.format_verdana_0xffed89_12px)) as Label;
         this._vipLevel.x = _loc9_.x + _loc9_.textWidth + 6;
         _loc9_.y = this._vipLevel.y = 161;
         _loc7_ = addChild(new Label(GL.RETURN_CYCLE + ":",TextFormatLib.format_0xffb932_12px)) as Label;
         _loc7_.x = 24;
         this._returnCycle = addChild(new Label(this._data.info,TextFormatLib.format_verdana_0xffed89_12px)) as Label;
         this._returnCycle.x = _loc7_.x + _loc7_.textWidth + 6;
         _loc7_.y = this._returnCycle.y = 185;
         this._returnCycle.width = 135;
         this._returnCycle.wordWrap = true;
         _loc10_.setSize(224,this._returnCycle.y + this._returnCycle.textHeight);
         setSize(240,_loc10_.y + _loc10_.height + 40);
         if(param2)
         {
            _loc8_ = addChild(new Button(GL.BUY,TextFormatLib.format_0xFFB932_14px,70,UIManager.getMultiUISkin("button_big"))) as Button;
            _loc5_ = addChild(new Button(GL.RECHARGE,TextFormatLib.format_0xFFB932_14px,70,UIManager.getMultiUISkin("battleHelp_Btn_change"))) as Button;
            _loc8_.x = 40;
            _loc8_.y = height - _loc8_.height - 7;
            _loc8_.name = "btnBuy";
            _loc5_.x = 126;
            _loc5_.y = height - _loc5_.height - 7;
            _loc5_.name = "btnRecharge";
         }
         else
         {
            _loc6_ = addChild(new Button(GL.CONFIRM,null,80)) as Button;
            _loc6_.x = 75;
            _loc6_.y = height - _loc6_.height - 8;
            _loc6_.name = "btnConfirm";
         }
         addEventListener("click",this.onClickHandler);
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
         var _loc4_:ButtonModel = null;
         var _loc2_:String = null;
         var _loc3_:DataProxy = null;
         if(param1.target is ButtonModel)
         {
            _loc4_ = param1.target as ButtonModel;
            _loc3_ = AppFacade.instance.retrieveProxy("depositplanmodule.mvc.model.DataProxy") as DataProxy;
            switch(_loc4_.name)
            {
               case "btnBuy":
                  _loc2_ = _loc3_.openSendCheck(this._data);
                  if(_loc2_)
                  {
                     AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                        "text":_loc2_,
                        "textFormat":TextFormatLib.format_0x00FF00_14px
                     });
                  }
                  else
                  {
                     PopUpCenter.addPopUp("depositplanmodule.mvc.view.components.SendComp",new SendComp(this._data,_loc3_.dataVO.max - _loc3_.dataVO.purchased,ChineseString.numFloorToString(MainData.getInstance().userData.gold_num,100000)),true,true);
                  }
                  close();
                  break;
               case "btnRecharge":
                  AppFacade.instance.sendNotification("chargeGold");
                  break;
               case "btnConfirm":
                  close();
            }
         }
      }
   }
}

