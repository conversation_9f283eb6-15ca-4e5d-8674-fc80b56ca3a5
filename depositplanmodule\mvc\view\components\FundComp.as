package depositplanmodule.mvc.view.components
{
   import depositplanmodule.mvc.model.vo.FundVO;
   import depositplanmodule.mvc.view.utils.GL;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mx.utils.StringUtil;
   
   public class FundComp extends UISprite
   {
      private var _data:FundVO;
      
      public function FundComp(param1:FundVO, param2:Boolean)
      {
         var _loc7_:Label = null;
         var _loc6_:Button = null;
         var _loc4_:Button = null;
         var _loc10_:UISkin = null;
         var _loc3_:UISkin = null;
         super();
         this._data = param1;
         addChild(UIManager.getUISkin("DepositPlanModuleFund" + this._data.id)) as UISkin;
         var _loc9_:UISprite = addChild(new UISprite()) as UISprite;
         _loc9_.x = 42;
         _loc9_.y = 40;
         var _loc5_:int = this._data.income.length;
         var _loc8_:int = 0;
         while(_loc8_ < _loc5_)
         {
            _loc10_ = _loc9_.addChild(UIManager.getUISkin("DepositPlanModuleNum" + this._data.income.charAt(_loc8_))) as UISkin;
            if(_loc8_)
            {
               _loc3_ = _loc9_.getChildAt(_loc8_ - 1) as UISkin;
               _loc10_.x = _loc3_.x + _loc3_.width - (_loc3_.width >> 1) + 2;
            }
            _loc8_++;
         }
         _loc9_.x = _loc9_.x - _loc9_.width + 10;
         _loc7_ = addChild(new Label("",TextFormatLib.format_0xFFB932_12px)) as Label;
         _loc7_.x = 0;
         _loc7_.y = 209;
         _loc7_.htmlText = StringUtil.substitute(GL.FUND_INFO,this._data.gold,this._data.returnGold[this._data.returnGold.length - 1][0],this._data.returnAllGold);
         _loc7_.x = (this.width - _loc7_.textWidth >> 1) + 4;
         _loc6_ = addChild(new Button(GL.BUY,null,60)) as Button;
         _loc6_.x = 26;
         _loc6_.name = "btnBuy";
         _loc6_.enabled = param2;
         _loc4_ = addChild(new Button(GL.DETAIL,null,60)) as Button;
         _loc4_.x = 100;
         _loc6_.y = 248;
         _loc4_.y = 248;
         _loc4_.name = "btnDetail";
      }
      
      public function get data() : FundVO
      {
         return this._data;
      }
   }
}

