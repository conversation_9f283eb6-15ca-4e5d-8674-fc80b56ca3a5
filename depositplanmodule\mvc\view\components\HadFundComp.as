package depositplanmodule.mvc.view.components
{
   import depositplanmodule.mvc.model.vo.HadFundVO;
   import depositplanmodule.mvc.view.utils.GL;
   import game.manager.UIManager;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   
   public class HadFundComp extends UISprite
   {
      private var _data:HadFundVO;
      
      public function HadFundComp(param1:HadFundVO)
      {
         var _loc9_:Label = null;
         var _loc2_:Label = null;
         var _loc4_:Label = null;
         var _loc7_:Label = null;
         var _loc6_:Label = null;
         var _loc5_:Button = null;
         var _loc3_:Button = null;
         super();
         this._data = param1;
         addChild(UIManager.getUISkin("DepositPlanModuleSCLine")) as UISkin;
         var _loc10_:UISprite = addChild(new UISprite()) as UISprite;
         _loc10_.x = 0;
         _loc10_.y = 2;
         var _loc8_:Label = _loc10_.addChild(new Label(this._data.fund.name,TextFormatLib.format_0xFFB932_12px)) as Label;
         _loc8_.x = 0;
         _loc8_.y = 0;
         _loc8_.width = 82;
         _loc8_.autoSize = "center";
         _loc9_ = _loc10_.addChild(new Label(this._data.num + GL.UNIT,TextFormatLib.format_0xFFB932_12px)) as Label;
         _loc9_.x = 85;
         _loc9_.y = 0;
         _loc9_.width = 70;
         _loc9_.autoSize = "center";
         _loc2_ = _loc10_.addChild(new Label(String(this._data.fund.returnAllGold * this._data.num),TextFormatLib.format_0xFFF600_12px)) as Label;
         _loc2_.x = 158;
         _loc2_.y = 0;
         _loc2_.width = 75;
         _loc2_.autoSize = "center";
         _loc4_ = _loc10_.addChild(new Label(String(this._data.returnedGold),TextFormatLib.format_0xFFF600_12px)) as Label;
         _loc4_.x = 236;
         _loc4_.y = 0;
         _loc4_.width = 71;
         _loc4_.autoSize = "center";
         _loc7_ = _loc10_.addChild(new Label("",TextFormatLib.format_0xFFF600_12px)) as Label;
         _loc7_.x = 310;
         _loc7_.y = 0;
         _loc7_.htmlText = this._data.nextCanReturnInfo;
         _loc7_.width = 113;
         _loc7_.autoSize = "center";
         _loc6_ = _loc10_.addChild(new Label(String(this._data.nowCanReturn),TextFormatLib.format_0xFFF600_12px)) as Label;
         _loc6_.x = 426;
         _loc6_.y = 0;
         _loc6_.width = 74;
         _loc6_.autoSize = "center";
         _loc5_ = _loc10_.addChild(new Button(GL.RECEIVE,TextFormatLib.format_0xFFB932_12px,48,UIManager.getMultiUISkin("button3"))) as Button;
         _loc5_.x = 508;
         _loc5_.y = -1;
         _loc5_.setTextOffset(0,-2);
         _loc5_.name = "btnReceive";
         if(this._data.nowCanReturn == 0)
         {
            _loc5_.enabled = false;
         }
         _loc3_ = _loc10_.addChild(new Button(GL.DETAIL,TextFormatLib.format_0xFFB932_12px,48,UIManager.getMultiUISkin("button3"))) as Button;
         _loc3_.x = 562;
         _loc3_.y = -1;
         _loc3_.setTextOffset(0,-2);
         _loc3_.name = "btnDetail";
      }
      
      public function get data() : HadFundVO
      {
         return this._data;
      }
   }
}

