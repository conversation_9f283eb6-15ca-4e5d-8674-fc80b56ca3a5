package depositplanmodule.mvc.view.components
{
   import depositplanmodule.mvc.model.ServiceProxy;
   import depositplanmodule.mvc.model.vo.HadFundVO;
   import depositplanmodule.mvc.view.utils.GL;
   import flash.events.MouseEvent;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.PopUpWindow;
   import mx.utils.StringUtil;
   
   public class ReturnGoldComp extends PopUpWindow
   {
      public static const NAME:String = "depositplanmodule.mvc.view.components.ReturnGoldComp";
      
      public static const BTN_CONFIRM:String = "btnConfirm";
      
      public static const BTN_CANCEL:String = "btnCancel";
      
      private var _addH:Number = 0;
      
      private var _data:HadFundVO;
      
      public function ReturnGoldComp(param1:HadFundVO)
      {
         var _loc8_:Label = null;
         var _loc10_:UISkin = null;
         var _loc4_:Label = null;
         var _loc6_:Button = null;
         var _loc2_:Button = null;
         var _loc9_:int = 0;
         var _loc11_:UISprite = null;
         var _loc14_:Label = null;
         var _loc12_:UISkin = null;
         var _loc13_:Label = null;
         var _loc3_:Label = null;
         this._data = param1;
         _loc9_ = int(this._data.canReturns.length);
         if(_loc9_ > 2)
         {
            this._addH = (_loc9_ - 2) * 32;
         }
         super(336,246 + this._addH);
         isLive = false;
         setTitleImageData(UIManager.getUISkin("TitlePrompt").bitmapData,-10);
         var _loc7_:UISkin = addChild(UIManager.getUISkin("pane_bg_light")) as UISkin;
         _loc7_.x = 7;
         _loc7_.y = 30;
         _loc7_.setSize(322,177 + this._addH);
         _loc8_ = addChild(new Label("",TextFormatLib.format_0xFFD800_14)) as Label;
         _loc8_.x = 14;
         _loc8_.y = 48;
         _loc8_.width = 306;
         _loc8_.autoSize = "center";
         _loc8_.htmlText = StringUtil.substitute(GL.YOU_BUY_FUND,"<font color=\'#FFFFFF\'>" + this._data.fund.name + "</font>");
         _loc10_ = addChild(UIManager.getUISkin("line2")) as UISkin;
         _loc10_.setSize(306,2);
         _loc10_.x = 14;
         _loc10_.y = 75;
         var _loc5_:int = 0;
         while(_loc5_ < _loc9_)
         {
            _loc11_ = addChild(new UISprite()) as UISprite;
            _loc14_ = _loc11_.addChild(new Label(StringUtil.substitute(GL.PHASE_RETURN,this._data.canReturns[_loc5_] + 1),TextFormatLib.format_0xffed89_14px)) as Label;
            _loc12_ = _loc11_.addChild(UIManager.getUISkin("text_bg_11")) as UISkin;
            _loc12_.x = _loc14_.textWidth + 6;
            _loc12_.y = -1;
            _loc12_.setSize(82,22);
            _loc13_ = _loc11_.addChild(new Label(String(this._data.fund.returnGold[this._data.canReturns[_loc5_]][1] * this._data.num),TextFormatLib.format_0xFFF600_14px)) as Label;
            _loc13_.x = _loc12_.x;
            _loc13_.y = 0;
            _loc13_.width = _loc12_.width;
            _loc13_.autoSize = "center";
            _loc3_ = _loc11_.addChild(new Label(GL.GOLD,TextFormatLib.format_0xffed89_14px)) as Label;
            _loc3_.x = _loc12_.x + _loc12_.width + 4;
            _loc11_.x = 336 - _loc11_.width >> 1;
            _loc11_.y = 95 + _loc5_ * 32;
            _loc5_++;
         }
         _loc4_ = addChild(new Label("",TextFormatLib.format_0xFFB932_14px)) as Label;
         _loc4_.htmlText = StringUtil.substitute(GL.CONFIRM_RECEIVE,"<font color=\'#FFFFFF\'>" + this._data.nowCanReturn + "</font>");
         _loc4_.x = 336 - _loc4_.width >> 1;
         _loc4_.y = 160 + this._addH;
         _loc6_ = addChild(new Button(GL.CONFIRM,null,80)) as Button;
         _loc6_.x = 78;
         _loc6_.y = 208 + this._addH;
         _loc6_.name = "btnConfirm";
         _loc2_ = addChild(new Button(GL.CANCEL,null,80)) as Button;
         _loc2_.x = 192;
         _loc2_.y = 208 + this._addH;
         _loc2_.name = "btnCancel";
         addEventListener("click",this.onClickHandler);
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
         var _loc3_:Button = null;
         var _loc2_:ServiceProxy = null;
         if(param1.target is Button)
         {
            _loc3_ = param1.target as Button;
            switch(_loc3_.name)
            {
               case "btnConfirm":
                  _loc2_ = AppFacade.instance.retrieveProxy("depositplanmodule.mvc.model.ServiceProxy") as ServiceProxy;
                  _loc2_.returnGold(this._data.orderID,this._data.nowCanReturn);
                  close();
                  break;
               case "btnCancel":
                  close();
            }
         }
      }
   }
}

