package depositplanmodule.mvc.view.components
{
   import depositplanmodule.mvc.model.DataProxy;
   import depositplanmodule.mvc.model.ServiceProxy;
   import depositplanmodule.mvc.model.vo.FundVO;
   import depositplanmodule.mvc.view.utils.GL;
   import flash.display.Sprite;
   import flash.events.Event;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.data.groupwar.GroupWarData;
   import game.manager.UIManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.button.ButtonModel;
   import mmo.ui.control.button.ImgButton;
   import mmo.ui.control.button.SwitchButton;
   import mmo.ui.control.input.TextInput;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.window.ConfirmSwitchShowWindow;
   import mmo.ui.control.window.PopUpWindow;
   import mmo.ui.event.ButtonEvent;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class SendComp extends PopUpWindow
   {
      public static const NAME:String = "depositplanmodule.mvc.view.components.SendComp";
      
      private var _goodName:Label;
      
      private var _userAllGold:Label;
      
      private var _canBuy:Label;
      
      private var _numInput:TextInput;
      
      private var _reduceBTN:ImgButton;
      
      private var _addBTN:ImgButton;
      
      private var _costLableValue:Label;
      
      private var _okBTN:Button;
      
      private var _cancelBTN:Button;
      
      private var _max:int;
      
      private var _data:FundVO;
      
      public function SendComp(param1:FundVO, param2:int, param3:String)
      {
         var _loc8_:UISkin = null;
         var _loc4_:UISkin = null;
         var _loc5_:Label = null;
         var _loc7_:UISkin = null;
         var _loc6_:Label = null;
         super(280,265);
         this._data = param1;
         this._max = param2;
         isLive = false;
         title = GL.BUY_FUND;
         _loc8_ = pane.addChild(UIManager.getUISkin("group_bg")) as UISkin;
         _loc8_.x = 6;
         _loc8_.setSize(258,180);
         this._goodName = pane.addChild(new Label("[" + param1.name + "]",TextFormatLib.format_0x00FF00_14px)) as Label;
         this._goodName.x = 0;
         this._goodName.y = 12;
         this._goodName.width = 280;
         this._goodName.autoSize = "center";
         this._userAllGold = pane.addChild(new Label("",TextFormatLib.format_verdana_0xffed89_12px)) as Label;
         this._userAllGold.x = 0;
         this._userAllGold.y = 34;
         this._userAllGold.htmlText = GL.NOW_GOLD + ":" + param3;
         this._userAllGold.width = 280;
         this._userAllGold.autoSize = "center";
         _loc4_ = pane.addChild(UIManager.getUISkin("text_bg_2")) as UISkin;
         _loc4_.x = 11;
         _loc4_.y = 61;
         _loc4_.setSize(245,108);
         this._canBuy = pane.addChild(new Label("",TextFormatLib.format_0x00FF00_14px)) as Label;
         this._canBuy.x = 40;
         this._canBuy.y = 68;
         this._canBuy.htmlText = StringUtil.substitute(GL.CAN_BUY_NUM,this._max);
         _loc5_ = pane.addChild(new Label(GL.INPUT_BUY_NUM,TextFormatLib.format_0xFFFFFF_14px)) as Label;
         _loc5_.x = 40;
         _loc5_.y = 88;
         this._numInput = pane.addChild(new TextInput("",100)) as TextInput;
         this._numInput.x = 40;
         this._numInput.y = 114;
         this._numInput.restrict = "0-9";
         this._numInput.addEventListener("change",this.onChangeText);
         this._reduceBTN = pane.addChild(new ImgButton(UIManager.getMultiUISkin("minus"))) as ImgButton;
         this._reduceBTN.x = 150;
         this._reduceBTN.y = 114;
         this._addBTN = pane.addChild(new ImgButton(UIManager.getMultiUISkin("plus"))) as ImgButton;
         this._addBTN.x = 180;
         this._addBTN.y = 114;
         _loc7_ = pane.addChild(UIManager.getUISkin("key_value_bg")) as UISkin;
         _loc7_.width = 165;
         _loc7_.x = 40;
         _loc7_.y = 142;
         _loc6_ = pane.addChild(new Label(GL.SPEN_GOLD,TextFormatLib.format_0xffb932_12px)) as Label;
         _loc6_.x = 40;
         _loc6_.y = 142;
         _loc6_.width = 66;
         _loc6_.autoSize = "center";
         this._costLableValue = pane.addChild(new Label("",TextFormatLib.format_verdana_0xffed89_12px)) as Label;
         this._costLableValue.x = 110;
         this._costLableValue.y = 142;
         this._costLableValue.width = 100;
         this._costLableValue.autoSize = "center";
         this._okBTN = pane.addChild(new Button(Globalization.queding,null,78)) as Button;
         this._okBTN.x = 48;
         this._okBTN.y = 190;
         this._cancelBTN = pane.addChild(new Button(Globalization.quxiao,null,78)) as Button;
         this._cancelBTN.x = 156;
         this._cancelBTN.y = 190;
         this.updateCost();
         pane.addEventListener("click",this.onClickHandler);
      }
      
      private function onChangeText(param1:Event) : void
      {
         this.updateCost(int(this._numInput.text));
      }
      
      private function onClickHandler(param1:MouseEvent) : void
      {
         var _loc2_:ButtonModel = null;
         if(param1.target is ButtonModel)
         {
            _loc2_ = param1.target as ButtonModel;
            switch(_loc2_)
            {
               case this._addBTN:
                  this.updateCost(int(this._numInput.text) + 1);
                  break;
               case this._reduceBTN:
                  this.updateCost(int(this._numInput.text) - 1);
                  break;
               case this._okBTN:
                  this._confirm();
                  break;
               case this._cancelBTN:
                  close();
            }
         }
      }
      
      private function _confirm() : void
      {
         var serviceProxy:ServiceProxy = null;
         var num:int = 0;
         var gold:Number = NaN;
         var dataProxy:DataProxy = AppFacade.instance.retrieveProxy("depositplanmodule.mvc.model.DataProxy") as DataProxy;
         serviceProxy = AppFacade.instance.retrieveProxy("depositplanmodule.mvc.model.ServiceProxy") as ServiceProxy;
         num = int(this._numInput.text);
         gold = Number(this._costLableValue.text);
         var tip:String = dataProxy.sendCheck(gold);
         if(tip)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":tip,
               "textFormat":TextFormatLib.format_0x00FF00_14px
            });
            close();
         }
         else
         {
            this._confirmSwitchPOP(function():void
            {
               serviceProxy.buy(_data.id,num,gold);
               close();
            },this,"depositplanmodule.mvc.view.components.SendComp",StringUtil.substitute(GL.CONFIRM_BUY,gold,num,this._data.name),42);
         }
      }
      
      private function _confirmSwitchPOP(param1:Function, param2:Sprite, param3:String, param4:String, param5:int = 0) : void
      {
         var pop:ConfirmSwitchShowWindow = null;
         var dt:Array = null;
         var callback:Function = param1;
         var par:Sprite = param2;
         var unique:String = param3;
         var content:String = param4;
         var node:int = param5;
         if(node && MainData.getInstance().closeGoldNoticeData.isCloseGoldNotice(node) || GroupWarData.getNotConfirm(unique))
         {
            callback();
         }
         else
         {
            pop = PopUpCenter.getWinByName(par.name + "confirmSwitchShowWindow" + unique) as ConfirmSwitchShowWindow;
            if(node && pop)
            {
               pop.cbAlertAgain.isCheck = MainData.getInstance().closeGoldNoticeData.isCloseGoldNotice(node);
            }
            pop = PopUpCenter.confirmSwitchShowWin(par,unique,content + "\r\n",callback,null,0,true);
            if(node)
            {
               (pop.cbAlertAgain as SwitchButton).addEventListener(ButtonEvent.Button_Update,function():void
               {
                  dt = MainData.getInstance().closeGoldNoticeData.setCloseGoldNotic(node,(pop.cbAlertAgain as SwitchButton).isCheck);
                  AppFacade.instance.sendNotification("CS_SET_COSTNOTICE",dt);
               });
            }
            else
            {
               (pop.cbAlertAgain as SwitchButton).addEventListener(ButtonEvent.Button_Update,function():void
               {
                  if(!pop.isAlertAgain)
                  {
                     GroupWarData.notConfirm[GroupWarData.notConfirm.length] = unique;
                  }
               });
               if(!pop.isAlertAgain)
               {
                  callback();
               }
            }
         }
      }
      
      private function updateCost(param1:int = 1) : void
      {
         if(param1 <= 0)
         {
            this._numInput.text = "1";
         }
         else if(param1 >= this._max)
         {
            this._numInput.text = String(this._max);
         }
         else
         {
            this._numInput.text = String(param1);
         }
         var _loc2_:int = int(this._numInput.text);
         if(this._max == 1)
         {
            this._addBTN.enabled = false;
            this._reduceBTN.enabled = false;
         }
         else
         {
            if(_loc2_ < this._max)
            {
               this._addBTN.enabled = true;
            }
            else
            {
               this._addBTN.enabled = false;
            }
            if(_loc2_ > 1)
            {
               this._reduceBTN.enabled = true;
            }
            else
            {
               this._reduceBTN.enabled = false;
            }
         }
         this._costLableValue.text = String(_loc2_ * int(this._data.gold));
      }
   }
}

