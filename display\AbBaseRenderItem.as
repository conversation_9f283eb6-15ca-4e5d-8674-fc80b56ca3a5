package display
{
   import flash.display.Bitmap;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import mmo.ui.control.UISprite;
   
   public class AbBaseRenderItem extends UISprite implements IRenderItem
   {
      public function AbBaseRenderItem()
      {
         super();
      }
      
      public function getRec() : Rectangle
      {
         return null;
      }
      
      public function resizeAnimation(param1:uint, param2:uint) : void
      {
      }
      
      public function getMCCurrentFrameBitmap() : Bitmap
      {
         return null;
      }
      
      public function resetAnimation() : void
      {
      }
      
      public function get currentFrame() : uint
      {
         return 0;
      }
      
      public function get totalFrames() : uint
      {
         return 0;
      }
      
      public function get currentBitmapPositon() : Point
      {
         return null;
      }
      
      public function get currentBitmapWidth() : uint
      {
         return 0;
      }
      
      public function get currentBitmapHeight() : uint
      {
         return 0;
      }
      
      public function get keyFrameNumber() : uint
      {
         return 0;
      }
      
      public function get EVENT_PRE_END() : String
      {
         return null;
      }
      
      public function get EVENT_END() : String
      {
         return null;
      }
      
      public function get loop() : Boolean
      {
         return false;
      }
      
      public function set loop(param1:Boolean) : void
      {
      }
      
      public function set reverse(param1:Boolean) : void
      {
      }
      
      public function get speed() : String
      {
         return null;
      }
      
      public function get flop() : Boolean
      {
         return false;
      }
      
      public function set flop(param1:Boolean) : void
      {
      }
      
      public function upDate() : void
      {
      }
   }
}

