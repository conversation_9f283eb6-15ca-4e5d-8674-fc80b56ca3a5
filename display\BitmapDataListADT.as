package display
{
   import flash.display.BitmapData;
   import flash.geom.Point;
   
   public final class BitmapDataListADT
   {
      private var _list:Vector.<BitmapData>;
      
      private var _fixPointList:Vector.<Point>;
      
      private var _currentFrame:uint;
      
      private var _totalFrames:uint;
      
      private var _finished:Boolean;
      
      private var _reverse:<PERSON><PERSON>an;
      
      private var _preEndList:Array;
      
      private var _soundNameList:Array;
      
      private var _loop:Boolean = true;
      
      public var speed:String;
      
      public function BitmapDataListADT(param1:Vector.<BitmapData> = null)
      {
         super();
         if(param1)
         {
            this._list = param1;
            this.reset();
         }
         else
         {
            this._list = new Vector.<BitmapData>();
         }
         this._fixPointList = new Vector.<Point>();
      }
      
      public function get preEndList() : Array
      {
         return this._preEndList;
      }
      
      public function set preEndList(param1:Array) : void
      {
         this._preEndList = param1;
      }
      
      public function isKeyFrame(param1:uint) : Boolean
      {
         if(this._preEndList && this._preEndList.length > 0)
         {
            if(this._preEndList.indexOf(param1) >= 0)
            {
               return true;
            }
         }
         return false;
      }
      
      public function getCurrentBitmapData() : BitmapData
      {
         return this._list[this._currentFrame];
      }
      
      public function getNextBitmapData() : BitmapData
      {
         var _loc1_:BitmapData = null;
         if(this._currentFrame < this._totalFrames)
         {
            _loc1_ = this._list[this._currentFrame];
         }
         else if(this._loop)
         {
            this.reset();
            _loc1_ = this._list[0];
         }
         else
         {
            _loc1_ = this._list[this._list.length - 1];
            this._finished = true;
         }
         !this._finished && this._currentFrame++;
         return _loc1_;
      }
      
      public function getFixPoint() : Point
      {
         if(this._currentFrame - 1 > 0)
         {
            return this._fixPointList[this._currentFrame - 1];
         }
         return this._fixPointList[0];
      }
      
      public function reset() : void
      {
         this._currentFrame = 0;
         this._totalFrames = this._list.length;
         this._finished = false;
         if(this._reverse && this._list)
         {
            this._list.reverse();
         }
      }
      
      public function dispose() : void
      {
         var _loc2_:uint = 0;
         var _loc1_:uint = 0;
         if(this._list)
         {
            _loc2_ = this._list.length;
            _loc1_ = 0;
            while(_loc1_ < _loc2_)
            {
               try
               {
                  this._list[_loc1_].dispose();
               }
               catch(e:Error)
               {
               }
               _loc1_++;
            }
            this._list.length = 0;
         }
         this._fixPointList && (this._fixPointList.length = 0);
         this._preEndList && (this._preEndList.length = 0);
      }
      
      public function pushOneBitmap(param1:BitmapData, param2:Point) : void
      {
         this._list.push(param1);
         this._fixPointList.push(param2);
         this._totalFrames = this._list.length;
      }
      
      public function copyLast() : void
      {
         if(this._list && this._list.length > 0)
         {
            this._list.push(this._list[this._list.length - 1]);
            this._fixPointList.push(this._fixPointList[this._fixPointList.length - 1]);
            this._totalFrames = this._list.length;
            return;
         }
         throw new Error("::bug id : 2012-7-2 14:22:12");
      }
      
      public function set list(param1:Vector.<BitmapData>) : void
      {
         this._list = param1;
         this.reset();
      }
      
      public function get list() : Vector.<BitmapData>
      {
         return this._list;
      }
      
      public function get fixPointList() : Vector.<Point>
      {
         return this._fixPointList;
      }
      
      public function set fixPointList(param1:Vector.<Point>) : void
      {
         this._fixPointList = param1;
      }
      
      public function clone() : BitmapDataListADT
      {
         var _loc1_:BitmapDataListADT = new BitmapDataListADT();
         _loc1_.list = this._list;
         _loc1_.fixPointList = this._fixPointList;
         _loc1_.preEndList = this.preEndList;
         _loc1_.soundNameList = this.soundNameList;
         _loc1_.speed = this.speed;
         return _loc1_;
      }
      
      public function get loop() : Boolean
      {
         return this._loop;
      }
      
      public function set loop(param1:Boolean) : void
      {
         this._loop = param1;
      }
      
      public function get finished() : Boolean
      {
         return this._finished;
      }
      
      public function get currentFrame() : uint
      {
         return this._currentFrame;
      }
      
      public function get totalFrames() : uint
      {
         return this._totalFrames;
      }
      
      public function get reverse() : Boolean
      {
         return this._reverse;
      }
      
      public function set reverse(param1:Boolean) : void
      {
         this._reverse = param1;
      }
      
      public function get soundNameList() : Array
      {
         return this._soundNameList;
      }
      
      public function set soundNameList(param1:Array) : void
      {
         this._soundNameList = param1;
      }
   }
}

