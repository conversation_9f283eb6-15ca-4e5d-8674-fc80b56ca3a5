package display
{
   import game.data.skill.SkillAndBuffXMLManager;
   
   public final class Buff extends BattleMovieClip
   {
      private var _id:uint = 0;
      
      private var _count:int = 0;
      
      public function Buff(param1:uint)
      {
         this._id = param1;
         this._animationName = SkillAndBuffXMLManager.indexBuffClassName(this._id);
         super(_animationName);
         this.loop = true;
      }
      
      public function get id() : uint
      {
         return this._id;
      }
      
      public function get count() : int
      {
         return this._count;
      }
      
      public function set count(param1:int) : void
      {
         this._count = param1;
      }
      
      public function lowCount() : void
      {
         this._count--;
      }
      
      public function upCount() : void
      {
         this._count++;
      }
      
      override public function dispose() : void
      {
         super.dispose();
         this._count = 0;
      }
   }
}

