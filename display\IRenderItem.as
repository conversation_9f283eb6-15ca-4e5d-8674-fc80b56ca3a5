package display
{
   import flash.display.Bitmap;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   
   public interface IRenderItem extends IRenderAble
   {
      function getRec() : Rectangle;
      
      function resizeAnimation(param1:uint, param2:uint) : void;
      
      function getMCCurrentFrameBitmap() : Bitmap;
      
      function resetAnimation() : void;
      
      function get currentFrame() : uint;
      
      function get totalFrames() : uint;
      
      function get currentBitmapPositon() : Point;
      
      function get currentBitmapWidth() : uint;
      
      function get currentBitmapHeight() : uint;
      
      function get keyFrameNumber() : uint;
      
      function get EVENT_PRE_END() : String;
      
      function get EVENT_END() : String;
      
      function get loop() : Boolean;
      
      function set loop(param1:Boolean) : void;
      
      function set reverse(param1:Boolean) : void;
      
      function get speed() : String;
      
      function get flop() : Boolean;
      
      function set flop(param1:Boolean) : void;
   }
}

