package display
{
   import application.utils.BattleBloodBar;
   import controler.EventBrige;
   import customEvent.BuffDebugEvent;
   import display.grid.Parallelogram;
   import flash.display.Bitmap;
   import flash.display.BitmapData;
   import flash.filters.GlowFilter;
   import flash.geom.Point;
   import flash.text.TextFormat;
   import flash.utils.getDefinitionByName;
   import game.clientTrace;
   import game.data.MainData;
   import game.data.group.HeroDataUtil;
   import game.xmlParsers.battle.BattleConfigerXMLManager;
   import mmo.Config;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.progressBar.ProgressBar;
   import sourceManager.SourceManager;
   import util.DisplayHelper;
   import util.Globalization;
   
   public class PlayerRole extends UISprite implements IPlayerRole, IRenderAble
   {
      public static var showBloodBar:Boolean = true;
      
      public static const BUFF_Y_FIX:int = -40;
      
      private var _id:uint;
      
      private var _nameTxt:Label;
      
      private var _roleName:String = " ";
      
      private var _heartPoint:Point;
      
      private var _feetPoint:Point;
      
      private var _headPoint:Point;
      
      private var _buffList:Vector.<Buff>;
      
      private var _position:Parallelogram;
      
      private var _currentPosition:Point;
      
      private var _roleType:String;
      
      private var _direction:uint = 0;
      
      private var _standAnimation:AbBaseRenderItem;
      
      private var _reverse:Boolean;
      
      private var _bloodBar:ProgressBar;
      
      private var _strongBloodBar:BattleBloodBar;
      
      private var _rolePaticalaerBitmap:Bitmap;
      
      private var _attackRomotePoint:Point;
      
      private var _rageSkillRomotePoint:Point;
      
      private var _nameColor:uint;
      
      public var curHeroHtid:int;
      
      public var rageAnimation:AbBaseRenderItem;
      
      public var roleManlyAnimation:AbBaseRenderItem;
      
      public var roleManlyBottomAnimation:AbBaseRenderItem;
      
      public var delBuffIdArr:Array = [];
      
      public function PlayerRole(param1:uint, param2:String, param3:Parallelogram, param4:String, param5:uint, param6:int = 0)
      {
         super();
         this._roleType = param4;
         this.curHeroHtid = param6;
         var _loc11_:String = SourceManager.instance.getRoleActionName(param2,this._roleType);
         this._direction = SourceManager.instance.getRoleDirection(this._roleType);
         this._buffList = new Vector.<Buff>();
         this._standAnimation = new BattleMovieClip(_loc11_,true);
         this.addChild(this._standAnimation);
         (this._standAnimation as BattleMovieClip).dispatchEvt = false;
         this._standAnimation.loop = true;
         this._id = param1;
         this._position = param3;
         this.x = param3.centerPoint.x;
         this.y = param3.centerPoint.y;
         this.currentPosition = new Point(this.x,this.y);
         this._heartPoint = BattleConfigerXMLManager.indexPlayerHeartPoint(this._roleType);
         this._headPoint = BattleConfigerXMLManager.indexPlayerHeadPoint(this._roleType);
         this._attackRomotePoint = BattleConfigerXMLManager.indexPlayerAttackRemotePoint(this._roleType);
         this._rageSkillRomotePoint = BattleConfigerXMLManager.indexPlayerRageSkillRemotePoint(this._roleType);
         var _loc10_:TextFormat = new TextFormat();
         _loc10_.size = 10;
         _loc10_.color = 16777215;
         this._nameColor = HeroDataUtil.indexHeroNameColor(param5);
         var _loc9_:UISkin = SourceManager.instance.getSkinByName("pro_red_small");
         var _loc7_:UISkin = SourceManager.instance.getSkinByName("pro_bg_small");
         var _loc8_:UISkin = SourceManager.instance.getSkinByName("pro_bg_small");
         this._bloodBar = new ProgressBar(_loc9_,_loc7_,55);
         if(this._headPoint)
         {
            this._bloodBar.x = this._headPoint.x - this._bloodBar.width / 2;
            this._bloodBar.y = this._headPoint.y - 15;
         }
         else
         {
            this._headPoint = new Point();
            this._heartPoint = new Point();
            clientTrace(this._roleType + "未找到该模型挂点配置文件,ErrorCode: 100001,具体询问 晓明");
            if(Config.clientDebug)
            {
               PopUpCenter.alertWin(this._roleType + "未找到该模型挂点配置文件 ErrorCode: 100001,具体询问 晓明");
            }
            this._bloodBar.x = -this.getBounds(this).width / 2 - _loc7_.width / 2;
            this._bloodBar.y = -this.getBounds(this).height - _loc7_.height - 5;
         }
         if(MainData.getInstance().manlyData.judeAfterTwoManlyHero(this.curHeroHtid) || MainData.getInstance().manlyData.judeHeroInPushTower(this.curHeroHtid))
         {
            this._strongBloodBar = new BattleBloodBar();
            this._strongBloodBar.x = this._bloodBar.x;
            this._strongBloodBar.y = this._bloodBar.y - 3;
            this.addChild(this._strongBloodBar);
            this._strongBloodBar.mouseChildren = false;
            this._strongBloodBar.mouseEnabled = false;
         }
         this.addChild(this._bloodBar);
         this._bloodBar.mouseChildren = false;
         this._bloodBar.mouseEnabled = false;
         if(this._nameColor == 16777215)
         {
            this._nameTxt = new Label(Globalization.getString("battle.8"),new TextFormat("Verdana",12,16775474),[new GlowFilter(2565927,1,2,2,10)]);
         }
         else
         {
            this._nameTxt = new Label(Globalization.getString("battle.8"),new TextFormat("Verdana",12,this._nameColor),[new GlowFilter(2565927,1,2,2,10)]);
         }
         this._nameTxt.selectable = false;
         this._nameTxt.mouseEnabled = false;
         this.addChild(this._nameTxt);
         this._nameTxt.text = this._roleName;
         this._nameTxt.x = this._headPoint.x - this._nameTxt.getWidth() / 2;
         this._rolePaticalaerBitmap = new Bitmap();
         this._rolePaticalaerBitmap.x = this._bloodBar.x - 20;
         this._rolePaticalaerBitmap.y = this._bloodBar.y - 20;
         if(MainData.getInstance().manlyData.judeAfterTwoManlyHero(this.curHeroHtid) || MainData.getInstance().manlyData.judeHeroInPushTower(this.curHeroHtid))
         {
            this._strongBloodBar.visible = showBloodBar;
            this._bloodBar.visible = false;
         }
         else
         {
            this._bloodBar.visible = showBloodBar;
            if(this._strongBloodBar)
            {
               this._strongBloodBar.visible = false;
            }
         }
         this.addChild(this._rolePaticalaerBitmap);
         if(SourceManager.onLineDebug)
         {
            this.graphics.beginFill(16711680);
            this.graphics.drawRect(this._headPoint.x - 1,this._headPoint.y - 1,2,2);
            this.graphics.endFill();
            this.graphics.beginFill(16711680);
            this.graphics.drawRect(this._attackRomotePoint.x - 1,this._attackRomotePoint.y - 1,2,2);
            this.graphics.endFill();
         }
         this.refresh();
      }
      
      public function get roleWidth() : int
      {
         if(this._standAnimation)
         {
            return this._standAnimation.currentBitmapWidth;
         }
         return this.width;
      }
      
      public function get roleHeight() : int
      {
         if(this._standAnimation)
         {
            return this._standAnimation.currentBitmapHeight;
         }
         return this.height;
      }
      
      public function refresh() : void
      {
         this._nameTxt.text = this._roleName;
         if(this._nameTxt)
         {
            this._nameTxt.x = this._headPoint.x - this._nameTxt.getBounds(this).width / 2;
            this._nameTxt.y = this._bloodBar.y - 20;
            this._rolePaticalaerBitmap.x = this._bloodBar.x - 20;
            this._rolePaticalaerBitmap.y = this._bloodBar.y - 20;
         }
      }
      
      public function upDate() : void
      {
         var _loc1_:uint = 0;
         if(this._standAnimation)
         {
            this._standAnimation.upDate();
         }
         var _loc2_:uint = this._buffList.length;
         if(_loc2_ > 0)
         {
            _loc1_ = 0;
            while(_loc1_ < _loc2_)
            {
               this._buffList[_loc1_].upDate();
               _loc1_++;
            }
         }
         if(this.rageAnimation)
         {
            this.rageAnimation.upDate();
            if(SourceManager.onLineDebug)
            {
               this.graphics.beginFill(16711680);
               this.graphics.drawRect(this.heartPoint.x,this.heartPoint.y,1,1);
               this.graphics.endFill();
            }
         }
         if(this.roleManlyAnimation)
         {
            this.roleManlyAnimation.upDate();
         }
         if(this.roleManlyBottomAnimation)
         {
            this.roleManlyBottomAnimation.upDate();
         }
      }
      
      public function addBuff(param1:uint) : void
      {
         var _loc3_:int = 0;
         var _loc4_:Buff = this.indexBuff(param1);
         var _loc2_:BuffDebugEvent = new BuffDebugEvent();
         _loc2_.info = "玩家: " + this._id + " 添加buff:" + param1 + "(" + SourceManager.instance.round + "回合)";
         EventBrige.instance.dispatchEvent(_loc2_);
         if(this.delBuffIdArr && this.delBuffIdArr.length != 0)
         {
            _loc3_ = int(this.delBuffIdArr.indexOf(param1));
            if(_loc3_ != -1)
            {
               this.delBuffIdArr.splice(_loc3_,1);
            }
            return;
         }
         if(!_loc4_)
         {
            _loc4_ = new Buff(param1);
            this.addChild(_loc4_);
            _loc4_.x = this.headPoint.x;
            _loc4_.y = this.headPoint.y + -40;
            this._buffList.push(_loc4_);
            _loc4_.upCount();
         }
         else
         {
            _loc4_.upCount();
         }
      }
      
      public function removeBuff(param1:uint) : void
      {
         var _loc2_:Buff = null;
         var _loc5_:BuffDebugEvent = new BuffDebugEvent();
         _loc5_.info = "玩家: " + this._id + " 删除buff:" + param1 + "(" + SourceManager.instance.round + "回合)";
         EventBrige.instance.dispatchEvent(_loc5_);
         var _loc3_:uint = this._buffList.length;
         if(_loc3_ != 0)
         {
            _loc2_ = this.indexBuff(param1);
            if(!_loc2_)
            {
               this.delBuffIdArr.push(param1);
            }
         }
         else
         {
            this.delBuffIdArr.push(param1);
         }
         var _loc4_:uint = 0;
         while(_loc4_ < _loc3_)
         {
            if(this._buffList[_loc4_].id == param1)
            {
               this._buffList[_loc4_].lowCount();
               if(this._buffList[_loc4_].count <= 0)
               {
                  this._buffList[_loc4_].dispose();
                  this._buffList.splice(_loc4_,1);
               }
               return;
            }
            _loc4_++;
         }
      }
      
      public function removeAllBuff() : void
      {
         while(this._buffList.length > 0)
         {
            this._buffList[this._buffList.length - 1].dispose();
            this._buffList.splice(this._buffList.length - 1,1);
         }
         this._buffList.length = 0;
      }
      
      override public function dispose() : void
      {
         if(this._standAnimation)
         {
            DisplayHelper.removeFromStage(this._standAnimation);
            this._standAnimation.dispose();
            this._standAnimation = null;
         }
         this.removeAllBuff();
         if(this.rageAnimation)
         {
            this.rageAnimation.parent && this.rageAnimation.parent.removeChild(this.rageAnimation);
            this.rageAnimation = null;
         }
         if(this.roleManlyAnimation)
         {
            this.roleManlyAnimation.parent && this.roleManlyAnimation.parent.removeChild(this.roleManlyAnimation);
            this.roleManlyAnimation = null;
         }
         if(this.roleManlyBottomAnimation)
         {
            this.roleManlyBottomAnimation.parent && this.roleManlyBottomAnimation.parent.removeChild(this.roleManlyBottomAnimation);
            this.roleManlyBottomAnimation = null;
         }
         if(this._position)
         {
            this._position = null;
         }
         super.dispose();
      }
      
      private function indexBuff(param1:uint) : Buff
      {
         var _loc3_:uint = this._buffList.length;
         var _loc2_:uint = 0;
         while(_loc2_ < _loc3_)
         {
            if(this._buffList[_loc2_].id == param1)
            {
               return this._buffList[_loc2_];
            }
            _loc2_++;
         }
         return null;
      }
      
      public function get headCenterPoint() : Point
      {
         if(this._bloodBar)
         {
            return new Point(this._bloodBar.x,this._bloodBar.y);
         }
         return null;
      }
      
      public function get heartPoint() : Point
      {
         if(this._heartPoint)
         {
            return this._heartPoint;
         }
         return new Point(this._standAnimation.x + this._standAnimation.currentBitmapWidth / 2,this._standAnimation.y + this.height / 2);
      }
      
      public function get feetPoint() : Point
      {
         return new Point(this.x,this.y);
      }
      
      public function get headPoint() : Point
      {
         if(this._headPoint)
         {
            return this._headPoint;
         }
         if(this._standAnimation)
         {
            return new Point(this._standAnimation.x + this._standAnimation.currentBitmapWidth / 2,this._standAnimation.y);
         }
         return new Point(this.x,this.y);
      }
      
      public function get uid() : uint
      {
         return this._id;
      }
      
      public function set position(param1:Parallelogram) : void
      {
         this._position = param1;
      }
      
      public function get position() : Parallelogram
      {
         return this._position;
      }
      
      public function get roleName() : String
      {
         return this._roleName;
      }
      
      public function set roleName(param1:String) : void
      {
         this._roleName = param1;
         if(this._roleName == null)
         {
            this._roleName = "";
         }
         this.refresh();
      }
      
      public function get buffList() : Vector.<Buff>
      {
         return this._buffList;
      }
      
      public function removeALlBuffUI() : void
      {
         var _loc2_:uint = this._buffList.length;
         var _loc1_:uint = 0;
         while(_loc1_ < _loc2_)
         {
            if(this._buffList[_loc1_].parent)
            {
               this._buffList[_loc1_].parent.removeChild(this._buffList[_loc1_]);
            }
            _loc1_++;
         }
      }
      
      public function addAllBuffUI() : void
      {
         var _loc2_:uint = this._buffList.length;
         var _loc1_:uint = 0;
         while(_loc1_ < _loc2_)
         {
            if(!this._buffList[_loc1_].parent)
            {
               this._buffList[_loc1_].x = this.headPoint.x;
               this._buffList[_loc1_].y = this.headPoint.y + -40;
               this.addChild(this._buffList[_loc1_]);
            }
            _loc1_++;
         }
      }
      
      public function get roleType() : String
      {
         return this._roleType;
      }
      
      public function get currentPosition() : Point
      {
         return this._currentPosition;
      }
      
      public function set currentPosition(param1:Point) : void
      {
         this._currentPosition = param1;
      }
      
      public function get direction() : uint
      {
         return this._direction;
      }
      
      public function get standAnimation() : AbBaseRenderItem
      {
         return this._standAnimation;
      }
      
      public function get bloodBar() : ProgressBar
      {
         return this._bloodBar;
      }
      
      public function get strongBloodBar() : BattleBloodBar
      {
         return this._strongBloodBar;
      }
      
      public function set rolePaticalaer(param1:String) : void
      {
         var _loc2_:Class = null;
         if(param1 && SourceManager.instance.hasDefine(param1))
         {
            _loc2_ = getDefinitionByName(param1) as Class;
            this._rolePaticalaerBitmap.bitmapData = new _loc2_() as BitmapData;
         }
         else
         {
            this._rolePaticalaerBitmap.bitmapData = null;
         }
      }
      
      public function get attackRomotePoint() : Point
      {
         return this._attackRomotePoint;
      }
      
      public function get rageSkillRomotePoint() : Point
      {
         return this._rageSkillRomotePoint;
      }
      
      public function get reverse() : Boolean
      {
         return this._reverse;
      }
      
      public function set reverse(param1:Boolean) : void
      {
         this._reverse = param1;
         if(this._reverse)
         {
            if(this._heartPoint)
            {
               this._heartPoint.x = -1 * this._heartPoint.x;
            }
            if(this._headPoint)
            {
               this._headPoint.x = -1 * this._headPoint.x;
            }
            if(this._attackRomotePoint)
            {
               this._attackRomotePoint.x = -1 * this._attackRomotePoint.x;
            }
            if(this._rageSkillRomotePoint)
            {
               this._rageSkillRomotePoint.x = -1 * this._rageSkillRomotePoint.x;
            }
            if(this._headPoint)
            {
               this._bloodBar.x = this._headPoint.x - this._bloodBar.width / 2;
               this._bloodBar.y = this._headPoint.y - 15;
               if(this._strongBloodBar)
               {
                  this._strongBloodBar.x = this._bloodBar.x - 2;
                  this._strongBloodBar.y = this._bloodBar.y - 3;
               }
            }
            if(SourceManager.onLineDebug)
            {
               this.graphics.clear();
               this.graphics.beginFill(16711680);
               this.graphics.drawRect(this._headPoint.x - 1,this._headPoint.y - 1,2,2);
               this.graphics.endFill();
               this.graphics.beginFill(16711680);
               this.graphics.drawRect(this._rageSkillRomotePoint.x - 1,this._rageSkillRomotePoint.y - 1,2,2);
               this.graphics.endFill();
            }
         }
      }
      
      public function setRoleManlyEffect(param1:int) : void
      {
         var _loc2_:Array = HeroDataUtil.getRoleManlyEffectArr(param1);
         this.roleManlyAnimation = SourceManager.instance.getRenderItem(_loc2_[0]);
         this.roleManlyAnimation.mouseEnabled = false;
         this.roleManlyAnimation.mouseChildren = false;
         this.roleManlyBottomAnimation = SourceManager.instance.getRenderItem(_loc2_[1]);
         this.roleManlyBottomAnimation.mouseEnabled = false;
         this.roleManlyBottomAnimation.mouseChildren = false;
         this.addChild(this.roleManlyBottomAnimation);
         this.swapChildren(this.roleManlyBottomAnimation,this._standAnimation);
         this.addChild(this.roleManlyAnimation);
      }
   }
}

