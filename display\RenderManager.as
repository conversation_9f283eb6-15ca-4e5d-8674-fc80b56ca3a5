package display
{
   import com.greensock.TweenMax;
   import controler.FrameCenterBaseOnTimer;
   import controler.animation.baseAction.BaseAction;
   import controler.animation.baseAction.IAction;
   import controler.animation.baseAction.IRoleStandAction;
   import flash.display.DisplayObject;
   import flash.events.Event;
   import flash.events.EventDispatcher;
   import flash.events.IEventDispatcher;
   import mmo.utils.FrameCenter;
   import mmo.utils.FrameCenterAdv;
   import sourceManager.SourceManager;
   import util.DisplayHelper;
   
   public final class RenderManager extends EventDispatcher implements IRenderAble
   {
      private static var _instance:RenderManager;
      
      private static var _speedUp:Boolean;
      
      public static var frameCenter:FrameCenter;
      
      public static var frameAdv:FrameCenterAdv;
      
      private var _renderList:Vector.<IRenderAble> = new Vector.<IRenderAble>();
      
      private var _ignoreList:Vector.<IRenderAble> = new Vector.<IRenderAble>();
      
      private var _baseActionCounter:uint = 0;
      
      private var _isDisposed:Boolean;
      
      public function RenderManager()
      {
         super();
      }
      
      public static function get instance() : RenderManager
      {
         if(!_instance)
         {
            _instance = new RenderManager();
         }
         return _instance;
      }
      
      public function addToRenderList(param1:IRenderAble) : void
      {
         if(!this.hasRenderItem(param1))
         {
            this._renderList.push(param1);
         }
         if(param1 is BaseAction)
         {
            this._baseActionCounter++;
         }
      }
      
      public function addToIgnorAbleList(param1:IRenderAble) : void
      {
         if(this._ignoreList.indexOf(param1) < 0)
         {
            this._ignoreList.push(param1);
            if(param1 is AbBaseRenderItem)
            {
               TweenMax.to(param1 as DisplayObject,0.3,{"alpha":0});
            }
            (param1 as IEventDispatcher).addEventListener("BASE_ACTION_END",this.remove);
         }
      }
      
      private function remove(param1:Event) : void
      {
         if(this._isDisposed)
         {
            return;
         }
         (param1.target as IEventDispatcher).removeEventListener("BASE_ACTION_END",this.remove);
         var _loc2_:int = int(this._ignoreList.indexOf(param1.target as IRenderAble));
         if(_loc2_ >= 0)
         {
            if(this._ignoreList[_loc2_] is DisplayObject)
            {
               if(this._ignoreList[_loc2_] is BattleMovieClip)
               {
                  (this._ignoreList[_loc2_] as BattleMovieClip).dispose();
               }
               DisplayHelper.removeFromStage(this._ignoreList[_loc2_] as DisplayObject);
            }
            if(this._ignoreList[_loc2_] is IAction)
            {
               (this._ignoreList[_loc2_] as IAction).dispose();
            }
            this._ignoreList.splice(_loc2_,1);
         }
      }
      
      public function checkRoleStandAction(param1:uint) : void
      {
         var _loc3_:uint = this._renderList.length;
         var _loc2_:uint = 0;
         while(_loc2_ < _loc3_)
         {
            if(this._renderList[_loc2_] is IRoleStandAction)
            {
               if(param1 == (this._renderList[_loc2_] as IRoleStandAction).uid)
               {
                  (this._renderList[_loc2_] as IRoleStandAction).hide();
               }
            }
            _loc2_++;
         }
      }
      
      public function resumeRoleAction(param1:uint) : void
      {
         var _loc3_:uint = this._renderList.length;
         var _loc2_:uint = 0;
         while(_loc2_ < _loc3_)
         {
            if(this._renderList[_loc2_] is IRoleStandAction)
            {
               if(param1 == (this._renderList[_loc2_] as IRoleStandAction).uid)
               {
                  (this._renderList[_loc2_] as IRoleStandAction).show();
               }
            }
            _loc2_++;
         }
      }
      
      public function startRender() : void
      {
         this._isDisposed = false;
         this.stopRender();
         if(!_speedUp)
         {
            frameCenter = new FrameCenter();
         }
         else
         {
            frameCenter = new FrameCenterBaseOnTimer();
            (frameCenter as FrameCenterBaseOnTimer).speedUpTo(0.25);
         }
         frameAdv = new FrameCenterAdv(frameCenter);
         frameCenter.addFrame(this.upDate);
      }
      
      public function stopRender() : void
      {
         if(frameAdv)
         {
            frameAdv.stop();
            frameAdv = null;
         }
         if(frameCenter)
         {
            frameCenter.stop();
            frameCenter = null;
         }
      }
      
      public function getSameLineAnmiationNumber(param1:uint) : uint
      {
         var _loc2_:BaseAction = null;
         var _loc5_:uint = this._renderList.length;
         var _loc3_:uint = 0;
         var _loc4_:uint = 0;
         while(_loc4_ < _loc5_)
         {
            if(this._renderList[_loc4_] is BaseAction)
            {
               _loc2_ = this._renderList[_loc4_] as BaseAction;
               if(_loc2_.yIndex <= param1)
               {
                  _loc3_++;
               }
            }
            _loc4_++;
         }
         return _loc3_;
      }
      
      public function hasRenderItem(param1:IRenderAble) : Boolean
      {
         var _loc3_:uint = this._renderList.length;
         var _loc2_:uint = 0;
         while(_loc2_ < _loc3_)
         {
            if(this._renderList[_loc2_] == param1)
            {
               return true;
            }
            _loc2_++;
         }
         return false;
      }
      
      public function removeFromRenderList(param1:IRenderAble) : void
      {
         if(this._isDisposed)
         {
            return;
         }
         var _loc3_:uint = this._renderList.length;
         var _loc2_:uint = 0;
         while(_loc2_ < _loc3_)
         {
            if(this._renderList[_loc2_] == param1)
            {
               this._renderList.splice(_loc2_,1);
               if(param1 is BaseAction)
               {
                  this._baseActionCounter--;
               }
               return;
            }
            _loc2_++;
         }
      }
      
      public function removeAll() : void
      {
         var _loc4_:int = 0;
         var _loc3_:Vector.<IRenderAble> = null;
         var _loc1_:int = 0;
         var _loc2_:int = 0;
         this._isDisposed = true;
         this.stopRender();
         try
         {
            _loc4_ = int(this._renderList.length);
            _loc3_ = this._renderList.concat();
            _loc1_ = 0;
            while(_loc1_ < this._renderList.length)
            {
               (_loc3_[_loc1_] as IEventDispatcher).removeEventListener("BASE_ACTION_END",this.remove);
               if(_loc3_[_loc1_] is IAction)
               {
                  (_loc3_[_loc1_] as IAction).dispose();
               }
               _loc1_++;
            }
            _loc4_ = int(this._ignoreList.length);
            _loc3_ = this._ignoreList.concat();
            _loc2_ = 0;
            while(_loc2_ < this._ignoreList.length)
            {
               if(_loc3_[_loc2_] is IAction)
               {
                  (_loc3_[_loc2_] as IEventDispatcher).removeEventListener("BASE_ACTION_END",this.remove);
                  (_loc3_[_loc2_] as IAction).dispose();
               }
               _loc2_++;
            }
         }
         catch(e:*)
         {
         }
         if(SourceManager.instance.animationCanvas)
         {
            SourceManager.instance.animationCanvas;
         }
         this._renderList.length = 0;
         this._ignoreList.length = 0;
      }
      
      public function upDate() : void
      {
         if(this._isDisposed)
         {
            return;
         }
         var _loc3_:uint = this._renderList.length;
         var _loc2_:uint = 0;
         while(_loc2_ < this._renderList.length)
         {
            this._renderList[_loc2_].upDate();
            _loc2_++;
         }
         var _loc1_:uint = 0;
         while(_loc1_ < this._ignoreList.length)
         {
            this._ignoreList[_loc1_].upDate();
            _loc1_++;
         }
      }
      
      public function get baseActionCounter() : uint
      {
         return this._baseActionCounter;
      }
      
      public function get speedUp() : Boolean
      {
         return _speedUp;
      }
      
      public function set speedUp(param1:Boolean) : void
      {
         _speedUp = param1;
      }
   }
}

