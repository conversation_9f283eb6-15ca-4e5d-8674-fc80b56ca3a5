package display.grid
{
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.text.TextField;
   import model.PlayerListModel;
   import model.PlayerModel;
   
   public class BattleGrid extends Grid
   {
      public function BattleGrid()
      {
         super();
      }
      
      public function reset() : void
      {
         var _loc2_:uint = this.boardList.length;
         var _loc1_:uint = 0;
         while(_loc1_ < _loc2_)
         {
            this.boardList[_loc1_].resetPlayerData();
            _loc1_++;
         }
      }
      
      public function indexAttackPosition(param1:Parallelogram) : Parallelogram
      {
         var _loc2_:Parallelogram = null;
         if(param1.teamid == 1)
         {
            _loc2_ = this.getRight(param1.xIndex,param1.yIndex);
         }
         else
         {
            _loc2_ = this.getLeft(param1.xIndex,param1.yIndex);
         }
         return _loc2_;
      }
      
      public function hasBehandPostion(param1:Parallelogram) : Boolean
      {
         var _loc2_:Point = null;
         if(param1.teamid == 1)
         {
            _loc2_ = this.getLeftIndex(param1.xIndex,param1.yIndex);
         }
         else
         {
            _loc2_ = this.getRightIndex(param1.xIndex,param1.yIndex);
         }
         return this.hasParallelogram(_loc2_.x,_loc2_.y);
      }
      
      public function indexBehandPostion(param1:Parallelogram) : Parallelogram
      {
         var _loc2_:Parallelogram = null;
         if(param1.teamid == 1)
         {
            _loc2_ = this.getLeft(param1.xIndex,param1.yIndex);
         }
         else
         {
            _loc2_ = this.getRight(param1.xIndex,param1.yIndex);
         }
         return _loc2_;
      }
      
      public function centerPoint(param1:Parallelogram) : Parallelogram
      {
         if(param1.teamid == 1)
         {
            return this.index(3,1);
         }
         return this.index(4,1);
      }
      
      public function sameLineArmyFixed(param1:Parallelogram, param2:Array) : Parallelogram
      {
         var _loc7_:PlayerModel = null;
         var _loc6_:Parallelogram = null;
         var _loc5_:Parallelogram = null;
         var _loc8_:Vector.<Parallelogram> = this.getTeamALine(param1.yIndex);
         var _loc9_:int = int(!!param2 ? int(param2.length) : 0);
         var _loc3_:uint = _loc8_.length;
         var _loc4_:int = 0;
         while(_loc4_ < _loc9_)
         {
            _loc7_ = PlayerListModel.instance.indexPlayer(uint(param2[_loc4_].defender));
            if(_loc7_)
            {
               if(_loc7_.postion.yIndex == param1.yIndex)
               {
                  if(param1.teamid == 1)
                  {
                     return this.index(_loc7_.postion.xIndex - 1,_loc7_.postion.yIndex);
                  }
                  return this.index(_loc7_.postion.xIndex + 1,_loc7_.postion.yIndex);
               }
               if(param1.yIndex > _loc7_.postion.yIndex)
               {
                  if(_loc6_ == null)
                  {
                     _loc6_ = _loc7_.postion;
                  }
                  else if(_loc6_.yIndex - param1.yIndex > _loc6_.yIndex - _loc7_.postion.yIndex)
                  {
                     _loc6_ = _loc7_.postion;
                  }
               }
               else if(_loc5_ == null)
               {
                  _loc5_ = _loc7_.postion;
               }
               else if(_loc5_.yIndex - param1.yIndex < _loc5_.yIndex - _loc7_.postion.yIndex)
               {
                  _loc5_ = _loc7_.postion;
               }
            }
            _loc4_++;
         }
         if(param1.yIndex == 0 && _loc5_ != null)
         {
            if(_loc5_.teamid == 1)
            {
               return this.index(_loc5_.xIndex + 1,_loc5_.yIndex);
            }
            return this.index(_loc5_.xIndex - 1,_loc5_.yIndex);
         }
         if((param1.yIndex == 1 || param1.yIndex == 2) && _loc6_ != null)
         {
            if(_loc6_.teamid == 1)
            {
               return this.index(_loc6_.xIndex + 1,_loc6_.yIndex);
            }
            return this.index(_loc6_.xIndex - 1,_loc6_.yIndex);
         }
         if(_loc5_)
         {
            if(_loc5_.teamid == 1)
            {
               return this.index(_loc5_.xIndex + 1,_loc5_.yIndex);
            }
            return this.index(_loc5_.xIndex - 1,_loc5_.yIndex);
         }
         if(_loc6_)
         {
            if(_loc6_.teamid == 1)
            {
               return this.index(_loc6_.xIndex + 1,_loc6_.yIndex);
            }
            return this.index(_loc6_.xIndex - 1,_loc6_.yIndex);
         }
         return null;
      }
      
      public function sameLineSelfFixed(param1:Parallelogram, param2:Array, param3:int = 1, param4:PlayerModel = null) : Parallelogram
      {
         var _loc10_:PlayerModel = null;
         var _loc7_:Parallelogram = null;
         var _loc8_:Parallelogram = null;
         var _loc5_:Vector.<Parallelogram> = this.getTeamALine(param1.yIndex);
         var _loc6_:int = int(param2.length);
         var _loc11_:uint = _loc5_.length;
         if(param4)
         {
            if(param4.teamid == 1)
            {
               return this.index(2 + param3,param4.postion.yIndex);
            }
            return this.index(5 - param3,param4.postion.yIndex);
         }
         var _loc9_:int = 0;
         while(_loc9_ < _loc6_)
         {
            _loc10_ = PlayerListModel.instance.indexPlayer(uint(param2[_loc9_].defender));
            if(_loc10_)
            {
               if(_loc10_.postion.yIndex == param1.yIndex)
               {
                  if(param1.teamid == 1)
                  {
                     return this.index(5 - param3,_loc10_.postion.yIndex);
                  }
                  return this.index(2 + param3,_loc10_.postion.yIndex);
               }
               if(param1.yIndex > _loc10_.postion.yIndex)
               {
                  if(_loc7_ == null)
                  {
                     _loc7_ = _loc10_.postion;
                  }
                  else if(_loc7_.yIndex - param1.yIndex > _loc7_.yIndex - _loc10_.postion.yIndex)
                  {
                     _loc7_ = _loc10_.postion;
                  }
               }
               else if(_loc8_ == null)
               {
                  _loc8_ = _loc10_.postion;
               }
               else if(_loc8_.yIndex - param1.yIndex < _loc8_.yIndex - _loc10_.postion.yIndex)
               {
                  _loc8_ = _loc10_.postion;
               }
            }
            _loc9_++;
         }
         if(param1.yIndex == 0 && _loc8_ != null)
         {
            if(_loc8_.teamid == 1)
            {
               return this.index(2 + param3,_loc8_.yIndex);
            }
            return this.index(5 - param3,_loc8_.yIndex);
         }
         if((param1.yIndex == 1 || param1.yIndex == 2) && _loc7_ != null)
         {
            if(_loc7_.teamid == 1)
            {
               return this.index(2 + param3,_loc7_.yIndex);
            }
            return this.index(5 - param3,_loc7_.yIndex);
         }
         if(_loc8_)
         {
            if(_loc8_.teamid == 1)
            {
               return this.index(2 + param3,_loc8_.yIndex);
            }
            return this.index(5 - param3,_loc8_.yIndex);
         }
         if(_loc7_)
         {
            if(_loc7_.teamid == 1)
            {
               return this.index(2 + param3,_loc7_.yIndex);
            }
            return this.index(5 - param3,_loc7_.yIndex);
         }
         return null;
      }
      
      public function sameLineNearArmy(param1:Parallelogram, param2:Parallelogram) : Parallelogram
      {
         if(param1.teamid == 1)
         {
            return this.index(3,param2.yIndex);
         }
         return this.index(4,param2.yIndex);
      }
      
      public function getLineBoardsFromLeftToRight(param1:uint) : Vector.<Parallelogram>
      {
         var _loc4_:Vector.<Parallelogram> = null;
         var _loc2_:uint = 0;
         var _loc3_:uint = 0;
         if(param1 < this._yNumber)
         {
            _loc4_ = new Vector.<Parallelogram>();
            _loc2_ = param1 * _xNumber;
            _loc3_ = 0;
            while(_loc3_ < this._xNumber)
            {
               _loc4_.push(this.boardList[_loc3_ + _loc2_]);
               _loc3_++;
            }
            return _loc4_;
         }
         throw new Error("Grid::getLineBoards 越界");
      }
      
      public function getLineBoardsFromRightToLeft(param1:uint) : Vector.<Parallelogram>
      {
         var _loc4_:Vector.<Parallelogram> = null;
         var _loc2_:uint = 0;
         var _loc3_:int = 0;
         if(param1 < this._yNumber)
         {
            _loc4_ = new Vector.<Parallelogram>();
            _loc2_ = (param1 + 1) * _xNumber - 1;
            _loc3_ = 0;
            while(_loc3_ < _xNumber - 1)
            {
               _loc4_.push(this.boardList[_loc2_ - _loc3_]);
               _loc3_++;
            }
            return _loc4_;
         }
         throw new Error("Grid::getLineBoards 越界");
      }
      
      public function getTeamALine(param1:uint) : Vector.<Parallelogram>
      {
         var _loc4_:Vector.<Parallelogram> = null;
         var _loc2_:uint = 0;
         var _loc3_:int = 0;
         if(param1 < this._yNumber)
         {
            _loc4_ = new Vector.<Parallelogram>();
            _loc2_ = uint(param1 * _xNumber - 1 + 3);
            _loc3_ = 0;
            while(_loc3_ <= 2)
            {
               _loc4_.push(this.boardList[_loc2_ - _loc3_]);
               _loc3_++;
            }
            return _loc4_;
         }
         throw new Error("Grid::getLineBoards 越界");
      }
      
      public function getTeamBLine(param1:uint) : Vector.<Parallelogram>
      {
         var _loc4_:Vector.<Parallelogram> = null;
         var _loc2_:uint = 0;
         var _loc3_:int = 0;
         if(param1 < this._yNumber)
         {
            _loc4_ = new Vector.<Parallelogram>();
            _loc2_ = uint(param1 * _xNumber - 1 + 6);
            _loc3_ = 0;
            while(_loc3_ < 3)
            {
               _loc4_.push(this.boardList[_loc2_ + _loc3_]);
               _loc3_++;
            }
            return _loc4_;
         }
         throw new Error("Grid::getLineBoards 越界");
      }
      
      public function getCrossAt(param1:uint, param2:uint) : Vector.<Parallelogram>
      {
         var _loc9_:Vector.<Parallelogram> = null;
         var _loc8_:Vector.<Parallelogram> = new Vector.<Parallelogram>();
         var _loc3_:Parallelogram = this.index(param1,param2);
         var _loc4_:uint = 0;
         var _loc7_:uint = this._yNumber;
         var _loc6_:uint = _loc3_.xIndex < 3 ? 0 : 5;
         var _loc5_:uint = _loc3_.xIndex < 3 ? 2 : this._yNumber;
         _loc8_.push(_loc3_);
         _loc9_ = this.getDirection(_loc3_,3,1);
         if(_loc9_ && _loc9_.length > 0)
         {
            _loc8_ = _loc8_.concat(_loc9_);
         }
         _loc9_ = this.getDirection(_loc3_,3,2);
         if(_loc9_ && _loc9_.length > 0)
         {
            _loc8_ = _loc8_.concat(_loc9_);
         }
         _loc9_ = this.getDirection(_loc3_,3,3);
         if(_loc9_ && _loc9_.length > 0)
         {
            _loc8_ = _loc8_.concat(_loc9_);
         }
         _loc9_ = this.getDirection(_loc3_,3,4);
         if(_loc9_ && _loc9_.length > 0)
         {
            _loc8_ = _loc8_.concat(_loc9_);
         }
         return _loc8_;
      }
      
      public function getObliqueRightLine(param1:uint, param2:uint) : Vector.<Parallelogram>
      {
         var _loc11_:Vector.<Parallelogram> = null;
         var _loc10_:Parallelogram = this.index(param1,param2);
         var _loc3_:uint = 0;
         var _loc5_:uint = 2;
         var _loc9_:uint = _loc10_.xIndex < 3 ? 0 : 5;
         var _loc8_:uint = _loc10_.xIndex < 3 ? 2 : uint(this._xNumber - 1);
         var _loc7_:uint = uint(_loc10_.xIndex);
         var _loc4_:uint = uint(_loc10_.yIndex);
         var _loc6_:Boolean = true;
         if(_loc10_)
         {
            _loc11_ = new Vector.<Parallelogram>();
            while(_loc6_)
            {
               if(this.isOnPlayersArea(param1 - 1,param2 - 1))
               {
                  _loc11_.push(this.index(param1 - 1,param2 - 1));
               }
               if(this.isOnPlayersArea(param1 + 1,param2 + 1))
               {
                  _loc11_.push(this.index(param1 + 1,param2 + 1));
               }
            }
         }
         return _loc11_;
      }
      
      public function indexByGridid(param1:uint) : Parallelogram
      {
         if(param1 > 8)
         {
            return this.indexTeamBInfo(param1 - 8);
         }
         return this.indexTeamAInfo(param1);
      }
      
      private function getDirection(param1:Parallelogram, param2:uint, param3:uint) : Vector.<Parallelogram>
      {
         var _loc11_:Vector.<Parallelogram> = new Vector.<Parallelogram>();
         var _loc4_:uint = 0;
         var _loc6_:uint = 2;
         var _loc10_:uint = param1.xIndex < 3 ? 0 : 5;
         var _loc9_:uint = param1.xIndex < 3 ? 2 : uint(this._xNumber - 1);
         var _loc8_:uint = uint(param1.xIndex);
         var _loc5_:uint = uint(param1.yIndex);
         var _loc7_:Boolean = true;
         while(_loc7_)
         {
            switch(int(param3) - 1)
            {
               case 0:
                  if(_loc5_ - 1 >= _loc4_)
                  {
                     _loc5_--;
                     _loc11_.push(this.index(_loc8_,_loc5_));
                  }
                  else
                  {
                     _loc7_ = false;
                  }
                  break;
               case 1:
                  if(_loc8_ + 1 <= _loc9_)
                  {
                     _loc8_ += 1;
                     _loc11_.push(this.index(_loc8_,_loc5_));
                  }
                  else
                  {
                     _loc7_ = false;
                  }
                  break;
               case 2:
                  if(_loc5_ + 1 <= _loc6_)
                  {
                     _loc5_ += 1;
                     _loc11_.push(this.index(_loc8_,_loc5_));
                  }
                  else
                  {
                     _loc7_ = false;
                  }
                  break;
               case 3:
                  if(_loc8_ - 1 >= _loc10_)
                  {
                     _loc8_--;
                     _loc11_.push(this.index(_loc8_,_loc5_));
                  }
                  else
                  {
                     _loc7_ = false;
                  }
                  break;
               default:
                  _loc7_ = false;
                  break;
            }
         }
         return _loc11_;
      }
      
      public function getRowBoardsFromUpToDown(param1:uint) : Vector.<Parallelogram>
      {
         var _loc4_:Vector.<Parallelogram> = null;
         var _loc2_:uint = 0;
         var _loc3_:uint = 0;
         if(param1 < this._xNumber)
         {
            _loc4_ = new Vector.<Parallelogram>();
            _loc2_ = 0;
            while(_loc2_ < this._yNumber)
            {
               _loc3_ = param1 + _loc2_ * _xNumber;
               _loc4_.push(this.boardList[_loc3_]);
               _loc2_++;
            }
            return _loc4_;
         }
         throw new Error("Grid::getLineBoards 越界");
      }
      
      public function getRowBoardsFromDownToUp(param1:uint) : Vector.<Parallelogram>
      {
         var _loc4_:Vector.<Parallelogram> = null;
         var _loc2_:int = 0;
         var _loc3_:uint = 0;
         if(param1 < this._xNumber)
         {
            _loc4_ = new Vector.<Parallelogram>();
            _loc2_ = _yNumber - 1;
            while(_loc2_ >= 0)
            {
               _loc3_ = param1 + _loc2_ * _xNumber;
               _loc4_.push(this.boardList[_loc3_]);
               _loc2_--;
            }
            return _loc4_;
         }
         throw new Error("Grid::getLineBoards 越界");
      }
      
      public function getFourPointAt(param1:uint, param2:uint) : Vector.<Parallelogram>
      {
         var _loc3_:Vector.<Parallelogram> = null;
         if(this.isOnPlayersArea(param1,param2))
         {
            _loc3_ = new Vector.<Parallelogram>();
            if(this.isOnPlayersAreaWithPoint(getLeftUpIndex(param1,param2)))
            {
               _loc3_.push(this.getLeftUp(param1,param2));
            }
            if(this.isOnPlayersAreaWithPoint(getRightUpIndex(param1,param2)))
            {
               _loc3_.push(this.getRightUp(param1,param2));
            }
            if(this.isOnPlayersAreaWithPoint(getRightDownIndex(param1,param2)))
            {
               _loc3_.push(this.getRightDown(param1,param2));
            }
            if(this.isOnPlayersAreaWithPoint(getLeftDownIndex(param1,param2)))
            {
               _loc3_.push(this.getLeftDown(param1,param2));
            }
         }
         return _loc3_;
      }
      
      public function isOnPlayersArea(param1:uint, param2:uint) : Boolean
      {
         var _loc5_:uint = 0;
         var _loc6_:uint = 0;
         var _loc3_:uint = 0;
         var _loc4_:uint = 0;
         if(hasParallelogram(param1,param2))
         {
            _loc5_ = 0;
            _loc6_ = 2;
            _loc3_ = param1 < 3 ? 0 : 5;
            _loc4_ = param1 < 3 ? 2 : uint(this._xNumber - 1);
            if(param1 >= _loc3_ && param1 <= _loc4_ && param2 >= _loc5_ && param2 <= _loc6_)
            {
               return true;
            }
         }
         return false;
      }
      
      public function isOnPlayersAreaWithPoint(param1:Point) : Boolean
      {
         return this.isOnPlayersArea(param1.x,param1.y);
      }
      
      public function indexTeamBInfo(param1:uint) : Parallelogram
      {
         var _loc4_:int = -1;
         var _loc2_:int = -1;
         switch(int(param1))
         {
            case 0:
               _loc4_ = 5;
               _loc2_ = 0;
               break;
            case 1:
               _loc4_ = 5;
               _loc2_ = 1;
               break;
            case 2:
               _loc4_ = 5;
               _loc2_ = 2;
               break;
            case 3:
               _loc4_ = 6;
               _loc2_ = 0;
               break;
            case 4:
               _loc4_ = 6;
               _loc2_ = 1;
               break;
            case 5:
               _loc4_ = 6;
               _loc2_ = 2;
               break;
            case 6:
               _loc4_ = 7;
               _loc2_ = 0;
               break;
            case 7:
               _loc4_ = 7;
               _loc2_ = 1;
               break;
            case 8:
               _loc4_ = 7;
               _loc2_ = 2;
         }
         return this.index(_loc4_,_loc2_);
      }
      
      public function get teamACenterPoint() : Parallelogram
      {
         return this.index(1,1);
      }
      
      public function get teamBCenterPoint() : Parallelogram
      {
         return this.index(6,1);
      }
      
      public function indexTeamAInfo(param1:uint) : Parallelogram
      {
         var _loc3_:uint = 0;
         var _loc2_:uint = 0;
         switch(int(param1))
         {
            case 0:
               _loc3_ = 2;
               _loc2_ = 0;
               break;
            case 1:
               _loc3_ = 2;
               _loc2_ = 1;
               break;
            case 2:
               _loc3_ = 2;
               _loc2_ = 2;
               break;
            case 3:
               _loc3_ = 1;
               _loc2_ = 0;
               break;
            case 4:
               _loc3_ = 1;
               _loc2_ = 1;
               break;
            case 5:
               _loc3_ = 1;
               _loc2_ = 2;
               break;
            case 6:
               _loc3_ = 0;
               _loc2_ = 0;
               break;
            case 7:
               _loc3_ = 0;
               _loc2_ = 1;
               break;
            case 8:
               _loc3_ = 0;
               _loc2_ = 2;
         }
         return this.index(_loc3_,_loc2_);
      }
      
      public function GridIndexToTeamBIndex(param1:uint, param2:uint) : uint
      {
         if(param1 == 5 && param2 == 0)
         {
            return 0;
         }
         if(param1 == 5 && param2 == 1)
         {
            return 1;
         }
         if(param1 == 5 && param2 == 2)
         {
            return 2;
         }
         if(param1 == 6 && param2 == 0)
         {
            return 3;
         }
         if(param1 == 6 && param2 == 1)
         {
            return 4;
         }
         if(param1 == 6 && param2 == 2)
         {
            return 5;
         }
         if(param1 == 7 && param2 == 0)
         {
            return 6;
         }
         if(param1 == 7 && param2 == 1)
         {
            return 7;
         }
         if(param1 == 7 && param2 == 2)
         {
            return 8;
         }
         return 99999;
      }
      
      public function getTeamBPosion(param1:Parallelogram) : uint
      {
         return this.GridIndexToTeamBIndex(param1.xIndex,param1.yIndex);
      }
      
      override public function drawBoard(param1:Parallelogram, param2:Sprite, param3:uint = 0) : void
      {
         var _loc5_:int = 0;
         var _loc6_:Vector.<Point> = param1.drawingLinePointList;
         param2.graphics.lineStyle(1,16777215);
         if(param3 > 0)
         {
            param2.graphics.beginFill(param3);
         }
         param2.graphics.moveTo(_loc6_[0].x,_loc6_[0].y);
         param2.graphics.lineTo(_loc6_[1].x,_loc6_[1].y);
         param2.graphics.lineTo(_loc6_[2].x,_loc6_[2].y);
         param2.graphics.lineTo(_loc6_[3].x,_loc6_[3].y);
         param2.graphics.lineTo(_loc6_[0].x,_loc6_[0].y);
         param2.graphics.endFill();
         param2.graphics.moveTo(0,0);
         param2.graphics.beginFill(param3);
         param2.graphics.drawRect(param1.centerPoint.x,param1.centerPoint.y,1,1);
         param2.graphics.endFill();
         var _loc4_:TextField = new TextField();
         param2.addChild(_loc4_);
         _loc4_.text = "x:" + param1.xIndex + ",y:" + param1.yIndex + ",\n";
         if(param1.teamid <= 1)
         {
            _loc5_ = int(this.getTeamAPosion(param1));
            if(_loc5_ < 9999)
            {
               _loc4_.appendText("index:" + _loc5_);
            }
         }
         else
         {
            _loc5_ = int(this.getTeamBPosion(param1));
            if(_loc5_ < 9999)
            {
               _loc4_.appendText("index:" + _loc5_);
            }
         }
         _loc4_.textColor = 65280;
         _loc4_.x = param1.centerPoint.x;
         _loc4_.y = param1.centerPoint.y;
         _loc4_.mouseEnabled = false;
         _loc4_.mouseWheelEnabled = false;
         _loc4_.selectable = false;
      }
      
      public function getTeamAPosion(param1:Parallelogram) : uint
      {
         return this.gridIndexToTeamAIndex(param1.xIndex,param1.yIndex);
      }
      
      public function gridIndexToTeamAIndex(param1:uint, param2:uint) : uint
      {
         if(param1 == 2 && param2 == 0)
         {
            return 0;
         }
         if(param1 == 2 && param2 == 1)
         {
            return 1;
         }
         if(param1 == 2 && param2 == 2)
         {
            return 2;
         }
         if(param1 == 1 && param2 == 0)
         {
            return 3;
         }
         if(param1 == 1 && param2 == 1)
         {
            return 4;
         }
         if(param1 == 1 && param2 == 2)
         {
            return 5;
         }
         if(param1 == 0 && param2 == 0)
         {
            return 6;
         }
         if(param1 == 0 && param2 == 1)
         {
            return 7;
         }
         if(param1 == 0 && param2 == 2)
         {
            return 8;
         }
         return 99999;
      }
   }
}

