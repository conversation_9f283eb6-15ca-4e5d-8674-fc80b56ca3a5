package display.grid
{
   import flash.display.Sprite;
   import flash.geom.Point;
   import flash.text.TextField;
   import util.AngleAndRadian;
   
   public class Grid
   {
      private static var _indexPoint:Point = new Point();
      
      protected var boardList:Vector.<Parallelogram>;
      
      protected var _xNumber:uint;
      
      protected var _yNumber:uint;
      
      private var _dx:int = 0;
      
      private var _dy:int = 0;
      
      private var _rightCenterPoint:Point;
      
      private var _leftCenterPoint:Point;
      
      private var _deepMax:uint = 180;
      
      private var _standAngle:int = 18;
      
      private var TAN:Number;
      
      private var _angle:uint;
      
      private const boardDefaultWidth:uint = 149;
      
      private const _boardDefaultHeight:uint = 100;
      
      public function Grid()
      {
         super();
         this.boardList = new Vector.<Parallelogram>();
         this.TAN = Math.tan(AngleAndRadian.angleToRadian(this._standAngle));
      }
      
      public function createArea(param1:uint, param2:uint, param3:int = 0, param4:int = 0) : void
      {
         var _loc5_:uint = 0;
         var _loc7_:Point = null;
         var _loc6_:Parallelogram = null;
         var _loc8_:int = 0;
         this._xNumber = param1;
         this._yNumber = param2;
         var _loc9_:Parallelogram = new Parallelogram(new Point(param3,param4),100,"1");
         var _loc10_:* = param3;
         var _loc13_:* = param4;
         var _loc11_:int = 0;
         var _loc12_:uint = 0;
         while(_loc12_ < this._yNumber)
         {
            _loc5_ = 0;
            while(_loc5_ < this._xNumber)
            {
               if(_loc12_ == 0)
               {
                  _loc11_ = 10;
               }
               else if(_loc12_ == 1)
               {
                  _loc11_ = 0;
               }
               else
               {
                  _loc11_ = -10;
               }
               _loc7_ = new Point(_loc10_ + _loc9_.B * _loc5_ + _loc5_ * this._dx,_loc13_ + 100 * _loc12_ + _loc12_ * this._dy);
               _loc6_ = new Parallelogram(_loc7_,100,"a" + _loc12_ * this._xNumber + _loc5_);
               if(_loc5_ <= 3)
               {
                  _loc6_.deep = this._deepMax - _loc12_ * this._xNumber + _loc5_;
               }
               else
               {
                  _loc6_.deep = this._deepMax - _loc12_ * this._xNumber - _loc5_;
               }
               this.boardList.push(_loc6_);
               _loc6_.xIndex = _loc5_;
               _loc6_.yIndex = _loc12_;
               _loc8_ = _loc6_.W - this.TAN * (_loc6_.H / 2 + _loc6_.H * _loc12_) + _loc11_;
               _loc6_.centerPointXFix = _loc8_;
               _loc6_.reset();
               _loc5_++;
            }
            _loc10_ = param3 + this.boardList[_loc12_ * this._xNumber].buttomXStart * (_loc12_ + 1);
            _loc12_++;
         }
         this._rightCenterPoint = this.index(this._xNumber - 2,Math.floor(this._yNumber / 2)).centerPoint;
         this._leftCenterPoint = this.index(1,Math.floor(this._yNumber / 2)).centerPoint;
      }
      
      public function indexByID(param1:String) : Parallelogram
      {
         var _loc2_:uint = 0;
         var _loc3_:uint = this.boardList.length;
         while(_loc2_ < _loc3_)
         {
            if(this.boardList[_loc2_].id == param1)
            {
               return this.boardList[_loc2_];
            }
            _loc2_++;
         }
         return null;
      }
      
      public function index(param1:uint, param2:uint) : Parallelogram
      {
         var _loc3_:Parallelogram = null;
         if(this.hasParallelogram(param1,param2))
         {
            return this.boardList[param2 * this._xNumber + param1];
         }
         return this.boardList[1];
      }
      
      public function indexWithPoint(param1:Point) : Parallelogram
      {
         return this.index(param1.x,param1.y);
      }
      
      public function showGrid(param1:Sprite) : void
      {
         var _loc3_:uint = this.boardList.length;
         var _loc2_:uint = 0;
         while(_loc2_ < _loc3_)
         {
            this.drawBoard(this.boardList[_loc2_],param1);
            _loc2_++;
         }
      }
      
      public function drawBoard(param1:Parallelogram, param2:Sprite, param3:uint = 0) : void
      {
         var _loc5_:Vector.<Point> = param1.drawingLinePointList;
         param2.graphics.lineStyle(1,16777215);
         if(param3 > 0)
         {
            param2.graphics.beginFill(param3);
         }
         param2.graphics.moveTo(_loc5_[0].x,_loc5_[0].y);
         param2.graphics.lineTo(_loc5_[1].x,_loc5_[1].y);
         param2.graphics.lineTo(_loc5_[2].x,_loc5_[2].y);
         param2.graphics.lineTo(_loc5_[3].x,_loc5_[3].y);
         param2.graphics.lineTo(_loc5_[0].x,_loc5_[0].y);
         param2.graphics.endFill();
         param2.graphics.moveTo(0,0);
         param2.graphics.beginFill(param3);
         param2.graphics.drawRect(param1.centerPoint.x,param1.centerPoint.y,1,1);
         param2.graphics.endFill();
         var _loc4_:TextField = new TextField();
         param2.addChild(_loc4_);
         _loc4_.text = "index :" + param1;
         _loc4_.textColor = 65280;
         _loc4_.x = param1.centerPoint.x;
         _loc4_.y = param1.centerPoint.y;
         _loc4_.mouseEnabled = false;
         _loc4_.mouseWheelEnabled = false;
         _loc4_.selectable = false;
      }
      
      public function getLeft(param1:uint, param2:uint) : Parallelogram
      {
         return this.indexWithPoint(this.getLeftIndex(param1,param2));
      }
      
      public function getRight(param1:uint, param2:uint) : Parallelogram
      {
         return this.indexWithPoint(this.getRightIndex(param1,param2));
      }
      
      public function getUp(param1:uint, param2:uint) : Parallelogram
      {
         return this.indexWithPoint(this.getUpIndex(param1,param2));
      }
      
      public function getDown(param1:uint, param2:uint) : Parallelogram
      {
         return this.indexWithPoint(this.getDownIndex(param1,param2));
      }
      
      public function getLeftUp(param1:uint, param2:uint) : Parallelogram
      {
         return this.indexWithPoint(this.getLeftUpIndex(param1,param2));
      }
      
      public function getRightUp(param1:uint, param2:uint) : Parallelogram
      {
         return this.indexWithPoint(this.getRightUpIndex(param1,param2));
      }
      
      public function getRightDown(param1:uint, param2:uint) : Parallelogram
      {
         return this.indexWithPoint(this.getRightDownIndex(param1,param2));
      }
      
      public function getLeftDown(param1:uint, param2:uint) : Parallelogram
      {
         return this.indexWithPoint(this.getLeftDownIndex(param1,param2));
      }
      
      public function getLeftIndex(param1:uint, param2:uint) : Point
      {
         _indexPoint.x = param1 - 1;
         _indexPoint.y = param2;
         return _indexPoint;
      }
      
      public function getRightIndex(param1:uint, param2:uint) : Point
      {
         _indexPoint.x = param1 + 1;
         _indexPoint.y = param2;
         return _indexPoint;
      }
      
      public function getUpIndex(param1:uint, param2:uint) : Point
      {
         _indexPoint.x = param1;
         _indexPoint.y = param2 - 1;
         return _indexPoint;
      }
      
      public function getDownIndex(param1:uint, param2:uint) : Point
      {
         _indexPoint.x = param1;
         _indexPoint.y = param2 + 1;
         return _indexPoint;
      }
      
      public function getLeftUpIndex(param1:uint, param2:uint) : Point
      {
         _indexPoint.x = param1 - 1;
         _indexPoint.y = param2 - 1;
         return _indexPoint;
      }
      
      public function getRightUpIndex(param1:uint, param2:uint) : Point
      {
         _indexPoint.x = param1 + 1;
         _indexPoint.y = param2 - 1;
         return _indexPoint;
      }
      
      public function getRightDownIndex(param1:uint, param2:uint) : Point
      {
         _indexPoint.x = param1 + 1;
         _indexPoint.y = param2 + 1;
         return _indexPoint;
      }
      
      public function getLeftDownIndex(param1:uint, param2:uint) : Point
      {
         _indexPoint.x = param1 - 1;
         _indexPoint.y = param2 + 1;
         return _indexPoint;
      }
      
      public function hasParallelogram(param1:uint, param2:uint) : Boolean
      {
         return param2 * this._xNumber + param1 < this.boardList.length;
      }
      
      public function get xNumber() : uint
      {
         return this._xNumber;
      }
      
      public function get yNumber() : uint
      {
         return this._yNumber;
      }
      
      public function get leftCenterPoint() : Point
      {
         return this._leftCenterPoint;
      }
      
      public function get rightCenterPoint() : Point
      {
         return this._rightCenterPoint;
      }
   }
}

