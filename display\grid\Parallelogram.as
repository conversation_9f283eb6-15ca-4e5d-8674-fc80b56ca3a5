package display.grid
{
   import flash.display.Sprite;
   import flash.geom.Point;
   import util.AngleAndRadian;
   
   public class Parallelogram
   {
      private var _B:uint;
      
      private var _H:uint;
      
      private var _W:uint;
      
      private var _topRightPoint:Point;
      
      private var _topLeftPoint:Point;
      
      private var _buttomLeftPoint:Point;
      
      private var _buttomRightPoint:Point;
      
      private var _centerPoint:Point;
      
      private var _deep:uint;
      
      private var _id:String;
      
      private var _needFlop:Boolean;
      
      private var _playerID:uint;
      
      private var _xIndex:int;
      
      private var _yIndex:int;
      
      private var _centerPointXFix:int;
      
      private var _idInTeam:uint;
      
      private var _deepStart:uint;
      
      private var _childrenNum:uint;
      
      private const _angle:int = 90;
      
      public function Parallelogram(param1:Point, param2:uint, param3:String)
      {
         super();
         this._topLeftPoint = param1;
         this._W = param2 * Math.atan(AngleAndRadian.angleToRadian(90));
         this._H = param2;
         this._id = param3;
         this.resetPlayerData();
         this.reset();
      }
      
      public function get H() : uint
      {
         return this._H;
      }
      
      public function get W() : uint
      {
         return this._W;
      }
      
      public function get centerPointXFix() : int
      {
         return this._centerPointXFix;
      }
      
      public function set centerPointXFix(param1:int) : void
      {
         this._centerPointXFix = param1;
      }
      
      public function reset() : void
      {
         this._buttomRightPoint = new Point(this._topLeftPoint.x + this._W,this._topLeftPoint.y + this._H);
         var _loc1_:Number = Math.tan(AngleAndRadian.angleToRadian(90));
         this._B = this._W - this._H / _loc1_;
         this._buttomLeftPoint = new Point(this._topLeftPoint.x + this._W - this._B,this._topLeftPoint.y + this._H);
         this._topRightPoint = new Point(this._topLeftPoint.x + this._B,this._topLeftPoint.y);
         if(this.teamid == 2)
         {
            this._centerPoint = new Point(this._topLeftPoint.x + this._W - this._centerPointXFix,this._H / 2 + this._topLeftPoint.y);
         }
         else
         {
            this._centerPoint = new Point(this._topLeftPoint.x + this._centerPointXFix,this._H / 2 + this._topLeftPoint.y);
         }
         this.deepStart = 0;
         this.childrenNum = 0;
      }
      
      public function resetPlayerData() : void
      {
         this._playerID = 0;
         this._needFlop = false;
      }
      
      public function get drawingLinePointList() : Vector.<Point>
      {
         var _loc1_:Vector.<Point> = new Vector.<Point>();
         _loc1_.push(this._topLeftPoint);
         _loc1_.push(this._topRightPoint);
         _loc1_.push(this._buttomRightPoint);
         _loc1_.push(this._buttomLeftPoint);
         return _loc1_;
      }
      
      public function drawBoard(param1:Parallelogram, param2:Sprite, param3:uint = 0) : void
      {
         var _loc4_:Vector.<Point> = param1.drawingLinePointList;
         param2.graphics.lineStyle(1,16777215);
         if(param3 > 0)
         {
            param2.graphics.beginFill(param3);
         }
         param2.graphics.moveTo(_loc4_[0].x,_loc4_[0].y);
         param2.graphics.lineTo(_loc4_[1].x,_loc4_[1].y);
         param2.graphics.lineTo(_loc4_[2].x,_loc4_[2].y);
         param2.graphics.lineTo(_loc4_[3].x,_loc4_[3].y);
         param2.graphics.lineTo(_loc4_[0].x,_loc4_[0].y);
         param2.graphics.endFill();
      }
      
      public function get attackerIndex() : Point
      {
         if(this.teamid == 1)
         {
            return new Point(this._xIndex + 1,this._yIndex);
         }
         return new Point(this._xIndex - 1,this._yIndex);
      }
      
      public function get B() : uint
      {
         return this._B;
      }
      
      public function get buttomXStart() : int
      {
         return this._W - this._B;
      }
      
      public function get centerPoint() : Point
      {
         return this._centerPoint.clone();
      }
      
      public function get abseluteCenterPoint() : Point
      {
         return new Point(this._topLeftPoint.x + this._W / 2,this._H / 2 + this._topLeftPoint.y);
      }
      
      public function get OpsiteTeamCenterPotion() : Point
      {
         if(this.teamid == 1)
         {
            return new Point(this._topLeftPoint.x + this._W - this._centerPointXFix,this._H / 2 + this._topLeftPoint.y);
         }
         return new Point(this._topLeftPoint.x + this._centerPointXFix,this._H / 2 + this._topLeftPoint.y);
      }
      
      public function get deep() : uint
      {
         return this._deep;
      }
      
      public function set deep(param1:uint) : void
      {
         this._deep = param1;
      }
      
      public function get id() : String
      {
         return this._id;
      }
      
      public function get needFlop() : Boolean
      {
         return this._needFlop;
      }
      
      public function set needFlop(param1:Boolean) : void
      {
         this._needFlop = param1;
      }
      
      public function get xIndex() : int
      {
         return this._xIndex;
      }
      
      public function set xIndex(param1:int) : void
      {
         this._xIndex = param1;
      }
      
      public function get yIndex() : int
      {
         return this._yIndex;
      }
      
      public function set yIndex(param1:int) : void
      {
         this._yIndex = param1;
      }
      
      public function get playerID() : uint
      {
         return this._playerID;
      }
      
      public function set playerID(param1:uint) : void
      {
         this._playerID = param1;
      }
      
      public function get buttomLeftPoint() : Point
      {
         return this._buttomLeftPoint;
      }
      
      public function get buttomRightPoint() : Point
      {
         return this._buttomRightPoint;
      }
      
      public function get teamid() : int
      {
         if(this.xIndex > 3)
         {
            return 2;
         }
         return 1;
      }
      
      public function get deepStart() : uint
      {
         return this._deepStart;
      }
      
      public function set deepStart(param1:uint) : void
      {
         this._deepStart = param1;
      }
      
      public function get childrenNum() : uint
      {
         return this._childrenNum;
      }
      
      public function set childrenNum(param1:uint) : void
      {
         this._childrenNum = param1;
      }
   }
}

