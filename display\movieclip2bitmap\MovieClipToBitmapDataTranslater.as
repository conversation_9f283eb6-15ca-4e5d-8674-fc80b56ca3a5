package display.movieclip2bitmap
{
   import battleConfig.BattleConfiger;
   import display.BitmapDataListADT;
   import flash.display.BitmapData;
   import flash.display.FrameLabel;
   import flash.display.MovieClip;
   import flash.geom.Matrix;
   import flash.geom.Point;
   import flash.geom.Rectangle;
   import sourceManager.AnimationJumpFrameConfigFile;
   
   public class MovieClipToBitmapDataTranslater
   {
      private static var ignor:uint = 999999;
      
      private static var transMatrix:Matrix = new Matrix();
      
      public function MovieClipToBitmapDataTranslater()
      {
         super();
      }
      
      public static function movieClipToBitmap(param1:MovieClip, param2:AnimationJumpFrameConfigFile = null) : BitmapDataListADT
      {
         var _loc24_:Boolean = false;
         var _loc15_:String = null;
         var _loc4_:int = 0;
         var _loc14_:* = null;
         var _loc6_:String = null;
         var _loc17_:* = null;
         var _loc18_:Rectangle = null;
         var _loc13_:BitmapData = null;
         var _loc5_:FrameLabel = null;
         var _loc21_:Rectangle = null;
         var _loc7_:int = 0;
         var _loc20_:int = 0;
         var _loc19_:int = 0;
         var _loc8_:Number = BattleConfiger.scale;
         transMatrix.a = _loc8_;
         transMatrix.d = _loc8_;
         var _loc23_:BitmapDataListADT = new BitmapDataListADT();
         var _loc10_:uint = uint(param1.totalFrames);
         var _loc25_:uint = 1;
         var _loc11_:Vector.<BitmapData> = new Vector.<BitmapData>();
         var _loc3_:Array = [];
         var _loc16_:Array = [];
         var _loc9_:Array = param1.currentLabels;
         var _loc22_:uint = 0;
         var _loc12_:* = true;
         var _loc26_:uint = 0;
         while(_loc26_ < _loc10_)
         {
            if(_loc9_ && _loc9_.length > 0)
            {
               _loc5_ = _loc9_[_loc22_] as FrameLabel;
               while(_loc5_ && _loc5_.frame == _loc25_)
               {
                  if(_loc25_ <= 1 && _loc5_.name.indexOf("speed") >= 0)
                  {
                     _loc6_ = _loc5_.name;
                  }
                  else if(_loc5_.name && _loc5_.name.indexOf("sound") < 0)
                  {
                     _loc3_.push(_loc25_);
                  }
                  else
                  {
                     _loc16_.push(_loc5_);
                  }
                  _loc22_++;
                  _loc5_ = _loc9_[_loc22_] as FrameLabel;
               }
            }
            param1.gotoAndStop(_loc25_);
            _loc21_ = param1.getBounds(param1);
            if(!param2 || !param2.isGapFrame(_loc25_))
            {
               _loc12_ = !_loc12_;
               if(_loc12_ && false)
               {
                  _loc19_ = -1;
                  _loc23_.copyLast();
               }
               else
               {
                  _loc7_ = Math.ceil(_loc21_.width) * _loc8_;
                  _loc20_ = Math.ceil(_loc21_.height) * _loc8_;
                  if(_loc7_ > 0 && _loc20_ > 0)
                  {
                     _loc13_ = new BitmapData(_loc7_,_loc20_,true,0);
                  }
                  else
                  {
                     _loc13_ = new BitmapData(1,1,true,16777215);
                  }
                  transMatrix.tx = (-_loc21_.x + 2) * _loc8_;
                  transMatrix.ty = (-_loc21_.y + 1) * _loc8_;
                  _loc13_.draw(param1,transMatrix);
                  _loc23_.pushOneBitmap(_loc13_,new Point(_loc21_.x * _loc8_,_loc21_.y * _loc8_));
                  _loc14_ = _loc13_;
                  _loc17_ = _loc21_;
               }
               _loc19_++;
            }
            else
            {
               if(!_loc12_)
               {
                  _loc17_ = _loc21_;
               }
               _loc23_.pushOneBitmap(_loc14_,new Point(_loc17_.x * _loc8_,_loc17_.y * _loc8_));
            }
            _loc25_++;
            _loc26_++;
         }
         _loc23_.speed = _loc6_;
         _loc23_.preEndList = _loc3_;
         _loc23_.soundNameList = _loc16_;
         param1.stop();
         param1.mouseChildren = false;
         param1.mouseEnabled = false;
         return _loc23_;
      }
   }
}

