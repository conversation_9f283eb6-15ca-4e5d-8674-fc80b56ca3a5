package fl.motion
{
   import flash.geom.ColorTransform;
   
   public class Color extends ColorTransform
   {
      private var _tintColor:Number = 0;
      
      private var _tintMultiplier:Number = 0;
      
      public function Color(param1:Number = 1, param2:Number = 1, param3:Number = 1, param4:Number = 1, param5:Number = 0, param6:Number = 0, param7:Number = 0, param8:Number = 0)
      {
         super(param1,param2,param3,param4,param5,param6,param7,param8);
      }
      
      public static function fromXML(param1:XML) : Color
      {
         return Color(new Color().parseXML(param1));
      }
      
      public static function interpolateTransform(param1:ColorTransform, param2:ColorTransform, param3:Number) : ColorTransform
      {
         var _loc5_:Number = 1 - param3;
         return new ColorTransform(param1.redMultiplier * _loc5_ + param2.redMultiplier * param3,param1.greenMultiplier * _loc5_ + param2.greenMultiplier * param3,param1.blueMultiplier * _loc5_ + param2.blueMultiplier * param3,param1.alphaMultiplier * _loc5_ + param2.alphaMultiplier * param3,param1.redOffset * _loc5_ + param2.redOffset * param3,param1.greenOffset * _loc5_ + param2.greenOffset * param3,param1.blueOffset * _loc5_ + param2.blueOffset * param3,param1.alphaOffset * _loc5_ + param2.alphaOffset * param3);
      }
      
      public static function interpolateColor(param1:uint, param2:uint, param3:Number) : uint
      {
         var _loc10_:Number = 1 - param3;
         var _loc13_:uint = uint(param1 >> 24 & 0xFF);
         var _loc14_:uint = uint(param1 >> 16 & 0xFF);
         var _loc17_:uint = uint(param1 >> 8 & 0xFF);
         var _loc15_:uint = uint(param1 & 0xFF);
         var _loc16_:uint = uint(param2 >> 24 & 0xFF);
         var _loc5_:uint = uint(param2 >> 16 & 0xFF);
         var _loc7_:uint = uint(param2 >> 8 & 0xFF);
         var _loc6_:uint = uint(param2 & 0xFF);
         var _loc8_:uint = _loc13_ * _loc10_ + _loc16_ * param3;
         var _loc4_:uint = _loc14_ * _loc10_ + _loc5_ * param3;
         var _loc11_:uint = _loc17_ * _loc10_ + _loc7_ * param3;
         var _loc12_:uint = _loc15_ * _loc10_ + _loc6_ * param3;
         return uint(_loc8_ << 24 | _loc4_ << 16 | _loc11_ << 8 | _loc12_);
      }
      
      public function get brightness() : Number
      {
         return !!this.redOffset ? 1 - this.redMultiplier : this.redMultiplier - 1;
      }
      
      public function set brightness(param1:Number) : void
      {
         if(param1 > 1)
         {
            param1 = 1;
         }
         else if(param1 < -1)
         {
            param1 = -1;
         }
         var _loc3_:Number = 1 - Math.abs(param1);
         var _loc2_:Number = 0;
         if(param1 > 0)
         {
            _loc2_ = param1 * 255;
         }
         this.redMultiplier = this.greenMultiplier = this.blueMultiplier = _loc3_;
         this.redOffset = this.greenOffset = this.blueOffset = _loc2_;
      }
      
      public function setTint(param1:uint, param2:Number) : void
      {
         this._tintColor = param1;
         this._tintMultiplier = param2;
         this.redMultiplier = this.greenMultiplier = this.blueMultiplier = 1 - param2;
         var _loc4_:uint = uint(param1 >> 16 & 0xFF);
         var _loc5_:uint = uint(param1 >> 8 & 0xFF);
         var _loc3_:uint = uint(param1 & 0xFF);
         this.redOffset = Math.round(_loc4_ * param2);
         this.greenOffset = Math.round(_loc5_ * param2);
         this.blueOffset = Math.round(_loc3_ * param2);
      }
      
      public function get tintColor() : uint
      {
         return this._tintColor;
      }
      
      public function set tintColor(param1:uint) : void
      {
         this.setTint(param1,this.tintMultiplier);
      }
      
      private function deriveTintColor() : uint
      {
         var _loc5_:Number = 1 / this.tintMultiplier;
         var _loc4_:uint = Math.round(this.redOffset * _loc5_);
         var _loc2_:uint = Math.round(this.greenOffset * _loc5_);
         var _loc3_:uint = Math.round(this.blueOffset * _loc5_);
         return uint(_loc4_ << 16 | _loc2_ << 8 | _loc3_);
      }
      
      public function get tintMultiplier() : Number
      {
         return this._tintMultiplier;
      }
      
      public function set tintMultiplier(param1:Number) : void
      {
         this.setTint(this.tintColor,param1);
      }
      
      private function parseXML(param1:XML = null) : Color
      {
         var _loc4_:XML = null;
         var _loc2_:String = null;
         var _loc5_:uint = 0;
         if(!param1)
         {
            return this;
         }
         var _loc3_:XML = param1.elements()[0];
         if(!_loc3_)
         {
            return this;
         }
         for each(_loc4_ in _loc3_.attributes())
         {
            _loc2_ = _loc4_.localName();
            if(_loc2_ == "tintColor")
            {
               _loc5_ = Number(_loc4_.toString()) as uint;
               this.tintColor = _loc5_;
            }
            else
            {
               this[_loc2_] = Number(_loc4_.toString());
            }
         }
         return this;
      }
   }
}

