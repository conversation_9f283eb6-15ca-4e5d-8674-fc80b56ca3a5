package formation.mediator
{
   import flash.events.DataEvent;
   import flash.events.Event;
   import formation.view.FormationWindow;
   import formation.view.component.BenchPanel;
   import formation.view.component.FormationGroup;
   import formation.view.component.FormationIconList;
   import formation.view.component.HeroPanel;
   import game.core.scene.GameScene;
   import game.data.MainData;
   import game.data.PirateMediator;
   import game.data.formation.FormationData;
   import game.drag.DragManager;
   import game.modules.formation.commands.FormationCommands;
   import game.modules.guide.manager.GuideManager;
   import game.mvc.AppFacade;
   import mmo.ext.font.TextFormatLib;
   import org.puremvc.as3.interfaces.INotification;
   import util.Globalization;
   
   public class FormationWindowMediator extends PirateMediator
   {
      public static const NAME:String = "FormationWindowMediator";
      
      private var addHeroFormat:int;
      
      public function FormationWindowMediator(param1:Object = null)
      {
         super("FormationWindowMediator",param1);
         FormationWindow(param1).showHander = this.showWin_Handler;
         FormationWindow(param1).closeHander = this.closeWin_Handler;
         FormationIconList(param1.formationsIconList).addEventListener("upFormationGrade",this.upFormationGrade_Handler);
         FormationIconList(param1.formationsIconList).addEventListener("useFormation",this.useFormation_Handler);
         HeroPanel(param1.heroPanel).addEventListener("underHeroFormation",this.underHeroFormation_Handler);
         FormationGroup(param1.formationGroup).addEventListener("underHeroFormation",this.underHeroFormation_Handler);
         FormationGroup(param1.formationGroup).addEventListener("addHeroFormation",this.addHeroToFormation_Handler);
         FormationGroup(param1.formationGroup).addEventListener("updateHeroFormation",this.updateHeroFormation_Handler);
         FormationIconList(param1.formationsIconList).addEventListener("selectedFormationChange",this.selectedFormationChange_Handler);
         FormationIconList(param1.formationsIconList).addEventListener("speedCD",this.SpeedCD_Handler);
         BenchPanel(param1.benchPanel).addEventListener("underHeroFormation",this.underFormation_Handler);
         FormationGroup(param1.formationGroup).addEventListener("delHeroBench",this.delHeroBench_Handler);
         HeroPanel(param1.heroPanel).addEventListener("delHeroBench",this.delHeroBench_Handler);
         BenchPanel(param1.benchPanel).addEventListener("benchHeroFormation",this.underFormation_Handler);
         HeroPanel(param1.heroPanel).addEventListener("underBenchHeroFormation",this.underBenchHeroFormationHandler);
         HeroPanel(param1.heroPanel).addEventListener("startDrag",this.startDragHandler);
         HeroPanel(param1.heroPanel).addEventListener("stopDrag",this.stopDragHandler);
      }
      
      private function delHeroBench_Handler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         BenchPanel(viewComponent.benchPanel).updateBench(0);
      }
      
      private function stopDragHandler(param1:Event) : void
      {
         var _loc3_:int = int(FormationIconList(viewComponent.formationsIconList).selectedFormationID);
         var _loc2_:FormationData = MainData.getInstance().ownFormationsData.getFormationDataByID(_loc3_);
         FormationGroup(viewComponent.formationGroup).setData(_loc2_);
      }
      
      private function startDragHandler(param1:Event) : void
      {
         FormationGroup(viewComponent.formationGroup).setDragState();
      }
      
      private function updateHeroFormation_Handler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         var _loc7_:int = int(FormationIconList(viewComponent.formationsIconList).selectedFormationID);
         var _loc5_:Array = MainData.getInstance().ownFormationsData.getFormationDataByID(_loc7_).heroList.slice();
         var _loc6_:int = int(param1.data.split("_")[0]);
         var _loc2_:int = int(param1.data.split("_")[1]);
         var _loc3_:int = int(_loc5_[_loc6_]);
         var _loc4_:int = int(_loc5_[_loc2_]);
         _loc5_[_loc2_] = _loc3_;
         _loc5_[_loc6_] = _loc4_;
         sendNotification("CS_FORMATION_UPDATE",{
            "fid":_loc7_,
            "formations":_loc5_,
            "benchId":MainData.getInstance().ownFormationsData.benchId[_loc7_]
         },"formation");
      }
      
      private function SpeedCD_Handler(param1:Event) : void
      {
      }
      
      private function selectedFormationChange_Handler(param1:DataEvent) : void
      {
         this.freshFormationDetailInfoLayout(int(param1.data));
         BenchPanel(viewComponent.benchPanel).addSp();
         BenchPanel(viewComponent.benchPanel).fid = int(param1.data);
         var _loc2_:int = int(MainData.getInstance().ownFormationsData.benchId[int(param1.data)]);
         if(_loc2_ == 0)
         {
            BenchPanel(viewComponent.benchPanel).delHeroBench();
         }
         else
         {
            BenchPanel(viewComponent.benchPanel).heroIntoBattle(_loc2_);
         }
      }
      
      private function addHeroToFormation_Handler(param1:DataEvent) : void
      {
         var _loc4_:String = null;
         var _loc8_:int = int(FormationIconList(viewComponent.formationsIconList).selectedFormationID);
         if(_loc8_ == -1)
         {
            _loc8_ = MainData.getInstance().userData.cur_formation;
         }
         var _loc6_:Array = MainData.getInstance().ownFormationsData.getFormationDataByID(_loc8_).heroList.slice();
         var _loc7_:int = int(param1.data.split("_")[0]);
         var _loc2_:int = int(param1.data.split("_")[1]);
         var _loc3_:int = int(MainData.getInstance().ownFormationsData.benchId[_loc8_]);
         if(_loc2_ == _loc3_)
         {
            _loc3_ = 0;
         }
         this.addHeroFormat = int(param1.data.split("_")[1]);
         var _loc5_:int = 0;
         while(_loc5_ < _loc6_.length)
         {
            if(String(_loc6_[_loc5_]) == _loc2_.toString())
            {
               break;
            }
            _loc5_++;
         }
         if(_loc5_ == _loc7_)
         {
            return;
         }
         _loc4_ = _loc6_[_loc7_];
         if(_loc5_ < _loc6_.length)
         {
            _loc6_[_loc5_] = _loc4_;
         }
         else if(this.isUserHero(int(_loc4_)))
         {
            return;
         }
         _loc6_[_loc7_] = _loc2_;
         sendNotification("CS_FORMATION_UPDATE",{
            "fid":_loc8_,
            "formations":_loc6_,
            "benchId":_loc3_
         },"formation");
      }
      
      private function underBenchHeroFormationHandler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         if(this.isUserHero(int(param1.data)))
         {
            return;
         }
         var _loc4_:int = int(FormationIconList(viewComponent.formationsIconList).selectedFormationID);
         var _loc2_:Array = MainData.getInstance().ownFormationsData.getFormationDataByID(_loc4_).heroList.slice();
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_.length)
         {
            if(String(_loc2_[_loc3_]) == int(param1.data).toString())
            {
               _loc2_[_loc3_] = 0;
               break;
            }
            _loc3_++;
         }
         sendNotification("CS_FORMATION_UPDATE",{
            "fid":_loc4_,
            "formations":_loc2_,
            "benchId":0
         },"formation");
      }
      
      private function underHeroFormation_Handler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         if(this.isUserHero(int(param1.data)))
         {
            return;
         }
         var _loc4_:int = int(FormationIconList(viewComponent.formationsIconList).selectedFormationID);
         var _loc2_:Array = MainData.getInstance().ownFormationsData.getFormationDataByID(_loc4_).heroList.slice();
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_.length)
         {
            if(String(_loc2_[_loc3_]) == int(param1.data).toString())
            {
               _loc2_[_loc3_] = 0;
               break;
            }
            _loc3_++;
         }
         sendNotification("CS_FORMATION_UPDATE",{
            "fid":_loc4_,
            "formations":_loc2_,
            "benchId":MainData.getInstance().ownFormationsData.benchId[_loc4_]
         },"formation");
      }
      
      private function underFormation_Handler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         if(this.isUserHero(int(param1.data)))
         {
            return;
         }
         this.heroUpdateData(int(param1.data));
      }
      
      public function heroUpdateData(param1:int) : void
      {
         var _loc4_:int = int(FormationIconList(viewComponent.formationsIconList).selectedFormationID);
         var _loc2_:Array = MainData.getInstance().ownFormationsData.getFormationDataByID(_loc4_).heroList.slice();
         var _loc3_:int = 0;
         while(_loc3_ < _loc2_.length)
         {
            if(String(_loc2_[_loc3_]) == param1.toString())
            {
               _loc2_[_loc3_] = 0;
               break;
            }
            _loc3_++;
         }
         sendNotification("CS_FORMATION_UPDATE",{
            "fid":_loc4_,
            "formations":_loc2_,
            "benchId":param1
         },"formation");
      }
      
      public function isUserHero(param1:int) : Boolean
      {
         var _loc2_:int = MainData.getInstance().groupData.roleModle.hid;
         if(_loc2_ == param1)
         {
            sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("formation.17"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
            return true;
         }
         return false;
      }
      
      private function useFormation_Handler(param1:DataEvent) : void
      {
         var _loc3_:int = int(param1.data);
         var _loc2_:Array = MainData.getInstance().ownFormationsData.getFormationDataByID(_loc3_).heroList.slice();
         sendNotification("CS_FORMATION_USE",{
            "fid":param1.data,
            "formations":_loc2_
         },"formation");
      }
      
      private function upFormationGrade_Handler(param1:DataEvent) : void
      {
         sendNotification("CS_FORMATION_INCREASELV",param1.data,"formation");
      }
      
      override public function onRegister() : void
      {
         if(!facade.hasCommand("CS_FORMATION_INCREASELV"))
         {
            AppFacade.instance.registerMultiCommand(FormationCommands,"CS_FORMATION_INCREASELV","CS_FORMATION_USE","CS_FORMATION_UPDATE","CS_FORMATION_GETCD_ENDTIME");
         }
      }
      
      override public function onRemove() : void
      {
      }
      
      override public function listNotificationInterests() : Array
      {
         return ["SC_FORMATION_UPDATE","SC_FORMATION_INCREASELV"];
      }
      
      override public function handleNotification(param1:INotification) : void
      {
         var _loc2_:int = 0;
         if(param1.getType() != "formation")
         {
            return;
         }
         switch(param1.getName())
         {
            case "SC_FORMATION_UPDATE":
               this.freshFormationDetailInfoLayout(FormationIconList(viewComponent.formationsIconList).selectedFormationID);
               _loc2_ = int(MainData.getInstance().ownFormationsData.benchId[int(param1.getBody())]);
               if(_loc2_ == 0)
               {
                  BenchPanel(viewComponent.benchPanel).delHeroBench();
               }
               else
               {
                  BenchPanel(viewComponent.benchPanel).heroIntoBattle(_loc2_);
               }
               GuideManager.getInstance().nextStep(9,1);
               break;
            case "SC_FORMATION_INCREASELV":
               if(param1.getType() != "formation")
               {
                  return;
               }
               FormationIconList(viewComponent.formationsIconList).formationUpgradeChange();
               this.freshFormationDetailInfoLayout(FormationIconList(viewComponent.formationsIconList).selectedFormationID);
               break;
         }
      }
      
      override protected function dataSource() : Array
      {
         return [MainData.getInstance().userData,MainData.getInstance().groupData,MainData.getInstance().ownFormationsData];
      }
      
      private function closeWin_Handler() : void
      {
         MainData.getInstance().userData.unBindSetter("cur_formation",this.setCurFormation);
         MainData.getInstance().ownFormationsData.unBindSetter("changedFormationData",this.freshFormationHandler);
         GuideManager.getInstance().nextStep(9);
         sendNotification("GUIDE_REMOVEBYMODULE",viewComponent);
         var _loc1_:int = GameScene.getCurrentScene();
         if(_loc1_ == 46)
         {
            AppFacade.instance.sendNotification("sendBullfightarenachangeformation");
         }
      }
      
      private function showWin_Handler(param1:Object = null) : void
      {
         DragManager.setDefaultDropHandler(this.defaultDropHandler);
         checkDataAvialable(this.getData);
         var _loc4_:Boolean = MainData.getInstance().openSwitchData.isAlreadyOpen(38);
         var _loc2_:Boolean = _loc4_ && GuideManager.getInstance().isGuide(9);
         FormationIconList(viewComponent.formationsIconList).upgrade(_loc4_,_loc2_);
         var _loc3_:int = MainData.getInstance().userData.cur_formation;
         BenchPanel(viewComponent.benchPanel).heroIntoBattle(MainData.getInstance().ownFormationsData.benchId[_loc3_]);
      }
      
      private function defaultDropHandler() : void
      {
      }
      
      private function getData() : void
      {
         MainData.getInstance().userData.bindSetter("cur_formation",this.setCurFormation);
         MainData.getInstance().ownFormationsData.bindSetter("changedFormationData",this.freshFormationHandler);
         this.freshHeroGroupData();
         this.freshFormationsData();
         GuideManager.getInstance().getRunningGuide(9,viewComponent);
         if(MainData.getInstance().openSwitchData.isAlreadyOpen(51))
         {
            viewComponent.formationsIconList.showUpStep();
         }
      }
      
      private function freshFormationHandler(param1:Object) : void
      {
         this.freshFormationsData();
      }
      
      private function setCurFormation(param1:int) : void
      {
         FormationIconList(viewComponent.formationsIconList).setCurFormation(param1);
      }
      
      private function freshHeroGroupData() : void
      {
         FormationWindow(viewComponent).setHeroListData(MainData.getInstance().groupData.list);
      }
      
      private function freshFormationsData() : void
      {
         FormationWindow(viewComponent).setFormationListData(MainData.getInstance().ownFormationsData.formationsList);
         this.freshFormationDetailInfoLayout(MainData.getInstance().userData.cur_formation);
      }
      
      private function freshFormationDetailInfoLayout(param1:int) : void
      {
         var _loc2_:FormationData = MainData.getInstance().ownFormationsData.getFormationDataByID(param1);
         if(_loc2_ != null)
         {
            FormationWindow(viewComponent).setCurFormationDetailInfo(_loc2_);
            HeroPanel(viewComponent.heroPanel).addIconForFormationHero(_loc2_.heroList,param1);
         }
      }
   }
}

