package formation.view.component
{
   import flash.events.DataEvent;
   import flash.events.MouseEvent;
   import game.data.MainData;
   import game.data.group.HeroDetailData;
   import game.manager.UIManager;
   import game.manager.XmlManager;
   import game.modules.helpWindow.FormationHelpWindow;
   import game.mvc.AppFacade;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.PopUpCenter;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.slot.PersonSlot;
   import mx.utils.StringUtil;
   import util.Globalization;
   
   public class BenchPanel extends UISprite
   {
      private var pane_bg:UISkin;
      
      private var benchTitle:Label;
      
      public var personHero:PersonSlot;
      
      public var fid:int = 0;
      
      public var help_Btn:Button;
      
      public function BenchPanel()
      {
         super();
         this.pane_bg = UIManager.getUISkin("pane_bg");
         this.pane_bg.setSize(80,92);
         addChild(this.pane_bg);
         this.benchTitle = new Label(Globalization.getString("formation.43"),TextFormatLib.format_0xebce82_12px,[FilterLib.glow_0x272727]);
         this.benchTitle.autoSize = "center";
         this.benchTitle.y = 2;
         this.benchTitle.x = 2;
         this.benchTitle.width = 80;
         this.benchTitle.height = 18;
         addChild(this.benchTitle);
         this.personHero = new PersonSlot("formation_bench");
         this.personHero.x = 15;
         this.personHero.y = 30;
         addChild(this.personHero);
         this.addSp();
         this.help_Btn = new Button(Globalization.getString("Globalization.60"),TextFormatLib.format_0xFFB932_12px_songti,60,UIManager.getMultiUISkin("btn_topMenu"));
         this.help_Btn.setTextOffset(0,-2);
         this.help_Btn.x = 10;
         this.help_Btn.y = 100;
         addChild(this.help_Btn);
         this.help_Btn.addEventListener("click",this.helpHandler);
         this.personHero.addEventListener("addHero",this.addHero_Handler);
         this.personHero.addEventListener("addHeroFormat",this.addHeroFormatHandler);
      }
      
      private function helpHandler(param1:MouseEvent) : void
      {
         var _loc2_:FormationHelpWindow = new FormationHelpWindow();
         PopUpCenter.addPopUp("FormationHelpWindow",_loc2_,true,true);
      }
      
      public function addSp() : void
      {
         var _loc2_:UISprite = null;
         var _loc4_:XML = null;
         var _loc1_:XML = null;
         var _loc3_:UISkin = null;
         _loc2_ = new UISprite();
         addChild(_loc2_);
         _loc4_ = XmlManager.opentermConf.elements().(@type == 58)[0];
         _loc1_ = XmlManager.taskConf[int(_loc4_.@taskid)];
         _loc3_ = UIManager.getUISkin("lock");
         _loc3_.x = 30;
         _loc3_.y = 40;
         _loc3_.visible = false;
         _loc2_.addChild(_loc3_);
         _loc2_.setToolTip(StringUtil.substitute(Globalization.getString("formation.45"),_loc1_.@rLevel,_loc1_.@taskName));
         if(MainData.getInstance().openSwitchData.isAlreadyOpen(58))
         {
            this.personHero.enable = true;
            _loc3_.visible = false;
         }
         else
         {
            _loc3_.visible = true;
            this.personHero.enable = false;
         }
      }
      
      public function addHeroFormatHandler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         if(this.fid == 0)
         {
            this.fid = MainData.getInstance().userData.cur_formation;
         }
         var _loc3_:Array = MainData.getInstance().ownFormationsData.getFormationDataByID(this.fid).heroList.slice();
         var _loc2_:int = 0;
         while(_loc2_ < _loc3_.length)
         {
            if(String(_loc3_[_loc2_]) == param1.data.toString())
            {
               AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
                  "text":Globalization.getString("formation.42"),
                  "textFormat":TextFormatLib.format_0xFF0000_12px
               });
               return;
            }
            _loc2_++;
         }
         this.updateBench(int(param1.data));
      }
      
      public function updateBench(param1:int) : void
      {
         var _loc2_:int = MainData.getInstance().groupData.roleModle.hid;
         if(_loc2_ == param1 && param1 != 0)
         {
            AppFacade.instance.sendNotification("POP_TEXT_TIPS",{
               "text":Globalization.getString("formation.44"),
               "textFormat":TextFormatLib.format_0xFF0000_12px
            });
            return;
         }
         dispatchEvent(new DataEvent("benchHeroFormation",false,false,param1.toString()));
      }
      
      private function addHero_Handler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         this.updateBench(int(param1.data));
      }
      
      public function heroIntoBattle(param1:int) : void
      {
         var _loc2_:HeroDetailData = MainData.getInstance().groupData.indexHeroByid(param1);
         this.personHero.setItem(_loc2_,true,true,true);
      }
      
      public function delHeroBench() : void
      {
         this.personHero.setItem(null,true,true,true);
         this.personHero.clearInfo();
      }
   }
}

