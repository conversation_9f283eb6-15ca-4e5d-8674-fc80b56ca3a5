package formation.view.component
{
   import flash.events.DataEvent;
   import game.data.MainData;
   import game.data.formation.FormationData;
   import game.drag.DragSprite;
   import game.drag.IDropable;
   import game.manager.UIManager;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.slot.FormationPersonSlot;
   
   public class FormationGroup extends UISprite implements IDropable
   {
      public static const space:int = 2;
      
      private var formationSlots:Vector.<FormationPersonSlot>;
      
      private var container:UISprite;
      
      private var bgSkin:UISkin;
      
      private var _width:int = 0;
      
      private var _height:int = 0;
      
      public function FormationGroup()
      {
         super();
         this.bgSkin = UIManager.getUISkin("formation_bg");
         addChild(this.bgSkin);
         this.formationSlots = new Vector.<FormationPersonSlot>();
         this.container = new UISprite();
         this.container.x = 11;
         this.container.y = 4;
         addChild(this.container);
         this.initSlot();
         this.bgSkin.setSize(263,245);
      }
      
      private function initSlot() : void
      {
         var _loc2_:int = 0;
         var _loc1_:FormationPersonSlot = null;
         var _loc3_:int = 2;
         while(_loc3_ >= 0)
         {
            _loc2_ = 0;
            while(_loc2_ < 3)
            {
               _loc1_ = new FormationPersonSlot("formation_hero_layout");
               _loc1_.addEventListener("addHero",this.addHero_Handler);
               _loc1_.addEventListener("updateHeroFormation",this.updateHeroFormationHandler);
               _loc1_.addEventListener("delHeroBench",this.delHeroBenchHandler);
               _loc1_.name = this.formationSlots.length.toString();
               _loc1_.x = _loc3_ * (_loc1_.width + 7);
               _loc1_.y = _loc2_ * (_loc1_.width + 7);
               this.container.addChild(_loc1_);
               this.formationSlots.push(_loc1_);
               _loc2_++;
            }
            _loc3_--;
         }
      }
      
      private function updateHeroFormationHandler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         var _loc2_:int = int(this.formationSlots.indexOf(FormationPersonSlot(param1.currentTarget)));
         dispatchEvent(new DataEvent("updateHeroFormation",false,false,param1.data + "_" + _loc2_));
      }
      
      private function addHero_Handler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         var _loc2_:int = int(this.formationSlots.indexOf(FormationPersonSlot(param1.currentTarget)));
         dispatchEvent(new DataEvent("addHeroFormation",false,false,_loc2_ + "_" + param1.data));
      }
      
      private function delHeroBenchHandler(param1:DataEvent) : void
      {
         param1.stopImmediatePropagation();
         dispatchEvent(new DataEvent("delHeroBench",false,false));
      }
      
      public function setData(param1:FormationData) : void
      {
         var _loc3_:* = 0;
         var _loc6_:Array = param1.openSort;
         var _loc4_:Array = param1.openPositionLv;
         var _loc5_:Array = param1.heroList;
         this.formationSlots.forEach(this.resetFormationSlot);
         var _loc2_:int = 0;
         while(_loc2_ < _loc4_.length)
         {
            _loc3_ = _loc6_[_loc2_];
            if(param1.level < _loc4_[_loc2_])
            {
               this.formationSlots[_loc3_].setState(4);
               this.formationSlots[_loc3_].setUnLockLv(param1.openPositionLv[_loc2_]);
            }
            else
            {
               this.formationSlots[_loc3_].setState(2);
               if(_loc5_[_loc3_] != 0)
               {
                  this.formationSlots[_loc3_].setItem(MainData.getInstance().groupData.getHeroDataByHeroID(_loc5_[_loc3_]),true,true,true);
               }
            }
            _loc2_++;
         }
         this.setSorts(_loc5_);
      }
      
      public function setDragState() : void
      {
         var _loc1_:uint = 0;
         while(_loc1_ < this.formationSlots.length)
         {
            this.formationSlots[_loc1_].setDraging();
            _loc1_++;
         }
      }
      
      private function setSorts(param1:Array) : void
      {
         var _loc3_:uint = 1;
         var _loc2_:uint = 0;
         while(_loc2_ < param1.length)
         {
            if(param1[_loc2_] != 0)
            {
               this.formationSlots[_loc2_].setSort(_loc3_);
               _loc3_++;
            }
            _loc2_++;
         }
      }
      
      private function resetFormationSlot(param1:FormationPersonSlot, param2:int, param3:Vector.<FormationPersonSlot>) : void
      {
         param1.clearInfo();
         param1.setState(1);
      }
      
      public function dragHandler(param1:String, param2:DragSprite, param3:IDropable) : void
      {
         var _loc4_:* = param1;
         if("dragDrop" === _loc4_)
         {
            param2.sourceSp.dragOver();
            param2.dragOver();
         }
      }
   }
}

