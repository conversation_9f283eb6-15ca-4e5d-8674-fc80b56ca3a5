package formation.view.component
{
   import flash.display.Bitmap;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.DataEvent;
   import flash.events.MouseEvent;
   import flash.utils.Timer;
   import formation.view.mc.FormationIconMC;
   import game.data.MainData;
   import game.data.formation.FormationData;
   import game.data.formation.FormationManager;
   import game.events.PageNavigatorEvent;
   import game.manager.AssetManager;
   import game.manager.UIManager;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mx.utils.StringUtil;
   import util.Globalization;
   import util.openModule;
   
   public class FormationIconList extends UISprite
   {
      private var iconMC:FormationIconMC;
      
      private var rowMaxChildrenNumber:int = 4;
      
      private var lineMaxChildrenNumber:int = 2;
      
      private var selectedIcon:Bitmap;
      
      private var _selectedFormationID:int = -1;
      
      private var _formations:Array;
      
      private var _showF_upStep:Boolean;
      
      private var timer:Timer;
      
      public function FormationIconList(param1:int = 125, param2:int = 288, param3:UISkin = null, param4:Boolean = false)
      {
         super();
         this._showF_upStep = param4;
         this.iconMC = new FormationIconMC(param1,param2,param3,this._showF_upStep);
         addChild(this.iconMC);
         this.selectedIcon = new Bitmap();
         this.selectedIcon.x = 36;
         this.iconMC.iconList.rowMaxChildrenNumber = this.rowMaxChildrenNumber;
         this.iconMC.iconList.lineMaxChildrenNumber = this.lineMaxChildrenNumber;
         this.iconMC.useFormationBtn.addEventListener("click",this.useFormation_Handler);
         this.iconMC.increaseLvBtn.enabled = false;
         this.iconMC.increaseLvBtn.addEventListener("click",this.upFormationLevel_Handler);
         this.iconMC.upStepBtn.addEventListener("click",this.upFormationStep_Handler);
         this.iconMC.btn_page.addEventListener("pageChange",this.changePageHandler);
      }
      
      private function upFormationStep_Handler(param1:MouseEvent) : void
      {
         openModule("FormationUpdateQualityWin");
      }
      
      public function updateIconListRowNumLineNum(param1:int, param2:int, param3:int, param4:int, param5:int, param6:int) : void
      {
         this.iconMC.iconList.rowMaxChildrenNumber = param1;
         this.iconMC.iconList.lineMaxChildrenNumber = param2;
         this.iconMC.iconList.lineSpace = param3;
         this.iconMC.iconList.rowSpace = param4;
         this.iconMC.iconList.y = param6;
         this.iconMC.iconList.x = param5;
      }
      
      public function hidePageInfo() : void
      {
         this.iconMC.useFormationBtn.visible = false;
         this.iconMC.increaseLvBtn.visible = false;
         this.iconMC.btn_page.visible = false;
         this.iconMC.nextOpen_txt.visible = false;
         this.iconMC.noticeTxt.visible = false;
      }
      
      private function changePageHandler(param1:PageNavigatorEvent) : void
      {
         this.updateFormationList(this._formations);
      }
      
      public function upgrade(param1:Boolean, param2:Boolean) : void
      {
         var mc:MovieClip = null;
         var value:Boolean = false;
         mc = null;
         value = param1;
         var showEffect:Boolean = param2;
         if(showEffect)
         {
            mc = AssetManager.getMc("ButtonPlay_MC");
            mc.x = this.iconMC.increaseLvBtn.x + this.iconMC.increaseLvBtn.width / 2;
            mc.y = this.iconMC.increaseLvBtn.y + this.iconMC.increaseLvBtn.height / 2;
            mc.play();
            this.iconMC.addChild(mc);
            mc.addFrameScript(mc.totalFrames - 1,function():void
            {
               mc.stop();
               mc.parent && mc.parent.removeChild(mc);
               iconMC.increaseLvBtn.visible = value;
               iconMC.increaseLvBtn.enabled = value;
            });
         }
         else
         {
            this.iconMC.increaseLvBtn.visible = value;
            this.iconMC.increaseLvBtn.enabled = value;
         }
      }
      
      private function upFormationLevel_Handler(param1:MouseEvent) : void
      {
         dispatchEvent(new DataEvent("upFormationGrade",false,false,this.selectedFormationID.toString()));
      }
      
      private function useFormation_Handler(param1:MouseEvent) : void
      {
         dispatchEvent(new DataEvent("useFormation",false,false,this.selectedFormationID.toString()));
      }
      
      public function get selectedFormationID() : int
      {
         return this._selectedFormationID;
      }
      
      private function get curFormation() : int
      {
         return MainData.getInstance().userData.cur_formation;
      }
      
      public function setData(param1:Array) : void
      {
         this._formations = param1;
         this._selectedFormationID = this.curFormation;
         var _loc2_:int = Math.ceil(this._formations.length / (this.rowMaxChildrenNumber * this.lineMaxChildrenNumber));
         this.iconMC.btn_page.init(1,_loc2_);
         if(_loc2_ == 1)
         {
            this.iconMC.btn_page.visible = false;
         }
         else
         {
            this.iconMC.btn_page.visible = true;
         }
         this.updateFormationList(this._formations);
         this.showNeedExperence(MainData.getInstance().ownFormationsData.getFormationDataByID(this.selectedFormationID));
      }
      
      private function updateFormationList(param1:Array) : void
      {
         var _loc3_:FormationData = null;
         var _loc6_:FormtionIcon = null;
         var _loc5_:Sprite = null;
         var _loc4_:UISkin = null;
         this.iconMC.iconList.clearAllChild(this.iconMC.iconList);
         var _loc9_:int = 0;
         var _loc7_:uint = uint((this.iconMC.btn_page.currentPage - 1) * this.rowMaxChildrenNumber * this.lineMaxChildrenNumber);
         var _loc8_:int = this.iconMC.btn_page.currentPage * this.rowMaxChildrenNumber * this.lineMaxChildrenNumber;
         while(_loc7_ < _loc8_)
         {
            _loc3_ = param1[_loc7_];
            if(_loc3_ != null)
            {
               _loc9_++;
               _loc6_ = new FormtionIcon();
               _loc6_.setIconData(_loc3_.getSortsbyCurrentLv(),_loc3_);
               this._showF_upStep && _loc6_.addValue(StringUtil.substitute(Globalization.getString("formation.24"),_loc3_.name + _loc3_.step));
               this.setIcon(_loc6_,_loc3_.formationID);
            }
            _loc7_++;
         }
         var _loc2_:int = FormationManager.getNextFormationOpenLvByFormationNum(param1.length);
         if(_loc2_ == 0)
         {
            this.iconMC.nextOpen_txt.visible = false;
         }
         else
         {
            this.iconMC.nextOpen_txt.visible = !this._showF_upStep;
            this.iconMC.nextOpen_txt.text = StringUtil.substitute(Globalization.getString("formation.15"),_loc2_);
         }
         if(_loc2_ != 0 && this.iconMC.btn_page.currentPage == this.iconMC.btn_page.totalPage && !this._showF_upStep)
         {
            _loc5_ = new Sprite();
            _loc4_ = UIManager.getUISkin("unKnow_formation");
            _loc4_.x = 4;
            _loc4_.y = 4;
            _loc5_.addChild(_loc4_);
            this.iconMC.iconList.addChild(_loc5_);
         }
      }
      
      public function setCurFormation(param1:int) : void
      {
         if(this.iconMC.iconList.numChildren == 0)
         {
            return;
         }
         this.setSelectFormationFilter(param1);
         this.addCurFormationIcon(param1);
         this._selectedFormationID = param1;
         this.iconMC.useFormationBtn.visible = false;
         this.showNeedExperence(MainData.getInstance().ownFormationsData.getFormationDataByID(this.selectedFormationID));
      }
      
      private function addCurFormationIcon(param1:int) : void
      {
         if(this.selectedIcon.parent)
         {
            this.selectedIcon.parent.removeChild(this.selectedIcon);
         }
         this.selectedIcon.bitmapData = UIManager.getUISkin("selected_bj").bitmapData;
         var _loc2_:UISprite = this.iconMC.iconList.getChildByName(param1.toString()) as UISprite;
         _loc2_ && _loc2_.addChild(this.selectedIcon);
      }
      
      private function setSelectFormationFilter(param1:int) : void
      {
         var _loc3_:FormtionIcon = this.iconMC.iconList.getChildByName(this.selectedFormationID.toString()) as FormtionIcon;
         var _loc2_:FormtionIcon = this.iconMC.iconList.getChildByName(param1.toString()) as FormtionIcon;
         _loc3_ && (_loc3_.selected = false);
         _loc2_ && (_loc2_.selected = true);
         if(param1 == this.curFormation)
         {
            this.iconMC.useFormationBtn.visible = false;
         }
         else
         {
            this.iconMC.useFormationBtn.visible = !this._showF_upStep;
         }
      }
      
      private function showNeedExperence(param1:FormationData) : void
      {
         var _loc3_:int = FormationManager.getFormationCostByID(param1.upGradeTableID).getNeedExperenceByFormationLv(param1.level + 1);
         var _loc2_:int = FormationManager.getFormationCostByID(param1.upGradeTableID).getNeedUserLevelByFormationLv(param1.level + 1);
         if(_loc3_ >= 0)
         {
            this.iconMC.noticeTxt.visible = !this._showF_upStep;
            this.iconMC.increaseLvBtn.enabled && (this.iconMC.increaseLvBtn.visible = true);
            this.iconMC.noticeTxt.text = StringUtil.substitute(Globalization.getString("formation.16"),param1.level + 1,_loc2_,_loc3_);
         }
         else
         {
            this.iconMC.noticeTxt.visible = false;
            this.iconMC.increaseLvBtn.visible = false;
         }
      }
      
      public function formationUpgradeChange() : void
      {
         var _loc1_:FormationData = null;
         if(this.selectedFormationID > 0)
         {
            _loc1_ = MainData.getInstance().ownFormationsData.getFormationDataByID(this.selectedFormationID);
            this.showNeedExperence(_loc1_);
            FormtionIcon(this.iconMC.iconList.getChildByName(this.selectedFormationID.toString())).setIconData(_loc1_.getSortsbyCurrentLv(),_loc1_);
         }
      }
      
      public function showUpStep() : void
      {
         this.iconMC.upStepBtn.visible = true;
      }
      
      private function setIcon(param1:UISprite, param2:int = 0) : void
      {
         var _loc3_:FormationData = null;
         if(param2 != 0)
         {
            param1.name = param2.toString();
            param1.buttonMode = true;
            param1.addEventListener("click",this.clickHandler);
            _loc3_ = FormationManager.getFormationBaseInfoByID(param2);
         }
         this.iconMC.iconList.addChild(param1);
         if(this.selectedFormationID == param2)
         {
            this.setSelectFormationFilter(param2);
         }
         if(this.curFormation == param2)
         {
            this.addCurFormationIcon(param2);
         }
      }
      
      private function clickHandler(param1:MouseEvent) : void
      {
         param1.stopImmediatePropagation();
         var _loc3_:int = int(param1.currentTarget.name);
         this.setSelectFormationFilter(_loc3_);
         this._selectedFormationID = _loc3_;
         dispatchEvent(new DataEvent("selectedFormationChange",false,false,this._selectedFormationID.toString()));
         var _loc2_:FormationData = MainData.getInstance().ownFormationsData.getFormationDataByID(this.selectedFormationID);
         this.showNeedExperence(_loc2_);
      }
      
      public function freshStepInfo(param1:int) : void
      {
         var _loc3_:FormtionIcon = this.iconMC.iconList.getChildByName(param1.toString()) as FormtionIcon;
         var _loc2_:FormationData = MainData.getInstance().ownFormationsData.getFormationDataByID(this.selectedFormationID);
         _loc3_ && _loc3_.addValue(StringUtil.substitute(Globalization.getString("formation.24"),_loc2_.name + _loc2_.step));
      }
   }
}

