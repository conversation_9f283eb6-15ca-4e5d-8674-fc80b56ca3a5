package formation.view.component
{
   import formation.view.mc.FormationInfoMC;
   import game.data.formation.FormationData;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   
   public class FormationInfo extends UISprite
   {
      private var descTxt:Label;
      
      private var notice:Label;
      
      private var formationMC:FormationInfoMC;
      
      public function FormationInfo()
      {
         super();
         this.formationMC = new FormationInfoMC();
         addChild(this.formationMC);
      }
      
      public function setData(param1:FormationData) : void
      {
         this.formationMC.name_txt.text = param1.name;
         this.formationMC.level_txt.text = param1.level.toString();
         this.formationMC.ability_desc.text = param1.desc;
         this.formationMC.setSkill(param1.<PERSON><PERSON>,param1.heroList);
      }
   }
}

