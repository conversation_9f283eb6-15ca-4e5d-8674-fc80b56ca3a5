package formation.view.component
{
   import flash.events.MouseEvent;
   import flash.geom.Point;
   import game.data.formation.FormationData;
   import game.manager.UIManager;
   import game.modules.formation.FormationToolTip;
   import mmo.Core;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.tab.TabButton;
   import mmo.utils.Guid;
   import util.createLabel;
   
   public class FormtionIcon extends UISprite
   {
      private var iconBox:UISprite;
      
      private var bg:TabButton;
      
      private var info:FormationToolTip;
      
      private var _formationDt:FormationData;
      
      private var txt_value:Label;
      
      public function FormtionIcon(param1:Vector.<UISkin> = null)
      {
         super();
         this.bg = new TabButton("",!!param1 ? param1 : UIManager.getMultiUISkin("formationIcon_bg"),Guid.getUID());
         addChild(this.bg);
         this.bg.setSize(38,38);
         this.iconBox = new UISprite();
         this.iconBox.x = 37;
         this.iconBox.y = 8;
         addChild(this.iconBox);
         addEventListener("rollOut",this.rollOutHandler);
         addEventListener("rollOver",this.rollOverHandler);
      }
      
      private function rollOverHandler(param1:MouseEvent) : void
      {
         if(!this._formationDt)
         {
            return;
         }
         this.info = new FormationToolTip();
         this.info.show(this._formationDt);
         var _loc2_:Point = parent.localToGlobal(new Point(this.x + 45,this.y + 15));
         this.info.x = _loc2_.x;
         this.info.y = _loc2_.y;
         Core.stg.addChild(this.info);
      }
      
      private function rollOutHandler(param1:MouseEvent) : void
      {
         this.info && this.info.parent && this.info.parent.removeChild(this.info);
         this.info && this.info.dispose();
         this.info = null;
      }
      
      public function setIconData(param1:Array, param2:FormationData) : void
      {
         var _loc4_:UISkin = null;
         this.iconBox.dispose();
         this._formationDt = param2;
         var _loc3_:int = 0;
         while(_loc3_ < 9)
         {
            if(param1.indexOf(_loc3_.toString()) != -1)
            {
               _loc4_ = UIManager.getUISkin("formation_open");
               _loc4_.x = 9 * (_loc3_ % 3);
               _loc4_.y = 9 * (int(_loc3_ / 3));
               this.iconBox.addChild(_loc4_);
            }
            _loc3_++;
         }
         this.iconBox.rotation = 90;
      }
      
      public function addValue(param1:String) : void
      {
         this.txt_value ||= createLabel("",-10,40,64,null,"center");
         this.txt_value.text = param1;
         if(!this.txt_value.parent)
         {
            addChild(this.txt_value);
         }
      }
      
      override public function get width() : Number
      {
         return 38;
      }
      
      override public function get height() : Number
      {
         return 38;
      }
      
      public function set selected(param1:Boolean) : void
      {
         this.bg.isCheck = param1;
      }
   }
}

