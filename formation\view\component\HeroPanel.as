package formation.view.component
{
   import flash.display.Bitmap;
   import flash.events.DataEvent;
   import flash.events.Event;
   import formation.view.mc.HeroPanelMC;
   import game.data.MainData;
   import game.data.group.HeroData;
   import game.drag.DragSprite;
   import game.drag.IDropable;
   import game.events.PageNavigatorEvent;
   import game.manager.UIManager;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.slot.PersonSlot;
   
   public class HeroPanel extends UISprite implements IDropable
   {
      private var space:int = 3;
      
      private var icons:Array;
      
      private var heroMC:HeroPanelMC;
      
      private var _herosData:Array;
      
      private const PAGENUM:int = 10;
      
      private var upFormationHeros:Array;
      
      private var fid:int;
      
      public function HeroPanel()
      {
         super();
         this.heroMC = new HeroPanelMC();
         addChild(this.heroMC);
         this.icons = [];
         this.heroMC.btn_page.addEventListener("pageChange",this.changePageHandler);
      }
      
      private function changePageHandler(param1:PageNavigatorEvent) : void
      {
         this.freshData(this._herosData);
      }
      
      public function setData(param1:Array) : void
      {
         this.heroMC.heroList.dispose();
         this.icons = [];
         this._herosData = param1;
         this.heroMC.btn_page.init(1,Math.ceil(param1.length / 10));
         this.freshData(param1);
      }
      
      private function freshData(param1:Array) : void
      {
         var _loc2_:PersonSlot = null;
         this.heroMC.heroList.dispose();
         var _loc5_:int = int(this.heroMC.btn_page.currentPage);
         var _loc3_:int = (_loc5_ - 1) * 10;
         var _loc4_:int = _loc5_ * 10;
         while(_loc3_ < _loc4_)
         {
            if(param1[_loc3_])
            {
               _loc2_ = new PersonSlot("formation_herolist");
               _loc2_.name = "hero_" + HeroData(param1[_loc3_]).hid;
               _loc2_.setItem(param1[_loc3_],true,true,true);
               _loc2_.enable = true;
               _loc2_.setSize(50,50);
               _loc2_.addEventListener("startDrag",this.startDragHandler);
               _loc2_.addEventListener("stopDrag",this.stopDragHandler);
               this.heroMC.heroList.addChild(_loc2_);
            }
            _loc3_++;
         }
         this.upFormationHeros && this.addIconForFormationHero(this.upFormationHeros,this.fid);
      }
      
      private function stopDragHandler(param1:Event) : void
      {
         dispatchEvent(param1);
      }
      
      private function startDragHandler(param1:Event) : void
      {
         dispatchEvent(param1);
      }
      
      public function addIconForFormationHero(param1:Array, param2:int) : void
      {
         var _loc4_:Bitmap = null;
         this.upFormationHeros = param1;
         param2 = param2;
         this.deleteHerosIcon();
         var _loc5_:uint = 0;
         while(_loc5_ < param1.length)
         {
            if(this.heroMC.heroList.getChildByName("hero_" + param1[_loc5_]))
            {
               _loc4_ = new Bitmap(UIManager.getUISkin("bj_2").bitmapData.clone());
               _loc4_.x = 30;
               UISprite(this.heroMC.heroList.getChildByName("hero_" + param1[_loc5_])).addChild(_loc4_);
               this.icons.push(_loc4_);
            }
            _loc5_++;
         }
         var _loc3_:int = int(MainData.getInstance().ownFormationsData.benchId[param2]);
         if(this.heroMC.heroList.getChildByName("hero_" + _loc3_.toString()))
         {
            _loc4_ = new Bitmap(UIManager.getUISkin("substitution").bitmapData.clone());
            _loc4_.x = 30;
            UISprite(this.heroMC.heroList.getChildByName("hero_" + _loc3_.toString())).addChild(_loc4_);
            this.icons.push(_loc4_);
         }
      }
      
      private function deleteHerosIcon() : void
      {
         var _loc1_:Bitmap = null;
         while(this.icons.length > 0)
         {
            _loc1_ = this.icons.pop();
            _loc1_.parent && _loc1_.parent.removeChild(_loc1_);
         }
      }
      
      public function dragHandler(param1:String, param2:DragSprite, param3:IDropable) : void
      {
         var _loc4_:Object = param2.sourceSp.getData();
         var _loc5_:* = param1;
         if("dragDrop" === _loc5_)
         {
            if(_loc4_.type == "formation_hero_layout")
            {
               dispatchEvent(new DataEvent("underHeroFormation",false,false,HeroData(_loc4_.hero).hid.toString()));
            }
            param2.sourceSp.dragOver();
            param2.dragOver();
         }
      }
   }
}

