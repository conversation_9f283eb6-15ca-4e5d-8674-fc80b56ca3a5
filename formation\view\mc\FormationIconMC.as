package formation.view.mc
{
   import flash.display.Sprite;
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.button.Button;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   import util.Globalization;
   
   public class FormationIconMC extends Sprite
   {
      public var iconList:UIBox;
      
      public var nextOpen_txt:Label;
      
      public var btn_page:PageNavigator;
      
      public var increaseLvBtn:Button;
      
      public var useFormationBtn:Button;
      
      public var noticeTxt:Label;
      
      public var upStepBtn:Button;
      
      public function FormationIconMC(param1:Number, param2:Number, param3:UISkin, param4:Boolean)
      {
         super();
         if(!param3)
         {
            param3 = UIManager.getUISkin("formation_bg");
         }
         param3.setSize(param1,param2);
         addChild(param3);
         this.iconList = new UIBox();
         this.iconList.x = 4;
         this.iconList.y = 12;
         this.iconList.leftSpace = 4;
         this.iconList.topSpace = 4;
         this.iconList.lineSpace = 16;
         this.iconList.rowSpace = 16;
         addChild(this.iconList);
         this.nextOpen_txt = new Label("",TextFormatLib.format_0x00A8FF_12px,[FilterLib.glow_0x272727]);
         this.nextOpen_txt.multiline = true;
         this.nextOpen_txt.wordWrap = true;
         this.nextOpen_txt.x = 6;
         this.nextOpen_txt.y = 220;
         this.nextOpen_txt.width = 112;
         this.nextOpen_txt.height = 38;
         addChild(this.nextOpen_txt);
         this.btn_page = new PageNavigator();
         this.btn_page.x = 6;
         this.btn_page.y = 260;
         addChild(this.btn_page);
         this.useFormationBtn = new Button(Globalization.getString("formation.2"),TextFormatLib.format_0xFFB932_12px,70,UIManager.getMultiUISkin("btn_topMenu"));
         this.useFormationBtn.setTextOffset(0,-1);
         this.useFormationBtn.x = 28;
         this.useFormationBtn.y = 294;
         addChild(this.useFormationBtn);
         this.increaseLvBtn = new Button(Globalization.getString("formation.1"),TextFormatLib.format_0xFFB932_12px,70,UIManager.getMultiUISkin("btn_topMenu"));
         this.increaseLvBtn.setTextOffset(0,-1);
         this.increaseLvBtn.x = 28;
         this.increaseLvBtn.y = 324;
         addChild(this.increaseLvBtn);
         this.upStepBtn = new Button(Globalization.getString("formation.22"),TextFormatLib.format_0xFFB932_12px,70,UIManager.getMultiUISkin("btn_topMenu"));
         this.upStepBtn.setTextOffset(0,-1);
         this.upStepBtn.x = 28;
         this.upStepBtn.y = 250;
         this.upStepBtn.visible = false;
         addChild(this.upStepBtn);
         var _loc5_:UISkin = UIManager.getUISkin("line2");
         _loc5_.width = 280;
         _loc5_.x = -270;
         _loc5_.y = 314;
         param4 || addChild(_loc5_);
         this.noticeTxt = new Label("",TextFormatLib.format_0x00A8FF_12px,[FilterLib.glow_0x272727]);
         this.noticeTxt.x = -265;
         this.noticeTxt.y = 330;
         addChild(this.noticeTxt);
      }
   }
}

