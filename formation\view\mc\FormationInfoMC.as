package formation.view.mc
{
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextFormat;
   import game.data.MainData;
   import game.data.skill.SkillAndBuffXMLManager;
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   import util.Globalization;
   
   public class FormationInfoMC extends Sprite
   {
      private var tooltip:Sprite = new Sprite();
      
      public var name_label:Label;
      
      public var skill1_label:Label;
      
      public var skill2_label:Label;
      
      public var skill3_label:Label;
      
      public var skill4_label:Label;
      
      public var upValue_label:Label;
      
      public var level_label:Label;
      
      public var friend_label:Label;
      
      public var experence_label:Label;
      
      public var name_txt:Label;
      
      public var level_txt:Label;
      
      public var experence_txt:Label;
      
      public var ability_desc:Label;
      
      public var desc:Label;
      
      public var noteinfo:Label = new Label("",TextFormatLib.format_0x00FF00_12px,[FilterLib.glow_0x272727]);
      
      public function FormationInfoMC()
      {
         super();
         this.createBgSkin(6,0);
         this.createBgSkin(134,0);
         this.name_label = this.createLabel(Globalization.getString("formation.3"),6,0,60,"center",TextFormatLib.format_0xFFB932_12px);
         addChild(this.name_label);
         this.level_label = this.createLabel(Globalization.getString("formation.4"),134,0,60,"center",TextFormatLib.format_0xFFB932_12px);
         addChild(this.level_label);
         this.name_txt = this.createLabel("",65,0,60,"center",TextFormatLib.format_0xFFF5CE_12px);
         addChild(this.name_txt);
         this.level_txt = this.createLabel("",193,0,60,"center",TextFormatLib.format_0xFFF5CE_12px);
         addChild(this.level_txt);
         this.ability_desc = this.createLabel("",6,67,240,"left",TextFormatLib.format_0xFFED89_12px);
         addChild(this.ability_desc);
         noteinfo.width = 100;
         noteinfo.wordWrap = true;
         noteinfo.background = true;
         noteinfo.backgroundColor = 4473924;
         noteinfo.alpha = 0.8;
      }
      
      public function showTips(param1:String, param2:int, param3:int) : void
      {
         if(!noteinfo.parent)
         {
            this.addChild(noteinfo);
         }
         noteinfo.text = param1;
         noteinfo.x = param2 + 4;
         noteinfo.y = param3 + 20;
      }
      
      public function hiddTips() : void
      {
         if(noteinfo.parent)
         {
            noteinfo.parent.removeChild(noteinfo);
         }
      }
      
      public function setSkill(param1:Array, param2:Array) : void
      {
         var _loc15_:Array = null;
         var _loc6_:int = 0;
         var _loc5_:int = 0;
         var _loc12_:Array = null;
         var _loc3_:int = 0;
         var _loc11_:Array = null;
         var _loc7_:int = 0;
         var _loc14_:Array = null;
         var _loc13_:int = 0;
         var _loc16_:Array = [];
         var _loc8_:Array = [];
         var _loc4_:Array = [];
         for each(var _loc10_ in param2)
         {
            if(_loc10_ > 0)
            {
               _loc16_.push(MainData.getInstance().groupData.getHeroDataByHeroID(_loc10_).htid);
               _loc8_.push(MainData.getInstance().groupData.getHeroDataByHeroID(_loc10_).name);
            }
         }
         for each(var _loc9_ in param1)
         {
            _loc15_ = _loc9_.split("|");
            if(_loc16_.indexOf(int(_loc15_[0])) > 0 && _loc16_.indexOf(int(_loc15_[1])) > 0)
            {
               _loc6_ = int(_loc16_.indexOf(int(_loc15_[0])));
               _loc5_ = int(_loc16_.indexOf(int(_loc15_[1])));
               _loc4_.push(_loc8_[_loc6_] + "|" + _loc8_[_loc5_] + "|" + _loc15_[2]);
            }
         }
         if(this.skill1_label && this.skill1_label.parent)
         {
            this.skill1_label.parent.removeChild(this.skill1_label);
         }
         if(this.skill2_label && this.skill2_label.parent)
         {
            this.skill2_label.parent.removeChild(this.skill2_label);
         }
         if(this.skill3_label && this.skill3_label.parent)
         {
            this.skill3_label.parent.removeChild(this.skill3_label);
         }
         if(_loc4_[0])
         {
            _loc12_ = _loc4_[0].split("|");
            _loc3_ = int(_loc12_[2]);
            this.skill1_label = this.createLabel("伙伴 " + _loc12_[0] + " 和 " + _loc12_[1] + " 激活技能[" + SkillAndBuffXMLManager.indexSkillName(_loc3_) + "]",6,17,120,"left",TextFormatLib.format_0xFFED89_12px);
            this.skill1_label.name = _loc3_ + "";
            this.skill1_label.addEventListener("rollOver",this.onItemOver);
            this.skill1_label.addEventListener("rollOut",this.onItemOver2);
            addChild(this.skill1_label);
         }
         else
         {
            this.skill1_label = this.createLabel("位置1伙伴羁绊-[未激活]",6,17,120,"left",TextFormatLib.grey_12px);
            addChild(this.skill1_label);
         }
         if(_loc4_[1])
         {
            _loc11_ = _loc4_[1].split("|");
            _loc7_ = int(_loc12_[2]);
            this.skill2_label = this.createLabel("伙伴 " + _loc11_[0] + " 和 " + _loc11_[1] + " 激活技能[" + SkillAndBuffXMLManager.indexSkillName(_loc3_) + "]",6,33,120,"left",TextFormatLib.format_0xFFED89_12px);
            this.skill2_label.name = _loc7_ + "";
            this.skill2_label.addEventListener("rollOver",this.onItemOver);
            this.skill2_label.addEventListener("rollOut",this.onItemOver2);
            addChild(this.skill2_label);
         }
         else
         {
            this.skill2_label = this.createLabel("位置2伙伴羁绊-[未激活]",6,33,120,"left",TextFormatLib.grey_12px);
            addChild(this.skill2_label);
         }
         if(_loc4_[2])
         {
            _loc14_ = _loc4_[2].split("|");
            _loc13_ = int(_loc12_[2]);
            this.skill3_label = this.createLabel("伙伴 " + _loc14_[0] + " 和 " + _loc14_[1] + " 激活技能[" + SkillAndBuffXMLManager.indexSkillName(_loc3_) + "]",6,49,120,"left",TextFormatLib.format_0xFFED89_12px);
            this.skill3_label.name = _loc13_ + "";
            this.skill3_label.addEventListener("rollOver",this.onItemOver);
            this.skill3_label.addEventListener("rollOut",this.onItemOver2);
            addChild(this.skill3_label);
         }
         else
         {
            this.skill3_label = this.createLabel("位置3伙伴羁绊-[未激活]",6,49,120,"left",TextFormatLib.grey_12px);
            addChild(this.skill3_label);
         }
      }
      
      private function onItemOver(param1:MouseEvent) : void
      {
         var _loc6_:int = int(Label(param1.target).x);
         var _loc5_:int = int(Label(param1.target).y);
         var _loc2_:int = int(Label(param1.target).name);
         var _loc4_:String = SkillAndBuffXMLManager.indexSkillName(_loc2_);
         var _loc3_:String = SkillAndBuffXMLManager.indexSkillDes(_loc2_);
         this.showTips("[" + _loc4_ + "]:" + _loc3_,x,y);
      }
      
      private function onItemOver2(param1:MouseEvent) : void
      {
         this.hiddTips();
      }
      
      private function createLabel(param1:String, param2:int, param3:int, param4:int, param5:String, param6:TextFormat) : Label
      {
         var _loc7_:Label = new Label(param1,param6,[FilterLib.glow_0x272727]);
         _loc7_.autoSize = param5;
         _loc7_.x = param2;
         _loc7_.y = param3;
         _loc7_.width = param4;
         _loc7_.height = 18;
         return _loc7_;
      }
      
      private function createBgSkin(param1:int, param2:int, param3:int = 119) : void
      {
         var _loc4_:UISkin = UIManager.getUISkin("level_bg");
         _loc4_.setSize(param3,18);
         _loc4_.x = param1;
         _loc4_.y = param2;
         addChild(_loc4_);
      }
   }
}

