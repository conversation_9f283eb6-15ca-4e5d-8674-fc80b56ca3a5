package formation.view.mc
{
   import flash.display.Sprite;
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.label.Label;
   import util.Globalization;
   
   public class FormationPanelMC extends Sprite
   {
      public var title:Label;
      
      private var pane_bg:UISkin;
      
      public function FormationPanelMC()
      {
         super();
         this.pane_bg = UIManager.getUISkin("pane_bg");
         this.pane_bg.setSize(424,378);
         addChild(this.pane_bg);
         this.title = new Label(Globalization.getString("formation.8"),TextFormatLib.format_0xebce82_12px,[FilterLib.glow_0x272727]);
         this.title.autoSize = "center";
         this.title.x = 25;
         this.title.y = 2;
         this.title.width = 400;
         this.title.height = 18;
         addChild(this.title);
      }
   }
}

