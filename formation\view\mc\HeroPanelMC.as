package formation.view.mc
{
   import game.manager.UIManager;
   import mmo.ext.filter.FilterLib;
   import mmo.ext.font.TextFormatLib;
   import mmo.ui.container.UIBox;
   import mmo.ui.control.UISkin;
   import mmo.ui.control.UISprite;
   import mmo.ui.control.label.Label;
   import mmo.ui.control.page.PageNavigator;
   import util.Globalization;
   
   public class HeroPanelMC extends UISprite
   {
      private var pane_bg:UISkin;
      
      public var title:Label;
      
      public var heroList:UIBox;
      
      public var btn_page:PageNavigator;
      
      public var desc_txt:Label;
      
      public function HeroPanelMC()
      {
         super();
         this.pane_bg = UIManager.getUISkin("pane_bg");
         this.pane_bg.setSize(140,378);
         addChild(this.pane_bg);
         this.title = new Label(Globalization.getString("copy.7"),TextFormatLib.format_0xebce82_12px,[FilterLib.glow_0x272727]);
         this.title.autoSize = "center";
         this.title.x = 20;
         this.title.y = 2;
         this.title.width = 102;
         this.title.height = 20;
         addChild(this.title);
         this.heroList = new UIBox();
         this.heroList.lineMaxChildrenNumber = 2;
         this.heroList.rowMaxChildrenNumber = 5;
         this.heroList.leftSpace = 9;
         this.heroList.topSpace = 0;
         this.heroList.lineSpace = 11;
         this.heroList.rowSpace = 14;
         this.heroList.x = 5;
         this.heroList.y = 26;
         addChild(this.heroList);
         this.btn_page = new PageNavigator();
         this.btn_page.x = 15;
         this.btn_page.y = 340;
         addChild(this.btn_page);
         this.desc_txt = new Label(Globalization.getString("formation.10"),TextFormatLib.format_0xFFB932_12px,[FilterLib.glow_0x272727]);
         this.desc_txt.y = 320;
         this.desc_txt.x = 13;
         addChild(this.desc_txt);
      }
   }
}

