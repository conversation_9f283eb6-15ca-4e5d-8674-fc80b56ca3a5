package game.data.user
{
   import flash.utils.Dictionary;
   import game.data.BindableData;
   import game.manager.XmlManager;
   
   public class UserAstrolabeData extends BindableData
   {
      private var _stone:int = 0;
      
      private var _currentViewTrayId:int = 0;
      
      private var _goldCount:int = 0;
      
      private var _bellyCount:int = 0;
      
      private var _activeSkill:Boolean = false;
      
      private var _mainMaxLevel:int = 11;
      
      private var _currentTrayId:int = 0;
      
      private var _couldTake:Boolean = false;
      
      private var _attrs:Vector.<Vector.<String>>;
      
      private var _attrByID:Dictionary;
      
      private var _mainLevel:int = 1;
      
      private const _trays:Dictionary = new Dictionary();
      
      private const _starts:Dictionary = new Dictionary();
      
      private var _mainAddAll:Dictionary;
      
      private var _giftAddAll:Dictionary;
      
      private var _mainIDGroupAll:Array;
      
      private var _mainAddAllConfig:Dictionary;
      
      private var _giftAddAllConfig:Dictionary;
      
      private var _giftFac:Dictionary;
      
      private var _giftNameInfo:Dictionary;
      
      private var _alertAgainNo:Array;
      
      public function UserAstrolabeData()
      {
         super();
         this.parseXml();
      }
      
      public function get currentViewTrayId() : int
      {
         return this._currentViewTrayId;
      }
      
      public function set currentViewTrayId(param1:int) : void
      {
         this._currentViewTrayId = param1;
      }
      
      override protected function getRemoteMethod() : String
      {
         return "astrolabe.askInitInfo";
      }
      
      override public function getOpenSwitchEnums() : Array
      {
         return [40];
      }
      
      override protected function parseProtoData(param1:Object) : void
      {
         this.parseInit(param1);
         this._initAll();
      }
      
      private function _initAll() : void
      {
         this._initStarConfig();
         this._initMainStar();
         this.initCurrentGift(String(this._currentTrayId));
      }
      
      public function parseInit(param1:Object) : void
      {
         var _loc3_:* = undefined;
         var _loc5_:* = undefined;
         var _loc4_:* = undefined;
         var _loc8_:AstrolabeNode = null;
         var _loc6_:Object = param1.cons_lev;
         var _loc7_:Object = param1.cons_status;
         var _loc2_:Object = param1.ast_status;
         this._couldTake = param1.can_get_exp != 0;
         this.bellyCount = param1.belly_count;
         this.goldCount = param1.vip_count;
         this.stone = param1.stone_num;
         this.activeSkill = param1.skill_stat == 1;
         this.mainLevel = param1.basic_ast_lev;
         this.currentTrayId = param1.talent_ast_id;
         if(_loc6_)
         {
            for(_loc3_ in _loc6_)
            {
               _loc8_ = this.starts[_loc3_];
               _loc8_.level = _loc6_[_loc3_];
            }
         }
         if(_loc7_)
         {
            for(_loc5_ in _loc7_)
            {
               _loc8_ = this.starts[_loc5_];
               _loc8_.state = _loc7_[_loc5_];
            }
         }
         if(_loc2_)
         {
            for(_loc4_ in _loc2_)
            {
               _loc8_ = this.trays[_loc4_][0] as AstrolabeNode;
               _loc8_.state = _loc2_[_loc4_];
            }
         }
      }
      
      private function _initStarConfig() : void
      {
         var _loc2_:XML = null;
         var _loc4_:Vector.<String> = null;
         var _loc3_:XML = null;
         var _loc5_:int = 0;
         var _loc1_:Array = null;
         var _loc10_:* = 0;
         var _loc12_:XML = null;
         var _loc6_:XML = null;
         var _loc11_:XMLList = XmlManager.starattri.starattri;
         var _loc9_:int = int(_loc11_.length());
         this._attrs = new Vector.<Vector.<String>>(_loc9_,true);
         this._attrByID = new Dictionary();
         var _loc7_:int = 0;
         while(_loc7_ < _loc9_)
         {
            _loc2_ = _loc11_[_loc7_];
            _loc4_ = Vector.<String>([String(_loc2_.@ID),String(_loc2_.@name),String(_loc2_.@rate),String(_loc2_.@factor),String(_loc2_.@br),String(_loc2_.@decimal),String(_loc2_.@order)]);
            _loc4_.fixed = true;
            this._attrs[_loc7_] = _loc4_;
            this._attrByID[_loc4_[0]] = _loc4_;
            _loc7_++;
         }
         var _loc8_:XMLList = XmlManager.starts.stars;
         this._mainAddAllConfig = new Dictionary();
         this._giftAddAllConfig = new Dictionary();
         var _loc13_:int = _loc8_.length() - 1;
         while(_loc13_ > -1)
         {
            _loc3_ = _loc8_[_loc13_];
            _loc5_ = int(_loc3_.attributes().length());
            _loc1_ = [];
            _loc10_ = _loc5_;
            while(_loc10_ > 0)
            {
               if(_loc3_.attribute("astAttr" + _loc10_).length() && String(_loc3_.attribute("astAttr" + _loc10_)) != "")
               {
                  _loc1_[_loc1_.length] = [String(_loc3_.attribute("astAttr" + _loc10_)),String(_loc3_.attribute("astAttr" + _loc10_ + "Value"))];
               }
               _loc10_--;
            }
            if(int(_loc3_.@isMain))
            {
               this._mainAddAllConfig[String(_loc3_.@astId)] = _loc1_;
            }
            else
            {
               this._giftAddAllConfig[String(_loc3_.@astId)] = _loc1_;
            }
            _loc13_--;
         }
         var _loc14_:XMLList = XmlManager.stargift.stargift;
         this._giftFac = new Dictionary();
         var _loc17_:int = _loc14_.length() - 1;
         while(_loc17_ > -1)
         {
            _loc12_ = _loc14_[_loc17_];
            this._giftFac[String(_loc12_.@giftID)] = [String(_loc12_.@phyattFac),String(_loc12_.@phydefFac),String(_loc12_.@ultattFac),String(_loc12_.@ultdefFac),String(_loc12_.@magattFac),String(_loc12_.@magdefFac)];
            _loc17_--;
         }
         this._giftNameInfo = new Dictionary();
         var _loc16_:XMLList = XmlManager.trayDower.trayDower;
         var _loc15_:int = _loc16_.length() - 1;
         while(_loc15_ > -1)
         {
            _loc6_ = _loc16_[_loc15_];
            this._giftNameInfo[String(_loc6_.@giftastID)] = [String(_loc6_.@name),String(_loc6_.@info)];
            _loc15_--;
         }
      }
      
      private function _initMainStar() : void
      {
         var _loc3_:Array = null;
         var _loc1_:int = 0;
         this._mainAddAll = new Dictionary();
         var _loc5_:XMLList = XmlManager.trayMain.trayMain;
         var _loc4_:int = int(_loc5_.length());
         this._mainIDGroupAll = [];
         var _loc2_:int = 0;
         while(_loc2_ < _loc4_)
         {
            _loc3_ = String(_loc5_[_loc2_].@mainIDGroup).split(",");
            this._mainIDGroupAll = this._mainIDGroupAll.concat(_loc3_);
            _loc2_++;
         }
         if(this._mainLevel)
         {
            _loc1_ = this._mainLevel - 1;
            while(_loc1_ > -1)
            {
               this.mainAttrAdd = this._mainIDGroupAll[_loc1_];
               _loc1_--;
            }
         }
      }
      
      public function set mainAttrAdd(param1:String) : void
      {
         this._attrAdd(param1,this._mainAddAllConfig,this._mainAddAll);
      }
      
      public function initCurrentGift(param1:String) : void
      {
         var _loc4_:Vector.<AstrolabeNode> = null;
         var _loc2_:int = 0;
         var _loc3_:AstrolabeNode = null;
         if(this._giftAddAll == null)
         {
            this._giftAddAll = new Dictionary();
         }
         if(int(param1) > 1)
         {
            this._giftAddAll[param1] = new Dictionary();
            _loc4_ = this._trays[param1];
            _loc2_ = _loc4_.length - 1;
            while(_loc2_ > -1)
            {
               _loc3_ = _loc4_[_loc2_];
               if(_loc3_.level)
               {
                  this._attrAdd(String(_loc3_.id),this._giftAddAllConfig,this._giftAddAll[param1],_loc3_.level);
               }
               _loc2_--;
            }
         }
      }
      
      public function set giftAttrAdd(param1:AstrolabeNode) : void
      {
         this._attrAdd(String(param1.id),this._giftAddAllConfig,this._giftAddAll[String(param1.trayID)]);
      }
      
      private function _attrAdd(param1:String, param2:Dictionary, param3:Dictionary, param4:int = 1) : void
      {
         var _loc10_:String = null;
         var _loc9_:Number = NaN;
         var _loc7_:Dictionary = null;
         var _loc8_:Number = NaN;
         var _loc5_:Array = param2[param1];
         var _loc6_:int = int(_loc5_.length);
         var _loc11_:int = _loc6_ - 1;
         while(_loc11_ > -1)
         {
            _loc10_ = _loc5_[_loc11_][0];
            _loc9_ = Number(_loc5_[_loc11_][1]) * param4;
            _loc7_ = param3["dicValues"];
            if(!_loc7_)
            {
               param3["dicValues"] = _loc7_ = new Dictionary();
            }
            _loc8_ = !!_loc7_[_loc10_] ? Number(_loc7_[_loc10_]) : 0;
            if(param3[_loc10_])
            {
               param3[_loc10_] = this.getfiniteDecimals((_loc8_ + _loc9_) / Number(this._attrByID[_loc10_][3]),Number(this._attrByID[_loc10_][5]));
            }
            else
            {
               param3[_loc10_] = this.getfiniteDecimals(_loc9_ / Number(this._attrByID[_loc10_][3]),Number(this._attrByID[_loc10_][5]));
            }
            _loc8_ += _loc9_;
            _loc7_[_loc10_] = _loc8_;
            _loc11_--;
         }
      }
      
      private function parseXml() : void
      {
         var _loc10_:XML = null;
         var _loc7_:Array = null;
         var _loc8_:* = undefined;
         var _loc1_:Array = null;
         var _loc3_:Array = null;
         var _loc6_:Vector.<AstrolabeNode> = null;
         var _loc5_:* = undefined;
         var _loc4_:* = undefined;
         var _loc2_:Array = null;
         var _loc9_:AstrolabeNode = null;
         this._mainMaxLevel = 0;
         for each(_loc10_ in XmlManager.trayMain.children())
         {
            _loc7_ = String(_loc10_.@mainIDGroup).split(",");
            this._mainMaxLevel += _loc7_.length;
         }
         for each(_loc10_ in XmlManager.starts.children())
         {
            if(int(_loc10_.@isMain) != 1)
            {
               _loc9_ = new AstrolabeNode(_loc10_.@astId,_loc10_.@trayId);
               this.starts[_loc9_.id] = _loc9_;
            }
         }
         for each(_loc10_ in XmlManager.trayDower.children())
         {
            if(<EMAIL>() != "")
            {
               _loc1_ = <EMAIL>().split(",");
               _loc3_ = <EMAIL>().split(",");
               _loc6_ = new Vector.<AstrolabeNode>();
               this.trays[int(_loc10_.@giftastID)] = _loc6_;
               for(_loc8_ in _loc1_)
               {
                  _loc5_ = _loc1_[_loc8_];
                  _loc4_ = _loc3_[_loc8_];
                  _loc2_ = String(_loc4_).split("|");
                  _loc9_ = this.starts[int(_loc5_)];
                  _loc9_.x = _loc2_[0];
                  _loc9_.y = _loc2_[1];
                  _loc6_.push(_loc9_);
               }
            }
         }
      }
      
      public function hadOpenDower() : Boolean
      {
         var _loc1_:Vector.<AstrolabeNode> = null;
         for each(_loc1_ in this._trays)
         {
            if(_loc1_.length)
            {
               if(_loc1_[0].state != 0)
               {
                  return true;
               }
            }
         }
         return false;
      }
      
      public function get isMaxLevel() : Boolean
      {
         return this._mainMaxLevel == this._mainLevel;
      }
      
      public function updateStarts(param1:Object) : void
      {
         var _loc3_:* = undefined;
         var _loc2_:AstrolabeNode = null;
         if(param1)
         {
            for(_loc3_ in param1)
            {
               _loc2_ = this.starts[_loc3_];
               _loc2_.state = param1[_loc3_];
            }
         }
      }
      
      public function updateTrays(param1:Object) : void
      {
         var _loc3_:* = undefined;
         var _loc2_:AstrolabeNode = null;
         if(param1)
         {
            for(_loc3_ in param1)
            {
               _loc2_ = this.trays[_loc3_][0];
               _loc2_.state = param1[_loc3_];
            }
         }
      }
      
      public function updateStartsLevel(param1:Object) : void
      {
         var _loc3_:* = undefined;
         var _loc2_:AstrolabeNode = null;
         if(param1)
         {
            for(_loc3_ in param1)
            {
               _loc2_ = this.starts[_loc3_];
               _loc2_.level = param1[_loc3_];
            }
         }
      }
      
      public function resetStartsToZero(param1:Object) : void
      {
         var _loc3_:* = undefined;
         var _loc2_:AstrolabeNode = null;
         if(param1)
         {
            for(_loc3_ in param1)
            {
               _loc2_ = this.starts[_loc3_];
               _loc2_.level = 0;
            }
         }
      }
      
      public function get mainLevel() : int
      {
         return this._mainLevel;
      }
      
      public function set mainLevel(param1:int) : void
      {
         this._mainLevel = param1;
      }
      
      public function get starts() : Dictionary
      {
         return this._starts;
      }
      
      public function get trays() : Dictionary
      {
         return this._trays;
      }
      
      public function get currentTrayId() : int
      {
         return this._currentTrayId;
      }
      
      public function set currentTrayId(param1:int) : void
      {
         this._currentTrayId = param1;
      }
      
      public function get activeSkill() : Boolean
      {
         return this._activeSkill;
      }
      
      public function set activeSkill(param1:Boolean) : void
      {
         this._activeSkill = param1;
      }
      
      public function get bellyCount() : int
      {
         return this._bellyCount;
      }
      
      public function set bellyCount(param1:int) : void
      {
         this._bellyCount = param1;
      }
      
      public function get goldCount() : int
      {
         return this._goldCount;
      }
      
      public function set goldCount(param1:int) : void
      {
         this._goldCount = param1;
      }
      
      public function get stone() : int
      {
         return this._stone;
      }
      
      public function set stone(param1:int) : void
      {
         this._stone = param1;
      }
      
      public function get mainMaxLevel() : int
      {
         return this._mainMaxLevel;
      }
      
      public function set mainMaxLevel(param1:int) : void
      {
         this._mainMaxLevel = param1;
      }
      
      public function get attrs() : Vector.<Vector.<String>>
      {
         return this._attrs;
      }
      
      public function get couldTake() : Boolean
      {
         return this._couldTake;
      }
      
      public function set couldTake(param1:Boolean) : void
      {
         this._couldTake = param1;
      }
      
      public function get attrByID() : Dictionary
      {
         return this._attrByID;
      }
      
      public function get mainAddAll() : Dictionary
      {
         return this._mainAddAll;
      }
      
      public function getfiniteDecimals(param1:Number, param2:int, param3:Boolean = true) : String
      {
         var _loc5_:String = null;
         var _loc6_:int = 0;
         var _loc7_:Array = String(param1).split(".");
         var _loc4_:String = _loc7_[0];
         if(_loc7_.length == 2)
         {
            _loc5_ = String(_loc7_[1]).substr(0,param2);
            if(param3)
            {
               _loc6_ = _loc5_.length - 1;
               while(_loc6_ > -1)
               {
                  if(_loc5_.charAt(_loc6_) != "0")
                  {
                     break;
                  }
                  _loc5_ = _loc5_.slice(0,_loc6_);
                  _loc6_--;
               }
            }
            if(_loc5_.length)
            {
               _loc4_ += "." + _loc5_;
            }
         }
         return _loc4_;
      }
      
      public function get mainIDGroupAll() : Array
      {
         return this._mainIDGroupAll;
      }
      
      public function get giftAddAll() : Dictionary
      {
         return this._giftAddAll;
      }
      
      public function get giftFac() : Dictionary
      {
         return this._giftFac;
      }
      
      public function get giftNameInfo() : Dictionary
      {
         return this._giftNameInfo;
      }
      
      public function getAlertAgainNo(param1:String) : Boolean
      {
         var onlyName:String = param1;
         var alertAgainNoFun:Function = function(param1:*, param2:int, param3:Array):Boolean
         {
            return param1 == onlyName;
         };
         var againNo:Boolean = Boolean(this._alertAgainNo.some(alertAgainNoFun));
         return againNo;
      }
      
      public function setAlertAgainNo(param1:String) : void
      {
         if(this._alertAgainNo == null)
         {
            this._alertAgainNo = [];
         }
         this._alertAgainNo[this._alertAgainNo.length] = param1;
      }
      
      public function get alertAgainNo() : Array
      {
         return this._alertAgainNo;
      }
   }
}

